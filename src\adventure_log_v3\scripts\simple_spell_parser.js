/**
 * 简化的法术解析脚本
 */

const fs = require('fs');
const path = require('path');

// 伤害类型映射
const DAMAGE_TYPE_MAP = {
  '火焰': 'fire',
  '寒冷': 'cold', 
  '闪电': 'lightning',
  '雷鸣': 'thunder',
  '强酸': 'acid',
  '毒素': 'poison',
  '光耀': 'radiant',
  '黯蚀': 'necrotic',
  '力场': 'force',
  '穿刺': 'piercing',
  '挥砍': 'slashing',
  '钝击': 'bludgeoning',
  '心灵': 'psychic'
};

/**
 * 解析HTML文件中的法术数据
 */
function parseSpellsFromHTML(htmlContent, level) {
  const spells = [];
  
  // 简化的正则表达式匹配法术条目
  const spellPattern = /<H4[^>]*id="([^"]*)"[^>]*>([^<]*)<\/H4>/g;
  
  let match;
  while ((match = spellPattern.exec(htmlContent)) !== null) {
    const [, id, nameWithEn] = match;
    
    // 解析中英文名称
    const nameMatch = nameWithEn.match(/^([^（]+)(?:（([^）]+)）)?/);
    if (!nameMatch) continue;
    
    const name_zh = nameMatch[1].trim();
    const name_en = nameMatch[2] || id.replace(/_/g, ' ');
    
    // 创建基础法术对象
    const spell = {
      name_zh: name_zh,
      name_en: name_en,
      level: level,
      school: '未知',
      casting_time: '1 动作',
      range: '未知',
      components: ['V', 'S'],
      duration: '立即',
      description_short: `${name_zh}是一个${level}环法术。`,
      description_long: `${name_zh}(${name_en})是一个${level}环法术，具体效果请参考玩家手册。`
    };
    
    // 尝试从HTML中提取更多信息
    const spellContentPattern = new RegExp(`<H4[^>]*id="${id}"[^>]*>.*?(?=<H4|$)`, 's');
    const contentMatch = htmlContent.match(spellContentPattern);
    
    if (contentMatch) {
      const content = contentMatch[0];
      
      // 解析学派
      const schoolMatch = content.match(/<EM>([^<]*)<\/EM>/);
      if (schoolMatch) {
        const schoolInfo = schoolMatch[1];
        const schoolParts = schoolInfo.split(' ');
        if (schoolParts.length > 1) {
          spell.school = schoolParts[1];
        }
      }
      
      // 解析施法时间
      const castingTimeMatch = content.match(/施法时间：<\/STRONG>([^<]*)/);
      if (castingTimeMatch) {
        spell.casting_time = castingTimeMatch[1].trim();
      }
      
      // 解析施法距离
      const rangeMatch = content.match(/施法距离：<\/STRONG>([^<]*)/);
      if (rangeMatch) {
        spell.range = rangeMatch[1].trim();
      }
      
      // 解析法术成分
      const componentsMatch = content.match(/法术成分：<\/STRONG>([^<]*)/);
      if (componentsMatch) {
        spell.components = componentsMatch[1].split('，').map(c => c.trim());
      }
      
      // 解析持续时间
      const durationMatch = content.match(/持续时间：<\/STRONG>([^<]*)/);
      if (durationMatch) {
        spell.duration = durationMatch[1].trim();
      }
      
      // 解析伤害
      const damageMatch = content.match(/(\d+d\d+(?:\+\d+)?)/);
      if (damageMatch) {
        spell.damage = damageMatch[1];
      }
      
      // 解析伤害类型
      for (const [zh] of Object.entries(DAMAGE_TYPE_MAP)) {
        if (content.includes(zh)) {
          spell.damage_type = zh;
          break;
        }
      }
      
      // 解析攻击类型
      if (content.includes('远程法术攻击')) {
        spell.attack_type = '法术攻击 (远程)';
      } else if (content.includes('近战法术攻击')) {
        spell.attack_type = '法术攻击 (近战)';
      } else if (content.includes('自动命中')) {
        spell.attack_type = '自动命中';
      }
      
      // 解析豁免检定
      const saveMatch = content.match(/([^，]+)豁免/);
      if (saveMatch) {
        const saveAttr = saveMatch[1].trim();
        spell.save = {
          attribute: saveAttr,
          effect_on_success: content.includes('伤害减半') ? '伤害减半' : '无效果'
        };
      }
      
      // 提取描述
      const cleanContent = content.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
      const descParts = cleanContent.split('。');
      if (descParts.length > 1) {
        spell.description_short = descParts[0] + '。';
        spell.description_long = cleanContent;
      }
    }
    
    spells.push(spell);
  }
  
  return spells;
}

/**
 * 主解析函数
 */
async function parseAllSpells() {
  const allSpells = [];
  const baseDir = '../../../DND5e_chm-main/DND5e_chm-main/玩家手册2024/法术详述';
  
  // 解析每个等级的法术文件
  for (let level = 0; level <= 9; level++) {
    const filename = `${level}环.htm`;
    const filepath = path.join(baseDir, filename);
    
    try {
      console.log(`正在解析 ${level}环法术...`);
      const htmlContent = fs.readFileSync(filepath, 'utf-8');
      const spells = parseSpellsFromHTML(htmlContent, level);
      
      console.log(`${level}环法术解析完成，共 ${spells.length} 个法术`);
      allSpells.push(...spells);
    } catch (error) {
      console.error(`解析 ${level}环法术时出错:`, error.message);
    }
  }
  
  console.log(`总共解析了 ${allSpells.length} 个法术`);
  return allSpells;
}

/**
 * 生成世界书格式的法术数据
 */
function generateWorldBookEntries(spells) {
  const entries = {};
  let uid = 1000; // 从1000开始，避免与现有条目冲突
  
  // 按等级分组法术
  const spellsByLevel = {};
  spells.forEach(spell => {
    if (!spellsByLevel[spell.level]) {
      spellsByLevel[spell.level] = [];
    }
    spellsByLevel[spell.level].push(spell);
  });
  
  // 为每个等级创建法术包条目
  for (const [level, levelSpells] of Object.entries(spellsByLevel)) {
    const levelNum = parseInt(level);
    const levelName = levelNum === 0 ? '戏法' : `${levelNum}环法术`;
    
    entries[uid] = {
      uid: uid,
      key: [`SPELLS_LEVEL_${levelNum}`, `${levelName}`, `LEVEL_${levelNum}_SPELLS`],
      keysecondary: [],
      comment: `${levelName}合集 (${levelSpells.length}个法术)`,
      content: JSON.stringify(levelSpells, null, 2),
      constant: true,
      vectorized: false,
      selective: true,
      selectiveLogic: 0,
      addMemo: true,
      order: 100,
      position: 0,
      disable: false,
      excludeRecursion: false,
      preventRecursion: false,
      delayUntilRecursion: false,
      probability: 100,
      useProbability: true,
      depth: 4,
      group: "",
      groupOverride: false,
      groupWeight: 100,
      scanDepth: null,
      caseSensitive: null,
      matchWholeWords: null,
      useGroupScoring: null,
      automationId: "",
      role: null,
      sticky: 0,
      cooldown: 0,
      delay: 0,
      displayIndex: uid
    };
    uid++;
  }
  
  // 创建完整法术库条目
  entries[uid] = {
    uid: uid,
    key: ["SPELLS_ALL", "所有法术", "ALL_SPELLS", "法术库"],
    keysecondary: [],
    comment: `完整法术库 (${spells.length}个法术)`,
    content: JSON.stringify(spells, null, 2),
    constant: true,
    vectorized: false,
    selective: true,
    selectiveLogic: 0,
    addMemo: true,
    order: 100,
    position: 0,
    disable: false,
    excludeRecursion: false,
    preventRecursion: false,
    delayUntilRecursion: false,
    probability: 100,
    useProbability: true,
    depth: 4,
    group: "",
    groupOverride: false,
    groupWeight: 100,
    scanDepth: null,
    caseSensitive: null,
    matchWholeWords: null,
    useGroupScoring: null,
    automationId: "",
    role: null,
    sticky: 0,
    cooldown: 0,
    delay: 0,
    displayIndex: uid
  };
  
  return { entries };
}

// 如果直接运行此脚本
if (require.main === module) {
  parseAllSpells().then(spells => {
    const worldBook = generateWorldBookEntries(spells);
    
    // 保存到文件
    const outputPath = '../../../DND5e_Spells_WorldBook.json';
    fs.writeFileSync(outputPath, JSON.stringify(worldBook, null, 2), 'utf-8');
    console.log(`世界书文件已生成: ${outputPath}`);
    console.log(`包含 ${spells.length} 个法术，分为 ${Object.keys(worldBook.entries).length} 个世界书条目`);
  }).catch(console.error);
}
