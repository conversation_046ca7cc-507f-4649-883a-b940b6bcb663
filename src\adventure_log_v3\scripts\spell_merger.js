/**
 * 法术合并器 - 将所有环数的法术合并成最终的世界书文件
 */

const fs = require('fs');

// 导入所有环数的法术生成器
const { CANTRIPS, generateCantripWorldBook } = require('./spell_generator_level_0.js');
const { LEVEL_1_SPELLS, generateLevel1WorldBook } = require('./spell_generator_level_1.js');
const { LEVEL_2_SPELLS, generateLevel2WorldBook } = require('./spell_generator_level_2.js');
const { LEVEL_3_SPELLS, generateLevel3WorldBook } = require('./spell_generator_level_3.js');
const { LEVEL_4_SPELLS, generateLevel4WorldBook } = require('./spell_generator_level_4.js');
const { LEVEL_5_SPELLS, generateLevel5WorldBook } = require('./spell_generator_level_5.js');
const { LEVEL_6_SPELLS, generateLevel6WorldBook } = require('./spell_generator_level_6.js');

// 环数名称映射
const LEVEL_NAMES = {
  0: '戏法',
  1: '一环法术',
  2: '二环法术',
  3: '三环法术',
  4: '四环法术',
  5: '五环法术',
  6: '六环法术',
  7: '七环法术',
  8: '八环法术',
  9: '九环法术'
};

/**
 * 生成所有单独的环数世界书文件
 */
function generateAllLevelWorldBooks() {
  console.log('=== 开始生成所有环数的法术世界书 ===\n');
  
  // 生成已有的环数世界书
  console.log('生成戏法世界书...');
  generateCantripWorldBook();
  
  console.log('生成1环法术世界书...');
  generateLevel1WorldBook();
  
  console.log('生成2环法术世界书...');
  generateLevel2WorldBook();
  
  console.log('生成3环法术世界书...');
  generateLevel3WorldBook();

  console.log('生成4环法术世界书...');
  generateLevel4WorldBook();

  console.log('生成5环法术世界书...');
  generateLevel5WorldBook();

  console.log('生成6环法术世界书...');
  generateLevel6WorldBook();

  // 为7-9环生成空的占位符世界书
  for (let level = 7; level <= 9; level++) {
    generatePlaceholderWorldBook(level);
  }
  
  console.log('\n所有单独环数的世界书文件生成完成！');
}

/**
 * 为高环法术生成占位符世界书
 */
function generatePlaceholderWorldBook(level) {
  const levelName = LEVEL_NAMES[level];
  const placeholderSpells = [
    {
      name_zh: `${levelName}占位符`,
      name_en: `Level ${level} Placeholder`,
      level: level,
      school: '占位',
      casting_time: '待补充',
      range: '待补充',
      components: ['待补充'],
      duration: '待补充',
      description_short: `${levelName}法术数据待补充，请使用其他环数的完整法术数据。`,
      description_long: `这是${levelName}的占位符条目。完整的${levelName}法术数据将在后续版本中添加。目前请使用0-3环的完整法术数据进行游戏。`
    }
  ];
  
  const worldBook = {
    entries: {
      [5000 + level]: {
        uid: 5000 + level,
        key: [`AI_LEVEL_${level}_SPELLS`, `AI${levelName}`, `DND5E_AI_LEVEL_${level}`, `AI_SPELLS_LEVEL_${level}`],
        keysecondary: [],
        comment: `AI生成的DND5e ${levelName}占位符 (待补充完整数据)`,
        content: JSON.stringify(placeholderSpells, null, 2),
        constant: true,
        vectorized: false,
        selective: true,
        selectiveLogic: 0,
        addMemo: true,
        order: 100,
        position: 0,
        disable: false,
        excludeRecursion: false,
        preventRecursion: false,
        delayUntilRecursion: false,
        probability: 100,
        useProbability: true,
        depth: 4,
        group: "",
        groupOverride: false,
        groupWeight: 100,
        scanDepth: null,
        caseSensitive: null,
        matchWholeWords: null,
        useGroupScoring: null,
        automationId: "",
        role: null,
        sticky: 0,
        cooldown: 0,
        delay: 0,
        displayIndex: 5000 + level
      }
    }
  };
  
  const outputPath = `AI_DND5e_Level${level}_Placeholder.json`;
  fs.writeFileSync(outputPath, JSON.stringify(worldBook, null, 2), 'utf-8');
  console.log(`生成${levelName}占位符世界书: ${outputPath}`);
}

/**
 * 合并所有法术到一个完整的世界书文件
 */
function generateCompleteSpellWorldBook() {
  console.log('\n=== 开始合并完整法术世界书 ===');
  
  // 收集所有法术数据
  const allSpells = [
    ...CANTRIPS,
    ...LEVEL_1_SPELLS,
    ...LEVEL_2_SPELLS,
    ...LEVEL_3_SPELLS,
    ...LEVEL_4_SPELLS,
    ...LEVEL_5_SPELLS,
    ...LEVEL_6_SPELLS
  ];
  
  console.log(`总共收集了 ${allSpells.length} 个完整法术数据`);
  
  // 按等级统计
  const spellCounts = {};
  allSpells.forEach(spell => {
    spellCounts[spell.level] = (spellCounts[spell.level] || 0) + 1;
  });
  
  console.log('法术分布统计:');
  Object.entries(spellCounts).forEach(([level, count]) => {
    console.log(`  ${LEVEL_NAMES[level]}: ${count}个`);
  });
  
  // 生成完整世界书
  const completeWorldBook = {
    entries: {
      6000: {
        uid: 6000,
        key: ["AI_ALL_SPELLS", "AI所有法术", "DND5E_AI_COMPLETE", "AI法术完整库", "COMPLETE_SPELL_DATABASE"],
        keysecondary: ["法术库", "完整法术", "DND5E法术", "AI法术数据"],
        comment: `AI生成的DND5e完整法术库 (${allSpells.length}个完整法术，包含0-3环完整数据)`,
        content: JSON.stringify(allSpells, null, 2),
        constant: true,
        vectorized: false,
        selective: true,
        selectiveLogic: 0,
        addMemo: true,
        order: 100,
        position: 0,
        disable: false,
        excludeRecursion: false,
        preventRecursion: false,
        delayUntilRecursion: false,
        probability: 100,
        useProbability: true,
        depth: 4,
        group: "",
        groupOverride: false,
        groupWeight: 100,
        scanDepth: null,
        caseSensitive: null,
        matchWholeWords: null,
        useGroupScoring: null,
        automationId: "",
        role: null,
        sticky: 0,
        cooldown: 0,
        delay: 0,
        displayIndex: 6000
      }
    }
  };
  
  const outputPath = 'AI_DND5e_Complete_Spell_Library.json';
  fs.writeFileSync(outputPath, JSON.stringify(completeWorldBook, null, 2), 'utf-8');
  console.log(`\n完整法术库世界书已生成: ${outputPath}`);
  console.log(`包含 ${allSpells.length} 个完整法术数据`);
  
  return allSpells;
}

/**
 * 生成使用说明文档
 */
function generateUsageGuide() {
  const guide = `# AI生成的DND5e法术世界书使用指南

## 📋 文件说明

### 单独环数文件（推荐按需使用）
- \`AI_DND5e_Cantrips_Complete.json\` - 戏法（0环）完整数据 (${CANTRIPS.length}个法术)
- \`AI_DND5e_Level1_Complete.json\` - 1环法术完整数据 (${LEVEL_1_SPELLS.length}个法术)
- \`AI_DND5e_Level2_Complete.json\` - 2环法术完整数据 (${LEVEL_2_SPELLS.length}个法术)
- \`AI_DND5e_Level3_Complete.json\` - 3环法术完整数据 (${LEVEL_3_SPELLS.length}个法术)
- \`AI_DND5e_Level4_Complete.json\` - 4环法术完整数据 (${LEVEL_4_SPELLS.length}个法术)
- \`AI_DND5e_Level5_Complete.json\` - 5环法术完整数据 (${LEVEL_5_SPELLS.length}个法术)
- \`AI_DND5e_Level6_Complete.json\` - 6环法术完整数据 (${LEVEL_6_SPELLS.length}个法术)
- \`AI_DND5e_Level7_Placeholder.json\` - 7环法术占位符（待补充）
- \`AI_DND5e_Level8_Placeholder.json\` - 8环法术占位符（待补充）
- \`AI_DND5e_Level9_Placeholder.json\` - 9环法术占位符（待补充）

### 完整合并文件
- \`AI_DND5e_Complete_Spell_Library.json\` - 包含所有0-6环完整法术数据

## 🎯 使用方式

### 1. 按需导入（推荐）
根据角色等级和需要，选择性导入对应环数的世界书：
- 低级角色：导入戏法和1-2环法术
- 中级角色：导入1-5环法术
- 高级角色：导入所有法术

### 2. 完整导入
直接导入完整法术库，一次性获得所有可用法术数据。

## 🔍 检索关键词

每个世界书都包含多个检索关键词：
- \`AI_CANTRIPS\`, \`AI戏法\` - 戏法
- \`AI_LEVEL_1_SPELLS\`, \`AI一环法术\` - 1环法术
- \`AI_ALL_SPELLS\`, \`AI所有法术\` - 完整库
- \`DND5E_AI_COMPLETE\`, \`完整法术\` - 完整库

## 📊 法术数据格式

每个法术包含完整的游戏属性：
- 中英文名称
- 法术等级和学派
- 施法时间、距离、成分、持续时间
- 详细描述
- 伤害、豁免、攻击类型
- 升环效果
- 作用范围等

## 🔧 系统集成

这些世界书文件可以直接与现有的法术系统集成：
1. 导入酒馆世界书
2. 修改 \`src/adventure_log_v3/spells/index.ts\` 加载世界书法术
3. 在法术书界面中显示和使用

## 📝 后续扩展

7-9环法术数据将在后续版本中补充完整。目前的0-6环法术数据已经足够支持大部分低高级游戏内容。

---
生成时间: ${new Date().toLocaleString()}
总法术数: ${CANTRIPS.length + LEVEL_1_SPELLS.length + LEVEL_2_SPELLS.length + LEVEL_3_SPELLS.length + LEVEL_4_SPELLS.length + LEVEL_5_SPELLS.length + LEVEL_6_SPELLS.length}个完整法术
`;

  fs.writeFileSync('AI_DND5e_Spell_Usage_Guide.md', guide, 'utf-8');
  console.log('使用指南已生成: AI_DND5e_Spell_Usage_Guide.md');
}

/**
 * 主执行函数
 */
function main() {
  console.log('🧙‍♂️ AI DND5e法术世界书生成器');
  console.log('=====================================\n');
  
  try {
    // 生成所有单独的环数世界书
    generateAllLevelWorldBooks();
    
    // 生成完整合并的世界书
    generateCompleteSpellWorldBook();
    
    // 生成使用指南
    generateUsageGuide();
    
    console.log('\n🎉 所有法术世界书文件生成完成！');
    console.log('📁 请查看生成的文件：');
    console.log('   - 单独环数文件：AI_DND5e_Level*_Complete.json');
    console.log('   - 完整合并文件：AI_DND5e_Complete_Spell_Library.json');
    console.log('   - 使用指南：AI_DND5e_Spell_Usage_Guide.md');
    console.log('\n✨ 现在可以将这些文件导入酒馆使用了！');
    
  } catch (error) {
    console.error('❌ 生成过程中出现错误:', error);
  }
}

// 执行主函数
if (require.main === module) {
  main();
}

module.exports = {
  generateAllLevelWorldBooks,
  generateCompleteSpellWorldBook,
  generateUsageGuide,
  main
};
