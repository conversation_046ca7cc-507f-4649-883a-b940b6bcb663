Html Webpack Plugin:
<pre>
  Error: Child compilation failed:
  Module not found: Error: Can't resolve 'F:\tavern\tavern_helper_template\src\界面模板\index.html' in 'F:\tavern\tavern_helper_template'
  ModuleNotFoundError: Module not found: Error: Can't resolve 'F:\tavern\tavern_helper_template\src\界面模板\index.html' in 'F:\tavern\tavern_helper_  template'
      at F:\tavern\tavern_helper_template\node_modules\.pnpm\webpack@5.99.9_webpack-cli@6.0.1\node_modules\webpack\lib\Compilation.js:2230:28
      at F:\tavern\tavern_helper_template\node_modules\.pnpm\webpack@5.99.9_webpack-cli@6.0.1\node_modules\webpack\lib\NormalModuleFactory.js:933  :13
      at eval (eval at create (F:\tavern\tavern_helper_template\node_modules\.pnpm\tapable@2.2.2\node_modules\tapable\lib\HookCodeFactory.js:33:1  0), <anonymous>:10:1)
      at F:\tavern\tavern_helper_template\node_modules\.pnpm\webpack@5.99.9_webpack-cli@6.0.1\node_modules\webpack\lib\NormalModuleFactory.js:347  :22
      at eval (eval at create (F:\tavern\tavern_helper_template\node_modules\.pnpm\tapable@2.2.2\node_modules\tapable\lib\HookCodeFactory.js:33:1  0), <anonymous>:9:1)
      at F:\tavern\tavern_helper_template\node_modules\.pnpm\webpack@5.99.9_webpack-cli@6.0.1\node_modules\webpack\lib\NormalModuleFactory.js:530  :22
      at F:\tavern\tavern_helper_template\node_modules\.pnpm\webpack@5.99.9_webpack-cli@6.0.1\node_modules\webpack\lib\NormalModuleFactory.js:163  :10
      at F:\tavern\tavern_helper_template\node_modules\.pnpm\webpack@5.99.9_webpack-cli@6.0.1\node_modules\webpack\lib\NormalModuleFactory.js:794  :25
      at F:\tavern\tavern_helper_template\node_modules\.pnpm\webpack@5.99.9_webpack-cli@6.0.1\node_modules\webpack\lib\NormalModuleFactory.js:101  8:8
      at F:\tavern\tavern_helper_template\node_modules\.pnpm\webpack@5.99.9_webpack-cli@6.0.1\node_modules\webpack\lib\NormalModuleFactory.js:114  7:5
  
  - Compilation.js:2230 
    [tavern_helper_template]/[webpack@5.99.9_webpack-cli@6.0.1]/[webpack]/lib/Compilation.js:2230:28
  
  - NormalModuleFactory.js:933 
    [tavern_helper_template]/[webpack@5.99.9_webpack-cli@6.0.1]/[webpack]/lib/NormalModuleFactory.js:933:13
  
  
  - NormalModuleFactory.js:347 
    [tavern_helper_template]/[webpack@5.99.9_webpack-cli@6.0.1]/[webpack]/lib/NormalModuleFactory.js:347:22
  
  
  - NormalModuleFactory.js:530 
    [tavern_helper_template]/[webpack@5.99.9_webpack-cli@6.0.1]/[webpack]/lib/NormalModuleFactory.js:530:22
  
  - NormalModuleFactory.js:163 
    [tavern_helper_template]/[webpack@5.99.9_webpack-cli@6.0.1]/[webpack]/lib/NormalModuleFactory.js:163:10
  
  - NormalModuleFactory.js:794 
    [tavern_helper_template]/[webpack@5.99.9_webpack-cli@6.0.1]/[webpack]/lib/NormalModuleFactory.js:794:25
  
  - NormalModuleFactory.js:1018 
    [tavern_helper_template]/[webpack@5.99.9_webpack-cli@6.0.1]/[webpack]/lib/NormalModuleFactory.js:1018:8
  
  - NormalModuleFactory.js:1147 
    [tavern_helper_template]/[webpack@5.99.9_webpack-cli@6.0.1]/[webpack]/lib/NormalModuleFactory.js:1147:5
  
  - child-compiler.js:211 
    [tavern_helper_template]/[html-webpack-plugin@5.6.3_webpack@5.99.9]/[html-webpack-plugin]/lib/child-compiler.js:211:18
  
  - Compiler.js:625 finalCallback
    [tavern_helper_template]/[webpack@5.99.9_webpack-cli@6.0.1]/[webpack]/lib/Compiler.js:625:5
  
  - Compiler.js:661 
    [tavern_helper_template]/[webpack@5.99.9_webpack-cli@6.0.1]/[webpack]/lib/Compiler.js:661:11
  
  - Compiler.js:1347 
    [tavern_helper_template]/[webpack@5.99.9_webpack-cli@6.0.1]/[webpack]/lib/Compiler.js:1347:17
  
  
  - task_queues:105 processTicksAndRejections
    node:internal/process/task_queues:105:5
  
</pre>