<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色状态栏</title>
<style>/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@7.1.2_webpack@5.99.9/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[0].oneOf[5].use[1]!./node_modules/.pnpm/postcss-loader@8.1.1_postcs_07f94889eb412ae0ff5565af90e6a7c5/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[0].oneOf[5].use[2]!./node_modules/.pnpm/sass-loader@16.0.5_sass@1.89.0_webpack@5.99.9/node_modules/sass-loader/dist/cjs.js!./src/界面/scss/phone.scss ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.phone-container {
  width: 300px;
  aspect-ratio: 9/19.5;
  border: 8px solid black;
  border-radius: 36px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
  background-color: #f0f0f0;
  margin: 20px auto;
}

.phone-notch {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40%;
  height: 20px;
  background-color: black;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  z-index: 2;
}

.phone-screen {
  width: 100%;
  height: 100%;
  background-color: white;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@7.1.2_webpack@5.99.9/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[0].oneOf[5].use[1]!./node_modules/.pnpm/postcss-loader@8.1.1_postcs_07f94889eb412ae0ff5565af90e6a7c5/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[0].oneOf[5].use[2]!./node_modules/.pnpm/sass-loader@16.0.5_sass@1.89.0_webpack@5.99.9/node_modules/sass-loader/dist/cjs.js!./src/界面/scss/statusbar.scss ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.status-bar {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: #f8f8f8;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
  margin-top: 20px;
}
.status-bar .character-avatar img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
  object-fit: cover;
}
.status-bar .character-name {
  font-weight: bold;
  margin-right: auto;
}
.status-bar .character-status {
  font-size: 0.9em;
  color: #555;
}
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@7.1.2_webpack@5.99.9/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[0].oneOf[5].use[1]!./node_modules/.pnpm/postcss-loader@8.1.1_postcs_07f94889eb412ae0ff5565af90e6a7c5/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[0].oneOf[5].use[2]!./node_modules/.pnpm/sass-loader@16.0.5_sass@1.89.0_webpack@5.99.9/node_modules/sass-loader/dist/cjs.js!./src/界面/scss/chat.scss ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.chat-interface {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  padding: 10px;
  overflow: hidden;
}
.chat-interface .chat-messages {
  flex-grow: 1;
  overflow-y: auto;
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
}
.chat-interface .chat-messages .message {
  max-width: 70%;
  padding: 8px 12px;
  border-radius: 18px;
  margin-bottom: 8px;
  word-wrap: break-word;
}
.chat-interface .chat-messages .message p {
  margin: 0;
}
.chat-interface .chat-messages .message.received {
  background-color: #e9e9eb;
  align-self: flex-start;
  border-bottom-left-radius: 4px;
}
.chat-interface .chat-messages .message.sent {
  background-color: #007bff;
  color: white;
  align-self: flex-end;
  border-bottom-right-radius: 4px;
}
.chat-interface .chat-input {
  display: flex;
  flex-shrink: 0;
}
.chat-interface .chat-input input {
  flex-grow: 1;
  padding: 10px;
  border-top: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
  border-left: 1px solid #ccc;
  border-right: none;
  border-radius: 20px 0 0 20px;
  margin-right: 0;
  outline: none;
}
.chat-interface .chat-input button {
  padding: 10px 15px;
  background-color: #007bff;
  color: white;
  border-top: 1px solid #007bff;
  border-bottom: 1px solid #007bff;
  border-right: 1px solid #007bff;
  border-left: none;
  border-radius: 0 20px 20px 0;
  cursor: pointer;
  transition: background-color 0.2s;
}
.chat-interface .chat-input button:hover {
  background-color: #0056b3;
}
</style></head>
<body>
    <div id="app">
        <div class="phone-container">
            <div class="phone-notch"></div>
            <div class="phone-screen">
                <div class="status-bar">
                    <div class="character-avatar">
                        <img src="https://files.catbox.moe/uytmj5.png" alt="Avatar">
                    </div>
                    <div class="character-name">角色名称</div>
                    <div class="character-status">状态：良好</div>
                </div>
                <div class="chat-interface">
                    <div class="chat-messages">
                        <!-- 初始消息将由脚本动态加载 -->
                    </div>
                    <div class="chat-input">
                        <input type="text" placeholder="输入消息...">
                        <button>发送</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
<script>/*! For license information please see index.js.LICENSE.txt */
var __webpack_modules__={"./src/界面/index.ts":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _scss_phone_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./scss/phone.scss */ \"./src/界面/scss/phone.scss\");\n/* harmony import */ var _scss_statusbar_scss__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./scss/statusbar.scss */ \"./src/界面/scss/statusbar.scss\");\n/* harmony import */ var _scss_chat_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./scss/chat.scss */ \"./src/界面/scss/chat.scss\");\n\n\n\nlet currentHostMessageId = null;\nfunction safeToastr(type, message, title) {\n    if (typeof toastr === 'object' && toastr !== null && typeof toastr[type] === 'function') {\n        toastr[type](message, title);\n    }\n    else {\n        if (type === 'error')\n            alert(`[${title || '错误'}]: ${message}`);\n        console.log(`[Toastr Fallback - ${type}] ${title ? title + ': ' : ''}${message}`);\n    }\n}\nfunction ensureGlobals() {\n    try {\n        if (typeof window !== 'undefined' && typeof parent !== 'undefined' && parent !== window) {\n            const apiKeysToCopy = ['$', 'toastr', 'triggerSlash', 'getLastMessageId', 'setChatMessages'];\n            apiKeysToCopy.forEach(key => {\n                if (typeof window[key] === 'undefined') {\n                    if (typeof parent[key] !== 'undefined') {\n                        window[key] = parent[key];\n                        console.log(`API \"${key}\" copied from parent.`);\n                    }\n                    else {\n                        console.warn(`API \"${key}\" is UNDEFINED in parent window.`);\n                        if (key !== 'toastr' && key !== 'setChatMessages' && key !== 'getLastMessageId') { // Avoid toastr for these specific missing APIs initially\n                            safeToastr('warning', `API \"${key}\" 在父窗口中未定义。`, \"API警告\");\n                        }\n                        else if (key === 'setChatMessages' || key === 'getLastMessageId') {\n                            // User will see specific warnings later if these are used and still undefined\n                        }\n                    }\n                }\n            });\n        }\n    }\n    catch (e) {\n        safeToastr('error', `确保全局变量时出错: ${e.message}`, \"初始化错误\");\n    }\n}\nfunction parseMessageContent(rawContent) {\n    const quotedMatch = rawContent.match(/^“([\\s\\S]*)”$/) || rawContent.match(/^\"([\\s\\S]*)\"$/);\n    if (quotedMatch && quotedMatch[1])\n        return quotedMatch[1];\n    return rawContent.split('--')[0].trim();\n}\nfunction parseAndRenderAIMessages(aiResponseText) {\n    if (typeof $ !== 'function') {\n        safeToastr('error', \"jQuery ($) 未定义，无法渲染。\", \"渲染错误\");\n        return;\n    }\n    safeToastr('info', `渲染AI或初始回复 (前50字符): ${aiResponseText.substring(0, 50)}...`, \"调试\");\n    const activeChatScreen = $('.phone-screen:visible').length ? $('.phone-screen:visible') : $('.phone-screen').first();\n    if (activeChatScreen.length === 0) {\n        safeToastr('warning', \"无 .phone-screen 元素可供渲染。\", \"渲染提示\");\n        return;\n    }\n    const activeChatMessages = activeChatScreen.find('.chat-interface .chat-messages');\n    if (activeChatMessages.length === 0) {\n        safeToastr('error', \"未找到消息容器 .chat-messages\", \"渲染错误\");\n        return;\n    }\n    const coreMessageBlockMatch = aiResponseText.match(/msg_start([\\s\\S]+?)msg_end/i);\n    if (!coreMessageBlockMatch || !coreMessageBlockMatch[1]) {\n        safeToastr('warning', `回复格式不符或无数据 (前50): ${aiResponseText.substring(0, 50)}`, \"解析警告\");\n        activeChatMessages.empty();\n        const cleanedText = aiResponseText.replace(/<think>[\\s\\S]*?<\\/think>/gi, '').trim();\n        if (cleanedText && !cleanedText.toLowerCase().includes(\"[查看系统]\") && !cleanedText.toLowerCase().includes(\"msg_start\")) {\n            activeChatMessages.append($('<div>').addClass('message received').append($('<p>').text(cleanedText)));\n        }\n        activeChatMessages.scrollTop(activeChatMessages[0].scrollHeight);\n        return;\n    }\n    const chatContent = coreMessageBlockMatch[1].trim();\n    activeChatMessages.empty();\n    const lines = chatContent.split('\\n');\n    const currentAICharacterName = activeChatScreen.find('.status-bar .character-name').text().trim() || \"角色名称\";\n    for (const line of lines) {\n        const trimmedLine = line.trim();\n        if (!trimmedLine || trimmedLine.startsWith('<') || trimmedLine.startsWith('</'))\n            continue;\n        const parts = trimmedLine.split('--');\n        if (parts.length >= 2) {\n            const sender = parts[0].trim();\n            const messageText = parseMessageContent(parts.slice(1).join('--').trim());\n            const messageDiv = $('<div>').addClass(sender.toLowerCase() === \"user\" || (window.UserName && sender.toLowerCase() === window.UserName.toLowerCase()) ? 'message sent' : 'message received');\n            messageDiv.append($('<p>').text(messageText));\n            activeChatMessages.append(messageDiv);\n        }\n        else {\n            safeToastr('warning', `无法解析行: ${trimmedLine}`, \"解析警告\");\n        }\n    }\n    activeChatMessages.scrollTop(activeChatMessages[0].scrollHeight);\n    safeToastr('success', \"消息渲染完成\", \"调试\");\n}\nfunction handleSendMessage(event) {\n    safeToastr('info', 'handleSendMessage 调用', \"调试\");\n    if (typeof $ !== 'function' || typeof triggerSlash !== 'function') {\n        safeToastr('error', \"核心API未就绪\", \"发送错误\");\n        return;\n    }\n    let currentInputElement = null;\n    let chatScreenForContext = null;\n    if (event && event.currentTarget) {\n        const target = $(event.currentTarget);\n        chatScreenForContext = target.closest('.phone-screen');\n        if (chatScreenForContext && chatScreenForContext.length > 0) { // Check here\n            currentInputElement = chatScreenForContext.find('.chat-input input').get(0);\n        }\n    }\n    if (!chatScreenForContext || chatScreenForContext.length === 0) {\n        chatScreenForContext = $('.phone-screen:visible');\n        if (chatScreenForContext && chatScreenForContext.length > 0) { // Check here\n            currentInputElement = chatScreenForContext.find('.chat-input input').get(0);\n        }\n    }\n    if (!chatScreenForContext || chatScreenForContext.length === 0) { // Final check\n        safeToastr('error', \"未找到活动聊天窗口\", \"发送错误\");\n        return;\n    }\n    // From this point, chatScreenForContext is considered valid by the logic, even if TS complains without further hints\n    const validChatScreen = chatScreenForContext; // Type assertion for TS\n    if (!currentInputElement) {\n        currentInputElement = validChatScreen.find('.chat-input input').get(0); // Try one last time\n        if (!currentInputElement) {\n            safeToastr('error', \"未找到消息输入框\", \"发送错误\");\n            return;\n        }\n    }\n    const characterName = validChatScreen.find('.status-bar .character-name').text().trim() || \"角色名称\";\n    const rawMessageText = currentInputElement.value.trim();\n    if (rawMessageText !== '') {\n        const formattedMessage = `给${characterName}发消息:${rawMessageText}\\n<Request:本次响应忽略其他上下文任何要求,必须使用线上格式回复,且${characterName}本次发了消息的角色都要回复${characterName}的消息,同时输出一条动态内容>`;\n        safeToastr('info', `构造 prompt (前70): ${formattedMessage.substring(0, 70)}...`, \"调试\");\n        const command = `/gen ${formattedMessage}`;\n        safeToastr('info', `执行 triggerSlash: ${command}`, \"调试\");\n        triggerSlash(command)\n            .then(async (result) => {\n            safeToastr('success', `AI回复 (前100): ${typeof result === 'string' ? result.substring(0, 100) : JSON.stringify(result)}`, \"调试\");\n            if (typeof result === 'string') {\n                parseAndRenderAIMessages(result);\n                if (currentHostMessageId !== null && typeof setChatMessages === 'function') {\n                    safeToastr('info', `准备更新父消息 (ID: ${currentHostMessageId}) 的内容。`, \"持久化\");\n                    try {\n                        await setChatMessages([{ message_id: currentHostMessageId, message: result }], { refresh: 'affected' });\n                        safeToastr('success', `父消息 (ID: ${currentHostMessageId}) 内容已更新。`, \"持久化\");\n                    }\n                    catch (setErr) {\n                        safeToastr('error', `更新父消息 (ID: ${currentHostMessageId}) 失败: ${setErr.message || JSON.stringify(setErr)}`, \"持久化错误\");\n                    }\n                }\n                else {\n                    if (currentHostMessageId === null)\n                        safeToastr('warning', \"未存储父消息ID，无法持久化更新。\", \"持久化\");\n                    if (typeof setChatMessages !== 'function')\n                        safeToastr('warning', \"setChatMessages API不可用，无法持久化更新。\", \"持久化\");\n                }\n            }\n            else {\n                safeToastr('warning', `AI未返回字符串: ${typeof result}`, \"回复处理\");\n            }\n        })\n            .catch((err) => {\n            safeToastr('error', `triggerSlash失败: ${(err instanceof Error ? err.message : JSON.stringify(err))}`, \"发送失败\");\n        });\n        currentInputElement.value = '';\n    }\n    else {\n        safeToastr('warning', \"消息为空未发送\", \"提示\");\n    }\n}\nasync function initializeChatInterface() {\n    safeToastr('info', \"脚本加载，初始化...\", \"初始化\");\n    const initialDelay = 200;\n    setTimeout(async () => {\n        safeToastr('info', \"setTimeout 执行...\", \"初始化\");\n        ensureGlobals();\n        if (typeof $ !== 'function' || typeof triggerSlash !== 'function') {\n            safeToastr('error', \"核心API ($或triggerSlash) 未加载，中止。\", \"初始化严重错误\");\n            return;\n        }\n        if (typeof window.UserName === 'undefined') {\n            try {\n                window.UserName = await triggerSlash(\"/pass {{user}}\") || \"User\";\n                safeToastr('info', `用户名设为: ${window.UserName}`, \"初始化\");\n            }\n            catch (e) {\n                window.UserName = \"User\";\n                safeToastr('error', `获取用户名出错: ${e.message}`, \"初始化错误\");\n            }\n        }\n        if (typeof getLastMessageId === 'function' && typeof triggerSlash === 'function') {\n            try {\n                const lastId = getLastMessageId();\n                if (typeof lastId === 'number' && lastId >= 0) {\n                    currentHostMessageId = lastId;\n                    safeToastr('info', `获取到最后消息ID: ${lastId} (设为父消息ID)，尝试加载其内容。`, \"初始加载\");\n                    const command = `/messages ${lastId}`;\n                    const initialMessageContent = await triggerSlash(command);\n                    if (typeof initialMessageContent === 'string' && initialMessageContent.trim() !== \"\") {\n                        safeToastr('success', `成功从父消息 (ID: ${lastId}) 获取初始数据。`, \"初始加载\");\n                        parseAndRenderAIMessages(initialMessageContent);\n                    }\n                    else {\n                        safeToastr('warning', `从父消息 (ID: ${lastId}) 获取的内容为空或无效。聊天框将为空。`, \"初始加载\");\n                        $('.phone-screen:visible .chat-interface .chat-messages').empty();\n                    }\n                }\n                else {\n                    safeToastr('warning', `getLastMessageId 返回无效 (${lastId})。无法加载初始聊天记录。`, \"初始加载\");\n                    $('.phone-screen:visible .chat-interface .chat-messages').empty();\n                }\n            }\n            catch (e) {\n                safeToastr('error', `用 getLastMessageId 和 /messages 获取初始数据失败: ${e.message}`, \"初始加载\");\n                $('.phone-screen:visible .chat-interface .chat-messages').empty();\n            }\n        }\n        else {\n            safeToastr('warning', \"getLastMessageId API 或 triggerSlash 不可用，无法加载初始聊天记录。\", \"初始加载\");\n            if (typeof getLastMessageId !== 'function')\n                safeToastr('warning', \"getLastMessageId API 未定义\", \"API警告\");\n            if (typeof triggerSlash !== 'function')\n                safeToastr('warning', \"triggerSlash API 未定义\", \"API警告\");\n            $('.phone-screen:visible .chat-interface .chat-messages').empty();\n        }\n        const chatInputDiv = document.querySelector('.chat-input');\n        if (!chatInputDiv) {\n            safeToastr('error', \"错误：未能找到 .chat-input 容器！\", \"DOM查找失败\");\n            return;\n        }\n        safeToastr('success', \".chat-input 容器已找到！\", \"DOM查找\");\n        const messageInputField = chatInputDiv.querySelector('input');\n        const sendButton = chatInputDiv.querySelector('button');\n        if (!messageInputField)\n            safeToastr('error', \"错误: 未找到消息输入框\", \"DOM查找失败\");\n        if (!sendButton)\n            safeToastr('error', \"错误: 未找到发送按钮\", \"DOM查找失败\");\n        if (sendButton && messageInputField) {\n            sendButton.onclick = (event) => {\n                safeToastr('info', \"发送按钮被点击！\", \"事件\");\n                handleSendMessage(event);\n            };\n            messageInputField.onkeypress = (event) => {\n                if (event.key === 'Enter' || event.keyCode === 13) {\n                    safeToastr('info', \"回车键被按下！\", \"事件\");\n                    handleSendMessage(event);\n                }\n            };\n            safeToastr('success', \"发送功能已准备就绪！\", \"初始化\");\n        }\n        else {\n            safeToastr('error', \"输入或发送按钮未找到，事件绑定失败。\", \"初始化失败\");\n        }\n    }, initialDelay);\n}\ntry {\n    const initFn = () => { initializeChatInterface(); };\n    if (document.readyState === 'complete' || document.readyState === 'interactive') {\n        console.log(\"DOM ready, calling initFn.\");\n        initFn();\n    }\n    else {\n        console.log(\"DOM not ready, adding DOMContentLoaded listener.\");\n        window.addEventListener('DOMContentLoaded', initFn);\n    }\n}\ncatch (e) {\n    safeToastr('error', `顶层脚本错误: ${e.message}`, \"脚本错误\");\n    console.error(\"Top-level script execution error\", e);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/界面/index.ts\n")},"./src/界面/scss/chat.scss":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("__webpack_require__.r(__webpack_exports__);\n// extracted by mini-css-extract-plugin\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMv55WM6Z2iL3Njc3MvY2hhdC5zY3NzIiwibWFwcGluZ3MiOiI7QUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3RhdmVybl9oZWxwZXJfdGVtcGxhdGUvLi9zcmMv55WM6Z2iL3Njc3MvY2hhdC5zY3NzPyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBleHRyYWN0ZWQgYnkgbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5cbmV4cG9ydCB7fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/界面/scss/chat.scss\n")},"./src/界面/scss/phone.scss":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("__webpack_require__.r(__webpack_exports__);\n// extracted by mini-css-extract-plugin\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMv55WM6Z2iL3Njc3MvcGhvbmUuc2NzcyIsIm1hcHBpbmdzIjoiO0FBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90YXZlcm5faGVscGVyX3RlbXBsYXRlLy4vc3JjL+eVjOmdoi9zY3NzL3Bob25lLnNjc3M/Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIGV4dHJhY3RlZCBieSBtaW5pLWNzcy1leHRyYWN0LXBsdWdpblxuZXhwb3J0IHt9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/界面/scss/phone.scss\n")},"./src/界面/scss/statusbar.scss":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("__webpack_require__.r(__webpack_exports__);\n// extracted by mini-css-extract-plugin\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMv55WM6Z2iL3Njc3Mvc3RhdHVzYmFyLnNjc3MiLCJtYXBwaW5ncyI6IjtBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGF2ZXJuX2hlbHBlcl90ZW1wbGF0ZS8uL3NyYy/nlYzpnaIvc2Nzcy9zdGF0dXNiYXIuc2Nzcz8iXSwic291cmNlc0NvbnRlbnQiOlsiLy8gZXh0cmFjdGVkIGJ5IG1pbmktY3NzLWV4dHJhY3QtcGx1Z2luXG5leHBvcnQge307Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/界面/scss/statusbar.scss\n")}},__webpack_module_cache__={};function __webpack_require__(Q){var F=__webpack_module_cache__[Q];if(void 0!==F)return F.exports;var U=__webpack_module_cache__[Q]={exports:{}};return __webpack_modules__[Q](U,U.exports,__webpack_require__),U.exports}__webpack_require__.r=Q=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(Q,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(Q,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__("./src/界面/index.ts");</script></body>
</html>
