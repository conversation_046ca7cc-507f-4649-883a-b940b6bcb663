/**
 * 澧炲己鐗堟硶鏈В鏋愯剼鏈� - 浠嶥ND5e鎵嬪唽HTML鏂囦欢涓В鏋愭硶鏈暟鎹�
 * 鏀寔姝ｇ‘鐨勪腑鏂囩紪鐮佸鐞嗗拰鎸夌幆鏁板垎缁勭殑涓栫晫涔︾敓鎴�
 */

const fs = require('fs');
const path = require('path');

// 浼ゅ绫诲瀷鏄犲皠
const DAMAGE_TYPE_MAP = {
  '鐏劙': 'fire',
  '瀵掑喎': 'cold',
  '闂數': 'lightning',
  '闆烽福': 'thunder',
  '寮洪吀': 'acid',
  '姣掔礌': 'poison',
  '鍏夎€€': 'radiant',
  '榛殌': 'necrotic',
  '鍔涘満': 'force',
  '绌垮埡': 'piercing',
  '鎸ョ爫': 'slashing',
  '閽濆嚮': 'bludgeoning',
  '蹇冪伒': 'psychic'
};

// 瀛︽淳鏄犲皠
const SCHOOL_MAP = {
  '鍜掓硶': 'conjuration',
  '棰勮█': 'divination', 
  '闄勯瓟': 'enchantment',
  '濉戣兘': 'evocation',
  '骞绘湳': 'illusion',
  '姝荤伒': 'necromancy',
  '鍙樺寲': 'transmutation',
  '闃叉姢': 'abjuration'
};

// 鐜暟鍚嶇О鏄犲皠
const LEVEL_NAMES = {
  0: '鎴忔硶',
  1: '涓€鐜硶鏈�',
  2: '浜岀幆娉曟湳', 
  3: '涓夌幆娉曟湳',
  4: '鍥涚幆娉曟湳',
  5: '浜旂幆娉曟湳',
  6: '鍏幆娉曟湳',
  7: '涓冪幆娉曟湳',
  8: '鍏幆娉曟湳',
  9: '涔濈幆娉曟湳'
};

/**
 * 璇诲彇HTML鏂囦欢骞舵纭鐞嗙紪鐮�
 */
function readHTMLFile(filepath) {
  try {
    // 棣栧厛灏濊瘯浣跨敤GB2312缂栫爜璇诲彇
    const buffer = fs.readFileSync(filepath);

    // 妫€娴嬬紪鐮佸苟杞崲
    let content;
    try {
      // 灏濊瘯浣跨敤iconv-lite澶勭悊GB2312缂栫爜
      const iconv = require('iconv-lite');
      content = iconv.decode(buffer, 'gb2312');
      console.log(`浣跨敤GB2312缂栫爜鎴愬姛璇诲彇鏂囦欢: ${filepath}`);
    } catch (iconvError) {
      // 濡傛灉iconv-lite涓嶅彲鐢紝灏濊瘯鎵嬪姩澶勭悊GB2312
      console.log('iconv-lite涓嶅彲鐢紝灏濊瘯鎵嬪姩澶勭悊GB2312缂栫爜...');

      // 绠€鍗曠殑GB2312鍒癠TF-8杞崲锛堜粎澶勭悊甯歌瀛楃锛�
      content = buffer.toString('binary');

      // 濡傛灉鍐呭鐪嬭捣鏉ュ儚涔辩爜锛屽皾璇曚笉鍚岀殑缂栫爜
      if (content.includes('锟斤拷') || content.includes('锟�')) {
        // 灏濊瘯latin1缂栫爜鐒跺悗鎵嬪姩杞崲
        content = buffer.toString('latin1');
      }
    }

    return content;
  } catch (error) {
    console.error(`璇诲彇鏂囦欢 ${filepath} 鏃跺嚭閿�:`, error);
    return null;
  }
}

/**
 * 瑙ｆ瀽HTML鏂囦欢涓殑娉曟湳鏁版嵁
 */
function parseSpellsFromHTML(htmlContent, level) {
  const spells = [];
  
  // 鏀硅繘鐨勬鍒欒〃杈惧紡锛屾洿濂藉湴鍖归厤涓枃鍐呭
  const spellPattern = /<H4[^>]*id="([^"]*)"[^>]*>([^<]*)<\/H4>\s*<P><EM>([^<]*)<\/EM>[^<]*<BR><STRONG>鏂芥硶鏃堕棿锛�<\/STRONG>([^<]*)<BR><STRONG>鏂芥硶璺濈锛�<\/STRONG>([^<]*)<BR><STRONG>娉曟湳鎴愬垎锛�<\/STRONG>([^<]*)<BR><STRONG>鎸佺画鏃堕棿锛�<\/STRONG>([^<]*)<BR>([^]*?)(?=<H4|$)/g;
  
  let match;
  while ((match = spellPattern.exec(htmlContent)) !== null) {
    const [, id, nameWithEn, schoolInfo, castingTime, range, components, duration, description] = match;
    
    // 瑙ｆ瀽涓嫳鏂囧悕绉� - 鏀寔锝滃垎闅旂
    let name_zh, name_en;
    if (nameWithEn.includes('锝�')) {
      [name_zh, name_en] = nameWithEn.split('锝�').map(s => s.trim());
    } else {
      // 澶囩敤瑙ｆ瀽鏂瑰紡
      const nameMatch = nameWithEn.match(/^([^锛圿+)(?:锛�([^锛塢+)锛�)?/);
      if (nameMatch) {
        name_zh = nameMatch[1].trim();
        name_en = nameMatch[2] || id.replace(/_/g, ' ');
      } else {
        name_zh = nameWithEn.trim();
        name_en = id.replace(/_/g, ' ');
      }
    }
    
    // 瑙ｆ瀽瀛︽淳淇℃伅
    const schoolMatch = schoolInfo.match(/([^ ]+) /);
    const school_zh = schoolMatch ? schoolMatch[1] : '鏈煡';
    const school_en = SCHOOL_MAP[school_zh] || 'unknown';
    
    // 瑙ｆ瀽娉曟湳鎴愬垎
    const componentsList = components.split(/[锛屻€乚/).map(c => c.trim());
    
    // 鍒涘缓娉曟湳瀵硅薄
    const spell = {
      name_zh,
      name_en,
      level,
      school_zh,
      school_en,
      casting_time: castingTime.trim(),
      range: range.trim(),
      components: componentsList,
      duration: duration.trim(),
      description_short: extractShortDescription(description),
      description_long: cleanDescription(description)
    };
    
    // 瑙ｆ瀽浼ゅ淇℃伅
    const damageMatch = description.match(/(\d+d\d+(?:\+\d+)?)/);
    if (damageMatch) {
      spell.damage = damageMatch[1];
    }
    
    // 瑙ｆ瀽浼ゅ绫诲瀷
    for (const [zh, en] of Object.entries(DAMAGE_TYPE_MAP)) {
      if (description.includes(zh)) {
        spell.damage_type_zh = zh;
        spell.damage_type_en = en;
        break;
      }
    }
    
    // 瑙ｆ瀽鏀诲嚮绫诲瀷
    if (description.includes('杩滅▼娉曟湳鏀诲嚮')) {
      spell.attack_type = '娉曟湳鏀诲嚮 (杩滅▼)';
    } else if (description.includes('杩戞垬娉曟湳鏀诲嚮')) {
      spell.attack_type = '娉曟湳鏀诲嚮 (杩戞垬)';
    }
    
    // 瑙ｆ瀽璞佸厤妫€瀹�
    const saveMatch = description.match(/([^锛宂+)璞佸厤/);
    if (saveMatch) {
      const saveAttr = saveMatch[1].trim();
      spell.save = {
        attribute: saveAttr,
        effect_on_success: description.includes('浼ゅ鍑忓崐') ? '浼ゅ鍑忓崐' : '鏃犳晥鏋�'
      };
    }
    
    // 瑙ｆ瀽鍗囩幆鏁堟灉
    const higherLevelMatch = description.match(/<STRONG>鍗囩幆鏂芥硶锛�<\/STRONG>([^<]*)/);
    if (higherLevelMatch) {
      spell.higher_level_cast = {
        per_slot_above_base: '姣忛珮浜庡熀纭€鐜綅鐨勬硶鏈綅',
        effect: higherLevelMatch[1].trim()
      };
    }
    
    // 瑙ｆ瀽鎴忔硶鍗囩骇
    if (level === 0) {
      const scalingMatch = description.match(/<STRONG>鎴忔硶寮哄寲[銆傦細]<\/STRONG>([^<]*)/);
      if (scalingMatch) {
        spell.scaling = parseCantripsScaling(scalingMatch[1]);
      }
    }
    
    spells.push(spell);
  }
  
  return spells;
}

/**
 * 鎻愬彇绠€鐭弿杩�
 */
function extractShortDescription(description) {
  // 绉婚櫎HTML鏍囩
  const cleaned = description.replace(/<[^>]*>/g, '');
  // 鍙栫涓€鍙ヨ瘽浣滀负绠€鐭弿杩�
  const firstSentence = cleaned.split('銆�')[0];
  return firstSentence.length > 100 ? firstSentence.substring(0, 100) + '...' : firstSentence + '銆�';
}

/**
 * 娓呯悊鎻忚堪鏂囨湰
 */
function cleanDescription(description) {
  return description
    .replace(/<[^>]*>/g, '') // 绉婚櫎HTML鏍囩
    .replace(/&nbsp;/g, ' ') // 鏇挎崲绌烘牸瀹炰綋
    .replace(/\s+/g, ' ') // 鍚堝苟澶氫釜绌烘牸
    .trim();
}

/**
 * 瑙ｆ瀽鎴忔硶鍗囩骇淇℃伅
 */
function parseCantripsScaling(scalingText) {
  const scaling = {};
  
  // 鍖归厤绫讳技 "5绾э細2d10锛�11绾э細3d10锛�17绾э細4d10" 鐨勬ā寮�
  const matches = scalingText.matchAll(/(\d+)绾锛�:]([^锛屻€俔+)/g);
  for (const match of matches) {
    scaling[match[1]] = match[2].trim();
  }
  
  return scaling;
}

/**
 * 涓昏В鏋愬嚱鏁�
 */
async function parseAllSpells() {
  const allSpells = [];
  const baseDir = '../../../DND5e_chm-main/DND5e_chm-main/鐜╁鎵嬪唽2024/娉曟湳璇﹁堪';
  
  // 瑙ｆ瀽姣忎釜绛夌骇鐨勬硶鏈枃浠�
  for (let level = 0; level <= 9; level++) {
    const filename = `${level}鐜�.htm`;
    const filepath = path.join(baseDir, filename);
    
    try {
      console.log(`姝ｅ湪瑙ｆ瀽 ${level}鐜硶鏈�...`);
      const htmlContent = readHTMLFile(filepath);
      
      if (!htmlContent) {
        console.log(`璺宠繃 ${level}鐜硶鏈� - 鏂囦欢璇诲彇澶辫触`);
        continue;
      }
      
      const spells = parseSpellsFromHTML(htmlContent, level);
      
      console.log(`${level}鐜硶鏈В鏋愬畬鎴愶紝鍏� ${spells.length} 涓硶鏈痐);
      allSpells.push(...spells);
    } catch (error) {
      console.error(`瑙ｆ瀽 ${level}鐜硶鏈椂鍑洪敊:`, error);
    }
  }
  
  console.log(`鎬诲叡瑙ｆ瀽浜� ${allSpells.length} 涓硶鏈痐);
  return allSpells;
}

/**
 * 鐢熸垚鎸夌幆鏁板垎缁勭殑涓栫晫涔︽枃浠�
 */
function generateLevelBasedWorldBooks(spells) {
  // 鎸夌瓑绾у垎缁勬硶鏈�
  const spellsByLevel = {};
  spells.forEach(spell => {
    if (!spellsByLevel[spell.level]) {
      spellsByLevel[spell.level] = [];
    }
    spellsByLevel[spell.level].push(spell);
  });

  // 涓烘瘡涓瓑绾х敓鎴愬崟鐙殑涓栫晫涔︽枃浠�
  for (const [level, levelSpells] of Object.entries(spellsByLevel)) {
    const levelNum = parseInt(level);
    const levelName = LEVEL_NAMES[levelNum] || `${levelNum}鐜硶鏈痐;

    const worldBook = {
      entries: {
        1000: {
          uid: 1000,
          key: [`SPELLS_LEVEL_${levelNum}`, `${levelName}`, `LEVEL_${levelNum}_SPELLS`, `DND5E_${levelName.toUpperCase()}`],
          keysecondary: [],
          comment: `DND5e ${levelName}鍚堥泦 (${levelSpells.length}涓硶鏈�)`,
          content: JSON.stringify(levelSpells, null, 2),
          constant: true,
          vectorized: false,
          selective: true,
          selectiveLogic: 0,
          addMemo: true,
          order: 100,
          position: 0,
          disable: false,
          excludeRecursion: false,
          preventRecursion: false,
          delayUntilRecursion: false,
          probability: 100,
          useProbability: true,
          depth: 4,
          group: "",
          groupOverride: false,
          groupWeight: 100,
          scanDepth: null,
          caseSensitive: null,
          matchWholeWords: null,
          useGroupScoring: null,
          automationId: "",
          role: null,
          sticky: 0,
          cooldown: 0,
          delay: 0,
          displayIndex: 1000
        }
      }
    };

    // 淇濆瓨鍗曚釜绛夌骇鐨勪笘鐣屼功鏂囦欢
    const outputPath = `DND5e_${levelName}_WorldBook.json`;
    fs.writeFileSync(outputPath, JSON.stringify(worldBook, null, 2), 'utf-8');
    console.log(`${levelName}涓栫晫涔︽枃浠跺凡鐢熸垚: ${outputPath}`);
  }
}

/**
 * 鐢熸垚瀹屾暣鐨勪笘鐣屼功鏂囦欢锛堝寘鍚墍鏈夋硶鏈級
 */
function generateCompleteWorldBook(spells) {
  const worldBook = {
    entries: {
      2000: {
        uid: 2000,
        key: ["SPELLS_ALL", "鎵€鏈夋硶鏈�", "ALL_SPELLS", "娉曟湳搴�", "DND5E_ALL_SPELLS"],
        keysecondary: [],
        comment: `DND5e瀹屾暣娉曟湳搴� (${spells.length}涓硶鏈�)`,
        content: JSON.stringify(spells, null, 2),
        constant: true,
        vectorized: false,
        selective: true,
        selectiveLogic: 0,
        addMemo: true,
        order: 100,
        position: 0,
        disable: false,
        excludeRecursion: false,
        preventRecursion: false,
        delayUntilRecursion: false,
        probability: 100,
        useProbability: true,
        depth: 4,
        group: "",
        groupOverride: false,
        groupWeight: 100,
        scanDepth: null,
        caseSensitive: null,
        matchWholeWords: null,
        useGroupScoring: null,
        automationId: "",
        role: null,
        sticky: 0,
        cooldown: 0,
        delay: 0,
        displayIndex: 2000
      }
    }
  };

  const outputPath = 'DND5e_Complete_Spells_WorldBook.json';
  fs.writeFileSync(outputPath, JSON.stringify(worldBook, null, 2), 'utf-8');
  console.log(`瀹屾暣娉曟湳搴撲笘鐣屼功鏂囦欢宸茬敓鎴�: ${outputPath}`);
}

// 濡傛灉鐩存帴杩愯姝よ剼鏈�
if (require.main === module) {
  parseAllSpells().then(spells => {
    if (spells.length === 0) {
      console.log('娌℃湁瑙ｆ瀽鍒颁换浣曟硶鏈紝璇锋鏌ユ枃浠惰矾寰勫拰缂栫爜璁剧疆');
      return;
    }

    console.log('\n寮€濮嬬敓鎴愪笘鐣屼功鏂囦欢...');

    // 鐢熸垚鎸夌幆鏁板垎缁勭殑涓栫晫涔�
    generateLevelBasedWorldBooks(spells);

    // 鐢熸垚瀹屾暣鐨勪笘鐣屼功
    generateCompleteWorldBook(spells);

    console.log('\n鎵€鏈変笘鐣屼功鏂囦欢鐢熸垚瀹屾垚锛�');
  }).catch(console.error);
}
