export function createDynamicEquipmentItem(name: string = '', details: string = ''): HTMLElement {
  const itemRow = document.createElement('div');
  itemRow.className = 'dynamic-item-row';

  const nameInput = document.createElement('input');
  nameInput.type = 'text';
  nameInput.name = 'equipmentName[]';
  nameInput.placeholder = '装备名称';
  nameInput.value = name;

  const detailsInput = document.createElement('input');
  detailsInput.type = 'text';
  detailsInput.name = 'equipmentDetails[]';
  detailsInput.placeholder = '详情 (可选)';
  detailsInput.value = details;

  const removeButton = document.createElement('button');
  removeButton.type = 'button';
  removeButton.className = 'remove-item-button';
  removeButton.textContent = '移除';
  removeButton.onclick = () => itemRow.remove();

  itemRow.appendChild(nameInput);
  itemRow.appendChild(detailsInput);
  itemRow.appendChild(removeButton);
  return itemRow;
}

export function setupDynamicEquipmentList() {
  const container = document.getElementById('equipment-list-container');
  const addButton = document.getElementById('add-equipment-button');
  if (container && addButton) {
    addButton.addEventListener('click', () => {
      container.appendChild(createDynamicEquipmentItem());
    });
  }
}

export function createDynamicInventoryItem(name: string = '', description: string = '', quantity: number = 1): HTMLElement {
  const itemRow = document.createElement('div');
  itemRow.className = 'dynamic-item-row';

  const nameInput = document.createElement('input');
  nameInput.type = 'text';
  nameInput.name = 'inventoryItemName[]';
  nameInput.placeholder = '物品名称';
  nameInput.value = name;

  const descInput = document.createElement('input');
  descInput.type = 'text';
  descInput.name = 'inventoryItemDetails[]';
  descInput.placeholder = '描述 (可选)';
  descInput.value = description;

  const quantityInput = document.createElement('input');
  quantityInput.type = 'number';
  quantityInput.name = 'inventoryItemQuantity[]';
  quantityInput.placeholder = '数量';
  quantityInput.value = quantity.toString();
  quantityInput.min = "1";

  const removeButton = document.createElement('button');
  removeButton.type = 'button';
  removeButton.className = 'remove-item-button';
  removeButton.textContent = '移除';
  removeButton.onclick = () => itemRow.remove();

  itemRow.appendChild(nameInput);
  itemRow.appendChild(descInput);
  itemRow.appendChild(quantityInput);
  itemRow.appendChild(removeButton);
  return itemRow;
}

export function setupDynamicInventoryList() {
  const container = document.getElementById('inventory-list-container');
  const addButton = document.getElementById('add-inventory-item-button');
  if (container && addButton) {
    addButton.addEventListener('click', () => {
      container.appendChild(createDynamicInventoryItem());
    });
  }
}
