# Galgame 界面开发历程全记录

本文档旨在详细记录在为 SillyTavern 开发 Galgame 风格界面的整个过程中遇到的问题、尝试的解决方案、关键的决策点以及最终的实现思路。目标是为未来的开发和维护提供一份尽可能完整的参考。

## 1. 项目初期设定与核心需求 (2025-05-23 及之前)

### 1.1 初始目标

*   **界面风格**: Galgame (视觉小说) 风格。
*   **显示端**: 优先考虑手机端显示效果。
*   **核心状态显示**: 游戏内时间、玩家体力。
*   **基本交互**:
    *   初始界面为“家”，提供固定选项：“出门”、“休息”、“消磨时间”。
    *   点击选项后，不直接与AI交互，而是进入下一个界面/状态（此设定后续有调整）。
    *   在不同场景中，根据AI返回的信息动态生成选项按钮。
*   **AI 交互**:
    *   用户选择后，向AI传递指定内容。
    *   AI返回特定格式的代码，UI根据此代码匹配并渲染界面。
    *   开发者会通过世界书（World Info）等方式确保AI返回指定格式。
*   **渲染机制**: 界面仅需正确渲染已有的消息记录，AI返回的数据驱动UI更新。

### 1.2 技术栈与环境约束

*   **可用库 (全局支持，无需导入)**: jQuery, jQuery-UI, Lodash, Toastr, Yaml.
*   **CSS/JS 加载**: 不应在 `index.html` 中直接 `<link>` CSS 或 `<script>` JS。所有脚本逻辑在 `index.ts` 中处理，SCSS也应在 `index.ts` 中导入 (`import './index.scss';`)。
*   **高度设计**: 严格禁止使用 `vh` 单位设置高度。应使用宽度配合 `aspect-ratio` 来动态调整高度，以适应不同屏幕。
*   **生命周期函数**: 加载/卸载时执行的功能，应参考 `src/脚本示例/加载和卸载时执行函数.ts` 的写法，而非标准 `DOMContentLoaded`。

## 2. HTML 结构与 SCSS 样式开发 (2025-05-23)

### 2.1 `index.html` 骨架搭建

根据需求，设计了以下主要区域：

*   `status-bar`: 显示时间和体力。
*   `scene-description`: 显示当前场景的文字描述。
*   `dialogue-area`: 用于显示角色对话。
    *   `character-sprite`: (预留) 可能用于显示角色图片。
    *   `dialogue-text`: 显示对话内容。
    *   `dialogue-choices`: 对话中的玩家选项按钮容器。
*   `choices`: 非对话场景下的行动选项按钮容器。

```html
<!-- src/galgame/index.html -->
<div id="galgame-container">
    <div id="status-bar">
        <span id="time">时间: 08:00</span>
        <span id="energy">体力: 100</span>
        <span id="character-info-ingame"></span>
    </div>
    <div id="main-content">
        <div id="scene-description" class="scene-element">
            <!-- 场景描述将在这里填充 -->
        </div>
        <div id="dialogue-area" class="scene-element" style="display: none;">
            <div id="character-sprite">
                <!-- 角色图片/立绘 (可选) -->
            </div>
            <div id="dialogue-text">
                <!-- 对话内容 -->
            </div>
            <div id="dialogue-choices" class="choices-container">
                <!-- 对话选项按钮 -->
            </div>
        </div>
        <div id="choices" class="choices-container scene-element">
            <!-- 行动选项按钮 -->
        </div>
    </div>
</div>
```

### 2.2 `index.scss` 样式设计

*   **核心原则**: 响应式设计，优先考虑移动端，高度使用 `aspect-ratio`。
*   **布局**: 使用 Flexbox 进行主要布局。
*   **问题**:
    *   初期在高度控制上，容易不自觉地想用 `vh` 或固定 `px` 值。
    *   **解决**: 严格遵守使用 `width` 和 `aspect-ratio` 来控制主要内容区域的高度，使其随宽度自适应。例如，主内容区可以设置 `width: 100%; aspect-ratio: 16/9;` (或适合手机的比例)。
*   **样式细节**: 包括按钮样式、文本区域样式、状态栏样式等。

```scss
// src/galgame/index.scss (部分示例)
body, html {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: sans-serif;
  background-color: #f0f0f0;
  color: #333;
}

#galgame-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 600px; // 限制最大宽度，便于桌面调试
  margin: auto;
  border: 1px solid #ccc;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
  // aspect-ratio: 9/16; // 示例：强制容器为特定高宽比
  // min-height: 100%; // 确保在内容不足时也能撑满屏幕
}

#status-bar {
  padding: 10px;
  background-color: #333;
  color: white;
  display: flex;
  justify-content: space-around;
  font-size: 0.9em;
}

#main-content {
  flex-grow: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  overflow-y: auto; // 如果内容超出会滚动
}

.scene-element {
  margin-bottom: 15px;
}

#scene-description p, #dialogue-text p {
  margin: 0;
  padding: 10px;
  background-color: #fff;
  border-radius: 5px;
  min-height: 50px; // 保证有一定高度
}

.choices-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  button {
    padding: 12px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1em;
    &:hover {
      background-color: #0056b3;
    }
  }
}

#dialogue-area {
  // 特定于对话区域的样式
}
```

## 3. TypeScript 核心逻辑 (`index.ts`) 开发

这是一个迭代和充满挑战的过程，下面按逻辑阶段和遇到的问题进行梳理。

### 3.1 阶段一：基础状态与UI元素获取 (2025-05-23)

*   **`GameState` 接口定义**:
    ```typescript
    interface GameState {
      time: string; energy: number; currentScene: string; interfaceMode: 'location' | 'characterDialogue';
      otherCharactersInLocation?: string[]; locationActions?: ActionChoice[];
      currentCharacterName?: string; characterFavorability?: number;
      characterDialogueText?: string; playerReplyOptions?: ActionChoice[];
      lastUserChoiceText?: string; 
    }
    let gameState: GameState = { /* ... initial values ... */ };
    ```
*   **DOM 元素获取**: 在 `onMounted` 中通过 `document.getElementById` 获取所有交互相关的DOM元素。
*   **`safeToastr`**: 实现了一个 `toastr` 的安全包装器，以防 `toastr` 对象在特定环境下未正确加载。
*   **`ensureGlobals`**: 尝试从 `parent`窗口复制必要的SillyTavern API到当前iframe窗口，以确保 `$`, `toastr`, `triggerSlash` 等可用。

### 3.2 阶段二：AI 数据格式定义与初步解析 (2025-05-23 - 2025-05-24)

*   **AI 返回格式**: 最初设想AI返回一个完整的包含所有信息的文本块，由 `查看系统...关闭系统` 包裹。核心内容在 `msg_start...msg_end` 之间。
    *   **关键点**: 每一行数据（如时间、体力、选项）都必须以 `--HH:MM` 结尾。这个标记最初是为了某种时间同步或版本控制，但后来成为解析的关键分隔符。
*   **`parseAndApplyGalgameData`**:
    *   早期版本直接解析包含 `查看系统...关闭系统` 的完整数据块。
    *   使用正则表达式匹配各种数据行。
    *   **问题1**: AI返回的格式不稳定，特别是 `--HH:MM` 标记的位置或存在性。
        *   **解决**: 通过修改 `galgame_world_info_prompt.txt` 中的提示词，反复强调AI必须严格遵守此格式。
    *   **问题2**: 解析逻辑对格式的依赖性过高，微小的格式错误就可能导致整个解析失败。
        *   **解决**: 逐步优化正则表达式，使其更健壮，但仍以AI遵循格式为前提。

### 3.3 阶段三：历史记录与持久化机制的探索与演进 (2025-05-24)

这是整个开发过程中最复杂、迭代次数最多的部分。

*   **核心问题**: 如何在AI不直接管理历史的情况下，让AI在生成回复时拥有必要的上下文，并且UI能够在刷新后恢复到之前的状态？

*   **演进1: 简单的 `previousSceneDataStack` (UI返回)**
    *   最初尝试使用一个简单的字符串数组 `previousSceneDataStack` 来存储前几个场景的完整数据块，用于UI的“返回上一步”功能。
    *   **问题**: 这只解决了UI本地返回，无法为AI提供历史上下文，也无法在完全刷新后恢复。

*   **演进2: `fullHistoryLog` 与 `HistoryEntry` 的引入**
    *   **`HistoryEntry` 接口**: 最初可能只包含一个 `sceneData` 字段，存储完整的 `查看系统...关闭系统` 块。
    *   **`fullHistoryLog`**: `HistoryEntry[]` 数组。
    *   **`persistCurrentStateWithHistory`**:
        *   早期想法：简单地将 `fullHistoryLog` 中所有 `sceneData` 用分隔符拼接起来。
        *   **问题**: 拼接后的字符串过长，且AI难以理解这种扁平化的历史。
    *   **`rebuildFullHistoryLog`**: 对应地从拼接字符串中恢复历史。

*   **演进3: 引入 `<!-- OLD_GALGAME_HISTORY_CHUNK_START/END -->` 标记 (关键转折)**
    *   **动机**: 需要一种方式在持久化的消息中明确区分“旧历史”和“当前最新场景数据”，同时也为了方便SillyTavern的正则表达式替换来隐藏旧历史文本。
    *   **`persistCurrentStateWithHistory` 修改**:
        1.  遍历 `fullHistoryLog` 中除最新条目外的所有条目。
        2.  每个旧条目的 `sceneData` (完整的 `查看系统...关闭系统` 块) 被提取出核心内容 ( `<地点/人物>` 块，通过 `stripOuterWrappers`)。
        3.  核心内容与该条目对应的 `userChoiceText` (如果存在) 拼接。
        4.  所有这些拼接后的旧历史条目用 `HISTORY_SEPARATOR` (`\n------------------------\n`) 连接，并整体用 `<!-- OLD_GALGAME_HISTORY_CHUNK_START -->` 和 `<!-- OLD_GALGAME_HISTORY_CHUNK_END -->` 包裹。
        5.  `fullHistoryLog` 中最新的条目的 `sceneData` (完整的 `查看系统...关闭系统` 块) 直接附加在这个大的注释块之后。
    *   **`rebuildFullHistoryLog` 修改**:
        1.  首先用正则匹配并提取 `<!-- OLD_GALGAME_HISTORY_CHUNK_START -->...<!-- OLD_GALGAME_HISTORY_CHUNK_END -->` 之间的内容。
        2.  将这部分内容按 `HISTORY_SEPARATOR` 分割成各个旧历史片段。
        3.  每个片段再解析出场景核心内容和 `userChoiceText`，存入 `fullHistoryLog`。
        4.  然后从注释块之后的部分提取最新的场景数据（完整的 `查看系统...关闭系统` 块），提取其核心内容，作为 `fullHistoryLog` 的最后一条。
    *   **`HistoryLogEntry` 接口调整**: 字段名从 `sceneData` 调整为 `sceneContent`，明确表示它存储的是**不带** `查看系统...关闭系统` 包裹的场景核心数据。AI返回的仍然是带包裹的完整块，但在存入 `fullHistoryLog` 前会通过 `stripOuterWrappers` 处理。
        *   **后续修正 (重要)**: 在多次尝试和错误后，发现 `HistoryLogEntry` 的字段名在代码中混用为 `sceneData` 和 `sceneContent`，导致了大量TypeScript编译错误。最终统一为 `sceneContent`。

*   **演进4: `stripOuterWrappers` 和 `wrapWithSystemTags` 辅助函数**
    *   `stripOuterWrappers`: 用于从完整的 `查看系统...关闭系统` 块中提取核心的 `<地点/人物>` 内容。
    *   `wrapWithSystemTags`: 用于将核心的 `<地点/人物>` 内容重新包裹成 `查看系统...关闭系统` 块，主要在持久化最新场景时使用。

### 3.4 阶段四：用户交互与AI通信 (2025-05-24)

*   **`handleChoiceClick`**:
    *   记录用户选择到 `fullHistoryLog` 中当前（即将成为上一个）场景的 `userChoiceText`。
    *   构造发送给AI的 `prompt`，通常包含当前场景信息和用户选择。
    *   调用 `triggerSlash('/gen ...')`。
    *   收到AI回复后 (一个完整的 `查看系统...关闭系统` 块)，调用 `stripOuterWrappers` 提取核心内容。
    *   将新的核心内容作为 `sceneContent` 添加到 `fullHistoryLog`。
    *   调用 `parseAndApplyGalgameData` 使用新的核心内容更新 `gameState` 和UI。
    *   调用 `persistCurrentStateWithHistory`。
*   **`returnToPreviousSceneOrHome`**:
    *   从 `fullHistoryLog` 中移除最后一条（当前状态）。
    *   取出新的最后一条作为要恢复的状态的 `sceneContent`。
    *   如果 `fullHistoryLog` 为空或只剩一条，则生成默认的“家”场景的 `sceneContent`。
    *   调用 `parseAndApplyGalgameData` 更新UI。
    *   调用 `persistCurrentStateWithHistory`。
    *   **问题**: `dataToParseForUI` 变量在 `else` 分支中被错误地注释掉了赋值语句，导致在某些返回场景下 `parseAndApplyGalgameData` 接收到 `undefined`。
        *   **解决**: 取消注释 `dataToParseForUI = homeStateData;` (或其等效的 `sceneContentToParse = homeSceneContent;`)。

### 3.5 阶段五：TypeScript 编译错误与工具使用问题 (贯穿始终，尤其在2025-05-24集中爆发)

这是最令人头痛的部分，多次导致开发停滞。

*   **`replace_in_file` "Diff Edit Mismatch" 错误**:
    *   **现象**: 即使是很小的代码修改，也频繁遭遇此错误。工具提示 `SEARCH` 块与文件内容不匹配。
    *   **原因分析**:
        1.  **文件状态不一致**: 我（模型）基于上一次 `read_file` 的内容生成 `SEARCH` 块，但实际文件可能已被VSCode的自动格式化器（如Prettier）修改了空格、缩进、引号风格或行尾符（CRLF vs LF）。
        2.  **工具链的不可见转换**: 从我生成XML到工具在您的系统上执行，中间环节可能存在对文本的处理。
        3.  **`write_to_file` 的后遗症**: 某次 `write_to_file` 错误地在文件末尾附加了大量无关文本（包括之前用户消息的元数据）。虽然系统提示“文件已恢复”，但恢复到的版本可能与我之后通过 `read_file` 获取的版本在一些细节（如行尾符）上仍有差异。
    *   **尝试的解决办法**:
        1.  在每次 `replace_in_file` 前都执行 `read_file`（但这增加了交互轮次）。
        2.  极力缩小 `SEARCH` 块的范围，只包含绝对必要且独特的几行。
        3.  多次放弃 `replace_in_file`，转而使用 `write_to_file` 覆盖整个文件。这虽然能避免匹配问题，但也引入了新的风险（如意外删除或修改了不应变动的代码，或引入了像意外的 `import` 语句）。

*   **TypeScript 类型错误**:
    *   **`HistoryLogEntry` 字段名混用**: 在接口定义中使用 `sceneContent`，但在代码多处错误地使用了 `sceneData` 来访问或赋值。
        *   **解决**: 经过多次尝试，最终将代码中所有对 `fullHistoryLog` 条目场景数据的引用统一为 `sceneContent`。
    *   **`string | null` 或 `string | undefined` 传递给期望 `string` 的函数**:
        *   `extractLatestSceneContent` 返回 `string | null`。在 `onMounted` 中，其结果 `latestSceneContent` 在传递给 `parseAndApplyGalgameData` (期望 `string`) 前未做充分的非空检查。
        *   **解决**: 在调用 `parseAndApplyGalgameData` 前添加 `if (latestSceneContent)` 判断。如果为 `null`，则加载默认的“家”场景。
    *   **模块级变量“找不到名称”**:
        *   `previousSceneDataStack` 和 `fullHistoryLog` 都是在模块顶层声明的 `let` 变量。理论上在所有函数中都应该可见。
        *   **现象**: TypeScript 编译器有时会报错说在 `handleChoiceClick` 或 `returnToPreviousSceneOrHome` 中找不到这些变量。
        *   **分析**: 这非常反常。可能的原因包括：
            *   工具链或IDE的TypeScript服务状态不同步。
            *   在极少数情况下，复杂的闭包或异步代码可能导致编译器暂时性的作用域分析问题（但在此项目中可能性较低）。
            *   之前 `write_to_file` 引入的损坏内容可能干扰了编译器的分析。
        *   **解决**: 通常通过确保代码逻辑清晰、重新加载文件或重启TypeScript服务（IDE层面）来解决。在我们的交互中，通常是在一次成功的 `write_to_file` (写入了相对干净的版本) 之后，这些“找不到名称”的错误会自行消失。
    *   **意外的 `import` 语句**:
        *   在某次 `write_to_file` 后，文件顶部出现了 `import { version } from 'toastr';` 和 `import { config, webpack } from 'webpack';`。这些并非项目所需，且会导致编译错误（因为没有实际安装这些包或配置路径）。
        *   **解决**: 手动或通过 `replace_in_file` 移除这些不必要的导入。

## 4. SillyTavern 集成与正则表达式 (讨论于 2025-05-24)

*   **目标**: 在SillyTavern中，只显示最新的Galgame界面，并隐藏所有旧的历史记录文本。
*   **方法**: 利用SillyTavern的“格式化输出”中的正则表达式替换功能。
*   **规则设计**:
    1.  **规则1 (高优先级，全局执行)**:
        *   **查找**: `<!-- OLD_GALGAME_HISTORY_CHUNK_START -->[\s\S]*?<!-- OLD_GALGAME_HISTORY_CHUNK_END -->\s*`
        *   **替换**: (空字符串)
        *   **目的**: 彻底移除所有被包裹的旧历史记录块以及它们之后可能存在的空白。`[\s\S]*?` 用于非贪婪匹配包括换行在内的任何字符。
    2.  **规则2 (较低优先级，应在规则1之后执行)**:
        *   **查找**: `查看系统\s*msg_start([\s\S]+?)msg_end\s*关闭系统`
        *   **替换**: 用于加载 `<iframe>` 并将捕获组 `([\s\S]+?)` (即核心场景内容) 传递给iframe的脚本。
        *   **目的**: 此时，由于规则1已清除了所有旧历史，这个规则只会匹配到最新的、未被注释包裹的场景数据块，并用它来加载UI。

## 5. 调试技巧与经验教训

*   **日志是生命线**: `console.log` 和 `safeToastr` 在追踪异步流程、变量状态和函数执行顺序方面至关重要。尤其是在处理AI返回的不确定数据和多步状态更新时。
*   **小步迭代**: 尤其是在处理历史记录和持久化这类复杂逻辑时，应该先实现一个小功能点，验证无误后再继续。我们这次的开发过程因为工具的限制和问题的复杂性，有时难以做到真正的小步。
*   **理解工具的限制和行为**:
    *   `replace_in_file` 的极高精度要求意味着它对环境的细微变化非常敏感。
    *   `write_to_file` 是一个“大锤”，能解决匹配问题，但也容易引入新问题（如覆盖不应修改的部分，或因模型记忆偏差写入错误逻辑）。
*   **保持同步**: 开发过程中，模型所“看到”的文件状态与开发者本地的实际文件状态必须尽可能保持同步。任何一方的自动格式化或手动修改都可能导致后续基于旧状态的修改失败。
*   **明确数据结构和约定**: `HistoryLogEntry` 中字段的含义（是包含包裹器还是不包含）在开发过程中有过摇摆，导致了不必要的错误。早期就明确并严格遵守数据结构约定非常重要。
*   **外部依赖 (AI)**: 当系统的一部分依赖于外部（如AI）的特定格式输出时，必须有健壮的错误处理和容错机制，或者投入大量精力去“训练”和约束外部系统的输出。

这份详细的开发历程希望能覆盖您要求的所有方面。如果还有其他需要补充或澄清的细节，请随时提出。
