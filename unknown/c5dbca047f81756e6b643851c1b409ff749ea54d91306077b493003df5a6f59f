// SillyTavern / TavernHelper injected function type declarations
declare function triggerSlash(command: string): Promise<string | undefined>;
declare const toastr: {
  success: (message: string, title?: string) => void;
  info: (message: string, title?: string) => void;
  error: (message: string, title?: string) => void;
  warning: (message: string, title?: string) => void;
};

export function safeToastr(type: 'info' | 'success' | 'warning' | 'error', message: string, title?: string) {
  try {
    if (
      typeof (window as any).toastr !== 'object' &&
      typeof parent !== 'undefined' &&
      typeof (parent as any).toastr === 'object'
    ) {
      (window as any).toastr = (parent as any).toastr;
    }
    if (typeof toastr === 'object' && toastr !== null && typeof toastr[type] === 'function') {
      toastr[type](message, title);
    } else {
      const consoleFn = type === 'error' ? console.error : type === 'warning' ? console.warn : console.log;
      consoleFn(`[ModuleSetup Toastr Fallback - ${type}] ${title ? title + ': ' : ''}${message}`);
    }
  } catch (e) {
    console.error(`[ModuleSetup] safeToastr Error: ${(e as Error).message}`);
  }
}

export async function saveContentToLorebook(entryKey: string, entryContent: string, outputMessageDiv: HTMLElement | null) {
  if (!outputMessageDiv) {
    console.error('Output message div not found for saveContentToLorebook');
    return;
  }
  if (!entryKey) {
    safeToastr('warning', '模组标题 (Key) 或角色名不能为空!', '输入错误');
    outputMessageDiv.textContent = '错误: 模组标题 (Key) 或角色名不能为空。';
    return;
  }

  outputMessageDiv.textContent = `正在尝试将内容保存到世界书条目 (Key: ${entryKey})...`;
  const lorebookFileName = 'RPG_Modules_Test.json';
  const command = `/createentry file="${lorebookFileName}" key="${entryKey}" ${entryContent}`;

  safeToastr('info', `执行命令... (Key: ${entryKey}, 内容长度: ${entryContent.length})`, '世界书操作');
  console.log(
    `[ModuleSetup] Executing command for key "${entryKey}" (content preview: ${entryContent.substring(0, 50)}...)`,
  );

  try {
    if (typeof triggerSlash !== 'function') {
      safeToastr('error', 'triggerSlash API 不可用!', 'API 错误');
      outputMessageDiv.textContent = '错误: triggerSlash API 不可用。';
      return;
    }

    const resultUid = await triggerSlash(command);

    if (resultUid && resultUid.trim() !== '') {
      const successMsg = `成功创建/更新世界书条目！\n文件名: ${lorebookFileName}\nKey: ${entryKey}\nUID: ${resultUid}\n内容预览: ${entryContent.substring(
        0,
        100,
      )}...`;
      safeToastr('success', `条目 '${entryKey}' 已保存。UID: ${resultUid}`, '操作成功');
      outputMessageDiv.innerHTML = successMsg.replace(/\n/g, '<br>');
    } else {
      const failMsg = `创建/更新世界书条目可能失败或没有返回UID。\n文件名: ${lorebookFileName}\nKey: ${entryKey}.\n请检查SillyTavern日志或世界书。可能原因：内容过长或包含无法处理的特殊字符。`;
      safeToastr('warning', `条目 '${entryKey}' 保存结果未知。`, '操作结果未知');
      outputMessageDiv.innerHTML = failMsg.replace(/\n/g, '<br>');
    }
  } catch (error) {
    const errorMsg = `保存条目 '${entryKey}' 时发生错误: ${(error as Error).message}`;
    safeToastr('error', errorMsg, '操作失败');
    outputMessageDiv.textContent = errorMsg;
    console.error(`[ModuleSetup] Error saving entry '${entryKey}':`, error);
  }
}
