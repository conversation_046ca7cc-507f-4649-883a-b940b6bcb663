// Adventure Log - SCSS Styles

// Global styles for the adventure log iframe
body {
    margin: 0;
    font-family: 'Merriweather', 'Georgia', serif; 
    // background-color is set below, after all image layers
    // More thematic texture - simulating old paper/leather
    background-image: 
        // New: Add a subtle vignette/side shadow effect to make the central content pop more - Adjusted for more visibility
        linear-gradient(to right, rgba(0,0,0,0.25) 0%, transparent 15%, transparent 85%, rgba(0,0,0,0.25) 100%),
        // Existing overlay
        linear-gradient(rgba(44, 40, 35, 0.5), rgba(44, 40, 35, 0.5)),
        // Existing SVG texture
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234a433b' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-color: #2c2823; // Base color for the body
    color: #e0e0e0; 
    // display: flex; // Removed flex display from body
    // justify-content: center; // Removed
    // align-items: flex-start; // Removed
    min-height: 100vh;
    box-sizing: border-box;
}

#adventure-log-container {
    width: 95vw; 
    max-width: 700px; // Increased max-width for wider screens
    // aspect-ratio: 9 / 17; // Removed to allow content to define height
    // min-height: 85vh; // Removed to allow content to fully define height
    background-color: #eaddc7; // Lighter parchment, less yellow
    // Attempting a more ornate border using pseudo-elements
    border: 2px solid #5a3d2b; // Base solid border
    position: relative; // For pseudo-elements
    border-radius: 12px; 
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.7), inset 0 0 20px rgba(0,0,0,0.2); 
    display: flex;
    flex-direction: column;
    // overflow-y: auto; // Removed to allow parent (body/iframe) to scroll
    padding: 18px; 
    box-sizing: border-box;
    margin: 0 auto; // Top 0, Sides auto, Bottom 0 to bring it closer to the top

    // Ornate border effect
    &::before, &::after {
        content: '';
        position: absolute;
        border-radius: 12px; // Match parent
        z-index: -1;
    }
    &::before { // Outer decorative border
        top: -6px; left: -6px; right: -6px; bottom: -6px;
        border: 3px dashed #9c7b60; // Dashed gold-ish border
        opacity: 0.7;
    }
    // &::after { // Inner decorative border (optional, can make it too busy)
    //     top: 3px; left: 3px; right: 3px; bottom: 3px;
    //     border: 1px solid #b89a7c;
    // }
}

#player-status-area {
    padding-bottom: 12px; 
    border-bottom: 1px solid #a8886c; // Softer separator
    margin-bottom: 12px; 
    display: flex;
    flex-direction: column; 
    gap: 9px; 
    font-size: 0.88em; 

    .status-row { 
        display: flex;
        flex-wrap: wrap; 
        gap: 10px; 
        align-items: center;
        justify-content: space-around; 
    }

    #health, #ac-display, #time, #location {
        padding: 6px 9px; 
        background-color: #d8c8b3; // Slightly darker parchment for status items
        border: 1px solid #7a5c44; // Muted brown border
        border-radius: 5px;
        white-space: nowrap; 
        color: #422d1a; // Darker text for contrast
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        font-weight: 500;
    }
}

#toggle-char-sheet-button {
    padding: 10px 15px;
    background-color: #8c6f4f; // Muted brown/bronze
    color: #f0e6d2; 
    border: 1px solid #604834; 
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.92em;
    margin-top: 10px;
    margin-bottom: 10px;
    text-align: center;
    width: 100%; 
    font-weight: 600; 
    transition: background-color 0.2s, box-shadow 0.2s, transform 0.1s;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.2);

    &:hover {
        background-color: #7a5c3f; 
        box-shadow: 0 0 8px rgba(140, 111, 79, 0.8);
        transform: translateY(-1px);
    }
}

#detailed-character-sheet {
    background-color: rgba(216, 200, 179, 0.85); // Lighter, more opaque parchment
    padding: 14px;
    border-radius: 8px;
    margin-top: 8px;
    border: 1px solid #7a5c44; 
    max-height: 35vh; 
    overflow-y: auto;
    color: #4a321f; 

    h4 {
        margin-top: 14px; 
        margin-bottom: 9px; 
        color: #5a3d2b; 
        font-size: 1.1em;   
        border-bottom: 1px solid #7a5c44;
        padding-bottom: 6px; 
        font-variant: small-caps; 
        // Adding small decorative lines
        position: relative;
        &::before, &::after {
            content: '';
            position: absolute;
            bottom: -2px; // Position below the border-bottom
            height: 2px;
            background-color: #9c7b60;
            width: 20px;
        }
        &::before { left: 0; }
        &::after { right: 0; }
    }
    h4:first-child {
        margin-top: 0;
    }

    .status-row {
        display: flex;
        flex-wrap: wrap;
        gap: 9px 13px; 
        align-items: center;
        margin-bottom: 9px; 

        div, span {
            padding: 4px 8px;
            background-color: #eaddc7; 
            border: 1px solid #b89a7c; 
            border-radius: 4px;
            white-space: nowrap;
            font-size: 0.92em; 
            color: #4a321f; 
        }
    }

    ul {
        list-style-type: '✧ '; // Using a unicode character as a bullet
        padding-left: 15px; 
        margin: 8px 0 11px 0; 
        font-size: 0.88em;
        li {
            padding: 3px 0; 
            border-bottom: 1px dotted rgba(122, 92, 68, 0.5); 
            &:last-child {
                border-bottom: none;
            }
        }
    }
    #spell-slots-display {
        font-size: 0.88em;
        padding-left: 12px;
        margin-bottom: 11px; 
    }
}

#main-narrative-area {
    flex-grow: 1; 
    padding: 14px;
    background-color: #faf0e0; // Even lighter, almost white, for max readability
    border: 1px solid #c8a064; 
    border-radius: 8px;
    // overflow-y: auto; // Removed to allow content to expand
    line-height: 1.7; 
    font-size: 1.02em; 
    color: #332211; // Very dark brown for text
    margin-bottom: 18px;
    // min-height: 115px; // Removed to allow content to expand
    box-shadow: inset 0 0 10px rgba(0,0,0,0.1);

    p {
        margin-top: 0;
        margin-bottom: 1em; 
        &:last-child {
            margin-bottom: 0;
        }
    }

    .system-message em {
        color: #7a522f; // Darker sienna for system messages
        font-style: italic;
        font-weight: 500;
    }
    .thought-message i {
        color: #607d8b; // Muted blue-grey for thoughts
        font-style: italic;
    }
    strong { 
        color: #5a3d2b; // Darker brown for speaker names
        font-weight: 700;
    }
}

#action-choices-area {
    display: flex;
    flex-direction: column;
    gap: 9px; 

    button {
        padding: 13px 17px;
        background-color: #7a5c3f; // Base button color - a rich brown
        color: #f5e8d0; 
        border: 1px solid #503d2e; // Darker border
        border-bottom: 3px solid #503d2e; // Thicker bottom border for 3D effect
        border-radius: 8px; // Slightly more rounded
        cursor: pointer;
        font-size: 0.98em;
        text-align: left;
        font-weight: 600; // Bolder text
        font-family: 'Merriweather', 'Georgia', serif;
        box-shadow: 0 2px 3px rgba(0,0,0,0.2);
        transition: background-color 0.15s ease-out, transform 0.1s ease-out, box-shadow 0.15s ease-out, border-bottom-width 0.1s ease-out;
        position: relative; // For potential pseudo-elements

        // Adding a subtle texture/shine to buttons
        &::before {
            content: '';
            position: absolute;
            top: 0; left: 0; right: 0; bottom: 0;
            background: linear-gradient(rgba(255,255,255,0.05), rgba(255,255,255,0));
            border-radius: 8px;
            pointer-events: none;
        }

        &:hover {
            background-color: #8c6f4f; // Lighter brown on hover
            border-color: #604834;
            box-shadow: 0 3px 5px rgba(0,0,0,0.25);
            transform: translateY(-2px); // More noticeable lift
        }

        &:active {
            background-color: #604834; // Darker on active
            transform: translateY(1px); // Pressed down effect
            border-bottom-width: 1px; // Reduce bottom border on press
            box-shadow: inset 0 1px 2px rgba(0,0,0,0.2);
        }
    }
}

// Styles for the Start Screen
#start-screen-container {
    display: flex; 
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 20px;
    box-sizing: border-box;
    width: 100%;
    height: 100%; 
    overflow-y: auto; 
    background-color: #f5e8d0; // Match container background for consistency when shown
    color: #4a2e00; // Dark brown text

    h1 {
        font-size: 2em; 
        color: #6b4226; // Darker brown title
        margin-bottom: 18px;
        font-variant: small-caps;
        text-shadow: 1px 1px 2px rgba(200,160,100,0.5);
    }

    p {
        font-size: 1.05em; 
        color: #5a3811; 
        margin-bottom: 22px; 
        max-width: 88%; 
        line-height: 1.55;
    }

    #start-new-game-button {
        padding: 14px 28px; 
        font-size: 1.2em; 
        background-color: #6B8E23; // OliveDrab - a classic adventure green
        color: #f5f5f5; // Off-white text
        border: 2px outset #8FBC8F; // DarkSeaGreen border
        border-radius: 8px;
        cursor: pointer;
        transition: background-color 0.25s, transform 0.15s;
        font-weight: 500;
        text-shadow: 1px 1px 1px rgba(0,0,0,0.4);
        box-shadow: 0 3px 5px rgba(0,0,0,0.3);

        &:hover {
            background-color: #556B2F; // DarkOliveGreen
            transform: scale(1.03);
        }
    }

    .start-screen-note {
        font-size: 0.78em; 
        color: #705432; 
        margin-top: 18px; 
        max-width: 82%; 
        line-height: 1.45;
    }
}

// Optional: Styles for Inventory/Skills Area (if implemented)
// #inventory-area { ... } // Current HTML has this commented out

// Responsive adjustments for smaller screens (e.g., mobile phones)
@media (max-width: 600px) {
    #adventure-log-container {
        width: 100vw;
        // height: 100vh; // Removed to allow content to define height
        max-width: 100vw;
        border-radius: 0;
        border: none; // Remove border on mobile for full screen feel
        border-image: none; // Remove border image on mobile
        padding: 10px;
        aspect-ratio: unset; 
        // overflow-y: auto; // Removed to allow parent (body/iframe) to scroll
    }

    #player-status-area { 
        font-size: 0.85em; 
        gap: 6px; 
        padding-bottom: 10px;
        margin-bottom: 10px;

        .status-row { 
            gap: 8px; 
        }
        #health, #ac-display, #time, #location {
            padding: 5px 8px;
        }
    }
    
    #detailed-character-sheet {
        font-size: 0.85em; // Base font for sheet on mobile
        padding: 10px;
        h4 {
            font-size: 1em; // Relative to sheet's font-size
        }
        .status-row { 
            flex-direction: column; 
            align-items: flex-start; 
            div, span {
                width: 100%; 
                margin-bottom: 4px; 
                text-align: left;
                font-size: 0.95em; // Relative to sheet's font-size
            }
        }
         ul, #spell-slots-display {
            font-size: 0.95em; // Relative to sheet's font-size
        }
    }

    #main-narrative-area {
        font-size: 1em; // Slightly larger for readability on mobile
        padding: 10px;
        margin-bottom: 15px;
    }

    #action-choices-area button {
        padding: 12px 15px;
        font-size: 0.95em;
    }

    #toggle-char-sheet-button {
        font-size: 0.9em;
        padding: 8px 12px;
    }

    // Detailed character sheet max height adjustment for mobile was already present
    // #detailed-character-sheet {
    //     max-height: 35vh; 
    // }
}
