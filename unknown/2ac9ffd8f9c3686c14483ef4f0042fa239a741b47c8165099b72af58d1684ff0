# 角色创建模块 (src/module_setup) 功能增强与问题修复任务列表

本文档旨在规划和追踪对 `src/module_setup` 中角色创建功能的修改，以解决现有问题并引入新特性。

## 一、 Bug修复 (已通过重构和验证解决)

1.  **属性保存问题**:
    *   **状态**: <ins>已解决</ins>。
    *   **说明**: `handleSaveCharacter` 函数现在能正确读取、计算并保存所有六维属性的最终值到 `CharacterGenerationData` 对象中。
2.  **法术信息丢失**:
    *   **状态**: <ins>已解决</ins>。
    *   **说明**: 动态添加的戏法和一环法术的名称，以及一环法术位信息，现在能被正确收集并保存到 `equippedSpells` 和 `spellSlots` 字段中。
3.  **其他角色信息保存**:
    *   **状态**: <ins>已解决</ins>。
    *   **说明**: 对 `handleSaveCharacter` 的全面审查和重构确保了HTML表单中的所有标准角色信息字段（如背景、特性、理想、牵绊、缺点、外貌、故事等）都能被正确捕获和保存。

## 二、 新功能添加

1.  **角色起源 (Origin)**:
    *   **需求**: 增加“角色起源”部分。
    *   **子任务**:
        *   在 `index.html` 中为角色起源添加新的表单区域。
        *   在 `index.ts` 的 `CharacterGenerationData` 接口中添加相应字段来存储起源信息。
        *   **起源专长**: 起源应包含一个“起源专长”的选择或显示。需要在表单中为此提供输入/选择机制，并在数据结构中体现。专长列表显示应简化，仅列出专长名称。
        *   考虑起源可能带来的其他影响（如特定技能熟练、语言等），并规划如何在数据结构和UI中体现。
2.  **熟练项 (Proficiencies)**:
    *   **需求**: 增加统一的“熟练项”部分，合并技能熟练和工具熟练。
    *   **子任务**:
        *   在 `index.html` 中设计一个新的表单区域用于展示和输入熟练项。
        *   考虑如何从现有的技能选择（如果保留）和新的工具熟练输入中整合数据。
        *   在 `CharacterGenerationData` 接口中调整或添加 `proficiencies` 字段，使其能清晰地记录各类熟练项（例如，可以使用一个包含类型和名称的对象数组，或者根据具体需求简化）。
        *   **简化显示**: 熟练项列表在UI上应仅显示熟练项的名称。
3.  **装备 (Equipment)**:
    *   **需求**: 增加“装备”部分，用于记录角色当前穿戴或持有的主要装备。
    *   **子任务**:
        *   在 `index.html` 中为装备添加新的表单区域（例如，一个可动态添加条目的列表）。
        *   在 `CharacterGenerationData` 接口中添加 `equipment` 字段（如果尚不完善），确保其能存储装备名称列表（可能包含类型等简化信息）。
        *   **简化显示**: 装备列表在UI上应仅显示装备的名称。
4.  **初始物品 (Starting Items / Inventory)**:
    *   **需求**: 增加“初始物品”或“物品栏”部分，用于记录角色开始冒险时携带的物品。
    *   **子任务**:
        *   在 `index.html` 中为初始物品添加新的表单区域。
        *   在 `CharacterGenerationData` 接口中添加 `inventory` 字段（如果尚不完善），确保其能存储物品名称和数量（可能包含简要描述）。
        *   **简化显示**: 物品列表在UI上应仅显示物品的名称（可能附带数量）。

## 三、 通用注意事项与后续步骤

1.  **数据格式定义**:
    *   对于所有新增和修改的字段，都需要在 `CharacterGenerationData` 接口 (`src/module_setup/index.ts`) 中明确定义其数据类型和结构。
    *   AI生成角色功能 (`handleAiGenerateCharacter` 和 `module_setup_ai_prompts_v2_json.md`) 也需要同步更新，以支持生成包含这些新信息的角色数据。
2.  **UI/UX**:
    *   所有新增的表单部分都需要在 `index.html` 中合理布局，并在 `index.scss` 中添加相应的样式，确保用户体验良好。
    *   列表的简化显示需要特别注意，确保信息足够的同时界面不冗余。
3.  **代码实现**:
    *   `src/module_setup/index.ts` 中的 `handleSaveCharacter` 函数将是修改的重点，需要确保能正确收集所有新旧字段的数据。
    *   `populateCharacterFormFromJSON` (如果AI生成功能填充表单) 也需要更新以处理新字段。
    *   相关的事件监听器和DOM操作函数也可能需要调整。
4.  **`src/adventure_log_v2` 的同步更新**:
    *   在 `src/module_setup` 的修改完成后，需要规划如何将这些更详细和准确的角色数据结构同步到 `src/adventure_log_v2` 的 `PlayerState` 接口中。
    *   `adventure_log_v2` 的UI显示（如详细角色卡）和AI交互提示词也需要相应调整，以利用这些新增的角色信息。
5.  **分步实施**:
    *   这些修改可以分阶段进行，例如先修复Bug，再逐步添加新功能。

## 四、已完成的工作总结 (截至 2025-05-30)

1.  **技能熟练项的结构化输入与动态计算**:
    *   **HTML (`index.html`)**:
        *   移除了原有的 `char-skill-proficiencies-text` 文本输入框。
        *   新增了一个“技能熟练”区域 (`skills-section`)，其中包含D&D 5e标准技能列表。
        *   每个技能条目 (`skill-item`) 包含：一个用于标记熟练的复选框、技能名称标签、一个用于输入额外修正的数字输入框 (`skill-modifier-input`)，以及一个只读的显示最终技能值的文本框 (`skill-final-value`)。
        *   添加了“清空技能修正与熟练”按钮。
    *   **TypeScript (`index.ts`)**:
        *   `CharacterGenerationData` 接口中的 `skills` 字段更新为 `SkillData[]` 类型，每个 `SkillData` 对象包含 `name`, `proficient`, `attribute` (关联属性), `modifierValue` (额外修正) 和 `finalValue` (计算后的最终值)。移除了 `skillProficienciesText` 字段。
        *   `handleSaveCharacter` 函数现在会遍历技能UI元素，收集每个技能的熟练状态、额外修正，并结合相关属性调整值和熟练加值计算最终技能值，然后存入 `characterDataToSave.skills` 数组。
        *   `populateCharacterFormFromJSON` 函数会根据加载的角色数据中的 `skills` 数组填充技能UI的复选框和修正值。
        *   添加了 `calculateSkillFinalValue`, `updateSkillFinalValue`, `updateAllSkillFinalValues`, `setupSkillCalculations` 等函数来处理技能值的动态计算和UI更新。当属性、等级或熟练选项变化时，技能最终值会自动更新。
    *   **工具熟练项**: 继续使用 `char-tool-proficiencies-text` 文本框输入，并在保存时解析到 `proficiencies` 数组。

2.  **装备与初始物品的动态结构化输入与保存**:
    *   **HTML (`index.html`)**:
        *   移除了原有的单一“装备”文本框。
        *   新增了“已装备物品” (`equipment-list-container`) 和“初始物品 (背包)” (`inventory-list-container`) 两个动态列表区域，允许用户通过按钮动态添加/移除条目。
        *   装备条目包含“名称”和“详情”输入框。
        *   初始物品条目包含“名称”、“详情”和“数量”输入框。
        *   添加了金币、银币、铜币的输入框。
    *   **TypeScript (`index.ts`)**:
        *   `CharacterGenerationData` 接口中移除了 `equipmentText`。`equipment` 和 `inventory` 数组用于存储结构化数据。`currency` 对象用于存储货币。
        *   `handleSaveCharacter` 函数会从这些动态列表和货币输入框中收集数据并保存。
        *   `populateCharacterFormFromJSON` 函数会根据加载的数据填充这些动态列表和货币字段。
        *   添加了 `createDynamicEquipmentItem`, `createDynamicInventoryItem`, `setupDynamicEquipmentList`, `setupDynamicInventoryList` 函数来管理这些列表的DOM操作。

3.  **AI角色生成指导**:
    *   **HTML (`index.html`)**: 在角色创建表单的底部、“AI生成角色”按钮前，添加了一个新的文本区域 (`ai-character-prompt-textarea`)，供用户输入对AI生成角色的具体指导提示。
    *   **TypeScript (`index.ts`)**: `handleAiGenerateCharacter` 函数现在会优先从这个新的文本区域读取用户提示。如果为空，则尝试使用“模组内容”区的文本作为提示；如果仍为空，则使用默认提示。
    *   **AI提示词 (`module_setup_ai_prompts_v2_json.md`)**: 更新了角色生成的AI指令，强调优先考虑用户提供的指导提示，并对JSON输出的严谨性（特别是 `skills` 数组的新结构和 `toolProficienciesText` 的使用）做了更详细的说明。

这些更改旨在提供一个更结构化、用户友好且功能更强大的角色创建体验，同时优化了与AI交互生成角色数据的流程。

此任务列表将作为后续开发工作的指南。
