﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>

<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=8">
<style>
html {
	overflow-y:scroll;
}

body {
	padding: 0px;
	margin: 0px;
	overflow: auto; 
	background-color: #FFFFFF; 
}

#winchm_template_top{
	margin: 0px;
	padding-top: 10px;
	padding-left: 5px;
	padding-bottom: 10px;
	padding-right: 10px;

	font-family: arial, helvetica, sans-serif;
	font-size: 35px;
	font-style: normal;
	font-weight: normal;
	color: #FFFFFF;

	border-bottom-style: solid;
	border-bottom-width: 2px;
	border-bottom-color: #00CC00;

	width: auto;
	background-color: #A6CAF0;
	text-align: left;
	background-image: url('hgrad.gif');
	background-repeat: repeat;
	
}

#winchm_template_navigation{
	margin-top: 0px;
	margin-left: 0px;
	margin-bottom: 0px;
	margin-right: 0px;

	padding-top: 7px;
	padding-left: 5px;
	padding-bottom: 6px;
	padding-right: 5px;

	font-family: arial, helvetica, sans-serif;
	font-size: 10px;
	font-style: normal;
	font-weight: normal;
	color: #808080; 

	border-top-style: solid;
	border-top-width: 3px;
	border-top-color: #C1C1C1;

	border-bottom-style: dashed;
	border-bottom-width: 1px;
	border-bottom-color: #DBDBDB;
	
	background-color: #FFFFFF;
	width: auto;
}

#winchm_template_button{
	float: right;
	top: -3px;
	position: relative;
	text-align: right;
	right: -5px;
	height: auto;
}

#winchm_template_title{
	font-family: Segoe UI, Tahoma, Geneva, Verdana, sans-serif;
	font-weight: bold;
	font-size: 18px;
	color: 353535;	

	margin-top: 0px;
	margin-left: 0px;
	margin-bottom: 0px;
	margin-right: 0px;

	padding-top: 10px;
	padding-left: 15px;
	padding-bottom: 10px;
	padding-right: 10px;
	
	border-bottom-style: dashed;
	border-bottom-width: 1px;
	border-bottom-color: #C7C7C7;

	width: auto;
	background-color: #FFFFFF;
	text-align: left;
}
#winchm_template_content{
	padding: 0px;
	margin-top: 15px;
	margin-left: 15px;
	margin-bottom: 15px;
	margin-right: 15px;

	width: auto  !important;
	width: 100%;
}

#winchm_template_footer{
	border-width: 1px;
	border-color: #c0c0c0;
	border-top-style: solid;
	
	margin-top: 0px;
	margin-left: 15px;
	margin-bottom: 5px;
	margin-right: 15px;

	padding-top: 6px;
	padding-left: 0px;
	padding-bottom: 20px;
	padding-right: 0px;

	font-family: arial, helvetica, sans-serif;
	font-size: 9px;
	color: #909090;

	text-align: left;	
	width: auto;	
}

#winchm_template_navigation a:link	{text-decoration: none;color: #0000ff}
#winchm_template_navigation a:visited  {text-decoration: none; color: #0000ff}
#winchm_template_navigation a:active {text-decoration: none; color: #0000ff}
#winchm_template_navigation a:hover {text-decoration: none;color: #0000ff}

a:link	{text-decoration: underline; color:#0033cc}
a:visited  {text-decoration: underline; color: #0033cc}
a:active {text-decoration: underline; color: #0033cc }
a:hover {text-decoration: underline;color: #ff0000;}

@media print
{
#winchm_template_button{
visibility:hidden;
}
}
</style>
</head>

<body>

<div id="winchm_template_top"><div id="winchm_template_button"><IMG alt="Previous topic" src="btn_prev_n.gif" ><IMG alt="Next topic" src="btn_next_n.gif" ></div>
<nobr><IMG align=absMiddle src="logo.gif"> XXXX Help</nobr></div>
<div id="winchm_template_navigation">Help &gt; 
($navigation$)</div>
<div id="winchm_template_title">($title$)</div>
<div id="winchm_template_content">($content$)</div>
<div id="winchm_template_footer">Copyright &copy; 2023. All rights reserved.(To change the copyright info or the title, just edit them in template.)</div>

</body>
</html>
