import './index.scss';

// --- 类型声明 ---
declare const $: any; // jQuery
declare const toastr: {
  success: (message: string, title?: string) => void;
  info: (message: string, title?: string) => void;
  error: (message: string, title?: string) => void;
  warning: (message: string, title?: string) => void;
};
declare function triggerSlash(command: string): Promise<string | undefined>;
declare function getLastMessageId(): number | undefined;
declare function setChatMessages(
  messages: { message_id: number; message: string }[],
  options?: { refresh?: string },
): Promise<void>;

// --- 核心数据接口 (JSON Schema v2) ---
interface Attribute {
  base: number;
  race_bonus: number;
  modifier_bonus: number;
  final: number;
  mod: number;
}

interface Hp {
  current: number;
  max: number;
}

interface Currency {
  gold: number;
  silver: number;
  copper: number;
}

interface Skill {
  name: string;
  proficient: boolean;
  attribute: string;
  modifierValue: number;
  finalValue: number; // This is the correct value to display
}

interface SpellSlotInfo {
  current: number;
  max: number;
}

interface Spell {
  name: string;
  level: number;
  source?: string;
  details?: string;
}

interface EquipmentItem {
  name: string;
  type: string;
  equipped?: boolean;
  details?: string;
  properties?: string[]; // For weapon properties like "Finesse"
}

interface InventoryItem {
  name: string;
  quantity: number;
  description?: string;
  properties?: string[];
}

interface PlayerState {
  name: string;
  race: string;
  class: string;
  level: number;
  exp: number;
  hp: Hp;
  ac: number;
  currency: Currency;
  attributes: {
    strength: Attribute;
    dexterity: Attribute;
    constitution: Attribute;
    intelligence: Attribute;
    wisdom: Attribute;
    charisma: Attribute;
  };
  proficiencies: string[]; // Includes weapon types, armor types, skills, saving throws
  skills: Skill[]; // Detailed skills with proficiency and value
  spellSlots: Record<string, SpellSlotInfo>; // e.g., { "1": { current: 3, max: 3 } }
  equippedSpells: Spell[];
  equipment: EquipmentItem[];
  inventory: InventoryItem[];
  activeQuests: string[];
  exhaustion: number;
  time: string;
  currentLocation: string;
  spellcastingAbility?: 'INT' | 'WIS' | 'CHA' | string;
  // Optional fields from previous PlayerState, can be added if needed by AI or UI
  player?: string;
  age?: string;
  gender?: string;
  alignment?: string;
  faith?: string;
  height?: string;
  weight?: string;
  appearance?: string;
  story?: string;
  background?: string;
  personalityTraits?: string;
  ideals?: string;
  bonds?: string;
  flaws?: string;
  subclass?: string;
}

interface NarrativeEntry {
  type: 'description' | 'dialogue' | 'systemMessage' | 'actionDescription' | 'thought';
  content: string;
  speaker?: string;
  actor?: string;
  emotion?: string;
}

interface PlayerChoiceJSON {
  id: string;
  text: string;
  actionCommand: string;
  // checkDetails?: CheckDetailJSON; // Future extension
}

interface VariableUpdateInstruction {
  target: '玩家';
  path: string;
  operation: '设置' | '增加' | '减少' | '添加元素' | '移除元素' | '物品获得' | '物品失去';
  value: any; // For item operations, this will be an object like { name: string, quantity: number, description?: string }
}

interface EnemyStateJSON {
  id: string;
  name: string;
  hp: { current: number; max: number };
  ac: number;
  statusEffects?: string[];
  intent?: string;
}

interface AdventureSceneJSON {
  sceneType: 'location' | 'dialogue' | 'combat' | 'system_message' | 'puzzle';
  sceneTitle: string;
  currentLocation: string;
  time: string;
  narrative: NarrativeEntry[];
  playerChoices: PlayerChoiceJSON[];
  variableUpdates?: VariableUpdateInstruction[];
  enemies?: EnemyStateJSON[];
  combatLog?: string[];
}

// --- 模块级状态变量 ---
function getDefaultPlayerState(): PlayerState {
  // (Same as existing getDefaultPlayerState, ensure it matches the updated PlayerState interface if necessary)
  return {
    name: '新冒险者',
    race: '人类',
    class: '平民',
    level: 1,
    exp: 0,
    hp: { current: 10, max: 10 },
    ac: 10,
    currency: { gold: 10, silver: 0, copper: 0 },
    attributes: {
      strength: { base: 10, race_bonus: 0, modifier_bonus: 0, final: 10, mod: 0 },
      dexterity: { base: 10, race_bonus: 0, modifier_bonus: 0, final: 10, mod: 0 },
      constitution: { base: 10, race_bonus: 0, modifier_bonus: 0, final: 10, mod: 0 },
      intelligence: { base: 10, race_bonus: 0, modifier_bonus: 0, final: 10, mod: 0 },
      wisdom: { base: 10, race_bonus: 0, modifier_bonus: 0, final: 10, mod: 0 },
      charisma: { base: 10, race_bonus: 0, modifier_bonus: 0, final: 10, mod: 0 },
    },
    proficiencies: ['匕首'],
    skills: [],
    spellSlots: {},
    equippedSpells: [],
    equipment: [
      { name: '平民服装', type: '衣物', equipped: true },
      { name: '匕首', type: '武器', equipped: true, properties: ['灵巧', '轻型'] },
    ],
    inventory: [
      { name: '背包', quantity: 1 },
      { name: '火绒盒', quantity: 1 },
      { name: '口粮', quantity: 2, description: '一天的食物' },
    ],
    activeQuests: [],
    exhaustion: 0,
    time: '第一天 清晨',
    currentLocation: '旅途的起点',
    spellcastingAbility: 'INT', // Example default
  };
}

let playerState: PlayerState = getDefaultPlayerState();

// AdventureLogEntry and fullHistoryLog are no longer needed as history is managed by a separate lorebook.
// interface AdventureLogEntry {
//   sceneJSONString: string; 
//   playerChoiceText?: string;
//   timestamp: number;
// }
// let fullHistoryLog: AdventureLogEntry[] = [];

let currentSceneData: AdventureSceneJSON | null = null; // Stores the latest scene data
let currentHostMessageId: number | null = null; // Stores the ID of the message where PlayerState and latest scene are persisted

// --- DOM元素引用 (保持不变) ---
// ... (all getElementById calls remain the same) ...
let startScreenContainer: HTMLElement | null = null;
let startNewGameButton: HTMLButtonElement | null = null;
let adventureLogContainer: HTMLElement | null = null;
let playerStatusArea: HTMLElement | null = null;
let mainNarrativeArea: HTMLElement | null = null;
let actionChoicesArea: HTMLElement | null = null;
let healthDisplay: HTMLElement | null = null;
let locationDisplay: HTMLElement | null = null;
let timeDisplay: HTMLElement | null = null;
let charNameDisplay: HTMLElement | null = null;
let charRaceClassDisplay: HTMLElement | null = null;
let charLevelDisplay: HTMLElement | null = null;
let acDisplay: HTMLElement | null = null;
let attrStrDisplay: HTMLElement | null = null;
let attrDexDisplay: HTMLElement | null = null;
let attrConDisplay: HTMLElement | null = null;
let attrIntDisplay: HTMLElement | null = null;
let attrWisDisplay: HTMLElement | null = null;
let attrChaDisplay: HTMLElement | null = null;
let currencyGoldDisplay: HTMLElement | null = null;
let expDisplay: HTMLElement | null = null;
let exhaustionDisplay: HTMLElement | null = null;
let toggleCharSheetButton: HTMLButtonElement | null = null;
let detailedCharacterSheet: HTMLElement | null = null;
let proficienciesDisplay: HTMLElement | null = null;
let skillsDisplay: HTMLElement | null = null;
let spellSlotsDisplay: HTMLElement | null = null;
let equippedSpellsDisplay: HTMLElement | null = null;
let equipmentDisplay: HTMLElement | null = null;
let inventoryDisplay: HTMLElement | null = null;
let activeQuestsDisplay: HTMLElement | null = null;

// --- 工具函数 (safeToastr, ensureGlobals - 保持不变) ---
// ... (safeToastr and ensureGlobals functions remain the same) ...
function safeToastr(type: 'info' | 'success' | 'warning' | 'error', message: string, title?: string) {
  try {
    if (
      typeof (window as any).toastr !== 'object' &&
      typeof parent !== 'undefined' &&
      typeof (parent as any).toastr === 'object'
    ) {
      (window as any).toastr = (parent as any).toastr;
    }
    if (typeof toastr === 'object' && toastr !== null && typeof toastr[type] === 'function') {
      toastr[type](message, title);
    } else {
      const consoleFn = type === 'error' ? console.error : type === 'warning' ? console.warn : console.log;
      consoleFn(`[AdvLog Toastr Fallback - ${type}] ${title ? title + ': ' : ''}${message}`);
    }
  } catch (e) {
    console.error(`[AdvLog] safeToastr Error: ${(e as Error).message}`);
  }
}

function ensureGlobals() {
  try {
    if (typeof window !== 'undefined' && typeof parent !== 'undefined' && parent !== window) {
      const apiKeysToCopy = ['$', 'toastr', 'triggerSlash', 'getLastMessageId', 'setChatMessages'];
      apiKeysToCopy.forEach(key => {
        if (typeof (window as any)[key] === 'undefined') {
          if (typeof (parent as any)[key] !== 'undefined') {
            (window as any)[key] = (parent as any)[key];
          }
        }
      });
    }
  } catch (e) {
    // console.error(`[AdvLog] ensureGlobals Error: ${(e as Error).message}`);
  }
}

// --- UI更新函数 ---
function updatePlayerStatusDisplay() {
  // (Logic remains largely the same, but reads from the new playerState structure)
  // Ensure all paths in playerState are correctly accessed.
  if (!playerState) return;
  if (charNameDisplay) charNameDisplay.textContent = `角色名: ${playerState.name || 'N/A'}`;
  if (charRaceClassDisplay)
    charRaceClassDisplay.textContent = `种族/职业: ${playerState.race || 'N/A'} / ${playerState.class || 'N/A'}`;
  if (charLevelDisplay) charLevelDisplay.textContent = `等级: ${playerState.level || 0}`;
  if (healthDisplay)
    healthDisplay.textContent = `生命值: ${playerState.hp?.current || '?'}/${playerState.hp?.max || '?'}`;
  if (acDisplay) acDisplay.textContent = `AC: ${playerState.ac || 0}`;
  if (timeDisplay) timeDisplay.textContent = `时间: ${playerState.time || 'N/A'}`;
  if (locationDisplay) locationDisplay.textContent = `地点: ${playerState.currentLocation || 'N/A'}`;
  const formatAttr = (attr: Attribute | undefined) =>
    attr ? `${attr.final}(${attr.mod >= 0 ? '+' : ''}${attr.mod})` : 'N/A';
  if (attrStrDisplay) attrStrDisplay.textContent = `力量: ${formatAttr(playerState.attributes?.strength)}`;
  if (attrDexDisplay) attrDexDisplay.textContent = `敏捷: ${formatAttr(playerState.attributes?.dexterity)}`;
  if (attrConDisplay) attrConDisplay.textContent = `体质: ${formatAttr(playerState.attributes?.constitution)}`;
  if (attrIntDisplay) attrIntDisplay.textContent = `智力: ${formatAttr(playerState.attributes?.intelligence)}`;
  if (attrWisDisplay) attrWisDisplay.textContent = `感知: ${formatAttr(playerState.attributes?.wisdom)}`;
  if (attrChaDisplay) attrChaDisplay.textContent = `魅力: ${formatAttr(playerState.attributes?.charisma)}`;
  if (currencyGoldDisplay) currencyGoldDisplay.textContent = `${playerState.currency?.gold || 0}`;
  if (expDisplay) expDisplay.textContent = `${playerState.exp || 0}`;
  if (exhaustionDisplay) exhaustionDisplay.textContent = `${playerState.exhaustion || 0}`;
  if (proficienciesDisplay)
    proficienciesDisplay.innerHTML = playerState.proficiencies?.map(p => `<li>${p}</li>`).join('') || '<li>无</li>';
  if (skillsDisplay)
    skillsDisplay.innerHTML =
      playerState.skills
        ?.filter(s => s.proficient)
        .map(s => `<li>${s.name} (${s.finalValue >= 0 ? '+' : ''}${s.finalValue})</li>`)
        .join('') || '<li>无熟练技能</li>';
  if (spellSlotsDisplay) {
    let html = '';
    if (playerState.spellSlots && Object.keys(playerState.spellSlots).length > 0) {
      for (const level in playerState.spellSlots) {
        const slots = playerState.spellSlots[level];
        html += `<div>${level}环: ${slots.current}/${slots.max}</div>`;
      }
    }
    spellSlotsDisplay.innerHTML = html || '无';
  }
  if (equippedSpellsDisplay)
    equippedSpellsDisplay.innerHTML =
      playerState.equippedSpells?.map(s => `<li>${s.name} (${s.level}环)</li>`).join('') || '<li>无</li>';
  if (equipmentDisplay)
    equipmentDisplay.innerHTML =
      playerState.equipment?.map(e => `<li>${e.name} (${e.type})${e.equipped ? ' [已装备]' : ''}</li>`).join('') ||
      '<li>无</li>';
  if (inventoryDisplay)
    inventoryDisplay.innerHTML =
      playerState.inventory
        ?.map(i => `<li>${i.name} x${i.quantity} ${i.description ? '(' + i.description + ')' : ''}</li>`)
        .join('') || '<li>空</li>';
  if (activeQuestsDisplay)
    activeQuestsDisplay.innerHTML = playerState.activeQuests?.map(q => `<li>${q}</li>`).join('') || '<li>无</li>';
}

function renderNarrative(scene: AdventureSceneJSON) {
  if (!mainNarrativeArea) return;
  mainNarrativeArea.innerHTML = ''; // Clear previous content

  scene.narrative.forEach(entry => {
    const p = document.createElement('p');
    let contentHtml = entry.content.replace(/\n/g, '<br>');

    switch (entry.type) {
      case 'description':
        p.innerHTML = contentHtml;
        break;
      case 'dialogue':
        p.innerHTML = `<strong>${entry.speaker || '某人'}${
          entry.emotion ? ` (${entry.emotion})` : ''
        }:</strong> ${contentHtml}`;
        break;
      case 'systemMessage':
        p.className = 'system-message';
        p.innerHTML = `<em>${contentHtml}</em>`;
        break;
      case 'actionDescription':
        p.innerHTML = `<em>${entry.actor || '某物'} ${contentHtml}</em>`;
        break;
      case 'thought':
        p.className = 'thought-message';
        p.innerHTML = `<i>"${contentHtml}"</i>`;
        break;
      default:
        p.innerHTML = contentHtml;
    }
    if (mainNarrativeArea) {
      mainNarrativeArea.appendChild(p);
    }
  });

  if (scene.combatLog && scene.combatLog.length > 0) {
    const combatLogDiv = document.createElement('div');
    combatLogDiv.className = 'combat-log';
    combatLogDiv.innerHTML =
      '<h4>战斗记录:</h4>' + scene.combatLog.map(log => `<p>${log.replace(/\n/g, '<br>')}</p>`).join('');
    mainNarrativeArea.appendChild(combatLogDiv);
  }

  if (scene.enemies && scene.enemies.length > 0) {
    const enemiesDiv = document.createElement('div');
    enemiesDiv.className = 'enemies-display';
    let enemiesHtml = '<h4>当前敌人:</h4><ul>';
    scene.enemies.forEach(enemy => {
      enemiesHtml += `<li><strong>${enemy.name} (ID: ${enemy.id})</strong> - HP: ${enemy.hp.current}/${enemy.hp.max}, AC: ${enemy.ac}`;
      if (enemy.intent) enemiesHtml += `, 意图: ${enemy.intent}`;
      if (enemy.statusEffects && enemy.statusEffects.length > 0)
        enemiesHtml += `, 状态: ${enemy.statusEffects.join(', ')}`;
      enemiesHtml += `</li>`;
    });
    enemiesHtml += '</ul>';
    enemiesDiv.innerHTML = enemiesHtml;
    mainNarrativeArea.appendChild(enemiesDiv);
  }
}

function renderActionChoices(choices: PlayerChoiceJSON[] | undefined) {
  if (!actionChoicesArea) return;
  actionChoicesArea.innerHTML = '';
  if (choices && choices.length > 0) {
    choices.forEach(choice => {
      const button = document.createElement('button');
      button.id = `choice-${choice.id}`; // Ensure unique ID if 'id' is just 'A', 'B'
      button.textContent = choice.text;
      button.dataset.action = choice.actionCommand;
      button.addEventListener('click', () => handleActionChoice(choice));
      actionChoicesArea!.appendChild(button);
    });
  } else {
    actionChoicesArea.innerHTML = '<p>暂无行动选项。</p>';
  }
}

// --- 核心逻辑函数 ---
function getProficiencyBonus(level: number): number {
  if (level >= 17) return 6;
  if (level >= 13) return 5;
  if (level >= 9) return 4;
  if (level >= 5) return 3;
  return 2; // Level 1-4
}

interface CheckResult {
  success: boolean;
  roll: number;
  attributeMod: number;
  proficiencyBonusApplied: number;
  total: number;
  dc: number;
  attributeName: string;
  skillName?: string;
  isCritical?: boolean;
  isFumble?: boolean;
}

function performCheck(
  dc: number,
  attributeName: string,
  skillName: string | undefined,
  pState: PlayerState,
): CheckResult {
  const roll = Math.floor(Math.random() * 20) + 1;
  let attributeMod = 0;
  let proficiencyBonusApplied = 0;
  const attributeKey = attributeName.toLowerCase() as keyof PlayerState['attributes'];

  if (pState.attributes[attributeKey]) {
    attributeMod = pState.attributes[attributeKey].mod;
  }

  if (skillName) {
    const skill = pState.skills.find(s => s.name.toLowerCase() === skillName.toLowerCase());
    if (skill?.proficient) {
      proficiencyBonusApplied = getProficiencyBonus(pState.level);
    } else {
      const genericProficiency = pState.proficiencies.find(
        p => p.toLowerCase().includes(skillName.toLowerCase()) && p.toLowerCase().includes(attributeName.toLowerCase()),
      );
      if (genericProficiency) {
        proficiencyBonusApplied = getProficiencyBonus(pState.level);
      }
    }
  }
  // For saving throws, check proficiencies array directly
  if (!skillName && pState.proficiencies.includes(`${attributeName}豁免`)) {
    proficiencyBonusApplied = getProficiencyBonus(pState.level);
  }

  const total = roll + attributeMod + proficiencyBonusApplied;
  let success = total >= dc;
  const isCritical = roll === 20;
  const isFumble = roll === 1;

  if (isCritical && dc > 0) success = true; // Natural 20 usually succeeds checks/attacks, but not necessarily auto-success for saves if DC is impossible.
  // For attacks, it's a critical hit. For ability checks/saves, it's just a success if total >= DC.
  // However, many DMs rule nat 20 on check/save as auto-success. Let's assume that for now.
  if (isFumble) success = false; // Natural 1 usually fails checks/attacks/saves.

  return {
    success,
    roll,
    attributeMod,
    proficiencyBonusApplied,
    total,
    dc,
    attributeName,
    skillName,
    isCritical,
    isFumble,
  };
}

async function handleActionChoice(choice: PlayerChoiceJSON) {
  if (actionChoicesArea) {
    actionChoicesArea.querySelectorAll('button').forEach(btn => ((btn as HTMLButtonElement).disabled = true));
  }
  // Ensure mainNarrativeArea is not null before using it
  const currentMainNarrativeArea = mainNarrativeArea;
  if (currentMainNarrativeArea) {
    const waitingP = document.createElement('p');
    waitingP.innerHTML = '<i>正在等待AI响应...</i>';
    currentMainNarrativeArea.appendChild(waitingP);
  }

  let promptContext = '';
  if (currentSceneData) {
    // Now currentSceneData is AdventureSceneJSON
    // The AI will get history from the dndRPG_history lorebook, so we only send the immediate previous scene.
    promptContext += `紧接之前的场景JSON数据是:\n${JSON.stringify(currentSceneData, null, 2)}\n`;
  }
  // Removed fullHistoryLog iteration for promptContext, as history is now managed by lorebook.

  let prompt = `你是一名D&D 5e的地下城主(DM)，正在主持一个文字冒险游戏“冒险日志 v2”。\n`;
  prompt += `当前玩家状态：\n${JSON.stringify(playerState, null, 2)}\n`; // Send full player state for context
  prompt += `\n${promptContext}`;

  let checkFeedbackToAI = '';
  const checkRegex = /\[(?:DC(\d+)\s*)?([^\(\]\s]+)(?:\s*\(([^\)]+)\))?\s*(检定)?\]/i;
  const attackCheckRegex = /\[攻击\s+(.+?)\s+使用\s+(.+?)\s+DC(\d+)\]/i;

  let checkMatch = choice.text.match(checkRegex);
  let attackMatch = choice.text.match(attackCheckRegex);

  if (attackMatch) {
    const targetName = attackMatch[1].trim();
    const weaponOrSpellName = attackMatch[2].trim();
    const dc = parseInt(attackMatch[3], 10); // This is enemy AC

    let attributeToUse: 'strength' | 'dexterity' | 'intelligence' | 'wisdom' | 'charisma' = 'strength';
    let isSpellAttack = false;
    const weapon = playerState.equipment.find(e => e.name === weaponOrSpellName && e.equipped);
    const spell = playerState.equippedSpells.find(s => s.name === weaponOrSpellName);

    if (spell) {
      isSpellAttack = true;
      attributeToUse = (playerState.spellcastingAbility?.toLowerCase() || 'intelligence') as typeof attributeToUse;
    } else if (weapon && weapon.properties?.includes('灵巧')) {
      if (playerState.attributes.dexterity.mod > playerState.attributes.strength.mod) {
        attributeToUse = 'dexterity';
      }
    }
    // TODO: Add logic for ranged weapons to default to dexterity if not finesse

    const result = performCheck(dc, attributeToUse, isSpellAttack ? weaponOrSpellName : undefined, playerState); // Pass spell name for potential spell-specific proficiencies if any
    let rollDetails = `投骰1d20[${result.roll}]`;
    rollDetails += `${result.attributeMod >= 0 ? '+' : ''}${result.attributeMod}(${result.attributeName})`;
    if (result.proficiencyBonusApplied !== 0)
      rollDetails += `${result.proficiencyBonusApplied >= 0 ? '+' : ''}${result.proficiencyBonusApplied}(熟练)`;
    rollDetails += `=${result.total}`;

    let outcome = result.isFumble ? '自动失手' : result.isCritical ? '重击' : result.success ? '攻击命中' : '攻击失手';
    checkFeedbackToAI = ` [${outcome} DC${dc} ${attributeToUse}(${weaponOrSpellName}) ${rollDetails}]`;
    const toastMessage = `${weaponOrSpellName} 攻击 ${targetName}: ${result.total} vs DC${dc} -> ${outcome} (${rollDetails})`;
    safeToastr(result.success ? 'success' : 'error', toastMessage, '攻击结果');

    const narrativeAreaForCheckToast = mainNarrativeArea; // Use the captured variable
    if (narrativeAreaForCheckToast) {
      narrativeAreaForCheckToast.innerHTML += `<p class="check-result"><em>${toastMessage}</em></p>`;
    }
  } else if (checkMatch) {
    const dcString = checkMatch[1];
    const primaryName = checkMatch[2].trim();
    const skillNameIfPresent = checkMatch[3] ? checkMatch[3].trim() : undefined;

    if (primaryName.toLowerCase().includes('先攻')) {
      const result = performCheck(0, '敏捷', undefined, playerState);
      let rollDetails = `投骰1d20[${result.roll}]${result.attributeMod >= 0 ? '+' : ''}${result.attributeMod}(敏捷)=${
        result.total
      }`;
      checkFeedbackToAI = ` [先攻检定 ${rollDetails}]`;
      safeToastr('info', `先攻: ${result.total} (${rollDetails})`, '先攻');
      const narrativeAreaForIniToast = mainNarrativeArea;
      if (narrativeAreaForIniToast)
        narrativeAreaForIniToast.innerHTML += `<p class="check-result"><em>先攻: ${result.total} (${rollDetails})</em></p>`;
    } else if (dcString) {
      const dc = parseInt(dcString, 10);
      const result = performCheck(dc, primaryName, skillNameIfPresent, playerState);
      let rollDetails = `投骰1d20[${result.roll}]`;
      if (result.attributeMod !== 0)
        rollDetails += `${result.attributeMod >= 0 ? '+' : ''}${result.attributeMod}(${result.attributeName})`;
      if (result.proficiencyBonusApplied !== 0)
        rollDetails += `${result.proficiencyBonusApplied >= 0 ? '+' : ''}${result.proficiencyBonusApplied}(熟练)`;
      rollDetails += `=${result.total}`;
      let outcome = result.isFumble
        ? '自动失败'
        : result.isCritical
        ? '大成功' // Nat 20 on skill check is often treated as auto-success by DMs
        : result.success
        ? '检定成功'
        : '检定失败';
      checkFeedbackToAI = ` [${outcome} DC${dc} ${result.attributeName}${
        result.skillName ? `(${result.skillName})` : ''
      } ${rollDetails}]`;
      const toastMessage = `${result.attributeName}${result.skillName ? `(${result.skillName})` : ''} 检定: ${
        result.total
      } vs DC${dc} -> ${outcome} (${rollDetails})`;
      safeToastr(result.success ? 'success' : 'error', toastMessage, '检定结果');
      const narrativeAreaForSkillToast = mainNarrativeArea;
      if (narrativeAreaForSkillToast)
        narrativeAreaForSkillToast.innerHTML += `<p class="check-result"><em>${toastMessage}</em></p>`;
    }
  }

  prompt += `\n玩家刚刚选择了行动： "${choice.text}"${checkFeedbackToAI}\n`;
  prompt += `\n请根据玩家的选择${checkFeedbackToAI ? '和检定结果' : ''}，继续发展剧情。`;
  prompt += `\n\n【非常重要：输出格式指令】\n`;
  prompt += `你的完整回复必须是一个单一的JSON对象字符串，被 "查看系统\\##@@_ADVENTURE_BEGIN_@@##" 和 "##@@_ADVENTURE_END_@@##\\n关闭系统" 包裹。JSON结构需严格遵循之前定义的Schema (AdventureSceneJSON, NarrativeEntry, PlayerChoiceJSON, VariableUpdateInstruction, EnemyStateJSON)。\n`;
  prompt += `确保JSON语法正确，所有字符串和键名使用双引号，对象和数组的最后一个元素后无逗号，特殊字符正确转义。长文本中的换行请使用 '\\n'。\n`;
  prompt += `请务必严格遵守！\n`;

  if (typeof triggerSlash !== 'function') {
    if (actionChoicesArea)
      actionChoicesArea.querySelectorAll('button').forEach(btn => ((btn as HTMLButtonElement).disabled = false));
    const waitingMsg = mainNarrativeArea?.querySelector('p > i');
    if (waitingMsg?.parentElement && mainNarrativeArea) mainNarrativeArea.removeChild(waitingMsg.parentElement);
    return;
  }

  try {
    const aiRawResponseWithWrappers = await triggerSlash(`/gen ${prompt}`);
    const narrativeArea = mainNarrativeArea; // Capture for use in this scope

    const waitingMsg = narrativeArea?.querySelector('p > i');
    if (waitingMsg?.parentElement && narrativeArea) {
      narrativeArea.removeChild(waitingMsg.parentElement);
    }

    if (aiRawResponseWithWrappers?.trim()) {
      let coreJSONString: string | null = null;
      const newMarkerPatternMatch = aiRawResponseWithWrappers.match(/##@@_ADVENTURE_BEGIN_@@##([\s\S]*?)##@@_ADVENTURE_END_@@##/);
      
      if (newMarkerPatternMatch && newMarkerPatternMatch[1]) {
        coreJSONString = newMarkerPatternMatch[1].trim();
      } else {
        console.warn('Unique markers ##@@_ADVENTURE_BEGIN_@@##...##@@_ADVENTURE_END_@@## not found in AI response.');
        // coreJSONString remains null, will be handled below
      }

      let parsedScene: AdventureSceneJSON | null = null;
      if (coreJSONString) {
        // Attempt to parse only if coreJSONString was successfully extracted
        parsedScene = parseAIResponse(coreJSONString);
      }

      if (parsedScene) {
        // JSON extracted and parsed successfully
        applySceneData(parsedScene); 
        // Persist current player state and this latest scene to the main host message
        await persistGameState(); 

        // Save the clean, expected AI response block to the dndRPG_history lorebook
        const historyLorebookName = 'dndRPG_history.json';
        
        // Generate a more descriptive entryKey using sceneTitle and time
        let entryKey = `scene_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`; // Fallback key
        if (parsedScene && parsedScene.sceneTitle && parsedScene.time) {
          const sanitize = (str: string) => str.replace(/[\\/:*?"<>|]/g, '_').replace(/\s+/g, '_');
          const titlePart = sanitize(parsedScene.sceneTitle).substring(0, 40);
          const timePart = sanitize(parsedScene.time).substring(0, 25);
          // Add a short random string to ensure uniqueness even if title and time are identical in quick succession
          const uniqueSuffix = Math.random().toString(36).substring(2, 7);
          entryKey = `${titlePart}_${timePart}_${uniqueSuffix}`;
        }
        
        let entryContent = aiRawResponseWithWrappers; // Default to full raw response
        const cleanBlockPattern = /查看系统\s*##@@_ADVENTURE_BEGIN_@@##[\s\S]*?##@@_ADVENTURE_END_@@##\s*关闭系统/;
        const cleanMatch = aiRawResponseWithWrappers.match(cleanBlockPattern);

        if (cleanMatch && cleanMatch[0]) {
          entryContent = cleanMatch[0]; // Use the cleanly matched block
          // safeToastr('info', '已提取干净的AI回复块用于历史记录。', '历史记录处理'); // Commented out as per user request
        } else {
          safeToastr('warning', '未能从AI回复中提取标准的干净记录块，将保存完整原始回复。可能包含额外数据。', '历史记录警告');
          console.warn('[AdvLogV2 History] Could not extract clean block, saving raw response. Raw:', aiRawResponseWithWrappers);
        }

        try {
          // Ensure entryContent is properly formatted for the slash command.
          // If entryContent contains characters that break the slash command syntax,
          // it might need to be quoted or escaped, but /createentry usually takes raw content.
          // For simplicity, assuming direct insertion works. If not, content might need to be passed differently.
          const createCommand = `/createentry file="${historyLorebookName}" key="${entryKey}" ${entryContent}`;
          const uidResult = await triggerSlash(createCommand);
          if (uidResult && uidResult.trim() !== '') {
            safeToastr('info', `历史场景已保存到 ${historyLorebookName} (Key: ${entryKey}, UID: ${uidResult})`, '历史记录');
            // Activate the entry (set to constant/blue light)
            const activateCommand = `/setentryfield file="${historyLorebookName}" uid="${uidResult.trim()}" field=constant true`;
            await triggerSlash(activateCommand);
            safeToastr('success', `历史条目 ${entryKey} 已激活 (蓝灯).`, '历史记录');
          } else {
            safeToastr('warning', `保存历史场景 ${entryKey} 到 ${historyLorebookName} 未返回有效UID。`, '历史记录警告');
          }
        } catch (loreError) {
          safeToastr('error', `保存或激活历史场景 ${entryKey} 到 ${historyLorebookName} 失败: ${(loreError as Error).message}`, '历史记录错误');
          console.error(`Error saving/activating history entry ${entryKey}:`, loreError);
        }

      } else {
        // This block now handles both extraction failure (coreJSONString is null) 
        // or parsing failure (parsedScene is null after attempting parseAIResponse)
        safeToastr('error', 'JSON提取或解析失败，请查看主区域显示的AI原始回复。', '处理错误');
        if (narrativeArea) {
          narrativeArea.innerHTML += `<p style="color: lightcoral; font-family: monospace; white-space: pre-wrap; border: 1px solid orange; padding: 10px; background-color: #333;"><strong>[调试] AI 原始回复 (提取或解析失败):</strong>\n${aiRawResponseWithWrappers.replace(/</g, '<').replace(/>/g, '>')}</p>`;
        }
        console.error('Failed to extract or parse JSON. Displaying Raw AI Response. Raw AI Response:', aiRawResponseWithWrappers);
        if (!coreJSONString && newMarkerPatternMatch && !newMarkerPatternMatch[1]) {
             console.error('Reason: Markers found, but no content between them or other regex issue.');
        } else if (!coreJSONString) {
          console.error('Reason: Core JSON string could not be extracted (markers not found).');
        } else {
          // coreJSONString was extracted, but parseAIResponse returned null
          console.error('Reason: JSON.parse failed for the extracted string, or extracted string was empty/invalid before parsing. Extracted string was:', coreJSONString);
        }
      }
    } else {
      if (narrativeArea) narrativeArea.innerHTML += "<p style='color:orange;'>AI未返回有效数据。</p>";
      safeToastr('warning', 'AI未返回任何数据。', '无响应');
    }
  } catch (e) {
    const narrativeAreaForError = mainNarrativeArea; // Re-capture for catch block
    const waitingMsgOnError = narrativeAreaForError?.querySelector('p > i');
    if (waitingMsgOnError?.parentElement && narrativeAreaForError) {
      narrativeAreaForError.removeChild(waitingMsgOnError.parentElement);
    }
    if (narrativeAreaForError) {
      narrativeAreaForError.innerHTML += `<p style='color:red;'>与AI交互时发生错误: ${(e as Error).message}</p>`;
    }
    safeToastr('error', `与AI交互时发生错误: ${(e as Error).message}`, '交互错误');
  } finally {
    if (actionChoicesArea)
      actionChoicesArea.querySelectorAll('button').forEach(btn => ((btn as HTMLButtonElement).disabled = false));
  }
}

function parseAIResponse(jsonString: string): AdventureSceneJSON | null {
  try {
    const parsedData = JSON.parse(jsonString);
    // Basic validation (can be expanded based on the full AdventureSceneJSON schema)
    if (
      parsedData &&
      parsedData.sceneType &&
      parsedData.sceneTitle &&
      parsedData.narrative &&
      parsedData.playerChoices
    ) {
      // Further deep validation can be added here if needed
      return parsedData as AdventureSceneJSON;
    }
    safeToastr('error', '解析后的JSON数据缺少必要字段。', 'JSON解析错误');
    console.error('Invalid JSON structure:', parsedData);
    return null;
  } catch (e) {
    safeToastr('error', `AI响应JSON解析失败: ${(e as Error).message}`, 'JSON解析错误');
    console.error('Failed to parse JSON string:', jsonString, e);
    return null;
  }
}

function applySceneData(scene: AdventureSceneJSON | null) {
  if (!scene) {
    if (mainNarrativeArea) mainNarrativeArea.innerHTML = '<p>错误：无法加载场景数据。</p>';
    if (actionChoicesArea) actionChoicesArea.innerHTML = '';
    return;
  }

  currentSceneData = scene; // Store the parsed JSON object

  // Update playerState based on top-level fields in AdventureSceneJSONsrc/adventure_log_v2
  playerState.currentLocation = scene.currentLocation;
  playerState.time = scene.time;

  if (scene.variableUpdates && scene.variableUpdates.length > 0) {
    scene.variableUpdates.forEach(update => {
      applyVariableUpdate(playerState, update.path, update.operation, update.value);
    });
  }

  renderNarrative(scene); // renderNarrative now takes AdventureSceneJSON
  updatePlayerStatusDisplay();
  renderActionChoices(scene.playerChoices); // renderActionChoices now takes PlayerChoiceJSON[]
}

// --- 变量更新核心函数 (applyVariableUpdate - 保持不变, 但确保 value for items is object) ---
// ... (applyVariableUpdate function remains largely the same, ensure it handles item objects correctly) ...
function applyVariableUpdate(targetObject: any, path: string, operation: string, value: any): void {
  const pathParts = path.split('.');
  let currentTarget = targetObject;
  for (let i = 0; i < pathParts.length - 1; i++) {
    if (currentTarget[pathParts[i]] === undefined || typeof currentTarget[pathParts[i]] !== 'object') {
      return;
    }
    currentTarget = currentTarget[pathParts[i]];
  }
  const finalPropertyKey = pathParts[pathParts.length - 1];

  switch (operation) {
    case '设置':
      if (typeof currentTarget === 'object' && currentTarget !== null) currentTarget[finalPropertyKey] = value;
      break;
    case '增加':
      if (
        typeof currentTarget === 'object' &&
        currentTarget !== null &&
        typeof currentTarget[finalPropertyKey] === 'number' &&
        typeof value === 'number'
      )
        currentTarget[finalPropertyKey] += value;
      break;
    case '减少':
      if (
        typeof currentTarget === 'object' &&
        currentTarget !== null &&
        typeof currentTarget[finalPropertyKey] === 'number' &&
        typeof value === 'number'
      )
        currentTarget[finalPropertyKey] -= value;
      break;
    case '添加元素':
      if (typeof currentTarget === 'object' && currentTarget !== null && Array.isArray(currentTarget[finalPropertyKey]))
        currentTarget[finalPropertyKey].push(value);
      break;
    case '移除元素':
      if (
        typeof currentTarget === 'object' &&
        currentTarget !== null &&
        Array.isArray(currentTarget[finalPropertyKey])
      ) {
        const index = currentTarget[finalPropertyKey].indexOf(value);
        if (index > -1) currentTarget[finalPropertyKey].splice(index, 1);
      }
      break;
    case '物品获得':
      if (
        path === 'inventory' &&
        Array.isArray(currentTarget.inventory) &&
        typeof value === 'object' &&
        value.name &&
        typeof value.quantity === 'number'
      ) {
        const existingItem = currentTarget.inventory.find((item: InventoryItem) => item.name === value.name);
        if (existingItem) {
          existingItem.quantity += value.quantity;
          if (value.description && !existingItem.description) existingItem.description = value.description;
        } else {
          currentTarget.inventory.push({
            name: value.name,
            quantity: value.quantity,
            description: value.description || '',
          });
        }
      }
      break;
    case '物品失去':
      if (
        path === 'inventory' &&
        Array.isArray(currentTarget.inventory) &&
        typeof value === 'object' &&
        value.name &&
        typeof value.quantity === 'number'
      ) {
        const itemIndex = currentTarget.inventory.findIndex((item: InventoryItem) => item.name === value.name);
        if (itemIndex > -1) {
          currentTarget.inventory[itemIndex].quantity -= value.quantity;
          if (currentTarget.inventory[itemIndex].quantity <= 0) currentTarget.inventory.splice(itemIndex, 1);
        }
      }
      break;
  }
}

// --- 持久化函数 ---
// HISTORY_START_TAG, HISTORY_END_TAG, and HISTORY_ENTRY_SEPARATOR are no longer needed.
// const HISTORY_START_TAG = '<!-- ADVENTURE_LOG_HISTORY_START -->';
// const HISTORY_END_TAG = '<!-- ADVENTURE_LOG_HISTORY_END -->';
// const HISTORY_ENTRY_SEPARATOR = '\n<!-- ENTRY_SEP_JSON -->\n';
const PLAYER_STATE_START_TAG = '<!-- PLAYER_STATE_START -->';
const PLAYER_STATE_END_TAG = '<!-- PLAYER_STATE_END -->';

async function persistGameState() {
  if (currentHostMessageId === null || typeof setChatMessages !== 'function') return;

  let persistedString = `${PLAYER_STATE_START_TAG}\n${JSON.stringify(
    playerState,
    null,
    2,
  )}\n${PLAYER_STATE_END_TAG}\n\n`;

  // History is no longer persisted in the main host message.
  // let historyToPersist = [...fullHistoryLog]; 
  let currentSceneJSONForDisplay: string | undefined = undefined;

  if (currentSceneData) {
    currentSceneJSONForDisplay = JSON.stringify(currentSceneData);
  } 
  // Removed fallback to fullHistoryLog for currentSceneJSONForDisplay as history is separate.
  // else if (fullHistoryLog.length > 0) {
  //   const lastEntry = historyToPersist[historyToPersist.length -1]; 
  //   if (lastEntry) currentSceneJSONForDisplay = lastEntry.sceneJSONString;
  // }

  // Removed history block persistence from main message.
  // if (historyToPersist.length > 0) {
  //   persistedString += `${HISTORY_START_TAG}\n`;
  //   persistedString += historyToPersist
  //     .map(entry => {
  //       let entryStr = entry.sceneJSONString;
  //       return entryStr;
  //     })
  //     .join(HISTORY_ENTRY_SEPARATOR);
  //   persistedString += `\n${HISTORY_END_TAG}\n\n`;
  // }

  if (currentSceneJSONForDisplay) {
    persistedString += `查看系统\n##@@_ADVENTURE_BEGIN_@@##\n${currentSceneJSONForDisplay}\n##@@_ADVENTURE_END_@@##\n关闭系统`;
  }

  try {
    await setChatMessages([{ message_id: currentHostMessageId, message: persistedString }], { refresh: 'affected' });
  } catch (e) {
    // safeToastr('error', `Failed to persist game state: ${(e as Error).message}`, 'Persistence Error');
  }
}

interface LoadGameResult {
  playerStateLoadedFromMsg: boolean;
  sceneDataLoadedFromMsg: boolean;
}

async function loadGameState(): Promise<LoadGameResult> {
  if (typeof getLastMessageId !== 'function' || typeof triggerSlash !== 'function') {
    return { playerStateLoadedFromMsg: false, sceneDataLoadedFromMsg: false };
  }
  const hostIdResult = getLastMessageId();
  currentHostMessageId = hostIdResult === undefined ? null : hostIdResult;
  if (currentHostMessageId === null) return { playerStateLoadedFromMsg: false, sceneDataLoadedFromMsg: false };

  let rawPersistedState: string | undefined;
  try {
    rawPersistedState = await triggerSlash(`/messages ${currentHostMessageId}`);
  } catch (e) {
    return { playerStateLoadedFromMsg: false, sceneDataLoadedFromMsg: false };
  }

  if (!rawPersistedState?.trim()) return { playerStateLoadedFromMsg: false, sceneDataLoadedFromMsg: false };

  let playerStateSuccessfullyParsed = false;
  let sceneDataSuccessfullyParsed = false;
  currentSceneData = null;
  // fullHistoryLog = []; // No longer needed

  // safeToastr('info', '开始加载游戏状态...', '加载流程'); // Commented out
  console.log('[AdvLogV2 Load] Starting loadGameState...');

  const playerStateMatch = rawPersistedState.match(
    new RegExp(`${PLAYER_STATE_START_TAG}\\n([\\s\\S]*?)\\n${PLAYER_STATE_END_TAG}`),
  );
  if (playerStateMatch?.[1]) {
    try {
      const tempState = JSON.parse(playerStateMatch[1]);
      if (tempState?.name && tempState.attributes) {
        playerState = tempState;
        playerStateSuccessfullyParsed = true;
      }
    } catch (e) {
      /* parsing error */
      console.error('[AdvLogV2 Load] Error parsing PlayerState JSON:', e, playerStateMatch[1]);
      safeToastr('error', '解析玩家状态JSON失败。', '加载错误');
    }
  }

  // History block loading from main message is removed.
  // const historyBlockMatch = rawPersistedState.match(
  //   new RegExp(`${HISTORY_START_TAG}\\n([\\s\\S]*?)\\n${HISTORY_END_TAG}`),
  // );
  // if (historyBlockMatch?.[1]) {
  //   const historyContent = historyBlockMatch[1].trim();
  //   if (historyContent) {
  //     const historyEntriesJSONStrings = historyContent.split(
  //       new RegExp(HISTORY_ENTRY_SEPARATOR.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'),
  //     );
  //     historyEntriesJSONStrings.forEach(jsonStr => {
  //       if (jsonStr.trim()) {
  //         // fullHistoryLog.push({ sceneJSONString: jsonStr.trim(), timestamp: 0 }); // No longer needed
  //       }
  //     });
  //   }
  // }

  // Logic to extract current scene using the new unique markers from the main host message
  const plotMatch = rawPersistedState.match(/##@@_ADVENTURE_BEGIN_@@##([\s\S]*?)##@@_ADVENTURE_END_@@##/);
  console.log('[AdvLogV2 Load Debug] Attempting to match ##@@_ADVENTURE_BEGIN_@@##...##@@_ADVENTURE_END_@@##');
  console.log('[AdvLogV2 Load Debug] plotMatch result:', plotMatch ? plotMatch.length : null);

  if (plotMatch && plotMatch[1]) {
    // safeToastr('info', '尝试从 "##@@_ADVENTURE_BEGIN_@@##...##@@_ADVENTURE_END_@@##" 块加载当前场景...', '加载状态'); // Commented out
    console.log('[AdvLogV2 Load] Found "##@@_ADVENTURE_BEGIN_@@##...##@@_ADVENTURE_END_@@##" block, attempting to extract JSON string...');
    const latestSceneJSONString = plotMatch[1].trim();
    console.log('[AdvLogV2 Load Debug] Extracted JSON string from new marker block (first 200 chars):', latestSceneJSONString.substring(0,200)+"...");

    if (latestSceneJSONString) {
      const parsedLatestScene = parseAIResponse(latestSceneJSONString);
      if (parsedLatestScene) {
        currentSceneData = parsedLatestScene;
        sceneDataSuccessfullyParsed = true;
        // safeToastr('success', '当前场景已从 "##@@_ADVENTURE_BEGIN_@@##...##@@_ADVENTURE_END_@@##" 块成功加载并解析。', '加载状态'); // Commented out
        console.log('[AdvLogV2 Load] Successfully parsed and loaded current scene from new marker block.');
      } else {
        safeToastr('error', '解析 "##@@_ADVENTURE_BEGIN_@@##...##@@_ADVENTURE_END_@@##" 块中的JSON失败。查看控制台获取详细原始JSON。', '加载错误');
        console.error('[AdvLogV2 Load] Failed to parse JSON from new marker block. Raw string was:', latestSceneJSONString);
      }
    } else {
      safeToastr('warning', '"##@@_ADVENTURE_BEGIN_@@##...##@@_ADVENTURE_END_@@##" 块中提取的JSON字符串为空。', '加载警告');
      console.warn('[AdvLogV2 Load] Extracted JSON string from new marker block is empty.');
    }
  } else {
    console.warn('[AdvLogV2 Load] Could not find or extract content from "##@@_ADVENTURE_BEGIN_@@##...##@@_ADVENTURE_END_@@##" block in the main host message.');
  }

  // Fallback to last history entry is removed as history is no longer in the main message.
  // if (!sceneDataSuccessfullyParsed && fullHistoryLog.length > 0) { ... }
  
  if (!sceneDataSuccessfullyParsed) {
    // safeToastr('warning', '无法从当前宿主消息加载最新场景数据。', '加载状态'); // Commented out
    // console.warn('[AdvLogV2 Load] No latest scene data could be loaded from the main host message.');
  }

  return {
    playerStateLoadedFromMsg: playerStateSuccessfullyParsed,
    sceneDataLoadedFromMsg: sceneDataSuccessfullyParsed,
  };
}

function generateInitialAdventureSceneJSON(ps: PlayerState): AdventureSceneJSON {
  return {
    sceneType: 'location',
    sceneTitle: '冒险的序章',
    currentLocation: ps.currentLocation || '未知起点',
    time: ps.time || '某个时刻',
    narrative: [
      {
        type: 'description',
        content:
          '你已准备好踏上征程！这个世界的故事将根据你所激活的冒险模组（通常位于 RPG_Modules_Test.json 世界书中）展开。请选择你的第一个行动，让传奇开始！',
      },
    ],
    playerChoices: [
      { id: 'A', text: '根据我的模组设定，正式开始冒险！', actionCommand: 'start_adventure_module' },
      { id: 'B', text: '我应该先了解一下我所处的环境（基于模组）。', actionCommand: 'survey_environment_module' },
      { id: 'C', text: '查看我的角色状态。', actionCommand: 'check_character_status' },
    ],
  };
}

async function handleStartNewGameClick() {
  if (!startNewGameButton || !adventureLogContainer || !startScreenContainer) return;
  startNewGameButton.disabled = true;
  startNewGameButton.textContent = '正在加载角色...';

  const characterDataJson = await loadCharacterDataFromLorebook('PLAYER');
  if (characterDataJson) {
    try {
      const loadedPlayerState: PlayerState = JSON.parse(characterDataJson);
      if (loadedPlayerState?.name && loadedPlayerState.attributes) {
        playerState = loadedPlayerState;
        currentSceneData = null;
        // fullHistoryLog = []; // No longer needed
        const initialSceneJSON = generateInitialAdventureSceneJSON(playerState);
        applySceneData(initialSceneJSON);
        // Persist the initial scene to the main host message (not to fullHistoryLog)
        // And also save this initial scene to the history lorebook
        if (currentHostMessageId !== null) { // Ensure host ID is available for initial persist
            await persistGameState(); // This will save playerState and currentSceneData (initialScene)
            
            const historyLorebookName = 'dndRPG_history.json';
            const entryKey = `scene_init_${Date.now()}`;
            // Construct the full AI-like response block for the initial scene
            const initialSceneFullResponse = `查看系统\n##@@_ADVENTURE_BEGIN_@@##\n${JSON.stringify(initialSceneJSON)}\n##@@_ADVENTURE_END_@@##\n关闭系统`;
            try {
                const createCommand = `/createentry file="${historyLorebookName}" key="${entryKey}" ${initialSceneFullResponse}`;
                const uidResult = await triggerSlash(createCommand);
                if (uidResult && uidResult.trim() !== '') {
                    const activateCommand = `/setentryfield file="${historyLorebookName}" uid="${uidResult.trim()}" field=constant true`;
                    await triggerSlash(activateCommand);
                    // safeToastr('info', `初始场景已作为历史记录保存并激活 (Key: ${entryKey})`, '新游戏'); // Commented out
                }
            } catch (loreError) {
                 safeToastr('error', `保存初始历史场景失败: ${(loreError as Error).message}`, '新游戏错误');
            }
        }
        updatePlayerStatusDisplay();
        startScreenContainer.style.display = 'none';
        adventureLogContainer.style.display = 'flex';
        if (currentHostMessageId !== null) await persistGameState();
      } else {
        playerState = getDefaultPlayerState(); // Fallback
        startNewGameButton.disabled = false;
        startNewGameButton.textContent = '开始新冒险 (从世界书加载角色)';
      }
    } catch (e) {
      playerState = getDefaultPlayerState(); // Fallback
      startNewGameButton.disabled = false;
      startNewGameButton.textContent = '开始新冒险 (从世界书加载角色)';
    }
  } else {
    playerState = getDefaultPlayerState(); // Fallback
    // If no char data, still generate an initial scene with default player state
    currentSceneData = null;
    // fullHistoryLog = []; // No longer needed
    const initialSceneJSON = generateInitialAdventureSceneJSON(playerState);
    applySceneData(initialSceneJSON);
    updatePlayerStatusDisplay();
    startScreenContainer.style.display = 'none';
    adventureLogContainer.style.display = 'flex';
    if (currentHostMessageId !== null) {
        await persistGameState(); // Persist default player state and initial scene
        // Also save this initial scene to the history lorebook
        const historyLorebookName = 'dndRPG_history.json';
        const entryKey = `scene_init_default_${Date.now()}`;
        const initialSceneFullResponse = `查看系统\n##@@_ADVENTURE_BEGIN_@@##\n${JSON.stringify(initialSceneJSON)}\n##@@_ADVENTURE_END_@@##\n关闭系统`;
        try {
            const createCommand = `/createentry file="${historyLorebookName}" key="${entryKey}" ${initialSceneFullResponse}`;
            const uidResult = await triggerSlash(createCommand);
            if (uidResult && uidResult.trim() !== '') {
                const activateCommand = `/setentryfield file="${historyLorebookName}" uid="${uidResult.trim()}" field=constant true`;
                await triggerSlash(activateCommand);
                // safeToastr('info', `默认初始场景已作为历史记录保存并激活 (Key: ${entryKey})`, '新游戏'); // Commented out
            }
        } catch (loreError) {
            safeToastr('error', `保存默认初始历史场景失败: ${(loreError as Error).message}`, '新游戏错误');
        }
    } else {
      startNewGameButton.disabled = false; // Re-enable if persistence fails due to no host ID
      startNewGameButton.textContent = '开始新冒险 (从世界书加载角色)';
    }
  }
}

// --- 初始化函数 ---
async function onMounted() {
  setTimeout(async () => {
    ensureGlobals();
    startScreenContainer = document.getElementById('start-screen-container');
    startNewGameButton = document.getElementById('start-new-game-button') as HTMLButtonElement;
    adventureLogContainer = document.getElementById('adventure-log-container');
    playerStatusArea = document.getElementById('player-status-area');
    mainNarrativeArea = document.getElementById('main-narrative-area');
    actionChoicesArea = document.getElementById('action-choices-area');
    healthDisplay = document.getElementById('health');
    locationDisplay = document.getElementById('location');
    timeDisplay = document.getElementById('time');
    charNameDisplay = document.getElementById('char-name-display');
    charRaceClassDisplay = document.getElementById('char-race-class-display');
    charLevelDisplay = document.getElementById('char-level-display');
    acDisplay = document.getElementById('ac-display');
    attrStrDisplay = document.getElementById('attr-str-display');
    attrDexDisplay = document.getElementById('attr-dex-display');
    attrConDisplay = document.getElementById('attr-con-display');
    attrIntDisplay = document.getElementById('attr-int-display');
    attrWisDisplay = document.getElementById('attr-wis-display');
    attrChaDisplay = document.getElementById('attr-cha-display');
    currencyGoldDisplay = document.getElementById('currency-gold-display');
    expDisplay = document.getElementById('exp-display');
    exhaustionDisplay = document.getElementById('exhaustion-display');
    toggleCharSheetButton = document.getElementById('toggle-char-sheet-button') as HTMLButtonElement;
    detailedCharacterSheet = document.getElementById('detailed-character-sheet');
    proficienciesDisplay = document.getElementById('proficiencies-display');
    skillsDisplay = document.getElementById('skills-display');
    spellSlotsDisplay = document.getElementById('spell-slots-display');
    equippedSpellsDisplay = document.getElementById('equipped-spells-display');
    equipmentDisplay = document.getElementById('equipment-display');
    inventoryDisplay = document.getElementById('inventory-display');
    activeQuestsDisplay = document.getElementById('active-quests-display');

    if (
      !startScreenContainer ||
      !startNewGameButton ||
      !adventureLogContainer ||
      !playerStatusArea ||
      !mainNarrativeArea ||
      !actionChoicesArea ||
      !toggleCharSheetButton ||
      !detailedCharacterSheet
    ) {
      return;
    }
    toggleCharSheetButton.addEventListener('click', () => {
      if (detailedCharacterSheet && toggleCharSheetButton) {
        const isHidden = detailedCharacterSheet.style.display === 'none';
        detailedCharacterSheet.style.display = isHidden ? 'block' : 'none';
        toggleCharSheetButton.textContent = isHidden ? '隐藏详细角色卡' : '显示详细角色卡';
      }
    });
    startNewGameButton.addEventListener('click', handleStartNewGameClick);

    const loadResult = await loadGameState();
    if (loadResult.playerStateLoadedFromMsg && loadResult.sceneDataLoadedFromMsg && currentSceneData) {
      applySceneData(currentSceneData);
      updatePlayerStatusDisplay();
      startScreenContainer.style.display = 'none';
      adventureLogContainer.style.display = 'flex';
    } else {
      startScreenContainer.style.display = 'flex';
      adventureLogContainer.style.display = 'none';
      updatePlayerStatusDisplay();
    }
  }, 200);
}

// --- 脚本入口 ---
function calculateAttributeModifier(score: number): number {
  return Math.floor((score - 10) / 2);
}

async function loadCharacterDataFromLorebook(characterName: string): Promise<string | null> {
  if (typeof triggerSlash !== 'function') return null;
  const lorebookFileName = 'RPG_Modules_Test.json';
  try {
    const findEntryCommand = `/findentry file="${lorebookFileName}" "${characterName}"`;
    const uidResult = await triggerSlash(findEntryCommand);
    if (!uidResult?.trim() || uidResult.trim() === '[]') return null;

    let entryUid: string | null = null;
    try {
      const parsedUidResult = JSON.parse(uidResult);
      if (Array.isArray(parsedUidResult) && parsedUidResult.length > 0) entryUid = parsedUidResult[0].toString();
      else if (typeof parsedUidResult === 'string' && parsedUidResult.trim() !== '') entryUid = parsedUidResult.trim();
      else if (typeof parsedUidResult === 'number') entryUid = parsedUidResult.toString();
    } catch (e) {
      if (typeof uidResult === 'string' && uidResult.trim() !== '') entryUid = uidResult.trim();
    }
    if (!entryUid) return null;

    const getContentCommand = `/getentryfield file="${lorebookFileName}" field=content "${entryUid}"`;
    const content = await triggerSlash(getContentCommand);
    return content?.trim() ? content : null;
  } catch (error) {
    return null;
  }
}

if (document.readyState === 'complete' || document.readyState === 'interactive') {
  onMounted();
} else {
  window.addEventListener('DOMContentLoaded', onMounted);
}
