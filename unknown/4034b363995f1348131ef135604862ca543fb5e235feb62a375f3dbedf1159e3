/*! For license information please see index.js.LICENSE.txt */
var __webpack_modules__={"./src/脚本/index.ts":()=>{eval("\n// This script (src/脚本/index.ts) is not currently configured\n// to be the main script for src/界面/index.html.\n// The logic for src/界面/index.html is expected to be in src/界面/index.ts\n// as per the likely default config.yaml entry:\n//   - script: src/界面/index.ts\n//     html: src/界面/index.html\n//\n// If you intend for this script (src/脚本/index.ts) to control src/界面/index.html,\n// you MUST update config.yaml accordingly.\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMv6ISa5pysL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7QUFBQSw0REFBNEQ7QUFDNUQsK0NBQStDO0FBQy9DLHVFQUF1RTtBQUN2RSwrQ0FBK0M7QUFDL0MsOEJBQThCO0FBQzlCLDhCQUE4QjtBQUM5QixFQUFFO0FBQ0YsZ0ZBQWdGO0FBQ2hGLDJDQUEyQyIsInNvdXJjZXMiOlsid2VicGFjazovL3RhdmVybl9oZWxwZXJfdGVtcGxhdGUvLi9zcmMv6ISa5pysL2luZGV4LnRzPyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBUaGlzIHNjcmlwdCAoc3JjL+iEmuacrC9pbmRleC50cykgaXMgbm90IGN1cnJlbnRseSBjb25maWd1cmVkXHJcbi8vIHRvIGJlIHRoZSBtYWluIHNjcmlwdCBmb3Igc3JjL+eVjOmdoi9pbmRleC5odG1sLlxyXG4vLyBUaGUgbG9naWMgZm9yIHNyYy/nlYzpnaIvaW5kZXguaHRtbCBpcyBleHBlY3RlZCB0byBiZSBpbiBzcmMv55WM6Z2iL2luZGV4LnRzXHJcbi8vIGFzIHBlciB0aGUgbGlrZWx5IGRlZmF1bHQgY29uZmlnLnlhbWwgZW50cnk6XHJcbi8vICAgLSBzY3JpcHQ6IHNyYy/nlYzpnaIvaW5kZXgudHNcclxuLy8gICAgIGh0bWw6IHNyYy/nlYzpnaIvaW5kZXguaHRtbFxyXG4vL1xyXG4vLyBJZiB5b3UgaW50ZW5kIGZvciB0aGlzIHNjcmlwdCAoc3JjL+iEmuacrC9pbmRleC50cykgdG8gY29udHJvbCBzcmMv55WM6Z2iL2luZGV4Lmh0bWwsXHJcbi8vIHlvdSBNVVNUIHVwZGF0ZSBjb25maWcueWFtbCBhY2NvcmRpbmdseS5cclxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/脚本/index.ts\n")}},__webpack_exports__={};__webpack_modules__["./src/脚本/index.ts"]();