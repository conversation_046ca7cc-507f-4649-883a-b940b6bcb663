import { safeToastr, saveContentToLorebook } from './utils';
import type { ModuleCreationData, AIResponseFormat } from './types';

// SillyTavern / TavernHelper injected function type declarations
declare function triggerSlash(command: string): Promise<string | undefined>;

export async function handleSaveCustomModule() {
  const titleInput = document.getElementById('module-title-input') as HTMLInputElement;
  const contentTextarea = document.getElementById('module-content-textarea') as HTMLTextAreaElement;
  const outputMessageDiv = document.getElementById('output-message') as HTMLElement;

  if (!titleInput || !contentTextarea || !outputMessageDiv) {
    safeToastr('error', '界面元素未完全找到 (save custom)!', '界面错误');
    return;
  }
  const moduleTitle = titleInput.value.trim();
  const moduleContent = contentTextarea.value.trim();

  if (!moduleContent) {
    safeToastr('warning', '模组内容不能为空!', '输入错误');
    outputMessageDiv.textContent = '错误: 模组内容不能为空。';
    contentTextarea.focus();
    return;
  }

  await saveContentToLorebook(moduleTitle, moduleContent, outputMessageDiv);
}

export async function handleAiGenerateContent() {
  const titleInput = document.getElementById('module-title-input') as HTMLInputElement;
  const contentTextarea = document.getElementById('module-content-textarea') as HTMLTextAreaElement;
  const outputMessageDiv = document.getElementById('output-message') as HTMLElement;

  if (!titleInput || !contentTextarea || !outputMessageDiv) {
    safeToastr('error', '界面元素未完全找到 (ai generate module)!', '界面错误');
    return;
  }

  const moduleTitleForKey = titleInput.value.trim();
  const userPromptForAi = contentTextarea.value.trim();

  if (!moduleTitleForKey) {
    safeToastr('warning', '请输入模组标题 (作为Key)!', '输入错误');
    outputMessageDiv.textContent = '错误: 模组标题 (Key) 不能为空。';
    titleInput.focus();
    return;
  }
  if (!userPromptForAi) {
    safeToastr('warning', '请输入给AI的创作提示!', '输入错误');
    outputMessageDiv.textContent = '错误: AI创作提示不能为空。';
    contentTextarea.focus();
    return;
  }

  outputMessageDiv.textContent = '正在向AI发送请求以生成模组内容 (JSON格式)...';
  safeToastr('info', '向AI发送模组创作请求 (JSON)...', 'AI交互');

  const aiPrompt = `请根据以下用户提示为D&D 5e游戏创作一个模组设定。用户提示： "${userPromptForAi}". 请严格按照 "module_setup_ai_prompts_v2_json.md" 中针对 "moduleCreation" 更新后的 "NarrativeModuleData" JSON Schema返回结果，核心内容应在 "narrativeContent" 字段中以人类易读的叙述形式提供。确保整个JSON响应被 "开始制作\\n##@@_MODULE_CONTENT_BEGIN_@@##" 和 "##@@_MODULE_CONTENT_END_@@##\\n结束制作" 包裹。`;

  try {
    if (typeof triggerSlash !== 'function') {
      safeToastr('error', 'triggerSlash API 不可用!', 'API 错误');
      outputMessageDiv.textContent = '错误: triggerSlash API 不可用。';
      return;
    }

    const aiRawResponseWithWrappers = await triggerSlash(`/gen ${aiPrompt}`);

    if (aiRawResponseWithWrappers?.trim()) {
      safeToastr('success', 'AI已返回内容!', 'AI交互完成');
      let coreJSONString: string | null = null;
      const newMarkerPatternMatch = aiRawResponseWithWrappers.match(
        /##@@_MODULE_CONTENT_BEGIN_@@##([\s\S]*?)##@@_MODULE_CONTENT_END_@@##/,
      );
      if (newMarkerPatternMatch && newMarkerPatternMatch[1]) {
        coreJSONString = newMarkerPatternMatch[1].trim();
      } else {
        console.warn(
          'New markers ##@@_MODULE_CONTENT_BEGIN_@@##...##@@_MODULE_CONTENT_END_@@## not found in AI response for module creation.',
        );
      }

      if (coreJSONString) {
        try {
          const parsedResponse = JSON.parse(coreJSONString) as AIResponseFormat<ModuleCreationData>;
          if (
            parsedResponse.requestType === 'moduleCreation' &&
            parsedResponse.data &&
            parsedResponse.data.narrativeContent
          ) {
            const moduleData = parsedResponse.data;
            const contentToSave = moduleData.narrativeContent;
            contentTextarea.value = contentToSave;
            outputMessageDiv.textContent = `AI模组叙述内容已生成并填充到文本框。标题: ${
              moduleData.title || moduleTitleForKey
            }。请审阅后手动保存，或直接保存。`;
            await saveContentToLorebook(moduleData.title || moduleTitleForKey, contentToSave, outputMessageDiv);
          } else {
            throw new Error('AI返回的JSON格式不符合预期的叙述性模组创作类型，或缺少 narrativeContent。');
          }
        } catch (e) {
          safeToastr(
            'error',
            `AI返回的JSON解析失败: ${(e as Error).message}. 将尝试保存原始提取内容。`,
            'JSON解析错误',
          );
          outputMessageDiv.textContent = `AI返回的JSON解析失败。原始提取内容 (可能需要手动清理):\n${coreJSONString.substring(
            0,
            300,
          )}...`;
          await saveContentToLorebook(moduleTitleForKey, coreJSONString, outputMessageDiv);
        }
      } else {
        safeToastr('warning', '未能从AI回复中提取有效的JSON内容 (新标记)。', '内容提取失败');
        outputMessageDiv.textContent =
          '未能从AI回复中提取有效的JSON内容。原始回复：\n' + aiRawResponseWithWrappers.substring(0, 300) + '...';
      }
    } else {
      safeToastr('warning', 'AI未能生成有效内容或返回为空。', 'AI交互失败');
      outputMessageDiv.textContent = 'AI未能生成有效内容或返回为空。';
    }
  } catch (error) {
    const errorMsg = `AI模组内容生成或保存过程中发生错误: ${(error as Error).message}`;
    safeToastr('error', errorMsg, 'AI或保存失败');
    outputMessageDiv.textContent = errorMsg;
  }
}
