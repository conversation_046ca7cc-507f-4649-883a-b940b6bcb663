// Adventure Log - SCSS Styles

// Global styles for the adventure log iframe
body {
    margin: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; // A common sans-serif font
    background-color: #2c3e50; // A darker, thematic background
    color: #ecf0f1; // Light text color for contrast
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh; // Ensure centering even if content is short
    box-sizing: border-box;
}

#adventure-log-container {
    width: 95vw; 
    max-width: 450px; // Make it narrower, more phone-like for desktop too
    aspect-ratio: 9 / 17; // Tall aspect ratio, similar to a phone screen, adjust if needed
    background-color: #34495e; 
    border: 2px solid #7f8c8d;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    overflow: hidden; 
    padding: 15px;
    box-sizing: border-box;
    // Ensure the container itself can be centered if body is flex
    margin: auto; // Add this to help center if body is truly 100vh flex container
}

#player-status-area {
    padding-bottom: 10px;
    // flex-shrink: 0; // Prevent status area from shrinking
    border-bottom: 1px solid #7f8c8d;
    margin-bottom: 10px;
    display: flex;
    flex-direction: column; // Stack rows vertically by default
    gap: 8px; // Space between status rows
    font-size: 0.85em; // Slightly smaller base font for status

    .status-row { // This now primarily applies to the main, simplified status bar
        display: flex;
        flex-wrap: wrap; // Allow items within a row to wrap
        gap: 10px; // Space between items in a row
        align-items: center;
        justify-content: space-around; // Distribute items like HP, AC, Time, Location
    }

    // Styling for items directly in the main status bar
    #health, #ac-display, #time, #location {
        padding: 4px 7px;
        background-color: #2c3e50;
        border-radius: 3px;
        white-space: nowrap; 
    }
}

#toggle-char-sheet-button {
    padding: 8px 12px;
    background-color: #4a5568; // A neutral, less prominent button
    color: #ecf0f1;
    border: 1px solid #7f8c8d;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
    margin-top: 10px;
    margin-bottom: 10px;
    text-align: center;
    width: 100%; // Make it full width for easier tapping

    &:hover {
        background-color: #2d3748;
    }
}

#detailed-character-sheet {
    background-color: rgba(44, 62, 80, 0.8); // Slightly darker than container for contrast
    padding: 10px;
    border-radius: 6px;
    margin-top: 5px;
    border: 1px solid #7f8c8d;
    max-height: 35vh; // Adjusted max-height for character sheet
    overflow-y: auto;

    h4 {
        margin-top: 12px; 
        margin-bottom: 8px; // Increased bottom margin
        color: #95a5a6; // Lighter heading color
        font-size: 1em;   // Slightly larger for section titles
        border-bottom: 1px solid #7f8c8d;
        padding-bottom: 4px; // Increased padding
    }
    h4:first-child {
        margin-top: 0;
    }

    // Styles for .status-row elements now inside #detailed-character-sheet
    .status-row {
        display: flex;
        flex-wrap: wrap;
        gap: 8px 12px; // Consistent gap for items
        align-items: center;
        margin-bottom: 8px; // Space below each status row in the sheet

        // Individual items within the detailed sheet's status rows
        div, span {
            padding: 3px 6px;
            background-color: #2c3e50; // Consistent background
            border-radius: 3px;
            white-space: nowrap;
            font-size: 0.9em; // Font size for items in detailed sheet
        }
         // Specifically target attribute display items if needed for finer control
        &.attributes-display div {
            // font-size: 0.85em; // Example: slightly smaller for attributes
        }
        &.simple-list-display span span { // Target the actual value span
             // font-weight: normal; // Example
        }
    }


    ul {
        list-style-type: none;
        padding-left: 5px;
        margin: 5px 0 10px 0; // Add more bottom margin to lists
        font-size: 0.85em;
        li {
            padding: 3px 0; // Slightly more padding for list items
        }
    }
    #spell-slots-display {
        font-size: 0.85em;
        padding-left: 5px;
        margin-bottom: 10px; // Add bottom margin
    }
}


#main-narrative-area {
    flex-grow: 1; // Takes up available vertical space
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.2); 
    border-radius: 6px;
    overflow-y: auto; 
    line-height: 1.6;
    font-size: 1em;
    margin-bottom: 15px;
    min-height: 100px; // Ensure narrative area has some minimum height

    p {
        margin-top: 0;
        margin-bottom: 0.8em;
        &:last-child {
            margin-bottom: 0;
        }
    }
}

#action-choices-area {
    display: flex;
    flex-direction: column;
    gap: 8px; 
    // flex-shrink: 0; // Prevent choices area from shrinking

    button {
        padding: 12px 15px;
        background-color: #3498db; 
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 0.95em;
        text-align: left;
        transition: background-color 0.2s ease-in-out;

        &:hover {
            background-color: #2980b9;
        }

        &:active {
            background-color: #1f638f;
        }
    }
}

// Styles for the Start Screen
#start-screen-container {
    display: flex; // Initially visible, will be hidden by JS if a game is in progress
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 20px;
    box-sizing: border-box;
    width: 100%;
    height: 100%; // Take full container height if adventure-log-container is hidden
    overflow-y: auto; // Allow scrolling for start screen content

    h1 {
        font-size: 1.8em; // Slightly reduced for smaller screens
        color: #ecf0f1;
        margin-bottom: 15px;
    }

    p {
        font-size: 1em; // Slightly reduced
        color: #bdc3c7;
        margin-bottom: 20px; // Reduced margin
        max-width: 90%; // Allow a bit wider text
    }

    #start-new-game-button {
        padding: 12px 25px; // Reduced padding
        font-size: 1.1em; // Slightly reduced
        background-color: #27ae60; // A welcoming green
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
            background-color: #229954;
        }
    }

    .start-screen-note {
        font-size: 0.75em; // Slightly reduced
        color: #95a5a6;
        margin-top: 15px; // Reduced margin
        max-width: 85%; // Allow a bit wider text
        line-height: 1.4;
    }
}

// Optional: Styles for Inventory/Skills Area (if implemented)
// #inventory-area { ... } // Current HTML has this commented out

// Responsive adjustments for smaller screens (e.g., mobile phones)
@media (max-width: 600px) {
    #adventure-log-container {
        width: 100vw;
        height: 100vh; // Take full viewport height on mobile
        max-width: 100vw;
        border-radius: 0;
        border: none;
        padding: 10px;
        aspect-ratio: unset; // Remove fixed aspect ratio on mobile
    overflow-y: auto; // Allow the main game container to scroll on mobile
    }

    #player-status-area { // Main status bar on mobile
        font-size: 0.8em; // Further reduce font size for status on small screens
        gap: 5px; // Reduce gap between rows

        .status-row { // Main status bar on mobile
            gap: 5px; // Reduce gap between items in a row
            justify-content: space-around; // Ensure items are spaced out
            // Items like #health, #ac-display will wrap if needed
        }
    }
    
    #detailed-character-sheet {
        .status-row { // Status rows within the character sheet on mobile
            flex-direction: column; // Stack items vertically within each row
            align-items: flex-start; // Align items to the start
            div, span {
                width: 100%; // Make each item take full width
                margin-bottom: 3px; // Small space between stacked items
                text-align: left;
            }
            &.attributes-display div, &.simple-list-display span {
                 // Already full width due to above, specific adjustments if needed
            }
        }
    }


    #main-narrative-area {
        font-size: 0.95em;
        padding: 8px;
    }

    #action-choices-area button {
        padding: 10px 12px;
        font-size: 0.9em;
    }

    #toggle-char-sheet-button {
        font-size: 0.85em;
    }

    #detailed-character-sheet {
        font-size: 0.8em;
        max-height: 35vh; // Adjust max height for smaller screens
        ul, #spell-slots-display {
            font-size: 1em; // Reset to parent's font-size (0.8em of body)
        }
    }
}
