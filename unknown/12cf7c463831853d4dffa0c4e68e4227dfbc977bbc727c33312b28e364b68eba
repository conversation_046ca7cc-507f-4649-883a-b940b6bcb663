# 动态生成式“跑团”/冒险日志 (Adventure Log)

## 1. 项目简介与目标

本项目旨在为 SillyTavern 开发一个动态生成式的文字冒险（跑团）界面。玩家将扮演一名冒险者，在一个由AI实时描述的奇幻或自定义世界中进行探索、互动并经历独特的故事。AI将扮演地下城主（DM）或世界引擎的角色，根据玩家的选择和预设的“世界书”规则，动态生成场景描述、事件、NPC对话以及玩家可进行的行动选项。

**核心目标**:

*   提供一个沉浸式的、由AI驱动的单人文字冒险体验。
*   界面能够清晰地展示环境描述、玩家状态、NPC对话和行动选项。
*   游戏流程和内容完全由AI返回的特定格式数据驱动。
*   借鉴现有 `galgame` 框架的成功经验，特别是在数据持久化、与SillyTavern集成以及响应式布局方面。
*   实现模块化的TypeScript代码结构，便于维护和扩展。

## 2. 核心玩法

1.  **角色创建/导入**: （初期可简化）玩家可以有一个简单的角色概念，其核心属性（如HP、MP、关键物品）由AI在游戏开始时设定或在过程中动态赋予。
2.  **场景探索**: AI描述玩家当前所处的环境、遇到的事物或NPC。
3.  **玩家决策**: 界面上会根据AI的描述显示若干行动选项，玩家选择其中一项。
4.  **AI响应**: AI根据玩家的选择，结合世界规则和当前状态，生成新的场景描述、事件结果、NPC回应，并提供新的行动选项。
5.  **状态管理**: 玩家的关键状态（如生命值、物品、任务线索）会由AI的输出动态更新，并在UI上显示。
6.  **日志记录**: 游戏的全过程（AI的描述和玩家的选择）将被记录下来，形成一个冒险日志。

## 3. UI 界面布局设想

界面将主要包含以下几个区域：

*   **主叙事区 (Main Narrative Area)**:
    *   占据屏幕大部分空间，用于显示AI生成的当前场景描述、事件详情、NPC的对话等。
    *   文本应清晰易读，支持滚动查看较长的描述。
*   **玩家状态区 (Player Status Area)**:
    *   通常位于界面顶部或侧边，固定显示玩家的关键信息。
    *   例如：`生命值: 80/100`, `魔法值: 50/50`, `当前地点: 幽暗森林`, `持有金币: 120`。
*   **行动选项区 (Action Choices Area)**:
    *   通常位于界面底部或主叙事区下方。
    *   以按钮列表的形式展示当前玩家可以进行的行动或对话选项。
*   **物品栏/技能栏 (Inventory/Skills Area - 可选，后期扩展)**:
    *   初期可以简化，物品信息直接在状态区或叙事中提及。
    *   后期可扩展为可交互的物品栏或技能列表。

**布局将遵循移动优先原则，使用宽度和 `aspect-ratio` 控制整体容器的高度，避免使用 `vh`。**

## 4. AI 数据格式约定

我们将借鉴 `galgame` 的数据格式，并针对冒险日志题材进行调整。AI返回的完整响应仍由 `查看系统\nmsg_start\n...\nmsg_end\n关闭系统` 包裹。

核心数据块标签可能包括：

*   **场景描述**: `<场景:地点名称或情景概述> ... </场景:地点名称或情景概述>`
    *   内部可以包含多行描述文本。
*   **NPC对话**: `<NPC:角色名称> ... </NPC:角色名称>`
    *   内部为NPC的对话内容。
*   **系统消息/提示**: `<系统:消息类型> ... </系统:消息类型>`
    *   例如 `<系统:战斗提示>`, `<系统:获得物品>`, `<系统:任务更新>`。
*   **玩家状态更新**: 每一行具体数据仍以 `--HH:MM` 作为行尾标记（此标记主要用于确保AI按行输出，实际时间不一定重要，但格式需统一）。
    *   `生命值--"80/100"--HH:MM`
    *   `金币--"120"--HH:MM`
    *   `当前地点--"幽暗森林入口"--HH:MM`
    *   `提示信息--"你感到一阵寒意"--HH:MM`
*   **行动选项**:
    *   `行动选项A--"向北边的洞穴探索"--HH:MM`
    *   `行动选项B--"检查地上的脚印"--HH:MM`
    *   `对话选项C--"询问村民关于怪物的传闻"--HH:MM` (如果当前是与NPC互动)

**数据清理**: 依然需要 `extractActualSceneBlock` 类似的函数来提取纯净的核心数据块，去除AI可能夹带的思考过程。

## 5. 主要数据结构 (TypeScript 接口初步设想)

```typescript
// src/adventure_log/types.ts (或在 index.ts 顶部声明)

interface PlayerState {
    currentLocation: string;
    health: string; // 例如 "80/100"
    mana?: string; // 可选
    gold?: number;
    inventory?: string[]; // 简单物品列表
    activeQuests?: string[];
    // ... 其他可追踪的状态
}

interface ActionChoiceDefinition {
    id: string; // 例如 "action_A", "dialogue_B"
    text: string; // 按钮上显示的文本
    actionCommand: string; // 发送给AI的内部指令或用于逻辑判断的键
}

// AI返回并解析后的单个场景/事件单元
interface AdventureScene {
    sceneType: 'location' | 'dialogue' | 'system_message' | 'combat'; // 场景类型
    title?: string; // 地点名称或事件标题
    description: string; // 主要的文本描述
    npcName?: string; // 如果是对话场景
    playerChoices?: ActionChoiceDefinition[];
    // 包含需要更新的玩家状态，直接从解析后的 --HH:MM 行获取
    time?: string; // 沿用galgame的时间概念，或改为游戏内日期/轮次
    healthUpdate?: string;
    goldUpdate?: number;
    // ... 其他状态更新
}

// 历史记录条目
interface AdventureLogEntry {
    sceneData: AdventureScene; // AI生成的场景数据
    playerChoiceText?: string; // 玩家在该场景下做出的选择
}
```

## 6. 模块化脚本设想 (在 `src/adventure_log/` 目录下)

*   `index.ts`: 主入口文件，负责初始化、加载模块、协调各部分工作。
*   `config.ts`: (可选) 存放游戏的一些基本配置，如初始状态、常量等。
*   `state.ts`: 负责管理游戏的核心状态 (`PlayerState`, `AdventureScene` 当前状态等)，提供状态更新和获取的函数。
*   `ui.ts`: 负责所有与DOM操作和UI更新相关的逻辑，如渲染场景描述、状态栏、选项按钮。
*   `parser.ts`: 负责解析AI返回的原始文本数据，将其转换为结构化的 `AdventureScene` 对象。包含 `extractActualSceneBlock` 和更详细的解析逻辑。
*   `interaction.ts`: 处理玩家与UI的交互（如按钮点击），构造发送给AI的提示，调用SillyTavern的API与AI通信。
*   `persistence.ts`: 负责游戏状态和冒险日志的持久化与恢复，改编自 `galgame` 的机制。
*   `utils.ts`: (可选) 存放一些通用辅助函数。

## 7. 关键函数功能拆解 (初步)

*   **`onMounted()` (`index.ts`)**:
    *   初始化各模块。
    *   获取DOM元素引用。
    *   调用 `persistence.loadGame()` 尝试恢复游戏状态和日志。
    *   如果无法恢复或新游戏，则初始化默认场景/状态。
    *   调用 `ui.renderInitialScene()`。
*   **`parser.parseAIResponse(rawText: string): AdventureScene | null` (`parser.ts`)**:
    *   调用 `extractActualSceneBlock`。
    *   解析纯净数据块，填充 `AdventureScene` 对象。
*   **`interaction.handlePlayerAction(choice: ActionChoiceDefinition)` (`interaction.ts`)**:
    *   记录玩家选择到当前日志条目。
    *   构造发送给AI的提示词（包含必要的上下文，如简化的历史或关键状态）。
    *   调用 `triggerSlash` 或类似API获取AI响应。
    *   获取响应后，调用 `parser.parseAIResponse()`。
    *   如果解析成功，调用 `state.updateGameScene(newScene)` 更新状态。
    *   调用 `ui.renderScene(newScene)` 更新界面。
    *   调用 `persistence.saveGame()` 持久化。
*   **`ui.renderScene(scene: AdventureScene)` (`ui.ts`)**:
    *   根据 `scene` 数据更新主叙事区、状态区、选项区。
*   **`persistence.saveGame(log: AdventureLogEntry[], currentState: PlayerState)` (`persistence.ts`)**:
    *   将冒险日志和当前关键状态组合成特定格式的字符串。
    *   使用 `setChatMessages` 存入宿主消息。
*   **`persistence.loadGame(): { log: AdventureLogEntry[], currentState: PlayerState } | null` (`persistence.ts`)**:
    *   从宿主消息读取数据。
    *   解析字符串，重建冒险日志和玩家状态。

## 8. 预期的交互流程

1.  **加载界面**: `onMounted` 执行，尝试加载旧游戏或开始新游戏。UI显示初始场景。
2.  **玩家行动**: 玩家点击一个行动选项按钮。
3.  **处理行动**: `interaction.handlePlayerAction` 被调用。
4.  **AI交互**: 构造提示，发送给AI，接收AI的文本响应。
5.  **数据解析**: `parser.parseAIResponse` 解析AI的响应。
6.  **状态更新**: `state.updateGameScene` (或类似函数) 更新游戏内部状态。
7.  **UI更新**: `ui.renderScene` 根据新状态刷新界面显示。
8.  **持久化**: `persistence.saveGame` 保存当前的游戏进度。
9.  循环步骤2-8。

## 9. 持久化机制

将沿用 `galgame` 的核心思想：
*   完整的冒险日志 (`AdventureLogEntry[]`) 将被序列化。
*   旧的日志条目组合后，用HTML注释 (`<!-- OLD_ADVENTURE_LOG_CHUNK_START -->` ... `<!-- OLD_ADVENTURE_LOG_CHUNK_END -->`) 包裹。
*   最新的场景数据（或一个包含当前完整状态的特定数据块）将以 `查看系统\nmsg_start\n...\nmsg_end\n关闭系统` 的形式附加在旧历史之后。
*   SillyTavern的正则表达式将用于在主聊天界面隐藏旧历史，并提取最新数据块供iframe加载时使用。

## 10. 与SillyTavern的集成

*   **iframe加载**: UI通过SillyTavern的机制加载到一个iframe中。
*   **API调用**: 使用 `window.TavernHelper` 或 `window.SillyTavern` 提供的API (如 `triggerSlash`, `getLastMessageId`, `setChatMessages`) 进行交互。
*   **正则表达式**: 配置SillyTavern的“格式化输出”以正确处理持久化数据。

---
此计划为初步设想，具体细节将在后续与你讨论和开发过程中进一步完善。
