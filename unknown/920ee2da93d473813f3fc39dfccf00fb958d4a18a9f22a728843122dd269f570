import { getAttributeModifier, getProficiencyBonus } from './attributes';

// Forward declaration for a function that will be in skills.ts
// This will be properly imported or handled by a central setup function later.
declare function updateAllSkillFinalValues(): void;

export const DND_SPELLS_0_HTML = `
<H4 id="Acid_Splash">酸液飞溅｜Acid Splash</H4>
<H4 id="Blade_Ward">剑刃防护｜Blade Ward</H4>
<H4 id="Chill_Touch">颤栗之触｜Chill Touch</H4>
<H4 id="Dancing_Lights">舞光术｜Dancing Lights</H4>
<H4 id="Druidcraft">德鲁伊伎俩｜Druidcraft</H4>
<H4 id="Eldritch_Blast">魔能爆｜Eldritch Blast</H4>
<H4 id="Elementalism">四象法门｜Elementalism</H4>
<H4 id="Fire_Bolt">火焰箭｜Fire Bolt</H4>
<H4 id="Friends">交友术｜Friends</H4>
<H4 id="Guidance">神导术｜Guidance</H4>
<H4 id="Light">光亮术｜Light</H4>
<H4 id="Mage_Hand">法师之手｜Mage Hand</H4>
<H4 id="Mending">修复术｜Mending</H4>
<H4 id="Message">传讯术｜Message</H4>
<H4 id="Mind_Sliver">心灵之楔｜Mind Sliver</H4>
<H4 id="Minor_Illusion">次级幻象｜Minor Illusion</H4>
<H4 id="Poison_Spray">毒气喷涌｜Poison Spray</H4>
<H4 id="Prestidigitation">魔法伎俩｜Prestidigitation</H4>
<H4 id="Produce_Flame">燃火术｜Produce Flame</H4>
<H4 id="Ray_of_Frost">冷冻射线｜Ray of Frost</H4>
<H4 id="Resistance">抵抗术｜Resistance</H4>
<H4 id="Sacred_Flame">圣火术｜Sacred Flame</H4>
<H4 id="Shillelagh">橡棍术｜Shillelagh</H4>
<H4 id="Shocking_Grasp">电爪｜Shocking Grasp</H4>
<H4 id="Sorcerous_Burst">术法爆发｜Sorcerous Burst</H4>
<H4 id="Spare_the_Dying">维生术｜Spare the Dying</H4>
<H4 id="Starry_Wisp">点点星芒｜Starry Wisp</H4>
<H4 id="Thaumaturgy">奇术｜Thaumaturgy</H4>
<H4 id="Thorn_Whip">荆棘之鞭｜Thorn Whip</H4>
<H4 id="Thunderclap">鸣雷破｜Thunderclap</H4>
<H4 id="Toll_the_Dead">亡者丧钟｜Toll the Dead</H4>
<H4 id="True_Strike">克敌先击｜True Strike</H4>
<H4 id="Vicious_Mockery">恶言相加｜Vicious Mockery</H4>
<H4 id="Word_of_Radiance">光耀祷词｜Word of Radiance</H4>
`;

export const DND_SPELLS_1_HTML = `
<H4 id="Alarm">警报术｜Alarm</H4>
<H4 id="Animal_Friendship">化兽为友｜Animal Friendship</H4>
<H4 id="Armor_of_Agathys">黯冰狱铠｜Armor of Agathys</H4>
<H4 id="Arms_of_Hadar">哈达之臂｜Arms of Hadar</H4>
<H4 id="Bane">灾祸术｜Bane</H4>
<H4 id="Bless">祝福术｜Bless</H4>
<H4 id="Burning_Hands">燃烧之手｜Burning Hands</H4>
<H4 id="Charm_Person">魅惑类人｜Charm Person</H4>
<H4 id="Chromatic_Orb">繁彩球｜Chromatic Orb</H4>
<H4 id="Color_Spray">七彩喷射｜Color Spray</H4>
<H4 id="Command">命令术｜Command</H4>
<H4 id="Compelled_Duel">强令对决｜Compelled Duel</H4>
<H4 id="Comprehend_Languages">通晓语言｜Comprehend Languages</H4>
<H4 id="Create_or_Destroy_Water">造水术/枯水术｜Create or Destroy Water</H4>
<H4 id="Cure_Wounds">疗伤术｜Cure Wounds</H4>
<H4 id="Detect_Evil_and_Good">侦测善恶｜Detect Evil and Good</H4>
<H4 id="Detect_Magic">侦测魔法｜Detect Magic</H4>
<H4 id="Detect_Poison_and_Disease">侦测毒性和疾病｜Detect Poison and Disease</H4>
<H4 id="Disguise_Self">易容术｜Disguise Self</H4>
<H4 id="Dissonant_Whispers">不谐低语｜Dissonant Whispers</H4>
<H4 id="Divine_Favor">神恩｜Divine Favor</H4>
<H4 id="Divine_Smite">至圣斩｜Divine Smite</H4>
<H4 id="Ensnaring_Strike">捕获打击｜Ensnaring Strike</H4>
<H4 id="Entangle">纠缠术｜Entangle</H4>
<H4 id="Expeditious_Retreat">脚底抹油｜Expeditious Retreat</H4>
<H4 id="Faerie_Fire">妖火｜Faerie Fire</H4>
<H4 id="False_Life">虚假生命｜False Life</H4>
<H4 id="Feather_Fall">羽落术｜Feather Fall</H4>
<H4 id="Find_Familiar">寻获魔宠｜Find Familiar</H4>
<H4 id="Fog_Cloud">云雾术｜Fog Cloud</H4>
<H4 id="Goodberry">神莓术｜Goodberry</H4>
<H4 id="Grease">油腻术｜Grease</H4>
<H4 id="Guiding_Bolt">光导箭｜Guiding Bolt</H4>
<H4 id="Hail_of_Thorns">荆棘之雨｜Hail of Thorns</H4>
<H4 id="Healing_Word">治愈真言｜Healing Word</H4>
<H4 id="Hellish_Rebuke">炼狱叱喝｜Hellish Rebuke</H4>
<H4 id="Heroism">英雄气概｜Heroism</H4>
<H4 id="Hex">脆弱诅咒｜Hex</H4>
<H4 id="Hunter's_Mark">猎人印记｜Hunter's Mark</H4>
<H4 id="Ice_Knife">冰刃｜Ice Knife</H4>
<H4 id="Identify">鉴定术｜Identify</H4>
<H4 id="Illusory_Script">迷幻手稿｜Illusory Script</H4>
<H4 id="Inflict_Wounds">致伤术｜Inflict Wounds</H4>
<H4 id="Jump">跳跃术｜Jump</H4>
<H4 id="Longstrider">大步奔行｜Longstrider</H4>
<H4 id="Mage_Armor">法师护甲｜Mage Armor</H4>
<H4 id="Magic_Missile">魔法飞弹｜Magic Missile</H4>
<H4 id="Protection_from_Evil_and_Good">防护善恶｜Protection from Evil and Good</H4>
<H4 id="Purify_Food_and_Drink">净化饮食｜Purify Food and Drink</H4>
<H4 id="Ray_of_Sickness">致病射线｜Ray of Sickness</H4>
<H4 id="Sanctuary">庇护术｜Sanctuary</H4>
<H4 id="Searing_Smite">炽焰斩｜Searing Smite</H4>
<H4 id="Shield">护盾术｜Shield</H4>
<H4 id="Shield_of_Faith">虔诚护盾｜Shield of Faith</H4>
<H4 id="Silent_Image">无声幻影｜Silent Image</H4>
<H4 id="Sleep">睡眠术｜Sleep</H4>
<H4 id="Speak_with_Animals">动物交谈｜Speak with Animals</H4>
<H4 id="Tasha's_Hideous_Laughter">塔莎狂笑术｜Tasha's Hideous Laughter</H4>
<H4 id="Tenser's_Floating_Disk">谭森浮碟术｜Tenser's Floating Disk</H4>
<H4 id="Thunderous_Smite">雷鸣斩｜Thunderous Smite</H4>
<H4 id="Thunderwave">雷鸣波｜Thunderwave</H4>
<H4 id="Unseen_Servant">隐形仆役｜Unseen Servant</H4>
<H4 id="Witch_Bolt">巫术箭｜Witch Bolt</H4>
<H4 id="Wrathful_Smite">激愤斩｜Wrathful Smite</H4>
`;

export function extractSpellNamesFromHtml(htmlContent: string): string[] {
  const spellNames: string[] = [];
  const regex = /<H4 id="[^"]*">([^｜]+)｜[^<]*<\/H4>/g;
  let match;
  while ((match = regex.exec(htmlContent)) !== null) {
    if (match[1]) {
      spellNames.push(match[1].trim());
    }
  }
  return spellNames;
}

export function populateSpellDatalists() {
  const cantripsDatalist = document.getElementById('cantrips-datalist');
  const level1SpellsDatalist = document.getElementById('level1-spells-datalist');

  if (cantripsDatalist) {
    const cantripNames = extractSpellNamesFromHtml(DND_SPELLS_0_HTML);
    cantripNames.forEach(name => {
      const option = document.createElement('option');
      option.value = name;
      cantripsDatalist.appendChild(option);
    });
  }

  if (level1SpellsDatalist) {
    const level1SpellNames = extractSpellNamesFromHtml(DND_SPELLS_1_HTML);
    level1SpellNames.forEach(name => {
      const option = document.createElement('option');
      option.value = name;
      level1SpellsDatalist.appendChild(option);
    });
  }
}

export function createSpellItem(
  spellLevelName: string,
  listId: string,
  datalistId: string,
  spellName: string = '',
): HTMLElement {
  const spellItemDiv = document.createElement('div');
  spellItemDiv.className = 'spell-item';

  const input = document.createElement('input');
  input.type = 'text';
  input.name = `${spellLevelName}[]`;
  input.placeholder = `${spellLevelName === 'cantrip' ? '戏法' : '一环法术'}`;
  input.setAttribute('list', datalistId);
  input.value = spellName;

  const removeButton = document.createElement('button');
  removeButton.type = 'button';
  removeButton.className = 'remove-spell-button';
  removeButton.textContent = '-';
  removeButton.addEventListener('click', () => {
    spellItemDiv.remove();
  });

  spellItemDiv.appendChild(input);
  spellItemDiv.appendChild(removeButton);
  return spellItemDiv;
}

export function setupDynamicSpellLists() {
  const addCantripButton = document.getElementById('add-cantrip-button');
  const cantripsListDiv = document.getElementById('cantrips-list');

  if (addCantripButton && cantripsListDiv) {
    addCantripButton.addEventListener('click', () => {
      const placeholder = cantripsListDiv.querySelector('input[name="cantrip1"]');
      if (
        placeholder &&
        placeholder.parentElement?.classList.contains('spell-item') &&
        cantripsListDiv.querySelectorAll('.spell-item').length === 1 &&
        !(placeholder as HTMLInputElement).value
      ) {
        placeholder.parentElement.remove();
      }
      cantripsListDiv.appendChild(createSpellItem('cantrip', 'cantrips-list', 'cantrips-datalist'));
    });
  }

  const addLevel1SpellButton = document.getElementById('add-level1-spell-button');
  const level1SpellsListDiv = document.getElementById('level1-spells-list');

  if (addLevel1SpellButton && level1SpellsListDiv) {
    addLevel1SpellButton.addEventListener('click', () => {
      const placeholder = level1SpellsListDiv.querySelector('input[name="level1spell1"]');
      if (
        placeholder &&
        placeholder.parentElement?.classList.contains('spell-item') &&
        level1SpellsListDiv.querySelectorAll('.spell-item').length === 1 &&
        !(placeholder as HTMLInputElement).value
      ) {
        placeholder.parentElement.remove();
      }
      level1SpellsListDiv.appendChild(createSpellItem('level1spell', 'level1-spells-list', 'level1-spells-datalist'));
    });
  }

  document.getElementById('character-sheet-form')?.addEventListener('click', function (event) {
    const target = event.target as HTMLElement;
    if (target && target.classList.contains('remove-spell-button')) {
      target.parentElement?.remove();
    }
  });
}

export function updateSpellAttackAndDc() {
  const spellAbilitySelect = document.getElementById('spell-ability') as HTMLSelectElement;
  const spellAttackBonusEl = document.getElementById('spell-attack-bonus') as HTMLInputElement;
  const spellSaveDcEl = document.getElementById('spell-save-dc') as HTMLInputElement;
  const level1El = document.getElementById('char-level-1') as HTMLInputElement;

  if (!spellAbilitySelect || !spellAttackBonusEl || !spellSaveDcEl || !level1El) {
    return;
  }

  const selectedAbilityKey = spellAbilitySelect.value.toLowerCase();
  const finalAbilityScoreEl = document.getElementById(`attr-${selectedAbilityKey}-final`) as HTMLInputElement;

  if (!finalAbilityScoreEl) {
    console.warn(`Final ability score element for '${selectedAbilityKey}' not found.`);
    return;
  }

  const finalAbilityScore = parseInt(finalAbilityScoreEl.value, 10) || 10;
  const abilityModifier = getAttributeModifier(finalAbilityScore);
  const characterLevel = parseInt(level1El.value, 10) || 1;
  const proficiencyBonus = getProficiencyBonus(characterLevel);

  const spellAttackBonus = abilityModifier + proficiencyBonus;
  const spellSaveDc = 8 + abilityModifier + proficiencyBonus;

  spellAttackBonusEl.value = (spellAttackBonus >= 0 ? '+' : '') + spellAttackBonus.toString();
  spellSaveDcEl.value = spellSaveDc.toString();
}

export function setupSpellcastingCalculations() {
  const spellAbilitySelect = document.getElementById('spell-ability') as HTMLSelectElement;
  const level1El = document.getElementById('char-level-1') as HTMLInputElement;

  if (spellAbilitySelect) {
    spellAbilitySelect.addEventListener('change', updateSpellAttackAndDc);
  }
  if (level1El) {
    level1El.addEventListener('input', () => {
        updateSpellAttackAndDc();
        // This will be called by a higher-level setup function
        if (typeof updateAllSkillFinalValues === 'function') {
            updateAllSkillFinalValues();
        }
    });
  }
  
  // Initial calculation
  // Ensure DOM is ready for initial calculation
  if (document.readyState === 'complete' || document.readyState === 'interactive') {
    updateSpellAttackAndDc();
  } else {
    window.addEventListener('DOMContentLoaded', updateSpellAttackAndDc);
  }
}
