// Forward declarations or stubs for functions that will be in other modules
// These will be properly imported or handled by a central setup function later.
declare function updateSpellAttackAndDc(): void;
declare function updateAllSkillFinalValues(): void;

export function calculateAttributeFinal(base: number, race: number, mod: number): number {
  return (base || 0) + (race || 0) + (mod || 0);
}

export function getAttributeModifier(finalValue: number): number {
  return Math.floor(((finalValue || 10) - 10) / 2);
}

export function setupAttributeCalculations() {
  const attributes = ['str', 'dex', 'con', 'int', 'wis', 'cha'];
  attributes.forEach(attr => {
    const baseEl = document.getElementById(`attr-${attr}-base`) as HTMLInputElement;
    const raceEl = document.getElementById(`attr-${attr}-race`) as HTMLInputElement;
    const modEl = document.getElementById(`attr-${attr}-mod`) as HTMLInputElement;
    const finalEl = document.getElementById(`attr-${attr}-final`) as HTMLInputElement;

    const updateFinalValue = () => {
      if (baseEl && raceEl && modEl && finalEl) {
        const baseVal = parseInt(baseEl.value, 10) || 0;
        const raceVal = parseInt(raceEl.value, 10) || 0;
        const modVal = parseInt(modEl.value, 10) || 0;
        const finalVal = calculateAttributeFinal(baseVal, raceVal, modVal);
        finalEl.value = finalVal.toString();
        
        // These will be called by a higher-level setup function that has access to all modules
        // to prevent circular dependencies here.
        if (typeof updateSpellAttackAndDc === 'function') {
            updateSpellAttackAndDc();
        }
        if (typeof updateAllSkillFinalValues === 'function') {
            updateAllSkillFinalValues();
        }
      }
    };

    [baseEl, raceEl, modEl].forEach(el => {
      if (el) el.addEventListener('input', updateFinalValue);
    });
    
    // Initial calculation
    // Ensure DOM is ready for initial calculation
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        updateFinalValue();
    } else {
        window.addEventListener('DOMContentLoaded', updateFinalValue);
    }
  });
}

export function getProficiencyBonus(level: number): number {
  if (level >= 17) return 6;
  if (level >= 13) return 5;
  if (level >= 9) return 4;
  if (level >= 5) return 3;
  if (level >= 1) return 2;
  return 0;
}
