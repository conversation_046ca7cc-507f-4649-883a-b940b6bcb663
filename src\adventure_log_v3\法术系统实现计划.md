# 法术系统实现计划

## 项目现状分析

### 已有功能
1. **基础法术系统框架** (`src/adventure_log_v3/spells/index.ts`)
   - 法术模板加载和管理
   - 法术攻击检定计算
   - 法术伤害计算（支持戏法升级、升环施法）
   - 法术槽管理
   - 默认法术模板（火焰箭、魔法飞弹、燃烧之手）

2. **类型定义** (`src/adventure_log_v3/types/index.ts`)
   - `SpellTemplate` 接口：完整的法术属性定义
   - `SpellDamageResult` 接口：法术伤害计算结果
   - `PlayerState` 中的法术相关字段

3. **UI界面元素** (`src/adventure_log_v3/index.html`)
   - 法术槽显示区域
   - 已准备法术显示区域

4. **基础集成** (`src/adventure_log_v3/app.ts`)
   - 法术攻击检定集成到选择处理逻辑中
   - 施法属性识别

### 缺失功能
1. **法术系统初始化**：未在应用启动时加载法术模板
2. **法术施放UI**：缺少法术选择和施放界面
3. **法术伤害集成**：法术伤害计算未集成到攻击系统
4. **法术槽消耗**：未实现法术槽的实际消耗
5. **更多法术模板**：只有3个基础法术
6. **法术豁免处理**：豁免检定逻辑未完全实现

## 实现计划

### 第一阶段：完善核心法术系统

#### 1.1 法术系统初始化
- **文件**：`src/adventure_log_v3/app.ts`
- **任务**：在应用初始化时调用 `loadSpellTemplates()`
- **位置**：在 `onMounted()` 函数中添加法术模板加载

#### 1.2 法术伤害集成
- **文件**：`src/adventure_log_v3/app.ts`
- **任务**：在攻击处理逻辑中集成法术伤害计算
- **功能**：
  - 识别法术攻击
  - 调用 `calculateSpellDamage()` 计算伤害
  - 处理法术槽消耗
  - 显示法术特殊效果（如豁免检定）

#### 1.3 扩展法术模板库
- **文件**：`src/adventure_log_v3/spells/index.ts`
- **任务**：添加更多常用法术模板
- **法术列表**：
  - 戏法：光亮术、法师之手、修复术、寒冰射线
  - 1环：治疗轻伤、魅惑人类、睡眠术、护盾术
  - 2环：蛛网术、隐身术、朦胧步
  - 3环：火球术、闪电束、反制法术

### 第二阶段：法术施放UI界面

#### 2.1 法术选择界面
- **文件**：`src/adventure_log_v3/ui/render.ts`
- **任务**：创建法术选择和施放界面
- **功能**：
  - 显示可用法术列表
  - 显示法术详细信息
  - 法术槽选择（升环施法）
  - 目标选择

#### 2.2 法术快捷栏
- **文件**：`src/adventure_log_v3/index.html`
- **任务**：在主界面添加法术快捷栏
- **功能**：
  - 显示常用法术
  - 一键施法按钮
  - 法术槽状态指示

#### 2.3 法术详情弹窗
- **文件**：新建 `src/adventure_log_v3/ui/spellModal.ts`
- **任务**：创建法术详情显示组件
- **功能**：
  - 法术完整描述
  - 施法参数设置
  - 升环效果预览

### 第三阶段：高级法术功能

#### 3.1 法术豁免系统
- **文件**：`src/adventure_log_v3/spells/index.ts`
- **任务**：完善豁免检定处理
- **功能**：
  - 自动计算豁免DC
  - 处理豁免成功/失败效果
  - 群体法术的多目标豁免

#### 3.2 法术效果系统
- **文件**：新建 `src/adventure_log_v3/spells/effects.ts`
- **任务**：实现法术持续效果管理
- **功能**：
  - 专注法术管理
  - 状态效果应用
  - 效果持续时间跟踪

#### 3.3 法术反制和反应
- **文件**：`src/adventure_log_v3/spells/reactions.ts`
- **任务**：实现反制法术等反应性法术
- **功能**：
  - 反制法术检定
  - 护盾术等防御反应
  - 机会攻击法术

### 第四阶段：用户体验优化

#### 4.1 法术搜索和过滤
- **功能**：按学派、等级、类型过滤法术
- **界面**：搜索框和过滤器

#### 4.2 法术使用统计
- **功能**：记录法术使用次数和效果
- **界面**：法术使用历史

#### 4.3 法术推荐系统
- **功能**：根据当前情况推荐合适的法术
- **逻辑**：基于敌人类型、距离、法术槽等因素

## 技术实现细节

### 文件修改清单
1. `src/adventure_log_v3/app.ts` - 添加法术系统初始化和伤害集成
2. `src/adventure_log_v3/spells/index.ts` - 扩展法术模板库
3. `src/adventure_log_v3/ui/render.ts` - 添加法术UI渲染
4. `src/adventure_log_v3/index.html` - 添加法术界面元素
5. `src/adventure_log_v3/index.scss` - 添加法术界面样式

### 新增文件
1. `src/adventure_log_v3/ui/spellModal.ts` - 法术详情弹窗
2. `src/adventure_log_v3/spells/effects.ts` - 法术效果管理
3. `src/adventure_log_v3/spells/reactions.ts` - 反应性法术

### 数据结构扩展
- 在 `PlayerState` 中添加法术使用历史
- 扩展 `SpellTemplate` 支持更复杂的效果
- 添加法术效果状态管理

## 优先级排序
1. **高优先级**：法术系统初始化、法术伤害集成、基础UI
2. **中优先级**：扩展法术库、豁免系统、法术效果
3. **低优先级**：高级功能、用户体验优化

## 预期成果
完成后，用户将能够：
1. 在战斗中正常使用法术进行攻击和施法
2. 通过直观的界面选择和管理法术
3. 体验完整的D&D 5e法术系统机制
4. 享受流畅的法术施放体验

## 下一步行动
开始第一阶段的实现，首先完善法术系统的核心功能集成。
