<!doctype html><html lang="zh-CN"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1"><title>冒险日志</title><style>body{margin:0;font-family:"Merriweather","Georgia",serif;background-image:linear-gradient(to right, rgba(0, 0, 0, 0.25) 0%, transparent 15%, transparent 85%, rgba(0, 0, 0, 0.25) 100%),linear-gradient(rgba(44, 40, 35, 0.5), rgba(44, 40, 35, 0.5)),url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234a433b' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");background-color:#2c2823;color:#e0e0e0;min-height:100vh;box-sizing:border-box}#adventure-log-container{width:95vw;max-width:700px;background-color:#eaddc7;border:2px solid #5a3d2b;position:relative;border-radius:12px;box-shadow:0 12px 35px rgba(0,0,0,.7),inset 0 0 20px rgba(0,0,0,.2);display:flex;flex-direction:column;padding:18px;box-sizing:border-box;margin:0 auto}#adventure-log-container::before,#adventure-log-container::after{content:"";position:absolute;border-radius:12px;z-index:-1}#adventure-log-container::before{top:-6px;left:-6px;right:-6px;bottom:-6px;border:3px dashed #9c7b60;opacity:.7}#player-status-area{padding-bottom:12px;border-bottom:1px solid #a8886c;margin-bottom:12px;display:flex;flex-direction:column;gap:9px;font-size:.88em}#player-status-area .status-row{display:flex;flex-wrap:wrap;gap:10px;align-items:center;justify-content:space-around}#player-status-area #health,#player-status-area #ac-display,#player-status-area #time,#player-status-area #location{padding:6px 9px;background-color:#d8c8b3;border:1px solid #7a5c44;border-radius:5px;white-space:nowrap;color:#422d1a;box-shadow:0 1px 2px rgba(0,0,0,.1);font-weight:500}#toggle-char-sheet-button{padding:10px 15px;background-color:#8c6f4f;color:#f0e6d2;border:1px solid #604834;border-radius:6px;cursor:pointer;font-size:.92em;margin-top:10px;margin-bottom:10px;text-align:center;width:100%;font-weight:600;transition:background-color .2s,box-shadow .2s,transform .1s;text-shadow:1px 1px 1px rgba(0,0,0,.2)}#toggle-char-sheet-button:hover{background-color:#7a5c3f;box-shadow:0 0 8px rgba(140,111,79,.8);transform:translateY(-1px)}#detailed-character-sheet{background-color:rgba(216,200,179,.85);padding:14px;border-radius:8px;margin-top:8px;border:1px solid #7a5c44;max-height:35vh;overflow-y:auto;color:#4a321f}#detailed-character-sheet h4{margin-top:14px;margin-bottom:9px;color:#5a3d2b;font-size:1.1em;border-bottom:1px solid #7a5c44;padding-bottom:6px;font-variant:small-caps;position:relative}#detailed-character-sheet h4::before,#detailed-character-sheet h4::after{content:"";position:absolute;bottom:-2px;height:2px;background-color:#9c7b60;width:20px}#detailed-character-sheet h4::before{left:0}#detailed-character-sheet h4::after{right:0}#detailed-character-sheet h4:first-child{margin-top:0}#detailed-character-sheet .status-row{display:flex;flex-wrap:wrap;gap:9px 13px;align-items:center;margin-bottom:9px}#detailed-character-sheet .status-row div,#detailed-character-sheet .status-row span{padding:4px 8px;background-color:#eaddc7;border:1px solid #b89a7c;border-radius:4px;white-space:nowrap;font-size:.92em;color:#4a321f}#detailed-character-sheet ul{list-style-type:"✧ ";padding-left:15px;margin:8px 0 11px 0;font-size:.88em}#detailed-character-sheet ul li{padding:3px 0;border-bottom:1px dotted rgba(122,92,68,.5)}#detailed-character-sheet ul li:last-child{border-bottom:none}#detailed-character-sheet #spell-slots-display{font-size:.88em;padding-left:12px;margin-bottom:11px}#main-narrative-area{flex-grow:1;padding:14px;background-color:#faf0e0;border:1px solid #c8a064;border-radius:8px;line-height:1.7;font-size:1.02em;color:#321;margin-bottom:18px;box-shadow:inset 0 0 10px rgba(0,0,0,.1)}#main-narrative-area p{margin-top:0;margin-bottom:1em}#main-narrative-area p:last-child{margin-bottom:0}#main-narrative-area .system-message em{color:#7a522f;font-style:italic;font-weight:500}#main-narrative-area .thought-message i{color:#607d8b;font-style:italic}#main-narrative-area strong{color:#5a3d2b;font-weight:700}#action-choices-area{display:flex;flex-direction:column;gap:9px}#action-choices-area button{padding:13px 17px;background-color:#7a5c3f;color:#f5e8d0;border:1px solid #503d2e;border-bottom:3px solid #503d2e;border-radius:8px;cursor:pointer;font-size:.98em;text-align:left;font-weight:600;font-family:"Merriweather","Georgia",serif;box-shadow:0 2px 3px rgba(0,0,0,.2);transition:background-color .15s ease-out,transform .1s ease-out,box-shadow .15s ease-out,border-bottom-width .1s ease-out;position:relative}#action-choices-area button::before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0));border-radius:8px;pointer-events:none}#action-choices-area button:hover{background-color:#8c6f4f;border-color:#604834;box-shadow:0 3px 5px rgba(0,0,0,.25);transform:translateY(-2px)}#action-choices-area button:active{background-color:#604834;transform:translateY(1px);border-bottom-width:1px;box-shadow:inset 0 1px 2px rgba(0,0,0,.2)}#start-screen-container{display:flex;flex-direction:column;justify-content:center;align-items:center;text-align:center;padding:20px;box-sizing:border-box;width:100%;height:100%;overflow-y:auto;background-color:#f5e8d0;color:#4a2e00}#start-screen-container h1{font-size:2em;color:#6b4226;margin-bottom:18px;font-variant:small-caps;text-shadow:1px 1px 2px rgba(200,160,100,.5)}#start-screen-container p{font-size:1.05em;color:#5a3811;margin-bottom:22px;max-width:88%;line-height:1.55}#start-screen-container #start-new-game-button{padding:14px 28px;font-size:1.2em;background-color:#6b8e23;color:#f5f5f5;border:2px outset #8fbc8f;border-radius:8px;cursor:pointer;transition:background-color .25s,transform .15s;font-weight:500;text-shadow:1px 1px 1px rgba(0,0,0,.4);box-shadow:0 3px 5px rgba(0,0,0,.3)}#start-screen-container #start-new-game-button:hover{background-color:#556b2f;transform:scale(1.03)}#start-screen-container .start-screen-note{font-size:.78em;color:#705432;margin-top:18px;max-width:82%;line-height:1.45}@media(max-width: 600px){#adventure-log-container{width:100vw;max-width:100vw;border-radius:0;border:none;border-image:none;padding:10px;aspect-ratio:unset}#player-status-area{font-size:.85em;gap:6px;padding-bottom:10px;margin-bottom:10px}#player-status-area .status-row{gap:8px}#player-status-area #health,#player-status-area #ac-display,#player-status-area #time,#player-status-area #location{padding:5px 8px}#detailed-character-sheet{font-size:.85em;padding:10px}#detailed-character-sheet h4{font-size:1em}#detailed-character-sheet .status-row{flex-direction:column;align-items:flex-start}#detailed-character-sheet .status-row div,#detailed-character-sheet .status-row span{width:100%;margin-bottom:4px;text-align:left;font-size:.95em}#detailed-character-sheet ul,#detailed-character-sheet #spell-slots-display{font-size:.95em}#main-narrative-area{font-size:1em;padding:10px;margin-bottom:15px}#action-choices-area button{padding:12px 15px;font-size:.95em}#toggle-char-sheet-button{font-size:.9em;padding:8px 12px}}
</style></head><body><div id="start-screen-container"><h1>冒险日志</h1><p>准备好开始一段新的旅程了吗？</p><button id="start-new-game-button">开始新冒险 (从世界书加载角色)</button><p class="start-screen-note">注意：开始新冒险将尝试从名为 "RPG_Modules_Test.json" 的世界书中的 "PLAYER" 条目加载角色数据。如果已有进行中的游戏，它可能会被新游戏覆盖（取决于当前消息内容）。</p></div><div id="adventure-log-container" style="display:none"><div id="player-status-area"><div class="status-row"><div id="health">生命值: ...</div><div id="ac-display">AC: ...</div><div id="time">时间: ...</div><div id="location">地点: ...</div></div><button id="toggle-char-sheet-button">显示/隐藏详细角色卡</button><div id="detailed-character-sheet" style="display:none"><h4>角色信息</h4><div class="status-row"><div id="char-name-display">角色名: ...</div><div id="char-race-class-display">种族/职业: .../...</div><div id="char-level-display">等级: ...</div></div><h4>属性</h4><div class="status-row attributes-display"><div id="attr-str-display">力量: ..(..)</div><div id="attr-dex-display">敏捷: ..(..)</div><div id="attr-con-display">体质: ..(..)</div><div id="attr-int-display">智力: ..(..)</div><div id="attr-wis-display">感知: ..(..)</div><div id="attr-cha-display">魅力: ..(..)</div></div><h4>状态</h4><div class="status-row simple-list-display"><span>金币: <span id="currency-gold-display">...</span></span> <span>经验: <span id="exp-display">...</span></span> <span>力竭: <span id="exhaustion-display">...</span></span></div><h4>熟练项</h4><ul id="proficiencies-display"><li>...</li></ul><h4>技能 (熟练)</h4><ul id="skills-display"><li>...</li></ul><h4>法术槽</h4><div id="spell-slots-display">...</div><h4>已准备法术</h4><ul id="equipped-spells-display"><li>...</li></ul><h4>装备</h4><ul id="equipment-display"><li>...</li></ul><h4>物品栏</h4><ul id="inventory-display"><li>...</li></ul><h4>当前任务</h4><ul id="active-quests-display"><li>...</li></ul></div></div><div id="main-narrative-area"><p>冒险即将开始...</p></div><div id="action-choices-area"></div></div><script>let e={name:"新冒险者",race:"人类",class:"平民",level:1,exp:0,hp:{current:10,max:10},ac:10,currency:{gold:10,silver:0,copper:0},attributes:{strength:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},dexterity:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},constitution:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},intelligence:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},wisdom:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},charisma:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0}},proficiencies:["匕首"],skills:[],spellSlots:{},equippedSpells:[],equipment:[{name:"平民服装",type:"衣物",equipped:!0},{name:"匕首",type:"武器",equipped:!0,properties:["灵巧","轻型"]}],inventory:[{name:"背包",quantity:1},{name:"火绒盒",quantity:1},{name:"口粮",quantity:2,description:"一天的食物"}],activeQuests:[],exhaustion:0,time:"第一天 清晨",currentLocation:"旅途的起点",spellcastingAbility:"INT"},t=null,n=null,i=null,o=null,r=null,a=null,s=null,l=null,c=null,d=null,u=null,m=null,p=null,y=null,f=null,g=null,b=null,$=null,_=null,E=null,h=null,N=null,S=null,A=null,L=null,v=null,x=null,w=null,T=null,I=null,D=null,C=null,M=null;function q(e,t,n){try{if("object"!=typeof window.toastr&&"undefined"!=typeof parent&&"object"==typeof parent.toastr&&(window.toastr=parent.toastr),"object"==typeof toastr&&null!==toastr&&"function"==typeof toastr[e])toastr[e](t,n);else{("error"===e?console.error:"warning"===e?console.warn:console.log)(`[AdvLog Toastr Fallback - ${e}] ${n?n+": ":""}${t}`)}}catch(e){console.error(`[AdvLog] safeToastr Error: ${e.message}`)}}function B(){if(!e)return;m&&(m.textContent=`角色名: ${e.name||"N/A"}`),p&&(p.textContent=`种族/职业: ${e.race||"N/A"} / ${e.class||"N/A"}`),y&&(y.textContent=`等级: ${e.level||0}`),c&&(c.textContent=`生命值: ${e.hp?.current||"?"}/${e.hp?.max||"?"}`),f&&(f.textContent=`AC: ${e.ac||0}`),u&&(u.textContent=`时间: ${e.time||"N/A"}`),d&&(d.textContent=`地点: ${e.currentLocation||"N/A"}`);const t=e=>e?`${e.final}(${e.mod>=0?"+":""}${e.mod})`:"N/A";if(g&&(g.textContent=`力量: ${t(e.attributes?.strength)}`),b&&(b.textContent=`敏捷: ${t(e.attributes?.dexterity)}`),$&&($.textContent=`体质: ${t(e.attributes?.constitution)}`),_&&(_.textContent=`智力: ${t(e.attributes?.intelligence)}`),E&&(E.textContent=`感知: ${t(e.attributes?.wisdom)}`),h&&(h.textContent=`魅力: ${t(e.attributes?.charisma)}`),N&&(N.textContent=`${e.currency?.gold||0}`),S&&(S.textContent=`${e.exp||0}`),A&&(A.textContent=`${e.exhaustion||0}`),x&&(x.innerHTML=e.proficiencies?.map((e=>`<li>${e}</li>`)).join("")||"<li>无</li>"),w&&(w.innerHTML=e.skills?.filter((e=>e.proficient)).map((e=>`<li>${e.name} (${e.finalValue>=0?"+":""}${e.finalValue})</li>`)).join("")||"<li>无熟练技能</li>"),T){let t="";if(e.spellSlots&&Object.keys(e.spellSlots).length>0)for(const n in e.spellSlots){const i=e.spellSlots[n];t+=`<div>${n}环: ${i.current}/${i.max}</div>`}T.innerHTML=t||"无"}I&&(I.innerHTML=e.equippedSpells?.map((e=>`<li>${e.name} (${e.level}环)</li>`)).join("")||"<li>无</li>"),D&&(D.innerHTML=e.equipment?.map((e=>`<li>${e.name} (${e.type})${e.equipped?" [已装备]":""}</li>`)).join("")||"<li>无</li>"),C&&(C.innerHTML=e.inventory?.map((e=>`<li>${e.name} x${e.quantity} ${e.description?"("+e.description+")":""}</li>`)).join("")||"<li>空</li>"),M&&(M.innerHTML=e.activeQuests?.map((e=>`<li>${e}</li>`)).join("")||"<li>无</li>")}function k(n){l&&(l.innerHTML="",n&&n.length>0?n.forEach((n=>{const i=document.createElement("button");i.id=`choice-${n.id}`,i.textContent=n.text,i.dataset.action=n.actionCommand,i.addEventListener("click",(()=>async function(n){l&&l.querySelectorAll("button").forEach((e=>e.disabled=!0));const i=s;if(i){const e=document.createElement("p");e.innerHTML="<i>正在等待AI响应...</i>",i.appendChild(e)}let o="";t&&(o+=`紧接之前的场景JSON数据是:\n${JSON.stringify(t,null,2)}\n`);let r="你是一名D&D 5e的地下城主(DM)，正在主持一个文字冒险游戏“冒险日志 v2”。\n";r+=`当前玩家状态：\n${JSON.stringify(e,null,2)}\n`,r+=`\n${o}`;let a="";const c=/\[(?:DC(\d+)\s*)?([^\(\]\s]+)(?:\s*\(([^\)]+)\))?\s*(检定)?\]/i,d=/\[攻击\s+(.+?)\s+使用\s+(.+?)\s+DC(\d+)\]/i;let u=n.text.match(c),m=n.text.match(d);if(m){const t=m[1].trim(),n=m[2].trim(),i=parseInt(m[3],10);let o="strength",r=!1;const l=e.equipment.find((e=>e.name===n&&e.equipped));e.equippedSpells.find((e=>e.name===n))?(r=!0,o=e.spellcastingAbility?.toLowerCase()||"intelligence"):l&&l.properties?.includes("灵巧")&&e.attributes.dexterity.mod>e.attributes.strength.mod&&(o="dexterity");const c=V(i,o,r?n:void 0,e);let d=`投骰1d20[${c.roll}]`;d+=`${c.attributeMod>=0?"+":""}${c.attributeMod}(${c.attributeName})`,0!==c.proficiencyBonusApplied&&(d+=`${c.proficiencyBonusApplied>=0?"+":""}${c.proficiencyBonusApplied}(熟练)`),d+=`=${c.total}`;let u=c.isFumble?"自动失手":c.isCritical?"重击":c.success?"攻击命中":"攻击失手";a=` [${u} DC${i} ${o}(${n}) ${d}]`;const p=`${n} 攻击 ${t}: ${c.total} vs DC${i} -> ${u} (${d})`;q(c.success?"success":"error",p,"攻击结果");s&&(s.innerHTML+=`<p class="check-result"><em>${p}</em></p>`)}else if(u){const t=u[1],n=u[2].trim(),i=u[3]?u[3].trim():void 0;if(n.toLowerCase().includes("先攻")){const t=V(0,"敏捷",void 0,e);let n=`投骰1d20[${t.roll}]${t.attributeMod>=0?"+":""}${t.attributeMod}(敏捷)=${t.total}`;a=` [先攻检定 ${n}]`,q("info",`先攻: ${t.total} (${n})`,"先攻");s&&(s.innerHTML+=`<p class="check-result"><em>先攻: ${t.total} (${n})</em></p>`)}else if(t){const o=parseInt(t,10),r=V(o,n,i,e);let l=`投骰1d20[${r.roll}]`;0!==r.attributeMod&&(l+=`${r.attributeMod>=0?"+":""}${r.attributeMod}(${r.attributeName})`),0!==r.proficiencyBonusApplied&&(l+=`${r.proficiencyBonusApplied>=0?"+":""}${r.proficiencyBonusApplied}(熟练)`),l+=`=${r.total}`;let c=r.isFumble?"自动失败":r.isCritical?"大成功":r.success?"检定成功":"检定失败";a=` [${c} DC${o} ${r.attributeName}${r.skillName?`(${r.skillName})`:""} ${l}]`;const d=`${r.attributeName}${r.skillName?`(${r.skillName})`:""} 检定: ${r.total} vs DC${o} -> ${c} (${l})`;q(r.success?"success":"error",d,"检定结果");s&&(s.innerHTML+=`<p class="check-result"><em>${d}</em></p>`)}}if(r+=`\n玩家刚刚选择了行动： "${n.text}"${a}\n`,r+=`\n请根据玩家的选择${a?"和检定结果":""}，继续发展剧情。`,r+="\n\n【非常重要：输出格式指令】\n",r+='你的完整回复必须是一个单一的JSON对象字符串，被 "查看系统\\##@@_ADVENTURE_BEGIN_@@##" 和 "##@@_ADVENTURE_END_@@##\\n关闭系统" 包裹。JSON结构需严格遵循之前定义的Schema (AdventureSceneJSON, NarrativeEntry, PlayerChoiceJSON, VariableUpdateInstruction, EnemyStateJSON)。\n',r+="确保JSON语法正确，所有字符串和键名使用双引号，对象和数组的最后一个元素后无逗号，特殊字符正确转义。长文本中的换行请使用 '\\n'。\n",r+="请务必严格遵守！\n","function"!=typeof triggerSlash){l&&l.querySelectorAll("button").forEach((e=>e.disabled=!1));const e=s?.querySelector("p > i");return void(e?.parentElement&&s&&s.removeChild(e.parentElement))}try{const e=await triggerSlash(`/gen ${r}`),t=s,n=t?.querySelector("p > i");if(n?.parentElement&&t&&t.removeChild(n.parentElement),e?.trim()){let n=null;const i=e.match(/##@@_ADVENTURE_BEGIN_@@##([\s\S]*?)##@@_ADVENTURE_END_@@##/);i&&i[1]?n=i[1].trim():console.warn("Unique markers ##@@_ADVENTURE_BEGIN_@@##...##@@_ADVENTURE_END_@@## not found in AI response.");let o=null;if(n&&(o=O(n)),o){J(o),await j();const t="dndRPG_history.json";let n=`scene_${Date.now()}_${Math.random().toString(36).substring(2,8)}`;if(o&&o.sceneTitle&&o.time){const e=e=>e.replace(/[\\/:*?"<>|]/g,"_").replace(/\s+/g,"_"),t=e(o.sceneTitle).substring(0,40),i=e(o.time).substring(0,25);n=`${t}_${i}_${Math.random().toString(36).substring(2,7)}`}let i=e;const r=/查看系统\s*##@@_ADVENTURE_BEGIN_@@##[\s\S]*?##@@_ADVENTURE_END_@@##\s*关闭系统/,a=e.match(r);a&&a[0]?i=a[0]:(q("warning","未能从AI回复中提取标准的干净记录块，将保存完整原始回复。可能包含额外数据。","历史记录警告"),console.warn("[AdvLogV2 History] Could not extract clean block, saving raw response. Raw:",e));try{const e=`/createentry file="${t}" key="${n}" ${i}`,o=await triggerSlash(e);if(o&&""!==o.trim()){q("info",`历史场景已保存到 ${t} (Key: ${n}, UID: ${o})`,"历史记录");const e=`/setentryfield file="${t}" uid="${o.trim()}" field=constant true`;await triggerSlash(e),q("success",`历史条目 ${n} 已激活 (蓝灯).`,"历史记录")}else q("warning",`保存历史场景 ${n} 到 ${t} 未返回有效UID。`,"历史记录警告")}catch(e){q("error",`保存或激活历史场景 ${n} 到 ${t} 失败: ${e.message}`,"历史记录错误"),console.error(`Error saving/activating history entry ${n}:`,e)}}else q("error","JSON提取或解析失败，请查看主区域显示的AI原始回复。","处理错误"),t&&(t.innerHTML+=`<p style="color: lightcoral; font-family: monospace; white-space: pre-wrap; border: 1px solid orange; padding: 10px; background-color: #333;"><strong>[调试] AI 原始回复 (提取或解析失败):</strong>\n${e.replace(/</g,"<").replace(/>/g,">")}</p>`),console.error("Failed to extract or parse JSON. Displaying Raw AI Response. Raw AI Response:",e),n||!i||i[1]?n?console.error("Reason: JSON.parse failed for the extracted string, or extracted string was empty/invalid before parsing. Extracted string was:",n):console.error("Reason: Core JSON string could not be extracted (markers not found)."):console.error("Reason: Markers found, but no content between them or other regex issue.")}else t&&(t.innerHTML+="<p style='color:orange;'>AI未返回有效数据。</p>"),q("warning","AI未返回任何数据。","无响应")}catch(e){const t=s,n=t?.querySelector("p > i");n?.parentElement&&t&&t.removeChild(n.parentElement),t&&(t.innerHTML+=`<p style='color:red;'>与AI交互时发生错误: ${e.message}</p>`),q("error",`与AI交互时发生错误: ${e.message}`,"交互错误")}finally{l&&l.querySelectorAll("button").forEach((e=>e.disabled=!1))}}(n))),l.appendChild(i)})):l.innerHTML="<p>暂无行动选项。</p>")}function R(e){return e>=17?6:e>=13?5:e>=9?4:e>=5?3:2}function V(e,t,n,i){const o=Math.floor(20*Math.random())+1;let r=0,a=0;const s=t.toLowerCase();if(i.attributes[s]&&(r=i.attributes[s].mod),n){const e=i.skills.find((e=>e.name.toLowerCase()===n.toLowerCase()));if(e?.proficient)a=R(i.level);else{i.proficiencies.find((e=>e.toLowerCase().includes(n.toLowerCase())&&e.toLowerCase().includes(t.toLowerCase())))&&(a=R(i.level))}}!n&&i.proficiencies.includes(`${t}豁免`)&&(a=R(i.level));const l=o+r+a;let c=l>=e;const d=20===o,u=1===o;return d&&e>0&&(c=!0),u&&(c=!1),{success:c,roll:o,attributeMod:r,proficiencyBonusApplied:a,total:l,dc:e,attributeName:t,skillName:n,isCritical:d,isFumble:u}}function O(e){try{const t=JSON.parse(e);return t&&t.sceneType&&t.sceneTitle&&t.narrative&&t.playerChoices?t:(q("error","解析后的JSON数据缺少必要字段。","JSON解析错误"),console.error("Invalid JSON structure:",t),null)}catch(t){return q("error",`AI响应JSON解析失败: ${t.message}`,"JSON解析错误"),console.error("Failed to parse JSON string:",e,t),null}}function J(n){if(!n)return s&&(s.innerHTML="<p>错误：无法加载场景数据。</p>"),void(l&&(l.innerHTML=""));t=n,e.currentLocation=n.currentLocation,e.time=n.time,n.variableUpdates&&n.variableUpdates.length>0&&n.variableUpdates.forEach((t=>{!function(e,t,n,i){const o=t.split(".");let r=e;for(let e=0;e<o.length-1;e++){if(void 0===r[o[e]]||"object"!=typeof r[o[e]])return;r=r[o[e]]}const a=o[o.length-1];switch(n){case"设置":"object"==typeof r&&null!==r&&(r[a]=i);break;case"增加":"object"==typeof r&&null!==r&&"number"==typeof r[a]&&"number"==typeof i&&(r[a]+=i);break;case"减少":"object"==typeof r&&null!==r&&"number"==typeof r[a]&&"number"==typeof i&&(r[a]-=i);break;case"添加元素":"object"==typeof r&&null!==r&&Array.isArray(r[a])&&r[a].push(i);break;case"移除元素":if("object"==typeof r&&null!==r&&Array.isArray(r[a])){const e=r[a].indexOf(i);e>-1&&r[a].splice(e,1)}break;case"物品获得":if("inventory"===t&&Array.isArray(r.inventory)&&"object"==typeof i&&i.name&&"number"==typeof i.quantity){const e=r.inventory.find((e=>e.name===i.name));e?(e.quantity+=i.quantity,i.description&&!e.description&&(e.description=i.description)):r.inventory.push({name:i.name,quantity:i.quantity,description:i.description||""})}break;case"物品失去":if("inventory"===t&&Array.isArray(r.inventory)&&"object"==typeof i&&i.name&&"number"==typeof i.quantity){const e=r.inventory.findIndex((e=>e.name===i.name));e>-1&&(r.inventory[e].quantity-=i.quantity,r.inventory[e].quantity<=0&&r.inventory.splice(e,1))}}}(e,t.path,t.operation,t.value)})),function(e){if(s){if(s.innerHTML="",e.narrative.forEach((e=>{const t=document.createElement("p");let n=e.content.replace(/\n/g,"<br>");switch(e.type){case"description":default:t.innerHTML=n;break;case"dialogue":t.innerHTML=`<strong>${e.speaker||"某人"}${e.emotion?` (${e.emotion})`:""}:</strong> ${n}`;break;case"systemMessage":t.className="system-message",t.innerHTML=`<em>${n}</em>`;break;case"actionDescription":t.innerHTML=`<em>${e.actor||"某物"} ${n}</em>`;break;case"thought":t.className="thought-message",t.innerHTML=`<i>"${n}"</i>`}s&&s.appendChild(t)})),e.combatLog&&e.combatLog.length>0){const t=document.createElement("div");t.className="combat-log",t.innerHTML="<h4>战斗记录:</h4>"+e.combatLog.map((e=>`<p>${e.replace(/\n/g,"<br>")}</p>`)).join(""),s.appendChild(t)}if(e.enemies&&e.enemies.length>0){const t=document.createElement("div");t.className="enemies-display";let n="<h4>当前敌人:</h4><ul>";e.enemies.forEach((e=>{n+=`<li><strong>${e.name} (ID: ${e.id})</strong> - HP: ${e.hp.current}/${e.hp.max}, AC: ${e.ac}`,e.intent&&(n+=`, 意图: ${e.intent}`),e.statusEffects&&e.statusEffects.length>0&&(n+=`, 状态: ${e.statusEffects.join(", ")}`),n+="</li>"})),n+="</ul>",t.innerHTML=n,s.appendChild(t)}}}(n),B(),k(n.playerChoices)}const U="\x3c!-- PLAYER_STATE_START --\x3e",H="\x3c!-- PLAYER_STATE_END --\x3e";async function j(){if(null===n||"function"!=typeof setChatMessages)return;let i,o=`${U}\n${JSON.stringify(e,null,2)}\n${H}\n\n`;t&&(i=JSON.stringify(t)),i&&(o+=`查看系统\n##@@_ADVENTURE_BEGIN_@@##\n${i}\n##@@_ADVENTURE_END_@@##\n关闭系统`);try{await setChatMessages([{message_id:n,message:o}],{refresh:"affected"})}catch(e){}}function F(e){return{sceneType:"location",sceneTitle:"冒险的序章",currentLocation:e.currentLocation||"未知起点",time:e.time||"某个时刻",narrative:[{type:"description",content:"你已准备好踏上征程！这个世界的故事将根据你所激活的冒险模组（通常位于 RPG_Modules_Test.json 世界书中）展开。请选择你的第一个行动，让传奇开始！"}],playerChoices:[{id:"A",text:"根据我的模组设定，正式开始冒险！",actionCommand:"start_adventure_module"},{id:"B",text:"我应该先了解一下我所处的环境（基于模组）。",actionCommand:"survey_environment_module"},{id:"C",text:"查看我的角色状态。",actionCommand:"check_character_status"}]}}async function G(){if(!o||!r||!i)return;o.disabled=!0,o.textContent="正在加载角色...";const a=await async function(e){if("function"!=typeof triggerSlash)return null;const t="RPG_Modules_Test.json";try{const n=`/findentry file="${t}" "${e}"`,i=await triggerSlash(n);if(!i?.trim()||"[]"===i.trim())return null;let o=null;try{const e=JSON.parse(i);Array.isArray(e)&&e.length>0?o=e[0].toString():"string"==typeof e&&""!==e.trim()?o=e.trim():"number"==typeof e&&(o=e.toString())}catch(e){"string"==typeof i&&""!==i.trim()&&(o=i.trim())}if(!o)return null;const r=`/getentryfield file="${t}" field=content "${o}"`,a=await triggerSlash(r);return a?.trim()?a:null}catch(e){return null}}("PLAYER");if(a)try{const s=JSON.parse(a);if(s?.name&&s.attributes){e=s,t=null;const o=F(e);if(J(o),null!==n){await j();const e="dndRPG_history.json",t=`scene_init_${Date.now()}`,n=`查看系统\n##@@_ADVENTURE_BEGIN_@@##\n${JSON.stringify(o)}\n##@@_ADVENTURE_END_@@##\n关闭系统`;try{const i=`/createentry file="${e}" key="${t}" ${n}`,o=await triggerSlash(i);if(o&&""!==o.trim()){const t=`/setentryfield file="${e}" uid="${o.trim()}" field=constant true`;await triggerSlash(t)}}catch(e){q("error",`保存初始历史场景失败: ${e.message}`,"新游戏错误")}}B(),i.style.display="none",r.style.display="flex",null!==n&&await j()}else e={name:"新冒险者",race:"人类",class:"平民",level:1,exp:0,hp:{current:10,max:10},ac:10,currency:{gold:10,silver:0,copper:0},attributes:{strength:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},dexterity:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},constitution:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},intelligence:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},wisdom:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},charisma:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0}},proficiencies:["匕首"],skills:[],spellSlots:{},equippedSpells:[],equipment:[{name:"平民服装",type:"衣物",equipped:!0},{name:"匕首",type:"武器",equipped:!0,properties:["灵巧","轻型"]}],inventory:[{name:"背包",quantity:1},{name:"火绒盒",quantity:1},{name:"口粮",quantity:2,description:"一天的食物"}],activeQuests:[],exhaustion:0,time:"第一天 清晨",currentLocation:"旅途的起点",spellcastingAbility:"INT"},o.disabled=!1,o.textContent="开始新冒险 (从世界书加载角色)"}catch(t){e={name:"新冒险者",race:"人类",class:"平民",level:1,exp:0,hp:{current:10,max:10},ac:10,currency:{gold:10,silver:0,copper:0},attributes:{strength:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},dexterity:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},constitution:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},intelligence:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},wisdom:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},charisma:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0}},proficiencies:["匕首"],skills:[],spellSlots:{},equippedSpells:[],equipment:[{name:"平民服装",type:"衣物",equipped:!0},{name:"匕首",type:"武器",equipped:!0,properties:["灵巧","轻型"]}],inventory:[{name:"背包",quantity:1},{name:"火绒盒",quantity:1},{name:"口粮",quantity:2,description:"一天的食物"}],activeQuests:[],exhaustion:0,time:"第一天 清晨",currentLocation:"旅途的起点",spellcastingAbility:"INT"},o.disabled=!1,o.textContent="开始新冒险 (从世界书加载角色)"}else{e={name:"新冒险者",race:"人类",class:"平民",level:1,exp:0,hp:{current:10,max:10},ac:10,currency:{gold:10,silver:0,copper:0},attributes:{strength:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},dexterity:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},constitution:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},intelligence:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},wisdom:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},charisma:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0}},proficiencies:["匕首"],skills:[],spellSlots:{},equippedSpells:[],equipment:[{name:"平民服装",type:"衣物",equipped:!0},{name:"匕首",type:"武器",equipped:!0,properties:["灵巧","轻型"]}],inventory:[{name:"背包",quantity:1},{name:"火绒盒",quantity:1},{name:"口粮",quantity:2,description:"一天的食物"}],activeQuests:[],exhaustion:0,time:"第一天 清晨",currentLocation:"旅途的起点",spellcastingAbility:"INT"},t=null;const a=F(e);if(J(a),B(),i.style.display="none",r.style.display="flex",null!==n){await j();const e="dndRPG_history.json",t=`scene_init_default_${Date.now()}`,n=`查看系统\n##@@_ADVENTURE_BEGIN_@@##\n${JSON.stringify(a)}\n##@@_ADVENTURE_END_@@##\n关闭系统`;try{const i=`/createentry file="${e}" key="${t}" ${n}`,o=await triggerSlash(i);if(o&&""!==o.trim()){const t=`/setentryfield file="${e}" uid="${o.trim()}" field=constant true`;await triggerSlash(t)}}catch(e){q("error",`保存默认初始历史场景失败: ${e.message}`,"新游戏错误")}}else o.disabled=!1,o.textContent="开始新冒险 (从世界书加载角色)"}}async function P(){setTimeout((async()=>{if(function(){try{"undefined"!=typeof window&&"undefined"!=typeof parent&&parent!==window&&["$","toastr","triggerSlash","getLastMessageId","setChatMessages"].forEach((e=>{void 0===window[e]&&void 0!==parent[e]&&(window[e]=parent[e])}))}catch(e){}}(),i=document.getElementById("start-screen-container"),o=document.getElementById("start-new-game-button"),r=document.getElementById("adventure-log-container"),a=document.getElementById("player-status-area"),s=document.getElementById("main-narrative-area"),l=document.getElementById("action-choices-area"),c=document.getElementById("health"),d=document.getElementById("location"),u=document.getElementById("time"),m=document.getElementById("char-name-display"),p=document.getElementById("char-race-class-display"),y=document.getElementById("char-level-display"),f=document.getElementById("ac-display"),g=document.getElementById("attr-str-display"),b=document.getElementById("attr-dex-display"),$=document.getElementById("attr-con-display"),_=document.getElementById("attr-int-display"),E=document.getElementById("attr-wis-display"),h=document.getElementById("attr-cha-display"),N=document.getElementById("currency-gold-display"),S=document.getElementById("exp-display"),A=document.getElementById("exhaustion-display"),L=document.getElementById("toggle-char-sheet-button"),v=document.getElementById("detailed-character-sheet"),x=document.getElementById("proficiencies-display"),w=document.getElementById("skills-display"),T=document.getElementById("spell-slots-display"),I=document.getElementById("equipped-spells-display"),D=document.getElementById("equipment-display"),C=document.getElementById("inventory-display"),M=document.getElementById("active-quests-display"),!(i&&o&&r&&a&&s&&l&&L&&v))return;L.addEventListener("click",(()=>{if(v&&L){const e="none"===v.style.display;v.style.display=e?"block":"none",L.textContent=e?"隐藏详细角色卡":"显示详细角色卡"}})),o.addEventListener("click",G);const k=await async function(){if("function"!=typeof getLastMessageId||"function"!=typeof triggerSlash)return{playerStateLoadedFromMsg:!1,sceneDataLoadedFromMsg:!1};const i=getLastMessageId();if(n=void 0===i?null:i,null===n)return{playerStateLoadedFromMsg:!1,sceneDataLoadedFromMsg:!1};let o;try{o=await triggerSlash(`/messages ${n}`)}catch(e){return{playerStateLoadedFromMsg:!1,sceneDataLoadedFromMsg:!1}}if(!o?.trim())return{playerStateLoadedFromMsg:!1,sceneDataLoadedFromMsg:!1};let r=!1,a=!1;t=null,console.log("[AdvLogV2 Load] Starting loadGameState...");const s=o.match(new RegExp(`${U}\\n([\\s\\S]*?)\\n${H}`));if(s?.[1])try{const t=JSON.parse(s[1]);t?.name&&t.attributes&&(e=t,r=!0)}catch(e){console.error("[AdvLogV2 Load] Error parsing PlayerState JSON:",e,s[1]),q("error","解析玩家状态JSON失败。","加载错误")}const l=o.match(/##@@_ADVENTURE_BEGIN_@@##([\s\S]*?)##@@_ADVENTURE_END_@@##/);if(console.log("[AdvLogV2 Load Debug] Attempting to match ##@@_ADVENTURE_BEGIN_@@##...##@@_ADVENTURE_END_@@##"),console.log("[AdvLogV2 Load Debug] plotMatch result:",l?l.length:null),l&&l[1]){console.log('[AdvLogV2 Load] Found "##@@_ADVENTURE_BEGIN_@@##...##@@_ADVENTURE_END_@@##" block, attempting to extract JSON string...');const e=l[1].trim();if(console.log("[AdvLogV2 Load Debug] Extracted JSON string from new marker block (first 200 chars):",e.substring(0,200)+"..."),e){const n=O(e);n?(t=n,a=!0,console.log("[AdvLogV2 Load] Successfully parsed and loaded current scene from new marker block.")):(q("error",'解析 "##@@_ADVENTURE_BEGIN_@@##...##@@_ADVENTURE_END_@@##" 块中的JSON失败。查看控制台获取详细原始JSON。',"加载错误"),console.error("[AdvLogV2 Load] Failed to parse JSON from new marker block. Raw string was:",e))}else q("warning",'"##@@_ADVENTURE_BEGIN_@@##...##@@_ADVENTURE_END_@@##" 块中提取的JSON字符串为空。',"加载警告"),console.warn("[AdvLogV2 Load] Extracted JSON string from new marker block is empty.")}else console.warn('[AdvLogV2 Load] Could not find or extract content from "##@@_ADVENTURE_BEGIN_@@##...##@@_ADVENTURE_END_@@##" block in the main host message.');return{playerStateLoadedFromMsg:r,sceneDataLoadedFromMsg:a}}();k.playerStateLoadedFromMsg&&k.sceneDataLoadedFromMsg&&t?(J(t),B(),i.style.display="none",r.style.display="flex"):(i.style.display="flex",r.style.display="none",B())}),200)}"complete"===document.readyState||"interactive"===document.readyState?P():window.addEventListener("DOMContentLoaded",P);</script></body></html>