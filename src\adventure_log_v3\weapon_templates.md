# 武器模板数据

本文档定义了冒险日志 v3 中使用的武器模板数据。这些数据基于 D&D 5e 规则，用于客户端进行伤害计算和属性判断。

## 武器模板JSON结构

每个武器模板对象包含以下字段：

*   `name_zh`: 武器的中文名称 (例如："长剑")
*   `name_en`: 武器的英文名称 (例如："Longsword")
*   `category`: 武器分类 ("简易近战", "军用近战", "简易远程", "军用远程")
*   `damage`: 基础伤害骰 (例如："1d8")
*   `damageType`: 伤害类型 (例如："挥砍", "穿刺", "钝击")
*   `properties`: 一个字符串数组，包含武器的属性 (例如：`["多用 (1d10)", "灵巧"]`)。中文属性名尽量与规则书保持一致。
*   `weight`: 重量 (磅)
*   `cost`: 价格 (例如："15 GP")
*   `mastery`: 精通属性 (例如："削弱") (此字段为 PHB 2024 新增，初期可选择性实现)

## 示例武器模板数据 (JSON格式)

```json
[
  {
    "name_zh": "匕首",
    "name_en": "Dagger",
    "category": "简易近战",
    "damage": "1d4",
    "damageType": "穿刺",
    "properties": ["灵巧", "轻型", "投掷 (射程 20/60)"],
    "weight": "1磅",
    "cost": "2 GP",
    "mastery": "迅击"
  },
  {
    "name_zh": "短棒",
    "name_en": "Club",
    "category": "简易近战",
    "damage": "1d4",
    "damageType": "钝击",
    "properties": ["轻型"],
    "weight": "2磅",
    "cost": "1 SP",
    "mastery": "缓速"
  },
  {
    "name_zh": "手斧",
    "name_en": "Handaxe",
    "category": "简易近战",
    "damage": "1d6",
    "damageType": "挥砍",
    "properties": ["轻型", "投掷 (射程 20/60)"],
    "weight": "2磅",
    "cost": "5 GP",
    "mastery": "侵扰"
  },
  {
    "name_zh": "标枪",
    "name_en": "Javelin",
    "category": "简易近战",
    "damage": "1d6",
    "damageType": "穿刺",
    "properties": ["投掷 (射程 30/120)"],
    "weight": "2磅",
    "cost": "5 SP",
    "mastery": "缓速"
  },
  {
    "name_zh": "轻锤",
    "name_en": "Light Hammer",
    "category": "简易近战",
    "damage": "1d4",
    "damageType": "钝击",
    "properties": ["轻型", "投掷 (射程 20/60)"],
    "weight": "2磅",
    "cost": "2 GP",
    "mastery": "迅击"
  },
  {
    "name_zh": "硬头锤",
    "name_en": "Mace",
    "category": "简易近战",
    "damage": "1d6",
    "damageType": "钝击",
    "properties": [],
    "weight": "4磅",
    "cost": "5 GP",
    "mastery": "削弱"
  },
  {
    "name_zh": "长棍",
    "name_en": "Quarterstaff",
    "category": "简易近战",
    "damage": "1d6",
    "damageType": "钝击",
    "properties": ["多用 (1d8)"],
    "weight": "4磅",
    "cost": "2 SP",
    "mastery": "失衡"
  },
  {
    "name_zh": "矛",
    "name_en": "Spear",
    "category": "简易近战",
    "damage": "1d6",
    "damageType": "穿刺",
    "properties": ["投掷 (射程 20/60)", "多用 (1d8)"],
    "weight": "3磅",
    "cost": "1 GP",
    "mastery": "削弱"
  },
  {
    "name_zh": "轻弩",
    "name_en": "Light Crossbow",
    "category": "简易远程",
    "damage": "1d8",
    "damageType": "穿刺",
    "properties": ["弹药 (射程 80/320；弩矢)", "装填", "双手"],
    "weight": "5磅",
    "cost": "25 GP",
    "mastery": "缓速"
  },
  {
    "name_zh": "短弓",
    "name_en": "Shortbow",
    "category": "简易远程",
    "damage": "1d6",
    "damageType": "穿刺",
    "properties": ["弹药 (射程 80/320；箭矢)", "双手"],
    "weight": "2磅",
    "cost": "25 GP",
    "mastery": "侵扰"
  },
  {
    "name_zh": "投石索",
    "name_en": "Sling",
    "category": "简易远程",
    "damage": "1d4",
    "damageType": "钝击",
    "properties": ["弹药 (射程 30/120；弹丸)"],
    "weight": "—",
    "cost": "1 SP",
    "mastery": "缓速"
  },
  {
    "name_zh": "战斧",
    "name_en": "Battleaxe",
    "category": "军用近战",
    "damage": "1d8",
    "damageType": "挥砍",
    "properties": ["多用 (1d10)"],
    "weight": "4磅",
    "cost": "10 GP",
    "mastery": "失衡"
  },
  {
    "name_zh": "长剑",
    "name_en": "Longsword",
    "category": "军用近战",
    "damage": "1d8",
    "damageType": "挥砍",
    "properties": ["多用 (1d10)"],
    "weight": "3磅",
    "cost": "15 GP",
    "mastery": "削弱"
  },
  {
    "name_zh": "巨剑",
    "name_en": "Greatsword",
    "category": "军用近战",
    "damage": "2d6",
    "damageType": "挥砍",
    "properties": ["重型", "双手"],
    "weight": "6磅",
    "cost": "50 GP",
    "mastery": "擦掠"
  },
  {
    "name_zh": "刺剑",
    "name_en": "Rapier",
    "category": "军用近战",
    "damage": "1d8",
    "damageType": "穿刺",
    "properties": ["灵巧"],
    "weight": "2磅",
    "cost": "25 GP",
    "mastery": "侵扰"
  },
  {
    "name_zh": "短剑",
    "name_en": "Shortsword",
    "category": "军用近战",
    "damage": "1d6",
    "damageType": "穿刺",
    "properties": ["灵巧", "轻型"],
    "weight": "2磅",
    "cost": "10 GP",
    "mastery": "侵扰"
  },
  {
    "name_zh": "长弓",
    "name_en": "Longbow",
    "category": "军用远程",
    "damage": "1d8",
    "damageType": "穿刺",
    "properties": ["弹药 (射程 150/600；箭矢)", "重型", "双手"],
    "weight": "2磅",
    "cost": "50 GP",
    "mastery": "缓速"
  }
]
```

**注意**:
*   以上列表仅为示例，实际游戏中可能需要更完整的武器列表。
*   武器的“精通”属性是 PHB 2024 的新内容，其具体机制实现可能较为复杂，初期可选择性实现或简化。
*   魔法武器将在基础模板上增加额外属性，例如 `bonus: { attack: number, damage: number }` 或 `additionalDamage: [{ dice: string, type: string }]`。
