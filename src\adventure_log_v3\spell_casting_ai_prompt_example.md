# 法术施放AI提示词示例

## 法术施放命令格式

当玩家施放法术时，系统会发送以下格式的命令给AI，**数值计算已在本地完成**：

### 命令类型：

1. **攻击法术**（使用现有攻击系统）：
   ```
   text: "[攻击 哥布林 使用 圣火术 DC15]"
   actionCommand: "attack:圣火术:哥布林"
   ```

2. **豁免法术**（本地已计算豁免结果）：
   ```
   text: "对 哥布林 施放 魅惑人类 [感知豁免]"
   actionCommand: "cast_spell_save:魅惑人类:哥布林:1"
   checkFeedbackToAI: " [魅惑人类 法术 DC13 感知豁免 目标投骰15 豁免成功]"
   ```

3. **工具法术**（无需检定）：
   ```
   text: "施放 光亮术 (场景中无目标)"
   actionCommand: "cast_spell_utility:光亮术:0"
   checkFeedbackToAI: " [施放 光亮术 法术成功]"
   ```

4. **需要选择目标**：
   ```
   text: "施放 魅惑人类 (场景中有: 哥布林、兽人，请选择目标)"
   actionCommand: "cast_spell_choose_target:魅惑人类:1"
   ```

## AI处理法术施放的建议

### 1. 重要：数值计算已完成
**AI无需进行任何数值计算**，包括：
- 攻击检定（已通过现有攻击系统处理）
- 豁免检定（已在本地计算完成）
- 伤害计算（已通过现有伤害系统处理）
- 法术槽消耗（已在本地完成）

### 2. AI的职责
AI只需要：
- **描述法术效果的叙述**
- **根据检定结果描述成功/失败的情况**
- **推进剧情发展**

### 3. 根据checkFeedbackToAI处理

#### 攻击法术（通过现有攻击系统）
```
checkFeedbackToAI: " [攻击命中 DC15 wisdom(圣火术) 投骰1d20[12]+5(感知)+2(熟练)=19] 造成 6 点 光耀 伤害 (1d8[6])。"
```
AI应该：
- 描述法术的视觉效果
- 根据命中/失手描述结果
- 如果命中，描述伤害效果

#### 豁免法术（本地已计算）
```
checkFeedbackToAI: " [魅惑人类 法术 DC13 感知豁免 目标投骰15 豁免成功]"
```
AI应该：
- 描述法术施放过程
- 根据豁免成功/失败描述效果
- 豁免成功：目标抵抗了法术
- 豁免失败：目标受到完整效果

#### 工具法术（无检定）
```
checkFeedbackToAI: " [施放 光亮术 法术成功]"
```
AI应该：
- 描述法术的功能效果
- 描述环境变化

### 4. 法术效果示例

#### 攻击法术（圣火术）- 命中
```json
{
  "narrative": [
    {
      "type": "description",
      "content": "你举起手，神圣的火焰从天而降，精准地击中了哥布林。哥布林发出痛苦的嚎叫，身上冒起了白色的烟雾，受到了神圣力量的灼烧。"
    }
  ]
}
```

#### 攻击法术（圣火术）- 失手
```json
{
  "narrative": [
    {
      "type": "description",
      "content": "你举起手召唤神圣火焰，但哥布林敏捷地闪避开来，火焰击中了它身后的地面，留下一个焦黑的印记。"
    }
  ]
}
```

#### 豁免法术（魅惑人类）- 豁免成功
```json
{
  "narrative": [
    {
      "type": "description",
      "content": "你凝视着哥布林的眼睛，施展魅惑法术。哥布林的眼神短暂地变得迷茫，但随即摇了摇头，成功抵抗住了你的魅惑。"
    }
  ]
}
```

#### 豁免法术（魅惑人类）- 豁免失败
```json
{
  "narrative": [
    {
      "type": "description",
      "content": "你凝视着哥布林的眼睛，施展魅惑法术。哥布林的眼神变得迷茫而友善，它现在将你视为朋友。"
    }
  ]
}
```

#### 工具法术（光亮术）
```json
{
  "narrative": [
    {
      "type": "description",
      "content": "你轻抚手中的物品，它开始发出温暖的白光，照亮了周围20尺的区域。黑暗中的细节变得清晰可见，为你的探索提供了便利。"
    }
  ]
}
```

## 法术类型处理指南

### 戏法（0环法术）
- 不消耗法术槽
- 可以无限使用
- 伤害随角色等级提升

### 攻击法术
- 需要进行法术攻击检定
- 命中后计算伤害
- 可能有额外效果

### 豁免法术
- 目标进行相应属性的豁免检定
- 成功豁免可能减少或无效化效果
- 失败豁免承受完整效果

### 工具法术
- 通常不需要攻击检定或豁免
- 产生特定的功能效果
- 持续时间各不相同

## 重要注意事项

### AI无需处理的内容（已在本地完成）
1. **法术槽消耗**：已自动处理
2. **攻击检定**：通过现有攻击系统处理
3. **豁免检定**：已在本地计算完成
4. **伤害计算**：已通过现有伤害系统处理
5. **数值验证**：法术可用性、目标有效性等已验证

### AI需要处理的内容
1. **叙述描述**：法术的视觉效果、声音、感觉等
2. **剧情推进**：根据法术效果推进故事发展
3. **环境变化**：法术对环境的影响
4. **NPC反应**：目标和其他NPC对法术的反应
5. **后续效果**：持续性法术的描述

### 特殊情况处理
- **需要选择目标**：AI可以让玩家明确选择，或智能选择合适目标
- **环境限制**：考虑法术在当前环境中的合理性
- **创意应用**：鼓励玩家创造性地使用法术
