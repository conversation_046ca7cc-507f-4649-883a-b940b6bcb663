/**
 * 完整版法术解析脚本 - 解决编码问题并生成所有法术
 */

const fs = require('fs');
const path = require('path');

/**
 * 解析HTML文件中的法术数据 - 修复编码问题
 */
function parseSpellsFromHTML(htmlContent, level) {
  const spells = [];
  
  // 使用更宽松的正则表达式匹配法术条目
  const spellPattern = /<H4[^>]*id="([^"]*)"[^>]*>([^<]*)<\/H4>/g;
  
  let match;
  while ((match = spellPattern.exec(htmlContent)) !== null) {
    const [, id, nameWithEn] = match;
    
    // 跳过空的或无效的条目
    if (!id || !nameWithEn || nameWithEn.trim() === '') continue;
    
    // 解析中英文名称 - 处理各种格式
    let name_zh = nameWithEn.trim();
    let name_en = id.replace(/_/g, ' ');
    
    // 尝试分离中英文名称
    const nameMatch = nameWithEn.match(/^([^（(]+)(?:[（(]([^）)]+)[）)])?/);
    if (nameMatch) {
      name_zh = nameMatch[1].trim();
      if (nameMatch[2]) {
        name_en = nameMatch[2].trim();
      }
    }
    
    // 创建基础法术对象
    const spell = {
      name_zh: name_zh,
      name_en: name_en,
      level: level,
      school: '未知',
      casting_time: '1 动作',
      range: '未知',
      components: ['V', 'S'],
      duration: '立即',
      description_short: `${name_zh}是一个${level === 0 ? '戏法' : level + '环法术'}。`,
      description_long: `${name_zh}(${name_en})是一个${level === 0 ? '戏法' : level + '环法术'}，具体效果请参考玩家手册。`
    };
    
    // 尝试从HTML中提取更多信息
    try {
      const spellContentPattern = new RegExp(`<H4[^>]*id="${id.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}"[^>]*>.*?(?=<H4|$)`, 's');
      const contentMatch = htmlContent.match(spellContentPattern);
      
      if (contentMatch) {
        const content = contentMatch[0];
        
        // 解析学派信息
        const schoolMatch = content.match(/<EM>([^<]*)<\/EM>/);
        if (schoolMatch) {
          const schoolInfo = schoolMatch[1];
          // 提取学派名称（通常是第一个词）
          const schoolParts = schoolInfo.split(/[，,\s]+/);
          if (schoolParts.length > 0) {
            const schoolName = schoolParts[0].trim();
            if (schoolName && schoolName !== '戏法' && schoolName !== '法术') {
              spell.school = schoolName;
            }
          }
        }
        
        // 解析施法时间
        const castingTimeMatch = content.match(/施法时间：<\/STRONG>([^<]*)/);
        if (castingTimeMatch) {
          spell.casting_time = castingTimeMatch[1].trim();
        }
        
        // 解析施法距离
        const rangeMatch = content.match(/施法距离：<\/STRONG>([^<]*)/);
        if (rangeMatch) {
          spell.range = rangeMatch[1].trim();
        }
        
        // 解析法术成分
        const componentsMatch = content.match(/法术成分：<\/STRONG>([^<]*)/);
        if (componentsMatch) {
          const componentStr = componentsMatch[1].trim();
          spell.components = componentStr.split(/[，,]+/).map(c => c.trim()).filter(c => c);
        }
        
        // 解析持续时间
        const durationMatch = content.match(/持续时间：<\/STRONG>([^<]*)/);
        if (durationMatch) {
          spell.duration = durationMatch[1].trim();
        }
        
        // 解析伤害骰子
        const damageMatches = content.match(/(\d+d\d+(?:\+\d+)?)/g);
        if (damageMatches && damageMatches.length > 0) {
          spell.damage = damageMatches[0]; // 取第一个伤害骰子
        }
        
        // 解析伤害类型
        const damageTypes = ['火焰', '寒冷', '闪电', '雷鸣', '强酸', '毒素', '光耀', '黯蚀', '力场', '穿刺', '挥砍', '钝击', '心灵'];
        for (const damageType of damageTypes) {
          if (content.includes(damageType)) {
            spell.damage_type = damageType;
            break;
          }
        }
        
        // 解析攻击类型
        if (content.includes('远程法术攻击')) {
          spell.attack_type = '法术攻击 (远程)';
        } else if (content.includes('近战法术攻击')) {
          spell.attack_type = '法术攻击 (近战)';
        } else if (content.includes('自动命中')) {
          spell.attack_type = '自动命中';
        }
        
        // 解析豁免检定
        const saveAttributes = ['力量', '敏捷', '体质', '智力', '感知', '魅力'];
        for (const attr of saveAttributes) {
          if (content.includes(attr + '豁免')) {
            spell.save = {
              attribute: attr,
              effect_on_success: content.includes('伤害减半') ? '伤害减半' : '无效果'
            };
            break;
          }
        }
        
        // 解析升环效果
        const higherLevelMatch = content.match(/升环施法：<\/STRONG>([^<]*)/);
        if (higherLevelMatch) {
          spell.higher_level_cast = {
            per_slot_above_base: `每高于${level}环的法术位`,
            effect: higherLevelMatch[1].trim()
          };
        }
        
        // 解析戏法升级
        if (level === 0) {
          const scalingMatch = content.match(/戏法强化：<\/STRONG>([^<]*)/);
          if (scalingMatch) {
            spell.scaling = parseCantripsScaling(scalingMatch[1]);
          }
        }
        
        // 提取并清理描述
        const cleanContent = content.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
        const descParts = cleanContent.split('。');
        if (descParts.length > 1) {
          spell.description_short = descParts[0] + '。';
          if (spell.description_short.length > 100) {
            spell.description_short = spell.description_short.substring(0, 100) + '...';
          }
          spell.description_long = cleanContent.length > 500 ? cleanContent.substring(0, 500) + '...' : cleanContent;
        }
      }
    } catch (error) {
      console.warn(`解析法术 ${name_zh} 的详细信息时出错:`, error.message);
    }
    
    spells.push(spell);
  }
  
  return spells;
}

/**
 * 解析戏法升级信息
 */
function parseCantripsScaling(scalingText) {
  const scaling = {};
  
  // 匹配类似 "5级：2d10，11级：3d10，17级：4d10" 的模式
  const matches = scalingText.matchAll(/(\d+)级：([^，。]+)/g);
  for (const match of matches) {
    scaling[match[1]] = match[2].trim();
  }
  
  return scaling;
}

/**
 * 主解析函数
 */
async function parseAllSpells() {
  const allSpells = [];
  const baseDir = '../../../DND5e_chm-main/DND5e_chm-main/玩家手册2024/法术详述';
  
  // 解析每个等级的法术文件
  for (let level = 0; level <= 9; level++) {
    const filename = `${level}环.htm`;
    const filepath = path.join(baseDir, filename);
    
    try {
      console.log(`正在解析 ${level}环法术...`);
      
      // 尝试不同的编码方式读取文件
      let htmlContent;
      try {
        // 先尝试UTF-8
        htmlContent = fs.readFileSync(filepath, 'utf-8');
      } catch (error) {
        try {
          // 再尝试GBK/GB2312
          htmlContent = fs.readFileSync(filepath, 'binary');
          // 简单的字符替换来处理常见的编码问题
          htmlContent = htmlContent.replace(/[\x80-\xFF]/g, '?');
        } catch (error2) {
          console.error(`无法读取文件 ${filepath}:`, error2.message);
          continue;
        }
      }
      
      const spells = parseSpellsFromHTML(htmlContent, level);
      
      console.log(`${level}环法术解析完成，共 ${spells.length} 个法术`);
      allSpells.push(...spells);
    } catch (error) {
      console.error(`解析 ${level}环法术时出错:`, error.message);
    }
  }
  
  console.log(`总共解析了 ${allSpells.length} 个法术`);
  return allSpells;
}

/**
 * 生成世界书格式的法术数据
 */
function generateWorldBookEntries(spells) {
  const entries = {};
  let uid = 1000;
  
  // 按等级分组法术
  const spellsByLevel = {};
  spells.forEach(spell => {
    if (!spellsByLevel[spell.level]) {
      spellsByLevel[spell.level] = [];
    }
    spellsByLevel[spell.level].push(spell);
  });
  
  // 为每个等级创建法术包条目
  for (const [level, levelSpells] of Object.entries(spellsByLevel)) {
    const levelNum = parseInt(level);
    const levelName = levelNum === 0 ? '戏法' : `${levelNum}环法术`;
    
    entries[uid] = {
      uid: uid,
      key: [`SPELLS_LEVEL_${levelNum}`, `${levelName}`, `LEVEL_${levelNum}_SPELLS`],
      keysecondary: [],
      comment: `${levelName}合集 (${levelSpells.length}个法术)`,
      content: JSON.stringify(levelSpells, null, 2),
      constant: true,
      vectorized: false,
      selective: true,
      selectiveLogic: 0,
      addMemo: true,
      order: 100,
      position: 0,
      disable: false,
      excludeRecursion: false,
      preventRecursion: false,
      delayUntilRecursion: false,
      probability: 100,
      useProbability: true,
      depth: 4,
      group: "",
      groupOverride: false,
      groupWeight: 100,
      scanDepth: null,
      caseSensitive: null,
      matchWholeWords: null,
      useGroupScoring: null,
      automationId: "",
      role: null,
      sticky: 0,
      cooldown: 0,
      delay: 0,
      displayIndex: uid
    };
    uid++;
  }
  
  // 按学派分组法术
  const spellsBySchool = {};
  spells.forEach(spell => {
    const school = spell.school || '未知';
    if (!spellsBySchool[school]) {
      spellsBySchool[school] = [];
    }
    spellsBySchool[school].push(spell);
  });
  
  // 为主要学派创建条目
  const majorSchools = ['塑能', '咒法', '预言', '惑控', '幻术', '死灵', '防护', '变化'];
  majorSchools.forEach(school => {
    if (spellsBySchool[school] && spellsBySchool[school].length > 0) {
      entries[uid] = {
        uid: uid,
        key: [`SPELLS_${school}`, `${school}法术`, `${school.toUpperCase()}_SPELLS`],
        keysecondary: [],
        comment: `${school}学派法术 (${spellsBySchool[school].length}个法术)`,
        content: JSON.stringify(spellsBySchool[school], null, 2),
        constant: true,
        vectorized: false,
        selective: true,
        selectiveLogic: 0,
        addMemo: true,
        order: 100,
        position: 0,
        disable: false,
        excludeRecursion: false,
        preventRecursion: false,
        delayUntilRecursion: false,
        probability: 100,
        useProbability: true,
        depth: 4,
        group: "",
        groupOverride: false,
        groupWeight: 100,
        scanDepth: null,
        caseSensitive: null,
        matchWholeWords: null,
        useGroupScoring: null,
        automationId: "",
        role: null,
        sticky: 0,
        cooldown: 0,
        delay: 0,
        displayIndex: uid
      };
      uid++;
    }
  });
  
  // 创建完整法术库条目
  entries[uid] = {
    uid: uid,
    key: ["SPELLS_ALL", "所有法术", "ALL_SPELLS", "法术库", "完整法术库"],
    keysecondary: [],
    comment: `完整法术库 (${spells.length}个法术)`,
    content: JSON.stringify(spells, null, 2),
    constant: true,
    vectorized: false,
    selective: true,
    selectiveLogic: 0,
    addMemo: true,
    order: 100,
    position: 0,
    disable: false,
    excludeRecursion: false,
    preventRecursion: false,
    delayUntilRecursion: false,
    probability: 100,
    useProbability: true,
    depth: 4,
    group: "",
    groupOverride: false,
    groupWeight: 100,
    scanDepth: null,
    caseSensitive: null,
    matchWholeWords: null,
    useGroupScoring: null,
    automationId: "",
    role: null,
    sticky: 0,
    cooldown: 0,
    delay: 0,
    displayIndex: uid
  };
  
  return { entries };
}

// 主函数
function main() {
  parseAllSpells().then(spells => {
    const worldBook = generateWorldBookEntries(spells);
    
    // 保存到文件
    const outputPath = '../../../DND5e_Complete_Spells_WorldBook.json';
    fs.writeFileSync(outputPath, JSON.stringify(worldBook, null, 2), 'utf-8');
    console.log(`完整版世界书文件已生成: ${outputPath}`);
    console.log(`包含 ${spells.length} 个法术，分为 ${Object.keys(worldBook.entries).length} 个世界书条目`);
    
    // 显示法术统计
    const levelCounts = {};
    const schoolCounts = {};
    spells.forEach(spell => {
      levelCounts[spell.level] = (levelCounts[spell.level] || 0) + 1;
      schoolCounts[spell.school] = (schoolCounts[spell.school] || 0) + 1;
    });
    
    console.log('\n法术等级分布:');
    for (let level = 0; level <= 9; level++) {
      const count = levelCounts[level] || 0;
      const levelName = level === 0 ? '戏法' : `${level}环`;
      console.log(`${levelName}: ${count}个法术`);
    }
    
    console.log('\n法术学派分布:');
    Object.entries(schoolCounts).sort((a, b) => b[1] - a[1]).forEach(([school, count]) => {
      console.log(`${school}: ${count}个法术`);
    });
  }).catch(console.error);
}

if (require.main === module) {
  main();
}
