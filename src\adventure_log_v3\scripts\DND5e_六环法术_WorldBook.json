{"entries": {"1006": {"uid": 1006, "key": ["SPELLS_LEVEL_6", "六环法术", "DND5E_六环法术"], "keysecondary": [], "comment": "DND5e 六环法术合集 (34个法术)", "content": "[\n  {\n    \"id\": \"Arcane_Gate\",\n    \"name_zh\": \"ÃØ·¨ÃÅ£üArcane Gate\",\n    \"name_en\": \"Arcane Gate\",\n    \"raw_name\": \"ÃØ·¨ÃÅ£üArcane Gate\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Blade_Barrier\",\n    \"name_zh\": \"½£ÈÐ»¤±Ú£üBlade Barrier\",\n    \"name_en\": \"Blade Barrier\",\n    \"raw_name\": \"½£ÈÐ»¤±Ú£üBlade Barrier\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Chain_Lightning\",\n    \"name_zh\": \"Á¬ËøÉÁµç£üChain Lightning\",\n    \"name_en\": \"Chain Lightning\",\n    \"raw_name\": \"Á¬ËøÉÁµç£üChain Lightning\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Circle_of_Death\",\n    \"name_zh\": \"ËÀÍö·¨Õó£üCircle of Death\",\n    \"name_en\": \"Circle of Death\",\n    \"raw_name\": \"ËÀÍö·¨Õó£üCircle of Death\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Conjure_Fey\",\n    \"name_zh\": \"Ö<PERSON>»½Ñý¾«£üConjure Fey\",\n    \"name_en\": \"Conjure Fey\",\n    \"raw_name\": \"Öä»½Ñý¾«£üConjure Fey\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Contingency\",\n    \"name_zh\": \"´¥·¢Êõ£üContingency\",\n    \"name_en\": \"Contingency\",\n    \"raw_name\": \"´¥·¢Êõ£üContingency\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Create_Undead\",\n    \"name_zh\": \"»½ÆðÍöÁé£üCreate Undead\",\n    \"name_en\": \"Create Undead\",\n    \"raw_name\": \"»½ÆðÍöÁé£üCreate Undead\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Disintegrate\",\n    \"name_zh\": \"½âÀëÊõ£üDisintegrate\",\n    \"name_en\": \"Disintegrate\",\n    \"raw_name\": \"½âÀëÊõ£üDisintegrate\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Drawmij's_Instant_Summons\",\n    \"name_zh\": \"×¿Ä·¼ªË²¼äÕÙ»½£üDrawmij's Instant Summons\",\n    \"name_en\": \"Drawmij's Instant Summons\",\n    \"raw_name\": \"×¿Ä·¼ªË²¼äÕÙ»½£üDrawmij's Instant Summons\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Eyebite\",\n    \"name_zh\": \"ÉãÐÄÄ¿¹â£üEyebite\",\n    \"name_en\": \"Eyebite\",\n    \"raw_name\": \"ÉãÐÄÄ¿¹â£üEyebite\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Find_the_Path\",\n    \"name_zh\": \"Ñ°Â·Êõ£üFind the Path\",\n    \"name_en\": \"Find the Path\",\n    \"raw_name\": \"Ñ°Â·Êõ£üFind the Path\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Flesh_to_Stone\",\n    \"name_zh\": \"Ê¯»¯Êõ£üFlesh to Stone\",\n    \"name_en\": \"Flesh to Stone\",\n    \"raw_name\": \"Ê¯»¯Êõ£üFlesh to Stone\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Forbiddance\",\n    \"name_zh\": \"½ûÖÆÊõ£üForbiddance\",\n    \"name_en\": \"Forbiddance\",\n    \"raw_name\": \"½ûÖÆÊõ£üForbiddance\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Globe_of_Invulnerability\",\n    \"name_zh\": \"·¨ÊõÎÞÐ§½á½ç£üGlobe of Invulnerability\",\n    \"name_en\": \"Globe of Invulnerability\",\n    \"raw_name\": \"·¨ÊõÎÞÐ§½á½ç£üGlobe of Invulnerability\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Guards_and_Wards\",\n    \"name_zh\": \"Í­Ç½Ìú±Ú£üGuards and Wards\",\n    \"name_en\": \"Guards and Wards\",\n    \"raw_name\": \"Í­Ç½Ìú±Ú£üGuards and Wards\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Harm\",\n    \"name_zh\": \"ÖØÉËÊõ£üHarm\",\n    \"name_en\": \"Harm\",\n    \"raw_name\": \"ÖØÉËÊõ£üHarm\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Heal\",\n    \"name_zh\": \"Ò½ÁÆÊõ£üHeal\",\n    \"name_en\": \"Heal\",\n    \"raw_name\": \"Ò½ÁÆÊõ£üHeal\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Heroes'_Feast\",\n    \"name_zh\": \"Ó¢ÐÛÑç£üHeroes' Feast\",\n    \"name_en\": \"Heroes' Feast\",\n    \"raw_name\": \"Ó¢ÐÛÑç£üHeroes' Feast\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Magic_Jar\",\n    \"name_zh\": \"Ä§»êºø£üMagic Jar\",\n    \"name_en\": \"Magic Jar\",\n    \"raw_name\": \"Ä§»êºø£üMagic Jar\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Mass_Suggestion\",\n    \"name_zh\": \"ÈºÌå°µÊ¾Êõ£üMass Suggestion\",\n    \"name_en\": \"Mass Suggestion\",\n    \"raw_name\": \"ÈºÌå°µÊ¾Êõ£üMass Suggestion\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Move_Earth\",\n    \"name_zh\": \"µØ¶¯Êõ£üMove Earth\",\n    \"name_en\": \"Move Earth\",\n    \"raw_name\": \"µØ¶¯Êõ£üMove Earth\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Otiluke's_Freezing_Sphere\",\n    \"name_zh\": \"Å·ÌáÂ·¿Ë±ù·â·¨Çò£üOtiluke's Freezing Sphere\",\n    \"name_en\": \"Otiluke's Freezing Sphere\",\n    \"raw_name\": \"Å·ÌáÂ·¿Ë±ù·â·¨Çò£üOtiluke's Freezing Sphere\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Otto's_Irresistible_Dance\",\n    \"name_zh\": \"°ÂÍ¼ÃÔÎè£üOtto's Irresistible Dance\",\n    \"name_en\": \"Otto's Irresistible Dance\",\n    \"raw_name\": \"°ÂÍ¼ÃÔÎè£üOtto's Irresistible Dance\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Planar_Ally\",\n    \"name_zh\": \"Òì½çÊÄÃË£üPlanar Ally\",\n    \"name_en\": \"Planar Ally\",\n    \"raw_name\": \"Òì½çÊÄÃË£üPlanar Ally\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Programmed_Illusion\",\n    \"name_zh\": \"Ô¤ÖÃ»ÃÏó£üProgrammed Illusion\",\n    \"name_en\": \"Programmed Illusion\",\n    \"raw_name\": \"Ô¤ÖÃ»ÃÏó£üProgrammed Illusion\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Summon_Fiend\",\n    \"name_zh\": \"Ð°Ä§ÕÙ»½Êõ£üSummon Fiend\",\n    \"name_en\": \"Summon Fiend\",\n    \"raw_name\": \"Ð°Ä§ÕÙ»½Êõ£üSummon Fiend\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Sunbeam\",\n    \"name_zh\": \"ÑôÑ×ÉäÏß£üSunbeam\",\n    \"name_en\": \"Sunbeam\",\n    \"raw_name\": \"ÑôÑ×ÉäÏß£üSunbeam\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Tasha's_Bubbling_Cauldron\",\n    \"name_zh\": \"ËþÉ¯ÅÝÅÝÛáÛö£üTasha's Bubbling Cauldron\",\n    \"name_en\": \"Tasha's Bubbling Cauldron\",\n    \"raw_name\": \"ËþÉ¯ÅÝÅÝÛáÛö£üTasha's Bubbling Cauldron\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Transport_via_Plants\",\n    \"name_zh\": \"Ä¾¶ÝÊõ£üTransport via Plants\",\n    \"name_en\": \"Transport via Plants\",\n    \"raw_name\": \"Ä¾¶ÝÊõ£üTransport via Plants\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"True_Seeing\",\n    \"name_zh\": \"ÕæÖªÊõ£üTrue Seeing\",\n    \"name_en\": \"True Seeing\",\n    \"raw_name\": \"ÕæÖªÊõ£üTrue Seeing\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Wall_of_Ice\",\n    \"name_zh\": \"±ùÇ½Êõ£üWall of Ice\",\n    \"name_en\": \"Wall of Ice\",\n    \"raw_name\": \"±ùÇ½Êõ£üWall of Ice\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Wall_of_Thorns\",\n    \"name_zh\": \"¼¬Ç½Êõ£üWall of Thorns\",\n    \"name_en\": \"Wall of Thorns\",\n    \"raw_name\": \"¼¬Ç½Êõ£üWall of Thorns\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Wind_Walk\",\n    \"name_zh\": \"Óù·ç¶øÐÐ£üWind Walk\",\n    \"name_en\": \"Wind Walk\",\n    \"raw_name\": \"Óù·ç¶øÐÐ£üWind Walk\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  },\n  {\n    \"id\": \"Word_of_Recall\",\n    \"name_zh\": \"»Ø·µÕæÑÔ£üWord of Recall\",\n    \"name_en\": \"Word of Recall\",\n    \"raw_name\": \"»Ø·µÕæÑÔ£üWord of Recall\",\n    \"level\": 6,\n    \"level_name\": \"六环法术\"\n  }\n]", "constant": true, "vectorized": false, "selective": true, "selectiveLogic": 0, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "delayUntilRecursion": false, "probability": 100, "useProbability": true, "depth": 4, "group": "", "groupOverride": false, "groupWeight": 100, "scanDepth": null, "caseSensitive": null, "matchWholeWords": null, "useGroupScoring": null, "automationId": "", "role": null, "sticky": 0, "cooldown": 0, "delay": 0, "displayIndex": 1006}}}