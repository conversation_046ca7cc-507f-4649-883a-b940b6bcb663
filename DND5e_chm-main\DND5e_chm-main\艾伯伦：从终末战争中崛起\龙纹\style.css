body,html {
	font-size: 10pt;
	font-family: Arial, Helvetica, sans-serif, 宋体;
	color: black;
	text-justify-trim:punctuation;
}

h2 {
	font-variant: small-caps;
	border-bottom: 0.1em solid #ccc080 !important;
	color: darkred;
}

h1 {
	font-variant: small-caps;
	margin-bottom: 0%;
	color: #ccc080;
	text-align: center;
}

p {
	margin: 0.15em;
	padding: 0.15em;
}
tr.colored {
	background-color: #cbe0d9;
}
tr.light {
	background-color: #fafbf6;
}

table {
	/* width: 400px; */
	border-collapse: collapse;
	text-align: center;
	table-layout: fixed;
	max-width: 95%;
	min-width: 40%
}

blockquote {
	/* background-color: #e2f4fb; */
	background-color: #fafbf6;
	color: #aaa06b;
	box-shadow: 0 0.2rem 0.2rem 0;
	border: 1px solid #eaebe6;
}

.note {
	color: grey;
	font-style: italic;
}

.spell {
	text-decoration: none;
	font-style: italic;
	font-weight: bold;
	color: #704cd9;
	position: relative;
	display: inline;
}
td>.spell {
	display: inline-block;
}
.spell .tooltip {
	visibility: hidden;
	position: absolute;
	background-color: #fdf1dc;
	border-top: 1px solid #e69a28;
	border-bottom: 1px solid #e69a28;
}

.spell:hover .tooltip {
	visibility: visible;
}

td {
	word-wrap: break-word;
}

td.divider div{
	background-color: #822000;
}

td.content {
	text-align: left;
}

.conditions {
	color: black;
	font-weight: bold;
	white-space: nowrap;
}

.traits {
	font-weight: bold;
	font-style: italic;
	white-space: nowrap;
}

.listitem {
	font-weight: bold;
}

th.spellHead {
	color: darkred;
	font-variant: small-caps;
	text-align: left;
	white-space: nowrap;
}

ul {
	text-align: left;
}

caption {
	font-weight: bold;
	text-align: center;
	width: 80%
}

hr.hr-double-arrow {
    color: #ccc080;
    border: double;
    border-width: 3px 5px;
    border-color: #ccc080 transparent;
    height: 2px;
    overflow: visible;
    margin-left: 20px;
    margin-right: 20px;
    position: relative;
}
hr.hr-double-arrow:before, 
hr.hr-double-arrow:after {
    content: '';
    position: absolute;
    width: 5px; height: 5px;
    border-width: 0 3px 3px 0;
    border-style: double;
    top: -3px;
    background: radial-gradient(2px at 1px 1px, currentColor 2px, transparent 0) no-repeat;
}
hr.hr-double-arrow:before {
    transform: rotate(-45deg);
    left: -20px;
}
hr.hr-double-arrow:after {
    transform: rotate(135deg);
    right: -20px;
}

.magicitem {
	font-style: italic;
}

ul {
	margin-top: 0;
	margin-bottom: 0;
	padding-top: 0;
	padding-bottom: 0;
}