// 测试法术等级过滤功能
import { getMaxAvailableSpellLevel } from './spells';
import { PlayerState } from './types';

// 模拟1级牧师的状态（只有1环法术槽）
const testPlayerState: PlayerState = {
  name: "影心",
  level: 1,
  spellSlots: {
    "1": {
      current: 1,
      max: 2
    }
  }
} as PlayerState;

// 测试函数
function testSpellLevelFilter() {
  console.log('=== 测试法术等级过滤 ===');
  
  const maxLevel = getMaxAvailableSpellLevel(testPlayerState);
  console.log(`1级牧师的最高可用法术等级: ${maxLevel}`);
  
  // 应该返回 1，因为只有1环法术槽
  if (maxLevel === 1) {
    console.log('✅ 测试通过：正确识别最高法术等级为1环');
  } else {
    console.log(`❌ 测试失败：期望1，实际${maxLevel}`);
  }
  
  // 测试没有法术槽的情况
  const noSpellsState: PlayerState = {
    name: "战士",
    level: 1,
    spellSlots: {}
  } as PlayerState;
  
  const noSpellsMaxLevel = getMaxAvailableSpellLevel(noSpellsState);
  console.log(`无法术槽角色的最高可用法术等级: ${noSpellsMaxLevel}`);
  
  if (noSpellsMaxLevel === 0) {
    console.log('✅ 测试通过：正确识别无法术槽角色只能使用戏法');
  } else {
    console.log(`❌ 测试失败：期望0，实际${noSpellsMaxLevel}`);
  }
  
  // 测试高等级法术使用者
  const highLevelState: PlayerState = {
    name: "高级法师",
    level: 5,
    spellSlots: {
      "1": { current: 4, max: 4 },
      "2": { current: 3, max: 3 },
      "3": { current: 2, max: 2 }
    }
  } as PlayerState;
  
  const highLevelMaxLevel = getMaxAvailableSpellLevel(highLevelState);
  console.log(`5级法师的最高可用法术等级: ${highLevelMaxLevel}`);
  
  if (highLevelMaxLevel === 3) {
    console.log('✅ 测试通过：正确识别最高法术等级为3环');
  } else {
    console.log(`❌ 测试失败：期望3，实际${highLevelMaxLevel}`);
  }
}

// 导出测试函数，可以在控制台中调用
(window as any).testSpellLevelFilter = testSpellLevelFilter;

console.log('法术等级过滤测试已加载，在控制台中运行 testSpellLevelFilter() 来测试');
