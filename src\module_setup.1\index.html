<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模组设置</title>
</head>
<body>
    <div id="module-setup-container">
        <h1>游戏模组创作</h1>
        
        <div class="form-group">
            <label for="module-title-input">模组标题 (将作为世界书条目Key):</label>
            <input type="text" id="module-title-input" placeholder="例如：失落的遗迹探险 或 附录章节A">
        </div>

        <div class="form-group">
            <label for="module-content-textarea">模组内容 (或 AI生成提示):</label>
            <textarea id="module-content-textarea" rows="10" placeholder="在此输入模组的详细描述，或输入给AI的创作指令（如：'详细描述一个被遗忘的森林神殿，包括它的历史和守护者。'）"></textarea>
        </div>
        
        <div class="button-group">
            <button id="save-custom-module-button">保存当前内容到世界书</button>
            <button id="ai-generate-content-button">AI辅助创作并保存</button>
        </div>
        <div id="output-message" style="margin-bottom: 20px;"></div>

        <hr style="margin: 20px 0;">

        <div class="button-group navigation-buttons">
            <button id="go-to-character-creation-button">创建新人物</button>
        </div>
    </div>

    <div id="character-creation-container" style="display: none; background-color: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.1); max-width: 700px; margin: 20px auto;">
        <h1>创建人物</h1>
        
        <form id="character-sheet-form">
            <!-- Section 1: Basic Info -->
            <div class="form-section">
                <div class="form-row">
                    <div class="form-group form-group-inline">
                        <label for="char-name">角色名</label>
                        <input type="text" id="char-name" name="charName">
                    </div>
                    <div class="form-group form-group-inline">
                        <label for="char-player">玩家</label>
                        <input type="text" id="char-player" name="charPlayer">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group form-group-inline">
                        <label for="char-age">年龄</label>
                        <input type="text" id="char-age" name="charAge">
                    </div>
                    <div class="form-group form-group-inline">
                        <label for="char-gender">性别</label>
                        <select id="char-gender" name="charGender">
                            <option value="male">男性</option>
                            <option value="female">女性</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                    <div class="form-group form-group-inline">
                        <label for="char-alignment">阵营</label>
                        <input type="text" id="char-alignment" name="charAlignment">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group form-group-inline">
                        <label for="char-faith">信仰</label>
                        <input type="text" id="char-faith" name="charFaith" style="width: 100%;">
                    </div>
                </div>
                <div class="form-row">
                     <div class="form-group form-group-inline">
                        <label for="char-height">身高</label>
                        <input type="text" id="char-height" name="charHeight">
                    </div>
                    <div class="form-group form-group-inline">
                        <label for="char-weight">体重</label>
                        <input type="text" id="char-weight" name="charWeight">
                    </div>
                    <div class="form-group form-group-inline">
                        <label for="char-xp">经验值</label>
                        <input type="number" id="char-xp" name="charXp" value="0">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group form-group-inline">
                        <label for="char-currency-gold">金币 (GP)</label>
                        <input type="number" id="char-currency-gold" name="charCurrencyGold" value="0" min="0">
                    </div>
                    <div class="form-group form-group-inline">
                        <label for="char-currency-silver">银币 (SP)</label>
                        <input type="number" id="char-currency-silver" name="charCurrencySilver" value="0" min="0">
                    </div>
                    <div class="form-group form-group-inline">
                        <label for="char-currency-copper">铜币 (CP)</label>
                        <input type="number" id="char-currency-copper" name="charCurrencyCopper" value="0" min="0">
                    </div>
                </div>
            </div>

            <!-- Section 2: Descriptions -->
            <div class="form-section">
                <div class="form-group">
                    <label for="char-appearance">外貌描写</label>
                    <textarea id="char-appearance" name="charAppearance" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label for="char-story">角色故事</label>
                    <textarea id="char-story" name="charStory" rows="5"></textarea>
                </div>
            </div>
            
            <!-- Section 3: Traits, Ideals, Bonds, Flaws, Proficiencies -->
            <div class="form-section">
                <div class="form-row">
                    <div class="form-group form-group-inline">
                        <label for="char-race">种族</label>
                        <select id="char-race" name="charRace">
                            <option value="人类">人类</option>
                            <option value="精灵">精灵</option>
                            <option value="矮人">矮人</option>
                            <option value="半身人">半身人</option>
                            <option value="龙裔">龙裔</option>
                            <option value="侏儒">侏儒</option>
                            <option value="提夫林">提夫林</option>
                            <option value="兽人">兽人</option>
                            <option value="阿斯莫">阿斯莫</option>
                            <option value="歌利亚">歌利亚</option>
                        </select>
                    </div>
                    <div class="form-group form-group-inline">
                        <label for="char-background">背景</label>
                        <input type="text" id="char-background" name="charBackground">
                    </div>
                </div>

                <!-- Skills Section -->
                <div class="form-section skills-section">
                    <h4>技能熟练</h4>
                    <div class="skills-grid">
                        <!-- Dynamically populated by JS or predefined -->
                        <div class="skill-item"><input type="checkbox" id="skill-athletics" name="skillAthleticsProf" data-skill-name="运动" data-attribute="strength"><label for="skill-athletics">运动 (力量)</label><input type="number" name="skillAthleticsMod" value="0" class="skill-modifier-input"><input type="text" name="skillAthleticsFinal" readonly class="skill-final-value"></div>
                        <div class="skill-item"><input type="checkbox" id="skill-acrobatics" name="skillAcrobaticsProf" data-skill-name="体操" data-attribute="dexterity"><label for="skill-acrobatics">体操 (敏捷)</label><input type="number" name="skillAcrobaticsMod" value="0" class="skill-modifier-input"><input type="text" name="skillAcrobaticsFinal" readonly class="skill-final-value"></div>
                        <div class="skill-item"><input type="checkbox" id="skill-sleight-of-hand" name="skillSleightOfHandProf" data-skill-name="巧手" data-attribute="dexterity"><label for="skill-sleight-of-hand">巧手 (敏捷)</label><input type="number" name="skillSleightOfHandMod" value="0" class="skill-modifier-input"><input type="text" name="skillSleightOfHandFinal" readonly class="skill-final-value"></div>
                        <div class="skill-item"><input type="checkbox" id="skill-stealth" name="skillStealthProf" data-skill-name="隐匿" data-attribute="dexterity"><label for="skill-stealth">隐匿 (敏捷)</label><input type="number" name="skillStealthMod" value="0" class="skill-modifier-input"><input type="text" name="skillStealthFinal" readonly class="skill-final-value"></div>
                        <div class="skill-item"><input type="checkbox" id="skill-arcana" name="skillArcanaProf" data-skill-name="奥秘" data-attribute="intelligence"><label for="skill-arcana">奥秘 (智力)</label><input type="number" name="skillArcanaMod" value="0" class="skill-modifier-input"><input type="text" name="skillArcanaFinal" readonly class="skill-final-value"></div>
                        <div class="skill-item"><input type="checkbox" id="skill-history" name="skillHistoryProf" data-skill-name="历史" data-attribute="intelligence"><label for="skill-history">历史 (智力)</label><input type="number" name="skillHistoryMod" value="0" class="skill-modifier-input"><input type="text" name="skillHistoryFinal" readonly class="skill-final-value"></div>
                        <div class="skill-item"><input type="checkbox" id="skill-investigation" name="skillInvestigationProf" data-skill-name="调查" data-attribute="intelligence"><label for="skill-investigation">调查 (智力)</label><input type="number" name="skillInvestigationMod" value="0" class="skill-modifier-input"><input type="text" name="skillInvestigationFinal" readonly class="skill-final-value"></div>
                        <div class="skill-item"><input type="checkbox" id="skill-nature" name="skillNatureProf" data-skill-name="自然" data-attribute="intelligence"><label for="skill-nature">自然 (智力)</label><input type="number" name="skillNatureMod" value="0" class="skill-modifier-input"><input type="text" name="skillNatureFinal" readonly class="skill-final-value"></div>
                        <div class="skill-item"><input type="checkbox" id="skill-religion" name="skillReligionProf" data-skill-name="宗教" data-attribute="intelligence"><label for="skill-religion">宗教 (智力)</label><input type="number" name="skillReligionMod" value="0" class="skill-modifier-input"><input type="text" name="skillReligionFinal" readonly class="skill-final-value"></div>
                        <div class="skill-item"><input type="checkbox" id="skill-animal-handling" name="skillAnimalHandlingProf" data-skill-name="驯兽" data-attribute="wisdom"><label for="skill-animal-handling">驯兽 (感知)</label><input type="number" name="skillAnimalHandlingMod" value="0" class="skill-modifier-input"><input type="text" name="skillAnimalHandlingFinal" readonly class="skill-final-value"></div>
                        <div class="skill-item"><input type="checkbox" id="skill-insight" name="skillInsightProf" data-skill-name="洞悉" data-attribute="wisdom"><label for="skill-insight">洞悉 (感知)</label><input type="number" name="skillInsightMod" value="0" class="skill-modifier-input"><input type="text" name="skillInsightFinal" readonly class="skill-final-value"></div>
                        <div class="skill-item"><input type="checkbox" id="skill-medicine" name="skillMedicineProf" data-skill-name="医药" data-attribute="wisdom"><label for="skill-medicine">医药 (感知)</label><input type="number" name="skillMedicineMod" value="0" class="skill-modifier-input"><input type="text" name="skillMedicineFinal" readonly class="skill-final-value"></div>
                        <div class="skill-item"><input type="checkbox" id="skill-perception" name="skillPerceptionProf" data-skill-name="察觉" data-attribute="wisdom"><label for="skill-perception">察觉 (感知)</label><input type="number" name="skillPerceptionMod" value="0" class="skill-modifier-input"><input type="text" name="skillPerceptionFinal" readonly class="skill-final-value"></div>
                        <div class="skill-item"><input type="checkbox" id="skill-survival" name="skillSurvivalProf" data-skill-name="求生" data-attribute="wisdom"><label for="skill-survival">求生 (感知)</label><input type="number" name="skillSurvivalMod" value="0" class="skill-modifier-input"><input type="text" name="skillSurvivalFinal" readonly class="skill-final-value"></div>
                        <div class="skill-item"><input type="checkbox" id="skill-deception" name="skillDeceptionProf" data-skill-name="欺瞒" data-attribute="charisma"><label for="skill-deception">欺瞒 (魅力)</label><input type="number" name="skillDeceptionMod" value="0" class="skill-modifier-input"><input type="text" name="skillDeceptionFinal" readonly class="skill-final-value"></div>
                        <div class="skill-item"><input type="checkbox" id="skill-intimidation" name="skillIntimidationProf" data-skill-name="威吓" data-attribute="charisma"><label for="skill-intimidation">威吓 (魅力)</label><input type="number" name="skillIntimidationMod" value="0" class="skill-modifier-input"><input type="text" name="skillIntimidationFinal" readonly class="skill-final-value"></div>
                        <div class="skill-item"><input type="checkbox" id="skill-performance" name="skillPerformanceProf" data-skill-name="表演" data-attribute="charisma"><label for="skill-performance">表演 (魅力)</label><input type="number" name="skillPerformanceMod" value="0" class="skill-modifier-input"><input type="text" name="skillPerformanceFinal" readonly class="skill-final-value"></div>
                        <div class="skill-item"><input type="checkbox" id="skill-persuasion" name="skillPersuasionProf" data-skill-name="游说" data-attribute="charisma"><label for="skill-persuasion">游说 (魅力)</label><input type="number" name="skillPersuasionMod" value="0" class="skill-modifier-input"><input type="text" name="skillPersuasionFinal" readonly class="skill-final-value"></div>
                    </div>
                    <button type="button" id="clear-skills-button" class="clear-section-button">清空技能修正与熟练</button>
                </div>
                
                <div class="form-group">
                    <label for="char-tool-proficiencies-text">工具与其它熟练项 (逗号分隔)</label>
                    <input type="text" id="char-tool-proficiencies-text" name="charToolProficienciesText" placeholder="例如：盗贼工具, 草药学工具套件, 轻甲, 简单武器">
                </div>

                <!-- Section for Equipment -->
                <div class="form-section dynamic-list-section">
                    <h4>已装备物品</h4>
                    <div id="equipment-list-container">
                        <!-- Equipment items will be added here by JS -->
                    </div>
                    <button type="button" id="add-equipment-button" class="add-item-button">添加装备</button>
                </div>

                <!-- Section for Inventory -->
                <div class="form-section dynamic-list-section">
                    <h4>初始物品 (背包)</h4>
                    <div id="inventory-list-container">
                        <!-- Inventory items will be added here by JS -->
                    </div>
                    <button type="button" id="add-inventory-item-button" class="add-item-button">添加物品</button>
                </div>

                <div class="form-group">
                    <label for="char-personality-traits">特点</label>
                    <textarea id="char-personality-traits" name="charPersonalityTraits" rows="2"></textarea>
                </div>
                <div class="form-group">
                    <label for="char-ideals">理想</label>
                    <textarea id="char-ideals" name="charIdeals" rows="2"></textarea>
                </div>
                <div class="form-group">
                    <label for="char-bonds">牵绊</label>
                    <textarea id="char-bonds" name="charBonds" rows="2"></textarea>
                </div>
                <div class="form-group">
                    <label for="char-flaws">缺点</label>
                    <textarea id="char-flaws" name="charFlaws" rows="2"></textarea>
                </div>
            </div>

            <!-- Section 4: Attributes -->
            <div class="form-section">
                <div class="attribute-grid">
                    <!-- Strength -->
                    <div class="attribute-row">
                        <strong>力量 (STR)</strong>
                        <div class="attribute-inputs">
                            <div><label for="attr-str-base">初始值</label><input type="number" id="attr-str-base" name="attrStrBase" value="10" min="1" max="20"></div>
                            <div><label for="attr-str-race">种族</label><input type="number" id="attr-str-race" name="attrStrRace" value="0"></div>
                            <div><label for="attr-str-mod">修正</label><input type="number" id="attr-str-mod" name="attrStrMod" value="0"></div>
                            <div><label for="attr-str-final">最终值</label><input type="text" id="attr-str-final" name="attrStrFinal" readonly></div>
                        </div>
                    </div>
                    <!-- Dexterity -->
                    <div class="attribute-row">
                        <strong>敏捷 (DEX)</strong>
                        <div class="attribute-inputs">
                            <div><label for="attr-dex-base">初始值</label><input type="number" id="attr-dex-base" name="attrDexBase" value="10" min="1" max="20"></div>
                            <div><label for="attr-dex-race">种族</label><input type="number" id="attr-dex-race" name="attrDexRace" value="0"></div>
                            <div><label for="attr-dex-mod">修正</label><input type="number" id="attr-dex-mod" name="attrDexMod" value="0"></div>
                            <div><label for="attr-dex-final">最终值</label><input type="text" id="attr-dex-final" name="attrDexFinal" readonly></div>
                        </div>
                    </div>
                    <!-- Constitution -->
                    <div class="attribute-row">
                        <strong>体质 (CON)</strong>
                        <div class="attribute-inputs">
                            <div><label for="attr-con-base">初始值</label><input type="number" id="attr-con-base" name="attrConBase" value="10" min="1" max="20"></div>
                            <div><label for="attr-con-race">种族</label><input type="number" id="attr-con-race" name="attrConRace" value="0"></div>
                            <div><label for="attr-con-mod">修正</label><input type="number" id="attr-con-mod" name="attrConMod" value="0"></div>
                            <div><label for="attr-con-final">最终值</label><input type="text" id="attr-con-final" name="attrConFinal" readonly></div>
                        </div>
                    </div>
                    <!-- Intelligence -->
                    <div class="attribute-row">
                        <strong>智力 (INT)</strong>
                        <div class="attribute-inputs">
                            <div><label for="attr-int-base">初始值</label><input type="number" id="attr-int-base" name="attrIntBase" value="10" min="1" max="20"></div>
                            <div><label for="attr-int-race">种族</label><input type="number" id="attr-int-race" name="attrIntRace" value="0"></div>
                            <div><label for="attr-int-mod">修正</label><input type="number" id="attr-int-mod" name="attrIntMod" value="0"></div>
                            <div><label for="attr-int-final">最终值</label><input type="text" id="attr-int-final" name="attrIntFinal" readonly></div>
                        </div>
                    </div>
                    <!-- Wisdom -->
                    <div class="attribute-row">
                        <strong>感知 (WIS)</strong>
                        <div class="attribute-inputs">
                            <div><label for="attr-wis-base">初始值</label><input type="number" id="attr-wis-base" name="attrWisBase" value="10" min="1" max="20"></div>
                            <div><label for="attr-wis-race">种族</label><input type="number" id="attr-wis-race" name="attrWisRace" value="0"></div>
                            <div><label for="attr-wis-mod">修正</label><input type="number" id="attr-wis-mod" name="attrWisMod" value="0"></div>
                            <div><label for="attr-wis-final">最终值</label><input type="text" id="attr-wis-final" name="attrWisFinal" readonly></div>
                        </div>
                    </div>
                    <!-- Charisma -->
                    <div class="attribute-row">
                        <strong>魅力 (CHA)</strong>
                        <div class="attribute-inputs">
                            <div><label for="attr-cha-base">初始值</label><input type="number" id="attr-cha-base" name="attrChaBase" value="10" min="1" max="20"></div>
                            <div><label for="attr-cha-race">种族</label><input type="number" id="attr-cha-race" name="attrChaRace" value="0"></div>
                            <div><label for="attr-cha-mod">修正</label><input type="number" id="attr-cha-mod" name="attrChaMod" value="0"></div>
                            <div><label for="attr-cha-final">最终值</label><input type="text" id="attr-cha-final" name="attrChaFinal" readonly></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section 5: Class & Level -->
            <div class="form-section">
                 <div class="form-row">
                    <div class="form-group form-group-inline">
                        <label for="char-class-1">职业1</label>
                        <select id="char-class-1" name="charClass1">
                            <option value="战士">战士</option>
                            <option value="法师">法师</option>
                            <option value="牧师">牧师</option>
                            <option value="游荡者">游荡者</option>
                            <option value="野蛮人">野蛮人</option>
                            <option value="吟游诗人">吟游诗人</option>
                            <option value="圣武士">圣武士</option>
                            <option value="游侠">游侠</option>
                            <option value="术士">术士</option>
                            <option value="魔契师">魔契师</option>
                            <option value="武僧">武僧</option>
                            <option value="德鲁伊">德鲁伊</option>
                        </select>
                    </div>
                    <div class="form-group form-group-inline">
                        <label for="char-level-1">等级</label>
                        <input type="number" id="char-level-1" name="charLevel1" value="1" min="1">
                    </div>
                    <div class="form-group form-group-inline">
                        <label for="char-subclass-1">子职</label>
                        <input type="text" id="char-subclass-1" name="charSubclass1">
                    </div>
                </div>
            </div>

            <!-- Section 6: Spells -->
            <div class="form-section">
                <h3>法术</h3>
                <div class="form-row">
                    <div class="form-group form-group-inline">
                        <label for="spell-ability">施法关键属性</label>
                        <select id="spell-ability" name="spellAbility">
                            <option value="STR">力量</option>
                            <option value="DEX">敏捷</option>
                            <option value="CON">体质</option>
                            <option value="INT">智力</option>
                            <option value="WIS">感知</option>
                            <option value="CHA">魅力</option>
                        </select>
                    </div>
                    <div class="form-group form-group-inline">
                        <label for="spell-attack-bonus">法术攻击加值</label>
                        <input type="text" id="spell-attack-bonus" name="spellAttackBonus" readonly>
                    </div>
                    <div class="form-group form-group-inline">
                        <label for="spell-save-dc">法术豁免DC</label>
                        <input type="text" id="spell-save-dc" name="spellSaveDc" readonly>
                    </div>
                </div>
                
                <h4>戏法</h4>
                <div class="spell-list" id="cantrips-list">
                    <!-- Initial spell item removed -->
                </div>
                <datalist id="cantrips-datalist"></datalist>
                <button type="button" id="add-cantrip-button">添加戏法</button>

                <h4>一环法术 (法术位: <input type="number" id="spell-slots-1" name="spellSlots1" value="0" style="width: 50px;">)</h4>
                <div class="spell-list" id="level1-spells-list">
                     <!-- Initial spell item removed -->
                </div>
                <datalist id="level1-spells-datalist"></datalist>
                <button type="button" id="add-level1-spell-button">添加一环法术</button>
            </div>
        </form>

        <div class="form-section">
            <label for="ai-character-prompt-textarea">AI角色生成指导提示 (可选):</label>
            <textarea id="ai-character-prompt-textarea" rows="3" placeholder="例如：我想要一个擅长潜行和欺骗的半精灵游荡者，背景是街头顽童。"></textarea>
        </div>

        <div class="button-group" style="margin-top: 20px;">
            <button id="save-character-button" style="background-color: #17a2b8;">保存人物卡</button>
            <button id="ai-generate-character-button" style="background-color: #28a745;">AI生成角色</button>
            <button id="back-to-module-setup-button">返回模组设置</button>
        </div>
    </div>
</body>
</html>
