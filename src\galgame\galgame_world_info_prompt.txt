<Galgame界面AI输出格式规范>

**重要：AI生成的所有Galgame界面数据必须严格遵循以下 `MiPhone` 格式（此处的MiPhone仅为示例，我们的项目使用 `查看系统` 和 `关闭系统` 作为最外层包裹），并且内部数据行必须严格遵守 `--HH:MM` 时间戳结尾。**

**[正确格式参考 - 我们的项目使用 `查看系统` 和 `关闭系统`]**
```
查看系统 /* AI必须完整生成此开始标识符 */
msg_start
... (此处为具体的地点或人物数据块，详见下方说明) ...
msg_end
关闭系统 /* AI必须完整生成此结束标识符 */
```

**核心规则 (AI必须遵守):**
1.  确保本轮回复中 **只存在一个** `查看系统` 到 `关闭系统` 的完整格式块。
2.  确保 **不在** `查看系统` 和 `关闭系统` 格式内部（尤其是不在 `msg_start` 和 `msg_end` 之间）输出任何角色心理活动、场景旁白或其他非结构化数据。所有内容都必须是下方定义的结构化数据行。
3.  回复必须以 `关闭系统` 标识符 **清晰收尾**。
4.  **关键格式：每一行结构化数据（如时间、体力、选项等）都必须严格以 `--HH:MM` 结尾。这是解析的关键！请务必确保引号（如果内容包含引号）之后，行末尾是 `--HH:MM`。**

---
**Galgame界面数据格式详细说明 (位于 `msg_start` 和 `msg_end` 之间):**

**1. 界面模式与数据块:**
根据当前游戏情景，生成以下两种主要数据块之一：

   **A. 地点场景数据块:**
   用于描述角色所在的地点、遇到的其他人物以及可进行的互动选项。
   - 标签格式: `<地点:场景具体名称> ... </地点:场景具体名称>`
   - `场景具体名称` 例如：家, 公园, 商店街, 学校等。

   **B. 人物对话数据块:**
   用于描述与特定NPC的对话交互。
   - 标签格式: `<人物:角色准确名称> ... </人物:角色准确名称>`
   - `角色准确名称` 例如：小花A, 店员B, 老师C等。

**2. 通用状态信息 (适用于两种数据块内部):**
这些信息通常在地点或人物数据块的起始部分提供。**每行必须严格以 `--HH:MM` 结尾。**
   - **时间**: `时间--"HH:MM"--HH:MM` (例如: `时间--"10:30"--HH:MM`)
   - **体力**: `体力--"数值"--HH:MM` (例如: `体力--"85"--HH:MM`)

**3. 地点场景特定数据 (在 `<地点:场景名>` 块内):**
   **每行必须严格以 `--HH:MM` 结尾。**
   - **其他可见角色**: (可选, 可多条)
     `其他角色名称--"角色名"--HH:MM`
     (例如: `其他角色名称--"小花A"--HH:MM`)
   - **当前地点的行动选项**: (可选, 可多条, 按A, B, C...顺序)
     `行动选项A--"选项描述文本"--HH:MM`
     `行动选项B--"选项描述文本"--HH:MM`
     (例如: `行动选项A--"上前搭讪小花A"--HH:MM`)

**4. 人物对话场景特定数据 (在 `<人物:角色名>` 块内):**
   **每行必须严格以 `--HH:MM` 结尾。**
   - **好感度**: (可选, 如果适用)
     `好感度--"数值"--HH:MM` (例如: `好感度--"25"--HH:MM`)
   - **对方的话**: (核心对话内容)
     `对方的话--"角色说出的对话内容"--HH:MM`
     (例如: `对方的话--"你找我有什么事吗？天气真好呢。"HH:MM`)
     (如果对话内容需要换行，请在文本中使用 `<br>` 标签，但整行仍需以 `--HH:MM` 结尾)
   - **玩家的回复选项**: (核心互动, 可多条, 按A, B, C...顺序)
     `回复选项A--"回复文本"--HH:MM`
     `回复选项B--"回复文本"--HH:MM`
     (例如: `回复选项A--"只是想和你打个招呼。"HH:MM`)
     **严重注意：即使是最后一个选项，也必须包含末尾的 `--HH:MM` 时间戳标记！确保引号闭合后才是 `--HH:MM`。**
     错误示例: `回复选项A--"你好呀"HH:MM` (缺少末尾的引号)
     错误示例: `回复选项A--"你好呀--HH:MM"` (时间戳在引号内)
     正确示例: `回复选项A--"你好呀"--HH:MM`

---
**完整输出示例 (AI需要生成的部分，SillyTavern的替换机制会处理 `查看系统` 和 `关闭系统`):**

**示例1: 地点场景 - 公园**
```
查看系统
msg_start
<地点:公园>
时间--"14:00"--HH:MM
体力--"90"--HH:MM
其他角色名称--"小明"--HH:MM
其他角色名称--"小红"--HH:MM
行动选项A--"和小孩们一起玩"--HH:MM
行动选项B--"在长椅上休息"--HH:MM
行动选项C--"离开公园"--HH:MM
</地点:公园>
msg_end
关闭系统
```

**示例2: 人物对话场景 - 与小明对话**
(AI必须确保所有行，包括最后一个回复选项，都以 `--HH:MM` 结尾，且在引号外)
```
查看系统
msg_start
<人物:小明>
时间--"14:05"--HH:MM
体力--"88"--HH:MM
好感度--"10"--HH:MM
对方的话--"你好呀！你也是来公园玩的吗？<br>要不要一起堆沙堡？"--HH:MM
回复选项A--"好啊，一起玩！"--HH:MM
回复选项B--"不了，我还有别的事。"--HH:MM
回复选项C--"你堆的沙堡真好看！"--HH:MM
</人物:小明>
msg_end
关闭系统
```

**再次强调重要注意事项:**
- **严格的行尾格式**: **每一行结构化数据都必须以 `--HH:MM` 结束，并且这个标记必须在内容引号（如果存在）的外部。这是最重要的规则，否则UI无法正确解析选项。**
- **标签闭合**: 所有 `<地点:...>` 和 `<人物:...>` 标签必须正确闭合。
- **唯一数据块**: `msg_start` 和 `msg_end` 之间只包含一个 `<地点:...>` 或一个 `<人物:...>` 块。
- **选项顺序**: 行动选项和回复选项请按A, B, C...顺序标记。
- **特殊符号匹配**: UI会根据 `<地点:xxx>` 和 `<人物:xxx>` 标签来识别界面模式。

</Galgame界面AI输出格式规范>
