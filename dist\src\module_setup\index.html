<!doctype html><html lang="zh-CN"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1"><title>模组设置</title><style>body{font-family:sans-serif;padding:20px;background-color:#f4f4f4;color:#333}#module-setup-container{background-color:#fff;padding:20px;border-radius:8px;box-shadow:0 0 10px rgba(0,0,0,.1);max-width:700px;margin:auto}h1{color:#333;text-align:center;margin-bottom:20px}.form-group{margin-bottom:15px}.form-group label{display:block;margin-bottom:5px;font-weight:bold}.form-group input[type=text],.form-group textarea{width:calc(100% - 22px);padding:10px;border:1px solid #ccc;border-radius:4px;font-size:14px;box-sizing:border-box}.form-group textarea{resize:vertical;min-height:100px}.button-group{display:flex;gap:10px;margin-top:20px}button{flex-grow:1;padding:12px 15px;background-color:#007bff;color:#fff;border:none;border-radius:4px;cursor:pointer;font-size:16px}#ai-generate-content-button{background-color:#28a745}button:hover{opacity:.9}#output-message{margin-top:20px;padding:10px;border:1px solid #ddd;background-color:#e9e9e9;min-height:30px;border-radius:4px;word-wrap:break-word}hr{border:0;height:1px;background-color:#ccc;margin:30px 0}.navigation-buttons button{background-color:#5a6268}.navigation-buttons button:hover{background-color:#4a4f54}#character-creation-container h1{color:#333;text-align:center;margin-bottom:20px}#character-sheet-form .form-section{margin-bottom:25px;padding-bottom:15px;border-bottom:1px dashed #eee}#character-sheet-form .form-section:last-child{border-bottom:none;margin-bottom:0;padding-bottom:0}#character-sheet-form .form-row{display:flex;flex-wrap:wrap;gap:15px;margin-bottom:10px}#character-sheet-form .form-group-inline{flex:1 1 calc(50% - 15px);min-width:180px;display:flex;flex-direction:column}@media(max-width: 600px){#character-sheet-form .form-group-inline{flex-basis:100%}}#character-sheet-form .form-group-inline label{margin-bottom:3px;font-size:.9em}#character-sheet-form .form-group-inline input[type=text],#character-sheet-form .form-group-inline input[type=number],#character-sheet-form .form-group-inline select{width:100%;padding:8px;border:1px solid #ccc;border-radius:4px;font-size:14px;box-sizing:border-box}#character-sheet-form textarea{min-height:60px}#character-sheet-form .clear-section-button{background-color:#dc3545;font-size:.8em;padding:6px 10px;margin-top:10px;flex-grow:0;max-width:120px}#character-sheet-form .attribute-grid{display:flex;flex-direction:column;gap:15px;margin-bottom:10px}#character-sheet-form .attribute-row{display:flex;flex-direction:column;gap:5px;padding:10px;border:1px solid #eee;border-radius:4px}#character-sheet-form .attribute-row>strong{font-size:1.1em;margin-bottom:8px;color:#333}#character-sheet-form .attribute-inputs{display:grid;grid-template-columns:repeat(auto-fit, minmax(70px, 1fr));gap:5px 8px;align-items:center}#character-sheet-form .attribute-inputs>div{display:flex;flex-direction:column}#character-sheet-form .attribute-inputs label{font-size:.75em;color:#555;margin-bottom:1px;text-align:center}#character-sheet-form .attribute-inputs input[type=number],#character-sheet-form .attribute-inputs input[type=text]{width:100%;padding:6px;font-size:13px;text-align:center;border:1px solid #ccc;border-radius:4px;box-sizing:border-box}#character-sheet-form .attribute-inputs input[readonly]{background-color:#e9ecef;font-weight:bold}#character-sheet-form .attribute-grid .grid-header,#character-sheet-form .attribute-grid span:not(.op),#character-sheet-form .attribute-grid span.op{display:none}#character-sheet-form h3,#character-sheet-form h4{margin-top:20px;margin-bottom:10px;color:#495057}#character-sheet-form h4{font-size:1.1em}#character-sheet-form .spell-list{display:flex;flex-direction:column;gap:5px;margin-bottom:10px}#character-sheet-form .spell-list .spell-item{display:flex;align-items:center;gap:5px;margin-bottom:5px}#character-sheet-form .spell-list input[type=text]{flex-grow:1;padding:8px;border:1px solid #ccc;border-radius:4px}#character-sheet-form .spell-list .remove-spell-button{background-color:#6c757d;color:#fff;border:none;padding:2px 6px;font-size:.75em;line-height:1;cursor:pointer;border-radius:3px;flex-shrink:0;min-width:auto;flex-grow:0;margin-left:5px}#character-sheet-form .spell-list .remove-spell-button:hover{background-color:#5a6268;opacity:1}#character-sheet-form #add-cantrip-button,#character-sheet-form #add-level1-spell-button{background-color:#6c757d;font-size:.9em;padding:10px 15px;margin-top:8px;margin-bottom:15px;flex-grow:0;max-width:180px}#character-sheet-form .form-row .clear-section-button{margin-left:auto;align-self:center}#character-sheet-form .skills-section h4{margin-bottom:10px}#character-sheet-form .skills-section .skills-grid{display:grid;grid-template-columns:repeat(2, 1fr);gap:10px 20px;margin-bottom:10px}@media(max-width: 600px){#character-sheet-form .skills-section .skills-grid{grid-template-columns:repeat(1, 1fr)}}#character-sheet-form .skills-section .skill-item{display:flex;flex-wrap:wrap;align-items:center;gap:5px;padding:5px;border:1px solid #efefef;border-radius:4px}#character-sheet-form .skills-section .skill-item label{font-weight:normal;margin-left:5px;margin-right:auto;font-size:.9em;flex-basis:calc(100% - 100px)}#character-sheet-form .skills-section .skill-item input[type=checkbox]{margin-right:5px}#character-sheet-form .skills-section .skill-item .skill-modifier-input,#character-sheet-form .skills-section .skill-item .skill-final-value{width:50px;padding:5px;font-size:.9em;text-align:center;border:1px solid #ccc;border-radius:3px;box-sizing:border-box}#character-sheet-form .skills-section .skill-item .skill-final-value{background-color:#e9ecef;font-weight:bold}#character-sheet-form .skills-section .clear-section-button{margin-top:10px}#character-sheet-form .dynamic-list-section{margin-bottom:20px}#character-sheet-form .dynamic-list-section h4{margin-bottom:8px}#character-sheet-form .dynamic-list-section .dynamic-item-row{display:flex;gap:10px;margin-bottom:8px;align-items:center}#character-sheet-form .dynamic-list-section .dynamic-item-row input[type=text],#character-sheet-form .dynamic-list-section .dynamic-item-row input[type=number]{padding:8px;border:1px solid #ccc;border-radius:4px;font-size:14px;box-sizing:border-box}#character-sheet-form .dynamic-list-section .dynamic-item-row input[name\$="Name[]"]{flex-grow:2}#character-sheet-form .dynamic-list-section .dynamic-item-row input[name\$="Details[]"],#character-sheet-form .dynamic-list-section .dynamic-item-row input[name\$="Description[]"]{flex-grow:3}#character-sheet-form .dynamic-list-section .dynamic-item-row input[name\$="Quantity[]"]{width:70px;flex-grow:0;text-align:center}#character-sheet-form .dynamic-list-section .dynamic-item-row .remove-item-button{padding:6px 10px;font-size:.8em;background-color:#dc3545;color:#fff;border:none;border-radius:3px;cursor:pointer;flex-shrink:0}#character-sheet-form .dynamic-list-section .add-item-button{background-color:#5cb85c;font-size:.9em;padding:10px 15px;margin-top:5px;flex-grow:0;max-width:150px}
</style></head><body><div id="module-setup-container"><h1>游戏模组创作</h1><div class="form-group"><label for="module-title-input">模组标题 (将作为世界书条目Key):</label> <input id="module-title-input" placeholder="例如：失落的遗迹探险 或 附录章节A"></div><div class="form-group"><label for="module-content-textarea">模组内容 (或 AI生成提示):</label> <textarea id="module-content-textarea" rows="10" placeholder="在此输入模组的详细描述，或输入给AI的创作指令（如：'详细描述一个被遗忘的森林神殿，包括它的历史和守护者。'）"></textarea></div><div class="button-group"><button id="save-custom-module-button">保存当前内容到世界书</button> <button id="ai-generate-content-button">AI辅助创作并保存</button></div><div id="output-message" style="margin-bottom:20px"></div><hr style="margin:20px 0"><div class="button-group navigation-buttons"><button id="go-to-character-creation-button">创建新人物</button></div></div><div id="character-creation-container" style="display:none;background-color:#fff;padding:20px;border-radius:8px;box-shadow:0 0 10px rgba(0,0,0,.1);max-width:700px;margin:20px auto"><h1>创建人物</h1><form id="character-sheet-form"><div class="form-section"><div class="form-row"><div class="form-group form-group-inline"><label for="char-name">角色名</label> <input id="char-name" name="charName"></div><div class="form-group form-group-inline"><label for="char-player">玩家</label> <input id="char-player" name="charPlayer"></div></div><div class="form-row"><div class="form-group form-group-inline"><label for="char-age">年龄</label> <input id="char-age" name="charAge"></div><div class="form-group form-group-inline"><label for="char-gender">性别</label> <select id="char-gender" name="charGender"><option value="male">男性</option><option value="female">女性</option><option value="other">其他</option></select></div><div class="form-group form-group-inline"><label for="char-alignment">阵营</label> <input id="char-alignment" name="charAlignment"></div></div><div class="form-row"><div class="form-group form-group-inline"><label for="char-faith">信仰</label> <input id="char-faith" name="charFaith" style="width:100%"></div></div><div class="form-row"><div class="form-group form-group-inline"><label for="char-height">身高</label> <input id="char-height" name="charHeight"></div><div class="form-group form-group-inline"><label for="char-weight">体重</label> <input id="char-weight" name="charWeight"></div><div class="form-group form-group-inline"><label for="char-xp">经验值</label> <input type="number" id="char-xp" name="charXp" value="0"></div></div><div class="form-row"><div class="form-group form-group-inline"><label for="char-currency-gold">金币 (GP)</label> <input type="number" id="char-currency-gold" name="charCurrencyGold" value="0" min="0"></div><div class="form-group form-group-inline"><label for="char-currency-silver">银币 (SP)</label> <input type="number" id="char-currency-silver" name="charCurrencySilver" value="0" min="0"></div><div class="form-group form-group-inline"><label for="char-currency-copper">铜币 (CP)</label> <input type="number" id="char-currency-copper" name="charCurrencyCopper" value="0" min="0"></div></div></div><div class="form-section"><div class="form-group"><label for="char-appearance">外貌描写</label> <textarea id="char-appearance" name="charAppearance" rows="3"></textarea></div><div class="form-group"><label for="char-story">角色故事</label> <textarea id="char-story" name="charStory" rows="5"></textarea></div></div><div class="form-section"><div class="form-row"><div class="form-group form-group-inline"><label for="char-race">种族</label> <select id="char-race" name="charRace"><option value="人类">人类</option><option value="精灵">精灵</option><option value="矮人">矮人</option><option value="半身人">半身人</option><option value="龙裔">龙裔</option><option value="侏儒">侏儒</option><option value="提夫林">提夫林</option><option value="兽人">兽人</option><option value="阿斯莫">阿斯莫</option><option value="歌利亚">歌利亚</option></select></div><div class="form-group form-group-inline"><label for="char-background">背景</label> <input id="char-background" name="charBackground"></div></div><div class="form-section skills-section"><h4>技能熟练</h4><div class="skills-grid"><div class="skill-item"><input type="checkbox" id="skill-athletics" name="skillAthleticsProf" data-skill-name="运动" data-attribute="strength"><label for="skill-athletics">运动 (力量)</label><input type="number" name="skillAthleticsMod" value="0" class="skill-modifier-input"><input name="skillAthleticsFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-acrobatics" name="skillAcrobaticsProf" data-skill-name="体操" data-attribute="dexterity"><label for="skill-acrobatics">体操 (敏捷)</label><input type="number" name="skillAcrobaticsMod" value="0" class="skill-modifier-input"><input name="skillAcrobaticsFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-sleight-of-hand" name="skillSleightOfHandProf" data-skill-name="巧手" data-attribute="dexterity"><label for="skill-sleight-of-hand">巧手 (敏捷)</label><input type="number" name="skillSleightOfHandMod" value="0" class="skill-modifier-input"><input name="skillSleightOfHandFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-stealth" name="skillStealthProf" data-skill-name="隐匿" data-attribute="dexterity"><label for="skill-stealth">隐匿 (敏捷)</label><input type="number" name="skillStealthMod" value="0" class="skill-modifier-input"><input name="skillStealthFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-arcana" name="skillArcanaProf" data-skill-name="奥秘" data-attribute="intelligence"><label for="skill-arcana">奥秘 (智力)</label><input type="number" name="skillArcanaMod" value="0" class="skill-modifier-input"><input name="skillArcanaFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-history" name="skillHistoryProf" data-skill-name="历史" data-attribute="intelligence"><label for="skill-history">历史 (智力)</label><input type="number" name="skillHistoryMod" value="0" class="skill-modifier-input"><input name="skillHistoryFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-investigation" name="skillInvestigationProf" data-skill-name="调查" data-attribute="intelligence"><label for="skill-investigation">调查 (智力)</label><input type="number" name="skillInvestigationMod" value="0" class="skill-modifier-input"><input name="skillInvestigationFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-nature" name="skillNatureProf" data-skill-name="自然" data-attribute="intelligence"><label for="skill-nature">自然 (智力)</label><input type="number" name="skillNatureMod" value="0" class="skill-modifier-input"><input name="skillNatureFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-religion" name="skillReligionProf" data-skill-name="宗教" data-attribute="intelligence"><label for="skill-religion">宗教 (智力)</label><input type="number" name="skillReligionMod" value="0" class="skill-modifier-input"><input name="skillReligionFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-animal-handling" name="skillAnimalHandlingProf" data-skill-name="驯兽" data-attribute="wisdom"><label for="skill-animal-handling">驯兽 (感知)</label><input type="number" name="skillAnimalHandlingMod" value="0" class="skill-modifier-input"><input name="skillAnimalHandlingFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-insight" name="skillInsightProf" data-skill-name="洞悉" data-attribute="wisdom"><label for="skill-insight">洞悉 (感知)</label><input type="number" name="skillInsightMod" value="0" class="skill-modifier-input"><input name="skillInsightFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-medicine" name="skillMedicineProf" data-skill-name="医药" data-attribute="wisdom"><label for="skill-medicine">医药 (感知)</label><input type="number" name="skillMedicineMod" value="0" class="skill-modifier-input"><input name="skillMedicineFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-perception" name="skillPerceptionProf" data-skill-name="察觉" data-attribute="wisdom"><label for="skill-perception">察觉 (感知)</label><input type="number" name="skillPerceptionMod" value="0" class="skill-modifier-input"><input name="skillPerceptionFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-survival" name="skillSurvivalProf" data-skill-name="求生" data-attribute="wisdom"><label for="skill-survival">求生 (感知)</label><input type="number" name="skillSurvivalMod" value="0" class="skill-modifier-input"><input name="skillSurvivalFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-deception" name="skillDeceptionProf" data-skill-name="欺瞒" data-attribute="charisma"><label for="skill-deception">欺瞒 (魅力)</label><input type="number" name="skillDeceptionMod" value="0" class="skill-modifier-input"><input name="skillDeceptionFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-intimidation" name="skillIntimidationProf" data-skill-name="威吓" data-attribute="charisma"><label for="skill-intimidation">威吓 (魅力)</label><input type="number" name="skillIntimidationMod" value="0" class="skill-modifier-input"><input name="skillIntimidationFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-performance" name="skillPerformanceProf" data-skill-name="表演" data-attribute="charisma"><label for="skill-performance">表演 (魅力)</label><input type="number" name="skillPerformanceMod" value="0" class="skill-modifier-input"><input name="skillPerformanceFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-persuasion" name="skillPersuasionProf" data-skill-name="游说" data-attribute="charisma"><label for="skill-persuasion">游说 (魅力)</label><input type="number" name="skillPersuasionMod" value="0" class="skill-modifier-input"><input name="skillPersuasionFinal" readonly="readonly" class="skill-final-value"></div></div><button type="button" id="clear-skills-button" class="clear-section-button">清空技能修正与熟练</button></div><div class="form-group"><label for="char-tool-proficiencies-text">工具与其它熟练项 (逗号分隔)</label> <input id="char-tool-proficiencies-text" name="charToolProficienciesText" placeholder="例如：盗贼工具, 草药学工具套件, 轻甲, 简单武器"></div><div class="form-section dynamic-list-section"><h4>已装备物品</h4><div id="equipment-list-container"></div><button type="button" id="add-equipment-button" class="add-item-button">添加装备</button></div><div class="form-section dynamic-list-section"><h4>初始物品 (背包)</h4><div id="inventory-list-container"></div><button type="button" id="add-inventory-item-button" class="add-item-button">添加物品</button></div><div class="form-group"><label for="char-personality-traits">特点</label> <textarea id="char-personality-traits" name="charPersonalityTraits" rows="2"></textarea></div><div class="form-group"><label for="char-ideals">理想</label> <textarea id="char-ideals" name="charIdeals" rows="2"></textarea></div><div class="form-group"><label for="char-bonds">牵绊</label> <textarea id="char-bonds" name="charBonds" rows="2"></textarea></div><div class="form-group"><label for="char-flaws">缺点</label> <textarea id="char-flaws" name="charFlaws" rows="2"></textarea></div></div><div class="form-section"><div class="attribute-grid"><div class="attribute-row"><strong>力量 (STR)</strong><div class="attribute-inputs"><div><label for="attr-str-base">初始值</label><input type="number" id="attr-str-base" name="attrStrBase" value="10" min="1" max="20"></div><div><label for="attr-str-race">种族</label><input type="number" id="attr-str-race" name="attrStrRace" value="0"></div><div><label for="attr-str-mod">修正</label><input type="number" id="attr-str-mod" name="attrStrMod" value="0"></div><div><label for="attr-str-final">最终值</label><input id="attr-str-final" name="attrStrFinal" readonly="readonly"></div></div></div><div class="attribute-row"><strong>敏捷 (DEX)</strong><div class="attribute-inputs"><div><label for="attr-dex-base">初始值</label><input type="number" id="attr-dex-base" name="attrDexBase" value="10" min="1" max="20"></div><div><label for="attr-dex-race">种族</label><input type="number" id="attr-dex-race" name="attrDexRace" value="0"></div><div><label for="attr-dex-mod">修正</label><input type="number" id="attr-dex-mod" name="attrDexMod" value="0"></div><div><label for="attr-dex-final">最终值</label><input id="attr-dex-final" name="attrDexFinal" readonly="readonly"></div></div></div><div class="attribute-row"><strong>体质 (CON)</strong><div class="attribute-inputs"><div><label for="attr-con-base">初始值</label><input type="number" id="attr-con-base" name="attrConBase" value="10" min="1" max="20"></div><div><label for="attr-con-race">种族</label><input type="number" id="attr-con-race" name="attrConRace" value="0"></div><div><label for="attr-con-mod">修正</label><input type="number" id="attr-con-mod" name="attrConMod" value="0"></div><div><label for="attr-con-final">最终值</label><input id="attr-con-final" name="attrConFinal" readonly="readonly"></div></div></div><div class="attribute-row"><strong>智力 (INT)</strong><div class="attribute-inputs"><div><label for="attr-int-base">初始值</label><input type="number" id="attr-int-base" name="attrIntBase" value="10" min="1" max="20"></div><div><label for="attr-int-race">种族</label><input type="number" id="attr-int-race" name="attrIntRace" value="0"></div><div><label for="attr-int-mod">修正</label><input type="number" id="attr-int-mod" name="attrIntMod" value="0"></div><div><label for="attr-int-final">最终值</label><input id="attr-int-final" name="attrIntFinal" readonly="readonly"></div></div></div><div class="attribute-row"><strong>感知 (WIS)</strong><div class="attribute-inputs"><div><label for="attr-wis-base">初始值</label><input type="number" id="attr-wis-base" name="attrWisBase" value="10" min="1" max="20"></div><div><label for="attr-wis-race">种族</label><input type="number" id="attr-wis-race" name="attrWisRace" value="0"></div><div><label for="attr-wis-mod">修正</label><input type="number" id="attr-wis-mod" name="attrWisMod" value="0"></div><div><label for="attr-wis-final">最终值</label><input id="attr-wis-final" name="attrWisFinal" readonly="readonly"></div></div></div><div class="attribute-row"><strong>魅力 (CHA)</strong><div class="attribute-inputs"><div><label for="attr-cha-base">初始值</label><input type="number" id="attr-cha-base" name="attrChaBase" value="10" min="1" max="20"></div><div><label for="attr-cha-race">种族</label><input type="number" id="attr-cha-race" name="attrChaRace" value="0"></div><div><label for="attr-cha-mod">修正</label><input type="number" id="attr-cha-mod" name="attrChaMod" value="0"></div><div><label for="attr-cha-final">最终值</label><input id="attr-cha-final" name="attrChaFinal" readonly="readonly"></div></div></div></div></div><div class="form-section"><div class="form-row"><div class="form-group form-group-inline"><label for="char-class-1">职业1</label> <select id="char-class-1" name="charClass1"><option value="战士">战士</option><option value="法师">法师</option><option value="牧师">牧师</option><option value="游荡者">游荡者</option><option value="野蛮人">野蛮人</option><option value="吟游诗人">吟游诗人</option><option value="圣武士">圣武士</option><option value="游侠">游侠</option><option value="术士">术士</option><option value="魔契师">魔契师</option><option value="武僧">武僧</option><option value="德鲁伊">德鲁伊</option></select></div><div class="form-group form-group-inline"><label for="char-level-1">等级</label> <input type="number" id="char-level-1" name="charLevel1" value="1" min="1"></div><div class="form-group form-group-inline"><label for="char-subclass-1">子职</label> <input id="char-subclass-1" name="charSubclass1"></div></div></div><div class="form-section"><h3>法术</h3><div class="form-row"><div class="form-group form-group-inline"><label for="spell-ability">施法关键属性</label> <select id="spell-ability" name="spellAbility"><option value="STR">力量</option><option value="DEX">敏捷</option><option value="CON">体质</option><option value="INT">智力</option><option value="WIS">感知</option><option value="CHA">魅力</option></select></div><div class="form-group form-group-inline"><label for="spell-attack-bonus">法术攻击加值</label> <input id="spell-attack-bonus" name="spellAttackBonus" readonly="readonly"></div><div class="form-group form-group-inline"><label for="spell-save-dc">法术豁免DC</label> <input id="spell-save-dc" name="spellSaveDc" readonly="readonly"></div></div><h4>戏法</h4><div class="spell-list" id="cantrips-list"></div><datalist id="cantrips-datalist"></datalist><button type="button" id="add-cantrip-button">添加戏法</button><h4>一环法术 (法术位: <input type="number" id="spell-slots-1" name="spellSlots1" value="0" style="width:50px">)</h4><div class="spell-list" id="level1-spells-list"></div><datalist id="level1-spells-datalist"></datalist><button type="button" id="add-level1-spell-button">添加一环法术</button></div></form><div class="form-section"><label for="ai-character-prompt-textarea">AI角色生成指导提示 (可选):</label> <textarea id="ai-character-prompt-textarea" rows="3" placeholder="例如：我想要一个擅长潜行和欺骗的半精灵游荡者，背景是街头顽童。"></textarea></div><div class="button-group" style="margin-top:20px"><button id="save-character-button" style="background-color:#17a2b8">保存人物卡</button> <button id="ai-generate-character-button" style="background-color:#28a745">AI生成角色</button> <button id="back-to-module-setup-button">返回模组设置</button></div></div><script>function e(e,t,n){try{if("object"!=typeof window.toastr&&"undefined"!=typeof parent&&"object"==typeof parent.toastr&&(window.toastr=parent.toastr),"object"==typeof toastr&&null!==toastr&&"function"==typeof toastr[e])toastr[e](t,n);else{("error"===e?console.error:"warning"===e?console.warn:console.log)(`[ModuleSetup Toastr Fallback - ${e}] ${n?n+": ":""}${t}`)}}catch(e){console.error(`[ModuleSetup] safeToastr Error: ${e.message}`)}}async function t(t,n,i){if(!i)return void console.error("Output message div not found for saveContentToLorebook");if(!t)return e("warning","模组标题 (Key) 或角色名不能为空!","输入错误"),void(i.textContent="错误: 模组标题 (Key) 或角色名不能为空。");i.textContent=`正在尝试将内容保存到世界书条目 (Key: ${t})...`;const r="RPG_Modules_Test.json",a=`/createentry file="${r}" key="${t}" ${n}`;e("info",`执行命令... (Key: ${t}, 内容长度: ${n.length})`,"世界书操作"),console.log(`[ModuleSetup] Executing command for key "${t}" (content preview: ${n.substring(0,50)}...)`);try{if("function"!=typeof triggerSlash)return e("error","triggerSlash API 不可用!","API 错误"),void(i.textContent="错误: triggerSlash API 不可用。");const o=await triggerSlash(a);if(o&&""!==o.trim()){const a=`成功创建/更新世界书条目！\n文件名: ${r}\nKey: ${t}\nUID: ${o}\n内容预览: ${n.substring(0,100)}...`;e("success",`条目 '${t}' 已保存。UID: ${o}`,"操作成功"),i.innerHTML=a.replace(/\n/g,"<br>")}else{const n=`创建/更新世界书条目可能失败或没有返回UID。\n文件名: ${r}\nKey: ${t}.\n请检查SillyTavern日志或世界书。可能原因：内容过长或包含无法处理的特殊字符。`;e("warning",`条目 '${t}' 保存结果未知。`,"操作结果未知"),i.innerHTML=n.replace(/\n/g,"<br>")}}catch(n){const r=`保存条目 '${t}' 时发生错误: ${n.message}`;e("error",r,"操作失败"),i.textContent=r,console.error(`[ModuleSetup] Error saving entry '${t}':`,n)}}async function n(){const n=document.getElementById("module-title-input"),i=document.getElementById("module-content-textarea"),r=document.getElementById("output-message");if(!n||!i||!r)return void e("error","界面元素未完全找到 (save custom)!","界面错误");const a=n.value.trim(),o=i.value.trim();if(!o)return e("warning","模组内容不能为空!","输入错误"),r.textContent="错误: 模组内容不能为空。",void i.focus();await t(a,o,r)}async function i(){const n=document.getElementById("module-title-input"),i=document.getElementById("module-content-textarea"),r=document.getElementById("output-message");if(!n||!i||!r)return void e("error","界面元素未完全找到 (ai generate module)!","界面错误");const a=n.value.trim(),o=i.value.trim();if(!a)return e("warning","请输入模组标题 (作为Key)!","输入错误"),r.textContent="错误: 模组标题 (Key) 不能为空。",void n.focus();if(!o)return e("warning","请输入给AI的创作提示!","输入错误"),r.textContent="错误: AI创作提示不能为空。",void i.focus();r.textContent="正在向AI发送请求以生成模组内容 (JSON格式)...",e("info","向AI发送模组创作请求 (JSON)...","AI交互");const l=`请根据以下用户提示为D&D 5e游戏创作一个模组设定。用户提示： "${o}". 请严格按照 "module_setup_ai_prompts_v2_json.md" 中针对 "moduleCreation" 更新后的 "NarrativeModuleData" JSON Schema返回结果，核心内容应在 "narrativeContent" 字段中以人类易读的叙述形式提供。确保整个JSON响应被 "开始制作\\n##@@_MODULE_CONTENT_BEGIN_@@##" 和 "##@@_MODULE_CONTENT_END_@@##\\n结束制作" 包裹。`;try{if("function"!=typeof triggerSlash)return e("error","triggerSlash API 不可用!","API 错误"),void(r.textContent="错误: triggerSlash API 不可用。");const n=await triggerSlash(`/gen ${l}`);if(n?.trim()){e("success","AI已返回内容!","AI交互完成");let o=null;const l=n.match(/##@@_MODULE_CONTENT_BEGIN_@@##([\s\S]*?)##@@_MODULE_CONTENT_END_@@##/);if(l&&l[1]?o=l[1].trim():console.warn("New markers ##@@_MODULE_CONTENT_BEGIN_@@##...##@@_MODULE_CONTENT_END_@@## not found in AI response for module creation."),o)try{const e=JSON.parse(o);if("moduleCreation"!==e.requestType||!e.data||!e.data.narrativeContent)throw new Error("AI返回的JSON格式不符合预期的叙述性模组创作类型，或缺少 narrativeContent。");{const n=e.data,o=n.narrativeContent;i.value=o,r.textContent=`AI模组叙述内容已生成并填充到文本框。标题: ${n.title||a}。请审阅后手动保存，或直接保存。`,await t(n.title||a,o,r)}}catch(n){e("error",`AI返回的JSON解析失败: ${n.message}. 将尝试保存原始提取内容。`,"JSON解析错误"),r.textContent=`AI返回的JSON解析失败。原始提取内容 (可能需要手动清理):\n${o.substring(0,300)}...`,await t(a,o,r)}else e("warning","未能从AI回复中提取有效的JSON内容 (新标记)。","内容提取失败"),r.textContent="未能从AI回复中提取有效的JSON内容。原始回复：\n"+n.substring(0,300)+"..."}else e("warning","AI未能生成有效内容或返回为空。","AI交互失败"),r.textContent="AI未能生成有效内容或返回为空。"}catch(t){const n=`AI模组内容生成或保存过程中发生错误: ${t.message}`;e("error",n,"AI或保存失败"),r.textContent=n}}function r(e="",t=""){const n=document.createElement("div");n.className="dynamic-item-row";const i=document.createElement("input");i.type="text",i.name="equipmentName[]",i.placeholder="装备名称",i.value=e;const r=document.createElement("input");r.type="text",r.name="equipmentDetails[]",r.placeholder="详情 (可选)",r.value=t;const a=document.createElement("button");return a.type="button",a.className="remove-item-button",a.textContent="移除",a.onclick=()=>n.remove(),n.appendChild(i),n.appendChild(r),n.appendChild(a),n}function a(e="",t="",n=1){const i=document.createElement("div");i.className="dynamic-item-row";const r=document.createElement("input");r.type="text",r.name="inventoryItemName[]",r.placeholder="物品名称",r.value=e;const a=document.createElement("input");a.type="text",a.name="inventoryItemDetails[]",a.placeholder="描述 (可选)",a.value=t;const o=document.createElement("input");o.type="number",o.name="inventoryItemQuantity[]",o.placeholder="数量",o.value=n.toString(),o.min="1";const l=document.createElement("button");return l.type="button",l.className="remove-item-button",l.textContent="移除",l.onclick=()=>i.remove(),i.appendChild(r),i.appendChild(a),i.appendChild(o),i.appendChild(l),i}function o(e,t,n){return(e||0)+(t||0)+(n||0)}function l(e){return Math.floor(((e||10)-10)/2)}function s(e){return e>=17?6:e>=13?5:e>=9?4:e>=5?3:e>=1?2:0}function c(e){const t=[],n=/<H4 id="[^"]*">([^｜]+)｜[^<]*<\/H4>/g;let i;for(;null!==(i=n.exec(e));)i[1]&&t.push(i[1].trim());return t}function d(e,t,n,i=""){const r=document.createElement("div");r.className="spell-item";const a=document.createElement("input");a.type="text",a.name=`${e}[]`,a.placeholder=""+("cantrip"===e?"戏法":"一环法术"),a.setAttribute("list",n),a.value=i;const o=document.createElement("button");return o.type="button",o.className="remove-spell-button",o.textContent="-",o.addEventListener("click",(()=>{r.remove()})),r.appendChild(a),r.appendChild(o),r}function u(){const e=document.getElementById("spell-ability"),t=document.getElementById("spell-attack-bonus"),n=document.getElementById("spell-save-dc"),i=document.getElementById("char-level-1");if(!(e&&t&&n&&i))return;const r=e.value.toLowerCase(),a=document.getElementById(`attr-${r}-final`);if(!a)return void console.warn(`Final ability score element for '${r}' not found.`);const o=l(parseInt(a.value,10)||10),c=s(parseInt(i.value,10)||1),d=o+c,u=8+o+c;t.value=(d>=0?"+":"")+d.toString(),n.value=u.toString()}function m(e){const t=e.querySelector('input[type="checkbox"]'),n=e.querySelector(".skill-modifier-input"),i=e.querySelector(".skill-final-value"),r=t.dataset.attribute,a=document.getElementById("char-level-1");if(!(t&&n&&i&&r&&a))return;const o=r.substring(0,3).toLowerCase(),c=document.getElementById(`attr-${o}-final`);if(!c)return;const d=l(parseInt(c.value,10)||10),u=t.checked,m=parseInt(n.value,10)||0,p=parseInt(a.value,10)||1,h=function(e,t,n,i,r){let a=t+i;return n&&(a+=s(r)),a}(t.dataset.skillName,d,u,m,p);i.value=(h>=0?"+":"")+h.toString()}function p(){document.querySelectorAll(".skill-item").forEach((e=>{m(e)}))}async function h(){const t=document.getElementById("output-message"),n=document.getElementById("char-name"),i=document.getElementById("ai-character-prompt-textarea");let o=i?.value.trim();if(o||(o=document.getElementById("module-content-textarea")?.value.trim()),o||(o="请为D&D 5e生成一个详细的1级角色。"),!t)return void e("error","输出消息区域未找到 (ai generate character)!","界面错误");t.textContent="正在向AI发送请求以生成角色数据 (JSON格式)...",e("info","向AI发送角色生成请求 (JSON)...","AI交互");const l=`请根据以下用户提示为D&D 5e游戏生成一个角色。用户提示： "${o}". 请严格按照 "module_setup_ai_prompts_v2_json.md" 中定义的 "characterGeneration" JSON Schema返回结果，确保整个JSON响应被 "开始制作\\n##@@_MODULE_CONTENT_BEGIN_@@##" 和 "##@@_MODULE_CONTENT_END_@@##\\n结束制作" 包裹。`;try{if("function"!=typeof triggerSlash)return e("error","triggerSlash API 不可用!","API 错误"),void(t.textContent="错误: triggerSlash API 不可用。");const i=await triggerSlash(`/gen ${l}`);if(i?.trim()){e("success","AI已返回角色数据!","AI交互完成");let o=null;const l=i.match(/##@@_MODULE_CONTENT_BEGIN_@@##([\s\S]*?)##@@_MODULE_CONTENT_END_@@##/);if(l&&l[1]?o=l[1].trim():console.warn("New markers ##@@_MODULE_CONTENT_BEGIN_@@##...##@@_MODULE_CONTENT_END_@@## not found in AI response for character generation."),o)try{const i=JSON.parse(o);if("characterGeneration"!==i.requestType||!i.data)throw new Error("AI返回的JSON格式不符合预期的角色生成类型。");!function(t){if(e("info",`正在使用AI生成的数据填充角色表单: ${t.name}`,"角色填充"),!document.getElementById("character-sheet-form"))return;const n=(e,t,n=!1)=>{const i=document.getElementById(e);if(i&&void 0!==t){if(n&&"options"in i){let e=!1;for(let n=0;n<i.options.length;n++)if(i.options[n].value===t.toString()||i.options[n].text===t.toString()){i.selectedIndex=n,e=!0;break}if(!e){const e=new Option(t.toString(),t.toString(),!0,!0);i.add(e)}}else i.value=t.toString();e.startsWith("attr-")&&(e.includes("-base")||e.includes("-race")||e.includes("-mod"))&&i.dispatchEvent(new Event("input"))}};n("char-name",t.name),n("char-race",t.race,!0),n("char-class-1",t.class,!0),n("char-level-1",t.level),n("char-alignment",t.alignment),n("char-background",t.background),n("char-personality-traits",t.personalityTraits),n("char-ideals",t.ideals),n("char-bonds",t.bonds),n("char-flaws",t.flaws),n("char-appearance",t.appearance),n("char-story",t.story),n("char-age",t.age),n("char-gender",t.gender,!0),n("char-faith",t.faith),n("char-height",t.height),n("char-weight",t.weight),n("char-xp",t.exp),n("char-subclass-1",t.subclass),n("spell-ability",t.spellcastingAbility,!0),n("char-tool-proficiencies-text",t.toolProficienciesText),t.currency&&(n("char-currency-gold",t.currency.gold),n("char-currency-silver",t.currency.silver),n("char-currency-copper",t.currency.copper));const i={str:"strength",dex:"dexterity",con:"constitution",int:"intelligence",wis:"wisdom",cha:"charisma"};["str","dex","con","int","wis","cha"].forEach((e=>{const r=i[e],a=t.attributes[r];a&&(n(`attr-${e}-base`,a.base),n(`attr-${e}-race`,a.race_bonus),n(`attr-${e}-mod`,a.modifier_bonus))})),t.skills?.forEach((e=>{const t=document.querySelector(`input[data-skill-name="${e.name}"]`),n=t?.closest(".skill-item")?.querySelector(".skill-modifier-input");t&&(t.checked=e.proficient),n&&(n.value=e.modifierValue.toString())})),p();const o=document.getElementById("equipment-list-container");o&&(o.innerHTML="",t.equipment?.forEach((e=>{o.appendChild(r(e.name,e.details))})));const l=document.getElementById("inventory-list-container");l&&(l.innerHTML="",t.inventory?.forEach((e=>{l.appendChild(a(e.name,e.description,e.quantity))})));const s=document.getElementById("cantrips-list");s&&(s.innerHTML="",t.equippedSpells?.filter((e=>0===e.level)).forEach((e=>{s.appendChild(d("cantrip",0,"cantrips-datalist",e.name))})),0===s.children.length&&0===t.equippedSpells?.filter((e=>0===e.level)).length&&s.appendChild(d("cantrip",0,"cantrips-datalist")));const c=document.getElementById("level1-spells-list");c&&(c.innerHTML="",t.equippedSpells?.filter((e=>1===e.level)).forEach((e=>{c.appendChild(d("level1spell",0,"level1-spells-datalist",e.name))})),0===c.children.length&&0===t.equippedSpells?.filter((e=>1===e.level)).length&&c.appendChild(d("level1spell",0,"level1-spells-datalist"))),t.spellSlots?.[1]&&n("spell-slots-1",t.spellSlots[1].max),u(),e("success",`角色 "${t.name}" 的数据已填充到表单。`,"AI角色生成")}(i.data),t.textContent="AI角色数据已填充到表单。请检查并保存。",n&&i.data.name&&(n.value=i.data.name)}catch(n){e("error",`AI返回的角色JSON解析失败: ${n.message}.`,"JSON解析错误"),t.textContent=`AI返回的角色JSON解析失败。原始提取内容:\n${o.substring(0,300)}...`}else e("warning","未能从AI回复中提取有效的JSON角色数据。","内容提取失败"),t.textContent="未能从AI回复中提取有效的JSON角色数据。"}else e("warning","AI未能生成角色数据或返回为空。","AI交互失败"),t.textContent="AI未能生成角色数据或返回为空。"}catch(n){const i=`AI角色生成过程中发生错误: ${n.message}`;e("error",i,"AI错误"),t.textContent=i}}async function g(){const n=document.getElementById("output-message"),i=document.getElementById("character-sheet-form");if(!i||!n)return void e("error","人物创建表单或输出区域未找到!","界面错误");const r=new FormData(i),a=e=>r.get(e)?.toString().trim()||"",s=(e,t=0)=>{const n=r.get(e)?.toString().trim();if(""===n||null==n)return t;const i=parseInt(n,10);return isNaN(i)?t:i},c=a("charName");if(!c)return e("warning","角色名不能为空!","保存错误"),n.textContent="错误: 角色名不能为空。",void document.getElementById("char-name")?.focus();const d={name:c,race:a("charRace"),class:a("charClass1"),level:s("charLevel1",1),alignment:a("charAlignment"),background:a("charBackground"),personalityTraits:a("charPersonalityTraits"),ideals:a("charIdeals"),bonds:a("charBonds"),flaws:a("charFlaws"),appearance:a("charAppearance"),story:a("charStory"),age:a("charAge"),gender:a("charGender"),faith:a("charFaith"),height:a("charHeight"),weight:a("charWeight"),exp:s("charXp"),subclass:a("charSubclass1"),spellcastingAbility:a("spellAbility"),toolProficienciesText:a("charToolProficienciesText"),attributes:{strength:{base:0,race_bonus:0,modifier_bonus:0,final:0,mod:0},dexterity:{base:0,race_bonus:0,modifier_bonus:0,final:0,mod:0},constitution:{base:0,race_bonus:0,modifier_bonus:0,final:0,mod:0},intelligence:{base:0,race_bonus:0,modifier_bonus:0,final:0,mod:0},wisdom:{base:0,race_bonus:0,modifier_bonus:0,final:0,mod:0},charisma:{base:0,race_bonus:0,modifier_bonus:0,final:0,mod:0}},hp:{current:0,max:0},ac:10,currency:{gold:s("charCurrencyGold",0),silver:s("charCurrencySilver",0),copper:s("charCurrencyCopper",0)},proficiencies:[],skills:[],equippedSpells:[],spellSlots:{},equipment:[],inventory:[]},u={str:"strength",dex:"dexterity",con:"constitution",int:"intelligence",wis:"wisdom",cha:"charisma"};if(["str","dex","con","int","wis","cha"].forEach((e=>{const t=document.getElementById(`attr-${e}-base`),n=document.getElementById(`attr-${e}-race`),i=document.getElementById(`attr-${e}-mod`),r=t?parseInt(t.value,10):NaN,a=n?parseInt(n.value,10):NaN,s=i?parseInt(i.value,10):NaN,c=isNaN(r)?8:r,m=isNaN(a)?0:a,p=isNaN(s)?0:s,h=o(c,m,p),g=l(h);d.attributes[u[e]]={base:c,race_bonus:m,modifier_bonus:p,final:h,mod:g}})),d.attributes.constitution&&d.level){const e=d.attributes.constitution.mod,t="战士"===d.class?10:"法师"===d.class?6:8;let n=t+e;d.level>1&&(n+=(d.level-1)*(Math.floor(t/2)+1+e)),d.hp={current:n>0?n:1,max:n>0?n:1}}d.attributes.dexterity&&(d.ac=10+d.attributes.dexterity.mod),d.skills=[],document.querySelectorAll(".skill-item").forEach((e=>{const t=e.querySelector('input[type="checkbox"]'),n=e.querySelector(".skill-modifier-input"),i=e.querySelector(".skill-final-value");t&&n&&i&&d.skills.push({name:t.dataset.skillName||"未知技能",proficient:t.checked,attribute:t.dataset.attribute||"unknown",modifierValue:parseInt(n.value,10)||0,finalValue:parseInt(i.value,10)||0})}));const m=a("charToolProficienciesText").split(",").map((e=>e.trim())).filter((e=>e));d.proficiencies=[...new Set([...d.proficiencies||[],...m])],d.equippedSpells=[],document.querySelectorAll('#cantrips-list div.spell-item input[type="text"]').forEach((e=>{const t=e.value.trim();t&&d.equippedSpells.push({name:t,level:0,source:"习得"})})),document.querySelectorAll('#level1-spells-list div.spell-item input[type="text"]').forEach((e=>{const t=e.value.trim();t&&d.equippedSpells.push({name:t,level:1,source:"习得"})})),d.spellSlots||(d.spellSlots={});const p=s("spellSlots1",0);p>=0&&d.spellSlots&&(d.spellSlots[1]={current:p,max:p}),d.equipment=[],document.querySelectorAll("#equipment-list-container .dynamic-item-row").forEach((e=>{const t=e.querySelector('input[name="equipmentName[]"]'),n=e.querySelector('input[name="equipmentDetails[]"]');t&&t.value.trim()&&d.equipment.push({name:t.value.trim(),type:"装备",equipped:!0,details:n?n.value.trim():""})})),d.inventory=[],document.querySelectorAll("#inventory-list-container .dynamic-item-row").forEach((e=>{const t=e.querySelector('input[name="inventoryItemName[]"]'),n=e.querySelector('input[name="inventoryItemDetails[]"]'),i=e.querySelector('input[name="inventoryItemQuantity[]"]');t&&t.value.trim()&&d.inventory.push({name:t.value.trim(),quantity:i&&parseInt(i.value,10)||1,description:n?n.value.trim():""})}));const h=JSON.stringify(d,null,2),g=document.getElementById("module-setup-container"),y=document.getElementById("character-creation-container");g&&y&&(y.style.display="none",g.style.display="block"),await t(c,h,n)}function y(){const t=document.getElementById("module-setup-container"),l=document.getElementById("character-creation-container"),s=document.getElementById("save-custom-module-button"),y=document.getElementById("ai-generate-content-button"),H=document.getElementById("go-to-character-creation-button"),f=document.getElementById("back-to-module-setup-button"),v=document.getElementById("save-character-button"),S=document.getElementById("ai-generate-character-button");t&&l?(s&&s.addEventListener("click",n),y&&y.addEventListener("click",i),H&&H.addEventListener("click",(()=>{t.style.display="none",l.style.display="block",e("info","已切换到人物创建界面。","界面切换")})),f&&f.addEventListener("click",(()=>{l.style.display="none",t.style.display="block",e("info","已返回模组设置界面。","界面切换")})),v&&v.addEventListener("click",g),S&&S.addEventListener("click",h),["str","dex","con","int","wis","cha"].forEach((e=>{const t=document.getElementById(`attr-${e}-base`),n=document.getElementById(`attr-${e}-race`),i=document.getElementById(`attr-${e}-mod`),r=document.getElementById(`attr-${e}-final`),a=()=>{if(t&&n&&i&&r){const e=o(parseInt(t.value,10)||0,parseInt(n.value,10)||0,parseInt(i.value,10)||0);r.value=e.toString(),"function"==typeof updateSpellAttackAndDc&&updateSpellAttackAndDc(),"function"==typeof updateAllSkillFinalValues&&updateAllSkillFinalValues()}};[t,n,i].forEach((e=>{e&&e.addEventListener("input",a)})),"complete"===document.readyState||"interactive"===document.readyState?a():window.addEventListener("DOMContentLoaded",a)})),function(){document.querySelectorAll(".skill-item").forEach((e=>{const t=e.querySelector('input[type="checkbox"]'),n=e.querySelector(".skill-modifier-input");t&&t.addEventListener("change",(()=>m(e))),n&&n.addEventListener("input",(()=>m(e))),m(e)}));const e=document.getElementById("clear-skills-button");e&&e.addEventListener("click",(()=>{document.querySelectorAll(".skill-item").forEach((e=>{const t=e.querySelector('input[type="checkbox"]'),n=e.querySelector(".skill-modifier-input");t&&(t.checked=!1),n&&(n.value="0"),m(e)}))}))}(),function(){const e=document.getElementById("spell-ability"),t=document.getElementById("char-level-1");e&&e.addEventListener("change",u),t&&t.addEventListener("input",(()=>{u(),"function"==typeof updateAllSkillFinalValues&&updateAllSkillFinalValues()})),"complete"===document.readyState||"interactive"===document.readyState?u():window.addEventListener("DOMContentLoaded",u)}(),function(){const e=document.getElementById("cantrips-datalist"),t=document.getElementById("level1-spells-datalist");e&&c('\n<H4 id="Acid_Splash">酸液飞溅｜Acid Splash</H4>\n<H4 id="Blade_Ward">剑刃防护｜Blade Ward</H4>\n<H4 id="Chill_Touch">颤栗之触｜Chill Touch</H4>\n<H4 id="Dancing_Lights">舞光术｜Dancing Lights</H4>\n<H4 id="Druidcraft">德鲁伊伎俩｜Druidcraft</H4>\n<H4 id="Eldritch_Blast">魔能爆｜Eldritch Blast</H4>\n<H4 id="Elementalism">四象法门｜Elementalism</H4>\n<H4 id="Fire_Bolt">火焰箭｜Fire Bolt</H4>\n<H4 id="Friends">交友术｜Friends</H4>\n<H4 id="Guidance">神导术｜Guidance</H4>\n<H4 id="Light">光亮术｜Light</H4>\n<H4 id="Mage_Hand">法师之手｜Mage Hand</H4>\n<H4 id="Mending">修复术｜Mending</H4>\n<H4 id="Message">传讯术｜Message</H4>\n<H4 id="Mind_Sliver">心灵之楔｜Mind Sliver</H4>\n<H4 id="Minor_Illusion">次级幻象｜Minor Illusion</H4>\n<H4 id="Poison_Spray">毒气喷涌｜Poison Spray</H4>\n<H4 id="Prestidigitation">魔法伎俩｜Prestidigitation</H4>\n<H4 id="Produce_Flame">燃火术｜Produce Flame</H4>\n<H4 id="Ray_of_Frost">冷冻射线｜Ray of Frost</H4>\n<H4 id="Resistance">抵抗术｜Resistance</H4>\n<H4 id="Sacred_Flame">圣火术｜Sacred Flame</H4>\n<H4 id="Shillelagh">橡棍术｜Shillelagh</H4>\n<H4 id="Shocking_Grasp">电爪｜Shocking Grasp</H4>\n<H4 id="Sorcerous_Burst">术法爆发｜Sorcerous Burst</H4>\n<H4 id="Spare_the_Dying">维生术｜Spare the Dying</H4>\n<H4 id="Starry_Wisp">点点星芒｜Starry Wisp</H4>\n<H4 id="Thaumaturgy">奇术｜Thaumaturgy</H4>\n<H4 id="Thorn_Whip">荆棘之鞭｜Thorn Whip</H4>\n<H4 id="Thunderclap">鸣雷破｜Thunderclap</H4>\n<H4 id="Toll_the_Dead">亡者丧钟｜Toll the Dead</H4>\n<H4 id="True_Strike">克敌先击｜True Strike</H4>\n<H4 id="Vicious_Mockery">恶言相加｜Vicious Mockery</H4>\n<H4 id="Word_of_Radiance">光耀祷词｜Word of Radiance</H4>\n').forEach((t=>{const n=document.createElement("option");n.value=t,e.appendChild(n)}));t&&c('\n<H4 id="Alarm">警报术｜Alarm</H4>\n<H4 id="Animal_Friendship">化兽为友｜Animal Friendship</H4>\n<H4 id="Armor_of_Agathys">黯冰狱铠｜Armor of Agathys</H4>\n<H4 id="Arms_of_Hadar">哈达之臂｜Arms of Hadar</H4>\n<H4 id="Bane">灾祸术｜Bane</H4>\n<H4 id="Bless">祝福术｜Bless</H4>\n<H4 id="Burning_Hands">燃烧之手｜Burning Hands</H4>\n<H4 id="Charm_Person">魅惑类人｜Charm Person</H4>\n<H4 id="Chromatic_Orb">繁彩球｜Chromatic Orb</H4>\n<H4 id="Color_Spray">七彩喷射｜Color Spray</H4>\n<H4 id="Command">命令术｜Command</H4>\n<H4 id="Compelled_Duel">强令对决｜Compelled Duel</H4>\n<H4 id="Comprehend_Languages">通晓语言｜Comprehend Languages</H4>\n<H4 id="Create_or_Destroy_Water">造水术/枯水术｜Create or Destroy Water</H4>\n<H4 id="Cure_Wounds">疗伤术｜Cure Wounds</H4>\n<H4 id="Detect_Evil_and_Good">侦测善恶｜Detect Evil and Good</H4>\n<H4 id="Detect_Magic">侦测魔法｜Detect Magic</H4>\n<H4 id="Detect_Poison_and_Disease">侦测毒性和疾病｜Detect Poison and Disease</H4>\n<H4 id="Disguise_Self">易容术｜Disguise Self</H4>\n<H4 id="Dissonant_Whispers">不谐低语｜Dissonant Whispers</H4>\n<H4 id="Divine_Favor">神恩｜Divine Favor</H4>\n<H4 id="Divine_Smite">至圣斩｜Divine Smite</H4>\n<H4 id="Ensnaring_Strike">捕获打击｜Ensnaring Strike</H4>\n<H4 id="Entangle">纠缠术｜Entangle</H4>\n<H4 id="Expeditious_Retreat">脚底抹油｜Expeditious Retreat</H4>\n<H4 id="Faerie_Fire">妖火｜Faerie Fire</H4>\n<H4 id="False_Life">虚假生命｜False Life</H4>\n<H4 id="Feather_Fall">羽落术｜Feather Fall</H4>\n<H4 id="Find_Familiar">寻获魔宠｜Find Familiar</H4>\n<H4 id="Fog_Cloud">云雾术｜Fog Cloud</H4>\n<H4 id="Goodberry">神莓术｜Goodberry</H4>\n<H4 id="Grease">油腻术｜Grease</H4>\n<H4 id="Guiding_Bolt">光导箭｜Guiding Bolt</H4>\n<H4 id="Hail_of_Thorns">荆棘之雨｜Hail of Thorns</H4>\n<H4 id="Healing_Word">治愈真言｜Healing Word</H4>\n<H4 id="Hellish_Rebuke">炼狱叱喝｜Hellish Rebuke</H4>\n<H4 id="Heroism">英雄气概｜Heroism</H4>\n<H4 id="Hex">脆弱诅咒｜Hex</H4>\n<H4 id="Hunter\'s_Mark">猎人印记｜Hunter\'s Mark</H4>\n<H4 id="Ice_Knife">冰刃｜Ice Knife</H4>\n<H4 id="Identify">鉴定术｜Identify</H4>\n<H4 id="Illusory_Script">迷幻手稿｜Illusory Script</H4>\n<H4 id="Inflict_Wounds">致伤术｜Inflict Wounds</H4>\n<H4 id="Jump">跳跃术｜Jump</H4>\n<H4 id="Longstrider">大步奔行｜Longstrider</H4>\n<H4 id="Mage_Armor">法师护甲｜Mage Armor</H4>\n<H4 id="Magic_Missile">魔法飞弹｜Magic Missile</H4>\n<H4 id="Protection_from_Evil_and_Good">防护善恶｜Protection from Evil and Good</H4>\n<H4 id="Purify_Food_and_Drink">净化饮食｜Purify Food and Drink</H4>\n<H4 id="Ray_of_Sickness">致病射线｜Ray of Sickness</H4>\n<H4 id="Sanctuary">庇护术｜Sanctuary</H4>\n<H4 id="Searing_Smite">炽焰斩｜Searing Smite</H4>\n<H4 id="Shield">护盾术｜Shield</H4>\n<H4 id="Shield_of_Faith">虔诚护盾｜Shield of Faith</H4>\n<H4 id="Silent_Image">无声幻影｜Silent Image</H4>\n<H4 id="Sleep">睡眠术｜Sleep</H4>\n<H4 id="Speak_with_Animals">动物交谈｜Speak with Animals</H4>\n<H4 id="Tasha\'s_Hideous_Laughter">塔莎狂笑术｜Tasha\'s Hideous Laughter</H4>\n<H4 id="Tenser\'s_Floating_Disk">谭森浮碟术｜Tenser\'s Floating Disk</H4>\n<H4 id="Thunderous_Smite">雷鸣斩｜Thunderous Smite</H4>\n<H4 id="Thunderwave">雷鸣波｜Thunderwave</H4>\n<H4 id="Unseen_Servant">隐形仆役｜Unseen Servant</H4>\n<H4 id="Witch_Bolt">巫术箭｜Witch Bolt</H4>\n<H4 id="Wrathful_Smite">激愤斩｜Wrathful Smite</H4>\n').forEach((e=>{const n=document.createElement("option");n.value=e,t.appendChild(n)}))}(),function(){const e=document.getElementById("add-cantrip-button"),t=document.getElementById("cantrips-list");e&&t&&e.addEventListener("click",(()=>{const e=t.querySelector('input[name="cantrip1"]');e&&e.parentElement?.classList.contains("spell-item")&&1===t.querySelectorAll(".spell-item").length&&!e.value&&e.parentElement.remove(),t.appendChild(d("cantrip",0,"cantrips-datalist"))}));const n=document.getElementById("add-level1-spell-button"),i=document.getElementById("level1-spells-list");n&&i&&n.addEventListener("click",(()=>{const e=i.querySelector('input[name="level1spell1"]');e&&e.parentElement?.classList.contains("spell-item")&&1===i.querySelectorAll(".spell-item").length&&!e.value&&e.parentElement.remove(),i.appendChild(d("level1spell",0,"level1-spells-datalist"))})),document.getElementById("character-sheet-form")?.addEventListener("click",(function(e){const t=e.target;t&&t.classList.contains("remove-spell-button")&&t.parentElement?.remove()}))}(),function(){const e=document.getElementById("equipment-list-container"),t=document.getElementById("add-equipment-button");e&&t&&t.addEventListener("click",(()=>{e.appendChild(r())}))}(),function(){const e=document.getElementById("inventory-list-container"),t=document.getElementById("add-inventory-item-button");e&&t&&t.addEventListener("click",(()=>{e.appendChild(a())}))}(),p(),u(),e("info","模组创作与人物创建导航界面已初始化。","初始化完成")):e("error","核心界面容器未找到!","界面初始化错误")}if(window.updateSpellAttackAndDc=u,window.updateAllSkillFinalValues=p,"undefined"!=typeof window&&"undefined"!=typeof parent&&parent!==window){["$","toastr","triggerSlash"].forEach((e=>{void 0===window[e]&&void 0!==parent[e]&&(window[e]=parent[e])}))}"complete"===document.readyState||"interactive"===document.readyState?y():document.addEventListener("DOMContentLoaded",y);</script></body></html>