import { EquipmentItem, WeaponTemplate, WeaponRange, PlayerState, MagicEffect } from '../types';

// --- 类型声明 ---
// These are typically provided by the SillyTavern environment.
// If not, they might need to be declared globally or passed as parameters.
declare const toastr: {
  success: (message: string, title?: string) => void;
  info: (message: string, title?: string) => void;
  error: (message: string, title?: string) => void;
  warning: (message: string, title?: string) => void;
};

export function safeToastr(type: 'info' | 'success' | 'warning' | 'error', message: string, title?: string): void {
  try {
    if (
      typeof (window as any).toastr !== 'object' &&
      typeof parent !== 'undefined' &&
      typeof (parent as any).toastr === 'object'
    ) {
      (window as any).toastr = (parent as any).toastr;
    }
    if (typeof toastr === 'object' && toastr !== null && typeof toastr[type] === 'function') {
      toastr[type](message, title);
    } else {
      const consoleFn = type === 'error' ? console.error : type === 'warning' ? console.warn : console.log;
      consoleFn(`[AdvLog Toastr Fallback - ${type}] ${title ? title + ': ' : ''}${message}`);
    }
  } catch (e) {
    console.error(`[AdvLog] safeToastr Error: ${(e as Error).message}`);
  }
}

export function ensureGlobals(): void {
  try {
    if (typeof window !== 'undefined' && typeof parent !== 'undefined' && parent !== window) {
      const apiKeysToCopy = ['$', 'toastr', 'triggerSlash', 'getLastMessageId', 'setChatMessages'];
      apiKeysToCopy.forEach(key => {
        if (typeof (window as any)[key] === 'undefined') {
          if (typeof (parent as any)[key] !== 'undefined') {
            (window as any)[key] = (parent as any)[key];
          }
        }
      });
    }
  } catch (e) {
    // console.error(`[AdvLog] ensureGlobals Error: ${(e as Error).message}`);
  }
}

export function getProficiencyBonus(level: number): number {
  if (level >= 17) return 6;
  if (level >= 13) return 5;
  if (level >= 9) return 4;
  if (level >= 5) return 3;
  return 2; // Level 1-4
}

export function parseDamageString(damageDice: string): { count: number; die: number; modifier: number } | null {
  const match = damageDice.match(/(\d+)d(\d+)(?:\s*([+-])\s*(\d+))?/);
  if (match) {
    const count = parseInt(match[1], 10);
    const die = parseInt(match[2], 10);
    let modifier = 0;
    if (match[3] && match[4]) {
      modifier = parseInt(match[4], 10);
      if (match[3] === '-') {
        modifier = -modifier;
      }
    }
    return { count, die, modifier };
  }
  return null;
}

export function rollDice(count: number, die: number): number {
  let total = 0;
  for (let i = 0; i < count; i++) {
    total += Math.floor(Math.random() * die) + 1;
  }
  return total;
}

export function calculateAttributeModifier(score: number): number {
  return Math.floor((score - 10) / 2);
}

export function getAmmunitionType(properties: string[]): string | null {
  const ammoProperty = properties.find(prop => prop.startsWith('弹药'));
  if (ammoProperty) {
    const match = ammoProperty.match(/；\s*([^)]+)\)/);
    if (match && match[1]) {
      return match[1].trim();
    }
  }
  return null;
}

export function getWeaponRangeIncrements(properties: string[]): WeaponRange | null {
  const rangeProperty = properties.find(prop => prop.includes('射程'));
  if (rangeProperty) {
    const match = rangeProperty.match(/射程\s*(\d+)(?:\/(\d+))?/);
    if (match) {
      const normal = parseInt(match[1], 10);
      const long = match[2] ? parseInt(match[2], 10) : null;
      if (!isNaN(normal)) {
        return { normal, long: long !== null && !isNaN(long) ? long : null };
      }
    }
  }
  return null;
}

export function getEffectiveWeaponProperties(
  weapon: EquipmentItem | undefined,
  weaponTemplates: WeaponTemplate[], // Added parameter
): string[] {
  if (!weapon) return [];

  let effectiveProperties: string[] = [];

  if (weapon.baseItemName && weaponTemplates && weaponTemplates.length > 0) {
    const template = weaponTemplates.find(
      wt => wt.name_zh === weapon.baseItemName || wt.name_en === weapon.baseItemName,
    );
    if (template && template.properties) {
      effectiveProperties = [...template.properties];
    }
  } else if (weapon.properties) {
    effectiveProperties = [...weapon.properties];
  }

  if (weapon.magicEffects) {
    weapon.magicEffects.forEach(effect => {
      if (effect.type === 'WEAPON_PROPERTY_GRANT' && typeof effect.value === 'string') {
        if (!effectiveProperties.includes(effect.value)) {
          effectiveProperties.push(effect.value);
        }
      } else if (effect.type === 'WEAPON_PROPERTY_MODIFY' && typeof effect.value === 'object') {
        // Example: effect.value = { action: "remove", propertyToRemove: "轻型" }
        // Example: effect.value = { action: "replace", propertyToReplace: "多用 (1d8)", newProperty: "多用 (1d10)" }
        // Example: effect.value = { action: "modifyDamageType", newType: "魔法钝击" } // This would be more complex
        if (effect.value.action === 'remove' && typeof effect.value.propertyToRemove === 'string') {
          effectiveProperties = effectiveProperties.filter(prop => prop !== effect.value.propertyToRemove);
        }
        // Add more modification actions as needed (e.g., 'replace', 'modifyDamageType')
      }
    });
  }
  return [...new Set(effectiveProperties)]; // Ensure uniqueness
}

export function getEffectivePlayerAC(playerState: PlayerState): number {
  let effectiveAC = playerState.ac; // Start with base AC

  playerState.equipment.forEach((item: EquipmentItem) => {
    if (item.equipped && item.magicEffects) {
      item.magicEffects.forEach((effect: MagicEffect) => {
        if (effect.type === 'AC_BONUS' && typeof effect.value === 'number') {
          effectiveAC += effect.value;
        }
      });
    }
  });
  return effectiveAC;
}

/**
 * 测试检定解析功能
 * 用于验证各种检定格式的解析是否正确
 */
export function testCheckParsing(): void {
  const testCases = [
    // 标准格式
    '"尝试撬开箱子[DC15 力量(运动)]"',
    '"说服守卫[DC12 魅力(游说)]"',
    '"感知陷阱[DC14 感知(察觉)]"',

    // 变体格式1: [属性(技能) DC数值]
    '"坎尼斯，我们共同的敌人是地精和脑子里的幼体。带我们去德鲁伊林地，对我们双方都有好处。[魅力(游说) DC13]"',
    '"威胁他交出钥匙[魅力(威吓) DC14]"',

    // 变体格式2: [属性(技能) DC数值 - 描述]
    '"软硬兼施地说服他[魅力(威吓) DC14 - 软硬兼施]"',
    '"巧妙地撬锁[敏捷(巧手) DC16 - 小心操作]"',

    // 变体格式3: [DC数值 属性检定(技能)]
    '"检查门锁[DC13 敏捷检定(巧手)]"',
    '"观察环境[DC12 感知检定(察觉)]"',

    // 变体格式4: [进行DC数值的属性检定]
    '"仔细搜索[进行DC15的感知检定]"',
    '"尝试跳跃[进行DC12的力量检定]"',
  ];

  const checkRegexPatterns = [
    // 标准格式: [DC15 力量(运动)]
    /\[DC(\d+)\s+([^\(\]\s]+)(?:\s*\(([^\)]+)\))?\s*(?:检定)?\]/i,
    // 变体格式1: [力量(运动) DC15]
    /\[([^\(\]\s]+)(?:\s*\(([^\)]+)\))?\s+DC(\d+)\s*(?:检定)?\]/i,
    // 变体格式2: [魅力(威吓) DC14 - 软硬兼施]
    /\[([^\(\]\s]+)(?:\s*\(([^\)]+)\))?\s+DC(\d+)\s*(?:-\s*[^\]]+)?\]/i,
    // 变体格式3: [DC13 魅力检定(游说)]
    /\[DC(\d+)\s+([^\(\]\s]+)(?:检定)?(?:\s*\(([^\)]+)\))?\]/i,
    // 变体格式4: [进行DC15的力量检定]
    /\[(?:进行)?DC(\d+)(?:的)?([^\(\]\s]+)(?:检定)?(?:\s*\(([^\)]+)\))?\]/i,
  ];

  const formatNames = [
    '标准格式 [DC15 力量(运动)]',
    '变体格式1 [力量(运动) DC15]',
    '变体格式2 [魅力(威吓) DC14 - 描述]',
    '变体格式3 [DC13 魅力检定(游说)]',
    '变体格式4 [进行DC15的力量检定]'
  ];

  console.log('=== 检定解析测试开始 ===');

  testCases.forEach((testCase, index) => {
    console.log(`\n测试案例 ${index + 1}: ${testCase}`);

    let matched = false;
    for (let i = 0; i < checkRegexPatterns.length; i++) {
      const regex = checkRegexPatterns[i];
      const match = testCase.match(regex);
      if (match) {
        let checkDC: number = 0;
        let checkAttribute: string = '';
        let checkSkill: string | undefined = undefined;

        if (i === 0 || i === 3 || i === 4) {
          // 标准格式: [DC15 力量(运动)] 或 [DC13 魅力检定(游说)] 或 [进行DC15的力量检定]
          checkDC = parseInt(match[1], 10);
          checkAttribute = match[2].trim();
          checkSkill = match[3] ? match[3].trim() : undefined;
        } else if (i === 1 || i === 2) {
          // 变体格式: [力量(运动) DC15] 或 [魅力(威吓) DC14 - 软硬兼施]
          checkAttribute = match[1].trim();
          checkSkill = match[2] ? match[2].trim() : undefined;
          checkDC = parseInt(match[3], 10);
        }

        console.log(`  ✅ 匹配成功: ${formatNames[i]}`);
        console.log(`     解析结果: DC${checkDC} ${checkAttribute}${checkSkill ? `(${checkSkill})` : ''}`);
        matched = true;
        break;
      }
    }

    if (!matched) {
      console.log('  ❌ 未匹配到任何格式');
    }
  });

  console.log('\n=== 检定解析测试结束 ===');
}
