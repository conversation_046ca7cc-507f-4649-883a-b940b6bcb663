/**
 * 1环法术生成器
 * 基于AI知识库生成完整的DND5e 1环法术数据
 */

const fs = require('fs');

// 1环法术数据
const LEVEL_1_SPELLS = [
  {
    name_zh: '魔法飞弹',
    name_en: 'Magic Missile',
    level: 1,
    school: '塑能',
    casting_time: '1 动作',
    range: '120尺',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '自动命中的魔法飞弹。',
    description_long: '你创造出三支闪光的魔法飞弹。每支飞弹自动命中你指定的一个位于射程内的可见生物，造成1d4+1力场伤害。',
    attack_type: '自动命中',
    damage: '1d4+1',
    damage_type: '力场',
    num_projectiles: 3,
    higher_level_cast: {
      per_slot_above_base: '每高于1环的法术位',
      effect: '增加一支飞弹'
    }
  },
  {
    name_zh: '治疗轻伤',
    name_en: 'Cure Wounds',
    level: 1,
    school: '塑能',
    casting_time: '1 动作',
    range: '触及',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '恢复目标的生命值。',
    description_long: '你触碰的生物恢复1d8+你的施法属性调整值点生命值。这个法术对不死生物或构装体无效。',
    damage: '1d8',
    damage_type: '治疗',
    add_casting_modifier_to_damage: true,
    higher_level_cast: {
      per_slot_above_base: '每高于1环的法术位',
      effect: '增加1d8治疗'
    }
  },
  {
    name_zh: '祝福术',
    name_en: 'Bless',
    level: 1,
    school: '惑控',
    casting_time: '1 动作',
    range: '30尺',
    components: ['V', 'S', 'M (一滴圣水)'],
    duration: '专注，至多1分钟',
    description_short: '为最多三个生物提供攻击和豁免加值。',
    description_long: '你祝福最多三个射程内的生物。每当目标进行攻击检定或豁免检定时，可以掷一个d4并将结果加到检定中。',
    higher_level_cast: {
      per_slot_above_base: '每高于1环的法术位',
      effect: '额外影响一个目标'
    }
  },
  {
    name_zh: '护盾术',
    name_en: 'Shield',
    level: 1,
    school: '防护',
    casting_time: '1 反应',
    range: '自身',
    components: ['V', 'S'],
    duration: '1轮',
    description_short: '获得+5AC加值并免疫魔法飞弹。',
    description_long: '一道无形的魔法力场出现并保护你。直到你下个回合开始，你的AC获得+5加值，包括触发此法术的攻击，且你不受魔法飞弹法术影响。'
  },
  {
    name_zh: '魅惑人类',
    name_en: 'Charm Person',
    level: 1,
    school: '惑控',
    casting_time: '1 动作',
    range: '30尺',
    components: ['V', 'S'],
    duration: '1小时',
    description_short: '魅惑一个类人生物。',
    description_long: '你试图魅惑射程内一个你能看见的类人生物。目标必须进行感知豁免。失败时，目标被你魅惑直到法术结束或你伤害它。',
    save: { attribute: '感知', effect_on_success: '无效果' },
    higher_level_cast: {
      per_slot_above_base: '每高于1环的法术位',
      effect: '额外影响一个目标'
    }
  },
  {
    name_zh: '侦测魔法',
    name_en: 'Detect Magic',
    level: 1,
    school: '预言',
    casting_time: '1 动作',
    range: '自身',
    components: ['V', 'S'],
    duration: '专注，至多10分钟',
    description_short: '感知附近的魔法灵光。',
    description_long: '在法术持续时间内，你感知到30尺内的魔法存在。如果你感知到魔法，你可以用动作看到微弱的灵光围绕着任何可见的魔法生物或物体。'
  },
  {
    name_zh: '燃烧之手',
    name_en: 'Burning Hands',
    level: 1,
    school: '塑能',
    casting_time: '1 动作',
    range: '自身（15尺锥形）',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '锥形火焰攻击多个目标。',
    description_long: '你的手指张开，火焰从中喷出。15尺锥形范围内的每个生物必须进行敏捷豁免。失败时受到3d6火焰伤害，成功时伤害减半。',
    save: { attribute: '敏捷', effect_on_success: '伤害减半' },
    damage: '3d6',
    damage_type: '火焰',
    area_of_effect: { type: '锥形', size: '15尺' },
    higher_level_cast: {
      per_slot_above_base: '每高于1环的法术位',
      effect: '增加1d6伤害'
    }
  },
  {
    name_zh: '睡眠术',
    name_en: 'Sleep',
    level: 1,
    school: '惑控',
    casting_time: '1 动作',
    range: '90尺',
    components: ['V', 'S', 'M (一撮沙子、玫瑰花瓣或蟋蟀)'],
    duration: '1分钟',
    description_short: '使生物陷入魔法睡眠。',
    description_long: '这个法术让生物陷入魔法睡眠。掷5d8；总和就是这个法术能影响的生命值总数。从射程内20尺半径内生命值最少的生物开始。',
    area_of_effect: { type: '球形', size: '20尺半径' },
    higher_level_cast: {
      per_slot_above_base: '每高于1环的法术位',
      effect: '增加2d8生命值'
    }
  },
  {
    name_zh: '雷鸣波',
    name_en: 'Thunderwave',
    level: 1,
    school: '塑能',
    casting_time: '1 动作',
    range: '自身（15尺立方）',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '雷鸣冲击波推开敌人。',
    description_long: '一阵雷鸣从你身上爆发。15尺立方范围内的每个生物必须进行体质豁免。失败时受到2d8雷鸣伤害并被推开10尺，成功时伤害减半且不被推开。',
    save: { attribute: '体质', effect_on_success: '伤害减半，不被推开' },
    damage: '2d8',
    damage_type: '雷鸣',
    area_of_effect: { type: '立方', size: '15尺' },
    higher_level_cast: {
      per_slot_above_base: '每高于1环的法术位',
      effect: '增加1d8伤害'
    }
  },
  {
    name_zh: '法师护甲',
    name_en: 'Mage Armor',
    level: 1,
    school: '防护',
    casting_time: '1 动作',
    range: '触及',
    components: ['V', 'S', 'M (一片皮革)'],
    duration: '8小时',
    description_short: '为目标提供护甲等级保护。',
    description_long: '你触碰一个不穿护甲的自愿生物。一层保护性的魔法力场围绕着它，直到法术结束。目标的基础AC变为13+敏捷调整值。'
  },
  {
    name_zh: '寻找魔宠',
    name_en: 'Find Familiar',
    level: 1,
    school: '咒法',
    casting_time: '1小时',
    range: '10尺',
    components: ['V', 'S', 'M (价值10gp的木炭、熏香和香草)'],
    duration: '立即',
    description_short: '召唤一个魔宠为你服务。',
    description_long: '你获得一个魔宠，一个为你服务的精魂。选择一种形态：蝙蝠、猫、蟹、青蛙、鹰、蜥蜴、章鱼、猫头鹰、毒蛇、鱼、鼠、渡鸦、海马、蜘蛛或黄鼠狼。'
  },
  {
    name_zh: '鉴定术',
    name_en: 'Identify',
    level: 1,
    school: '预言',
    casting_time: '1分钟',
    range: '触及',
    components: ['V', 'S', 'M (一颗价值100gp的珍珠)'],
    duration: '立即',
    description_short: '了解魔法物品的属性。',
    description_long: '你选择一个在法术持续时间内你必须接触的物品。如果它是魔法物品或其他魔法注入的物品，你了解它的属性以及如何使用它们。'
  },
  {
    name_zh: '跳跃术',
    name_en: 'Jump',
    level: 1,
    school: '变化',
    casting_time: '1 动作',
    range: '触及',
    components: ['V', 'S', 'M (一只蚱蜢的后腿)'],
    duration: '1分钟',
    description_short: '大幅提高目标的跳跃能力。',
    description_long: '你触碰一个生物。该生物的跳跃距离增加三倍，直到法术结束。'
  },
  {
    name_zh: '造成轻伤',
    name_en: 'Inflict Wounds',
    level: 1,
    school: '死灵',
    casting_time: '1 动作',
    range: '触及',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '通过触碰造成黯蚀伤害。',
    description_long: '进行一次近战法术攻击检定对抗你能触及的一个生物。命中时，目标受到3d10黯蚀伤害。',
    attack_type: '法术攻击 (近战)',
    damage: '3d10',
    damage_type: '黯蚀',
    higher_level_cast: {
      per_slot_above_base: '每高于1环的法术位',
      effect: '增加1d10伤害'
    }
  },
  {
    name_zh: '导引箭',
    name_en: 'Guiding Bolt',
    level: 1,
    school: '塑能',
    casting_time: '1 动作',
    range: '120尺',
    components: ['V', 'S'],
    duration: '1轮',
    description_short: '光耀能量攻击并标记目标。',
    description_long: '一道闪光的光束朝着射程内的一个生物射去。进行一次远程法术攻击检定。命中时，目标受到4d6光耀伤害，且下次攻击检定对其具有优势。',
    attack_type: '法术攻击 (远程)',
    damage: '4d6',
    damage_type: '光耀',
    higher_level_cast: {
      per_slot_above_base: '每高于1环的法术位',
      effect: '增加1d6伤害'
    }
  },
  {
    name_zh: '治疗之语',
    name_en: 'Healing Word',
    level: 1,
    school: '塑能',
    casting_time: '1 附赠动作',
    range: '60尺',
    components: ['V'],
    duration: '立即',
    description_short: '远程快速治疗。',
    description_long: '射程内一个你能看见的生物恢复1d4+你的施法属性调整值点生命值。这个法术对不死生物或构装体无效。',
    damage: '1d4',
    damage_type: '治疗',
    add_casting_modifier_to_damage: true,
    higher_level_cast: {
      per_slot_above_base: '每高于1环的法术位',
      effect: '增加1d4治疗'
    }
  },
  {
    name_zh: '英雄气概',
    name_en: 'Heroism',
    level: 1,
    school: '惑控',
    casting_time: '1 动作',
    range: '触及',
    components: ['V', 'S'],
    duration: '专注，至多1分钟',
    description_short: '使目标免疫恐惧并获得临时生命值。',
    description_long: '一个自愿的生物被你触碰后充满勇气。直到法术结束，该生物免疫恐惧，且在每个回合开始时获得等于你施法属性调整值的临时生命值。'
  },
  {
    name_zh: '妖术',
    name_en: 'Hex',
    level: 1,
    school: '惑控',
    casting_time: '1 附赠动作',
    range: '90尺',
    components: ['V', 'S', 'M (一只死去的蝾螈的眼珠)'],
    duration: '专注，至多1小时',
    description_short: '诅咒目标，增加伤害并削弱属性。',
    description_long: '你对射程内一个你能看见的生物施加诅咒。直到法术结束，你用攻击对目标造成的伤害额外增加1d6黯蚀伤害。',
    higher_level_cast: {
      per_slot_above_base: '3环或4环法术位',
      effect: '持续时间变为8小时；5环或更高法术位持续时间变为24小时'
    }
  },
  {
    name_zh: '猎人印记',
    name_en: 'Hunter\'s Mark',
    level: 1,
    school: '预言',
    casting_time: '1 附赠动作',
    range: '90尺',
    components: ['V'],
    duration: '专注，至多1小时',
    description_short: '标记猎物，增加伤害并便于追踪。',
    description_long: '你选择射程内一个你能看见的生物并神秘地标记它为你的猎物。直到法术结束，你用武器攻击对目标造成的伤害额外增加1d6。',
    higher_level_cast: {
      per_slot_above_base: '3环或4环法术位',
      effect: '持续时间变为8小时；5环或更高法术位持续时间变为24小时'
    }
  }
];

/**
 * 生成1环法术世界书
 */
function generateLevel1WorldBook() {
  const worldBook = {
    entries: {
      5001: {
        uid: 5001,
        key: ["AI_LEVEL_1_SPELLS", "AI一环法术", "DND5E_AI_LEVEL_1", "AI_SPELLS_LEVEL_1"],
        keysecondary: [],
        comment: `AI生成的DND5e一环法术完整数据 (${LEVEL_1_SPELLS.length}个法术)`,
        content: JSON.stringify(LEVEL_1_SPELLS, null, 2),
        constant: true,
        vectorized: false,
        selective: true,
        selectiveLogic: 0,
        addMemo: true,
        order: 100,
        position: 0,
        disable: false,
        excludeRecursion: false,
        preventRecursion: false,
        delayUntilRecursion: false,
        probability: 100,
        useProbability: true,
        depth: 4,
        group: "",
        groupOverride: false,
        groupWeight: 100,
        scanDepth: null,
        caseSensitive: null,
        matchWholeWords: null,
        useGroupScoring: null,
        automationId: "",
        role: null,
        sticky: 0,
        cooldown: 0,
        delay: 0,
        displayIndex: 5001
      }
    }
  };
  
  const outputPath = 'AI_DND5e_Level1_Complete.json';
  fs.writeFileSync(outputPath, JSON.stringify(worldBook, null, 2), 'utf-8');
  console.log(`生成1环法术世界书: ${outputPath} (${LEVEL_1_SPELLS.length}个法术)`);
  
  return LEVEL_1_SPELLS;
}

// 执行生成
if (require.main === module) {
  console.log('=== 1环法术生成器 ===');
  generateLevel1WorldBook();
  console.log('1环法术世界书生成完成！');
}

module.exports = { LEVEL_1_SPELLS, generateLevel1WorldBook };
