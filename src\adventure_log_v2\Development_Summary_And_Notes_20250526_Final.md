# 冒险日志 v2 (JSON版) 及 模组设置功能 - 开发总结与注意事项 (截至 2025-05-27)

本文档总结了“冒险日志 v2” (`src/adventure_log_v2`) 和“模组设置” (`src/module_setup`) 功能模块在近期开发和调试过程中的主要工作、已实现的功能、架构变更以及需要注意的事项。核心的转变是从基于自定义文本行的AI交互迁移到完全基于JSON对象的交互模式，并对历史记录管理和调试流程进行了优化。

## 一、已完成的主要工作与架构变更

### 1. AI交互格式与JSON处理优化

*   **核心目标**: 提高数据传输的健壮性、可扩展性，并优化调试体验。
*   **`adventure_log_v2` 的JSON化与标记更新**:
    *   **AI提示词 (`src/adventure_log_v2/adventure_log_v2_ai_prompts.md`)**:
        *   继续使用 `AdventureSceneJSON` Schema，但对AI的输出包裹格式进行了明确和强化。
        *   AI的完整回复现在**必须**严格遵循以下格式：
            ```
            查看系统
            ##@@_ADVENTURE_BEGIN_@@##
            {
              // ... AdventureSceneJSON 对象 ...
            }
            ##@@_ADVENTURE_END_@@##
            关闭系统
            ```
        *   引入了独特的标记 `##@@_ADVENTURE_BEGIN_@@##` 和 `##@@_ADVENTURE_END_@@##` 来包裹核心JSON数据，以避免与SillyTavern或其他扩展的通用标记冲突。
        *   **特别强调**：JSON字符串值内部如果需要换行，**必须**使用 `\\n` 转义序列，而不是实际的换行符，以确保 `JSON.parse()` 的成功。
    *   **客户端代码 (`src/adventure_log_v2/index.ts`)**:
        *   `handleActionChoice` 函数中的JSON提取逻辑已更新，以精确匹配新的 `##@@_ADVENTURE_BEGIN_@@##` 和 `##@@_ADVENTURE_END_@@##` 标记，并忽略外部的 `查看系统` 和 `关闭系统` 行。
        *   **调试流程改进**：移除了原有的 `DEBUG_RAW_AI_RESPONSE` 常量。现在，如果客户端在提取或解析AI返回的JSON时失败，会自动在主叙事区域显示AI的完整原始回复，并辅以 `toastr` 提示和控制台错误日志，方便快速定位问题。
        *   部分与加载状态和历史记录流程相关的 `toastr` 提示已被注释掉，以减少在正常操作期间的界面通知。

### 2. 历史记录管理机制的重大变更

*   **目标**: 简化客户端历史管理逻辑，利用SillyTavern的世界书功能实现更高效和用户友好的历史记录。
*   **实现方式 (`src/adventure_log_v2/index.ts`)**:
    *   **移除本地历史数组**: 不再使用客户端的 `fullHistoryLog` 数组在当前宿主消息中通过特定HTML注释标签（如 `HISTORY_START_TAG`）存储完整的对话历史。
    *   **专用历史世界书**:
        *   每次AI成功生成并返回一个场景后，其**完整的、干净的回复块**（即从 `查看系统` 开始，到 `关闭系统` 结束，包含了新的唯一标记和JSON内容的整个文本块）会被保存为一个新的条目到名为 `dndRPG_history.json` 的专用世界书中。
        *   如果无法从AI原始回复中提取出这个“干净块”，则会保存完整的原始回复并附带警告。
    *   **条目自动激活 (蓝灯)**:
        *   使用 `/createentry` 命令创建历史条目后，会立即通过 `/setentryfield` 命令将该条目的 `constant` 属性设置为 `true`。这相当于在SillyTavern中激活了该条目的“蓝灯”，使其内容能被SillyTavern自动包含在发送给AI的后续上下文提示中。
    *   **条目键名优化**: 保存到 `dndRPG_history.json` 的条目键名现在由场景的 `sceneTitle` 和 `time`（经过字符清理和长度限制）以及一个短随机后缀构成，例如 `失落神庙的秘密_第一天_黄昏_a1b2c`，提高了可读性。如果无法获取标题或时间，则使用基于时间戳的备用键名。
    *   **当前宿主消息简化**: 当前宿主消息（通过 `persistGameState` 函数管理）现在只持久化两部分核心内容：
        1.  最新的玩家状态 (`PlayerState` 对象，JSON格式，包裹在 `PLAYER_STATE_START_TAG` 和 `PLAYER_STATE_END_TAG`之间）。
        2.  最新的一个AI场景回复（同样是完整的 `查看系统 ... 关闭系统` 块）。
    *   **加载逻辑简化**: `loadGameState` 函数现在只从当前宿主消息中加载上述两部分内容，不再处理旧的历史记录块。

### 3. `adventure_log_v2` 客户端其他逻辑保持与增强 (与之前版本类似)

*   **玩家状态 (`PlayerState`) 管理**: 结构保持，用于跟踪角色数据。
*   **变量更新机制 (`VariableUpdateInstruction`, `applyVariableUpdate`)**: 核心逻辑不变，AI通过JSON指令精确更新玩家状态。
*   **本地投骰与检定反馈**: 流程保持，客户端执行检定并将详细结果反馈给AI。

### 4. `module_setup` 功能 (基本保持不变，但受益于JSON交互经验)
*   AI辅助模组创作和角色生成功能继续使用其独立的JSON Schema (`module_setup_ai_prompts_v2_json.md`)。
*   其与世界书的交互（如保存模组内容）为 `adventure_log_v2` 的历史记录新机制提供了参考。

## 二、重要注意事项与潜在问题

1.  **AI稳定输出有效JSON的挑战**:
    *   **核心依赖**: 仍然高度依赖AI准确生成符合Schema的JSON，特别是字符串内特殊字符（如换行符 `\\n`，双引号 `\\"`）的正确转义。
    *   **应对**:
        *   **严格的AI提示词**: `adventure_log_v2_ai_prompts.md` 中已针对换行符等问题进行了特别强调和示例说明。
        *   **客户端健壮解析与调试**: `parseAIResponse` 包含 `try...catch`。新的调试流程会在解析失败时直接展示AI原始回复，便于定位问题。
        *   **问题诊断文档**: 创建了 `src/adventure_log_v2/known_issues/json_parsing_debug.md` 来记录和指导此类问题的排查。

2.  **世界书交互的可靠性**:
    *   新的历史记录机制依赖于 `/createentry` 和 `/setentryfield` 斜杠命令的稳定执行。需要注意这些命令对内容长度、特殊字符的限制，以及执行权限等问题。
    *   `entryContent` 在传递给 `/createentry` 时，如果内容过长或包含破坏命令语法的字符，可能需要进一步处理（例如，分块、更严格的转义，或通过其他方式传递大数据）。目前是直接传递。

3.  **SillyTavern上下文管理**:
    *   依赖SillyTavern正确处理大量“蓝灯”世界书条目并将其有效地注入AI上下文。如果历史记录条目过多，可能会超出SillyTavern或AI模型的上下文长度限制。用户可能需要定期清理或禁用旧的历史条目。

4.  **JSON Schema的维护与演进**: (与之前相同)
    *   任何Schema的变动都需要同步更新AI提示词文档和客户端的TypeScript接口定义。

5.  **客户端与AI的职责边界 (检定与伤害)**: (与之前相同)
    *   检定由客户端完成，伤害计算（掷骰）由AI在叙述中宣告。这个边界需要持续关注和测试。

## 三、总结与后续展望

通过引入独特的JSON包裹标记、将历史记录管理迁移到专用世界书并利用SillyTavern的上下文机制，以及改进调试流程，`adventure_log_v2` 模块的健壮性、可维护性和用户体验得到了进一步提升。

**后续可能的优化方向：**
*   **细化战斗逻辑**: (同前)
*   **增强JSON Schema验证**: (同前)
*   **UI交互细节打磨**: (同前)
*   **历史世界书管理**: 考虑在UI中提供一些辅助功能来管理 `dndRPG_history.json` 中的条目，例如按日期归档、批量禁用旧条目等，以帮助用户控制上下文长度。
*   **`/createentry` 内容处理**: 针对非常长的 `entryContent`，研究更可靠的传递方式给斜杠命令，或在AI提示中限制单次回复的长度。

这些改进为构建一个更稳定、功能更丰富的文字冒险游戏奠定了更坚实的基础。

---
## 四、迭代更新 (截至 2025-05-30)

### 1. 本次迭代工作总结

*   **`adventure_log_v2` TypeScript 逻辑修复与调整**:
    *   **技能显示修复**: 修复了“显示详细角色卡”模块中技能值显示为 `(undefined)` 的问题。更新了 `PlayerState` 中的 `Skill` 接口定义，以匹配 `module_setup` 生成的新角色数据格式（包含 `name`, `proficient`, `attribute`, `modifierValue`, `finalValue`）。相应地，修改了 `updatePlayerStatusDisplay` 函数，使其正确读取并显示技能的 `finalValue`。
    *   **移除AI端HP解析**: 根据用户指示，从 `applySceneData` 函数中移除了对AI返回的 `AdventureSceneJSON.playerHp` 字段的解析逻辑。现在玩家的生命值完全由本地 `playerState.hp` 管理和更新。同时，从 `AdventureSceneJSON` 接口定义和 `generateInitialAdventureSceneJSON` 函数中移除了 `playerHp` 字段。

*   **`adventure_log_v2` UI 布局与样式优化 (`index.scss`)**:
    *   **解决内部滚动问题**: 针对窄屏设备（如手机），移除了主叙述区 (`#main-narrative-area`) 的 `overflow-y: auto` 和高度限制，允许其内容自然撑高。同时调整了主容器 (`#adventure-log-container`) 的高度管理方式（移除了 `aspect-ratio`、`min-height` 和自身的 `overflow-y: auto`），使其高度能完全由内容撑开，滚动条由父级（最终为SillyTavern主界面或`body`）处理，实现了更流畅的“长页面”滚动体验。
    *   **优化宽屏显示**:
        *   将 `#adventure-log-container` 在宽屏下的 `max-width` 增加到 `700px`，提供了更宽敞的内容阅读区域。
        *   移除了 `body` 的 `flex` 布局，并调整 `#adventure-log-container` 的 `margin` 为 `0 auto`，使其在正常的文档流中从靠近页面顶部的位置开始显示，有效解决了之前在内容较少时，容器顶部和底部可能出现的多余空白问题。
    *   **背景美化**: 为 `body` 背景在宽屏显示时，于原有SVG纹理和颜色叠加层之上，新增了一层从两侧向中间过渡的半透明暗色线性渐变 (`linear-gradient(to right, rgba(0,0,0,0.25) 0%, transparent 15%, transparent 85%, rgba(0,0,0,0.25) 100%)`)，旨在增强内容区域的视觉焦点，并美化两侧空白区域。

### 2. 下一步计划：升级系统初步规划

*   **核心需求**:
    *   当角色的经验值 (`playerState.exp`) 达到预设经验表的升级阈值时，系统应向玩家发出可以升级的提示。
    *   玩家应可以选择是否立即进行升级。无论选择如何，相关提示或状态应有所体现。
    *   如果玩家选择升级，将进入一个升级流程/界面，根据简化的D&D规则（或其他预设规则）来提升角色能力，例如增加属性点、添加新技能或学习新法术。

*   **简化设计与D&D趣味性平衡的初步思路**:
    *   **经验表 (Experience Table)**:
        *   在客户端或通过可配置的JSON文件（例如，世界书条目）预设一个等级与所需经验值的对照表。例如，遵循D&D 5e标准经验阈值或一个自定义的简化版本。
    *   **升级触发与提示**:
        *   在每次经验值发生变动后（例如，通过 `applyVariableUpdate` 函数），检查 `playerState.exp` 是否达到当前等级 `playerState.level` 对应的升级经验。
        *   如果满足条件且角色尚未达到最高等级，可以在行动选项区 (`actionChoicesArea`) 动态添加一个“角色升级！”或类似文本的按钮/选项。
        *   如果玩家选择“暂不升级”（如果提供该选项），该升级提示可以暂时隐藏，或在后续某些时刻（如长休后、进入新区域后）再次出现。
    *   **升级流程与UI交互**:
        *   **生命值 (HP) 提升**: 根据角色职业的生命骰（Hit Dice）和体质调整值，在新等级自动计算并增加最大生命值。当前生命值可以选择完全恢复或按比例增加。
        *   **属性点提升 (Ability Score Improvement - ASI)**:
            *   在D&D 5e规则中的特定等级（通常是4级、8级、12级、16级、19级），允许玩家进行属性提升。
            *   简化选项：提供“将一个属性点+2”或“将两个不同的属性点各+1”的选择。
            *   更简化：某些职业在特定等级可能会有固定的属性提升，或者提供非常有限的几个预设方案供选择。
        *   **职业特性、技能与法术学习**:
            *   **职业特性**: 根据角色等级和职业，自动解锁新的职业特性，或从少量可选特性中让玩家选择。这些特性及其效果描述可以预存在数据中。
            *   **技能熟练**: 在特定等级（例如，某些背景或职业特性触发时）允许玩家选择一个新的技能熟练项。
            *   **法术学习 (施法职业)**:
                *   根据等级和职业的法术表，自动获得新的法术槽。
                *   提供一个该等级可选的新法术列表（从简化的、与模组主题相关的法术库中筛选），允许玩家学习规定数量的新法术。
        *   **UI呈现方式**:
            *   避免设计过于复杂的独立升级界面。
            *   可以考虑在点击“升级”按钮后，通过一系列的模态对话框（例如，使用 `window.prompt` 或自定义的简单HTML模态框）或者在主叙事区利用AI引导和 `PlayerChoiceJSON` 选项来分步完成升级决策（如先选择ASI，再选择法术等）。
            *   升级完成后，`updatePlayerStatusDisplay()` 会刷新角色卡显示。
    *   **AI在升级过程中的角色**:
        *   AI可以用于生成升级过程的描述性文本，例如“经过一番刻苦冥想，你对奥术的理解加深了，现在可以选择一个新的法术…”
        *   AI也可以在玩家做出选择后（如选择了某个新法术），生成一段关于这个新能力的描述或角色获得新力量的感受。
        *   AI不直接处理升级的规则逻辑（如哪些属性可以提升、可选法术列表），这些规则应由客户端预设或从数据源加载。
    *   **数据结构考量**:
        *   `PlayerState` 可能需要扩展，以记录ASI的选择历史、额外获得的技能/法术及其来源（例如，“通过4级ASI获得”）。
        *   需要为每个职业定义其升级路径数据，包括：生命骰类型、特性获取等级及列表、法术表（对施法者而言）、ASI获得等级等。这些数据可以结构化为JSON对象，存储在单独的文件中，或作为世界书条目，在游戏初始化或需要时加载。
    *   **保持D&D规则性趣味**:
        *   即使流程简化，核心的选择（如属性点分配给哪个属性、学习哪个新法术）应保留给玩家，这是角色扮演和成长的核心乐趣。
        *   关键的、能显著改变玩法的职业特性应尽量保留其核心机制。

这个初步规划旨在平衡系统的复杂度和D&D核心升级体验的保留。具体实现细节（如UI交互方式、数据存储结构）还需要进一步细化。
