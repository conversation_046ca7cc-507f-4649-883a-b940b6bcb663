.status-bar {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: #f8f8f8; // 状态栏背景色
  border-bottom: 1px solid #e0e0e0; // 状态栏底部边框
  flex-shrink: 0; // 防止状态栏在flex布局中被压缩
  margin-top: 20px; // 为刘海留出空间

  .character-avatar {
    img {
      width: 40px; // 头像宽度
      height: 40px; // 头像高度
      border-radius: 50%; // 圆形头像
      margin-right: 10px;
      object-fit: cover; // 确保图片不变形
    }
  }

  .character-name {
    font-weight: bold;
    margin-right: auto; // 将状态推到右边
  }

  .character-status {
    font-size: 0.9em;
    color: #555;
  }
}
