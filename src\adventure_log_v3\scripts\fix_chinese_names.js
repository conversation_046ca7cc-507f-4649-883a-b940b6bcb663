/**
 * 修正中文法术名称的脚本
 * 手动映射常见法术的正确中文名称
 */

const fs = require('fs');

// 法术中英文名称映射表
const SPELL_NAME_MAP = {
  // 戏法 (0环)
  'Acid_Splash': '酸液飞溅',
  'Blade_Ward': '剑刃防护',
  'Chill_Touch': '颤栗之触',
  'Dancing_Lights': '舞光术',
  'Druidcraft': '德鲁伊伎俩',
  'Eldritch_Blast': '魔能爆',
  'Elementalism': '四象法门',
  'Fire_Bolt': '火焰箭',
  'Friends': '交友术',
  'Guidance': '神导术',
  'Light': '光亮术',
  'Mage_Hand': '法师之手',
  'Mending': '修复术',
  'Message': '传讯术',
  'Mind_Sliver': '心灵之屑',
  'Minor_Illusion': '次级幻象',
  'Poison_Spray': '毒气喷涌',
  'Prestidigitation': '魔法伎俩',
  'Produce_Flame': '燃火术',
  'Ray_of_Frost': '冷冻射线',
  'Resistance': '抵抗术',
  'Sacred_Flame': '圣火术',
  'Shillelagh': '橡棍术',
  'Shocking_Grasp': '电爪',
  'Sorcerous_Burst': '术法爆发',
  'Spare_the_Dying': '维生术',
  'Starry_Wisp': '点点星茫',
  'Thaumaturgy': '奇术',
  'Thorn_Whip': '荆棘之鞭',
  'Thunderclap': '鸣雷破',
  'Toll_the_Dead': '亡者丧钟',
  'True_Strike': '克敌先击',
  'Vicious_Mockery': '恶言相加',
  'Word_of_Radiance': '光耀圣词',
  
  // 1环法术 (部分常见的)
  'Alarm': '警报术',
  'Animal_Friendship': '化兽为友',
  'Armor_of_Agathys': '阿迦希斯护甲',
  'Bane': '灾祸术',
  'Bless': '祝福术',
  'Burning_Hands': '燃烧之手',
  'Charm_Person': '魅惑人类',
  'Cure_Wounds': '治疗轻伤',
  'Detect_Magic': '侦测魔法',
  'Disguise_Self': '易容术',
  'Faerie_Fire': '妖火',
  'False_Life': '虚假生命',
  'Feather_Fall': '羽落术',
  'Find_Familiar': '寻找魔宠',
  'Fog_Cloud': '雾云术',
  'Goodberry': '造粮术',
  'Grease': '油腻术',
  'Guiding_Bolt': '导引箭',
  'Healing_Word': '治疗之语',
  'Heroism': '英雄气概',
  'Hex': '妖术',
  'Hunter_Mark': '猎人印记',
  'Identify': '鉴定术',
  'Inflict_Wounds': '造成轻伤',
  'Jump': '跳跃术',
  'Longstrider': '行动自如',
  'Mage_Armor': '法师护甲',
  'Magic_Missile': '魔法飞弹',
  'Protection_from_Evil_and_Good': '防护善恶',
  'Shield': '护盾术',
  'Shield_of_Faith': '信念护盾',
  'Silent_Image': '无声幻象',
  'Sleep': '睡眠术',
  'Speak_with_Animals': '动物交谈',
  'Spiritual_Weapon': '灵体武器',
  'Thunderwave': '雷鸣波',
  'Unseen_Servant': '隐形仆役',
  
  // 2环法术 (部分常见的)
  'Aid': '援助术',
  'Alter_Self': '变身术',
  'Animal_Messenger': '动物信使',
  'Barkskin': '树肤术',
  'Blindness_Deafness': '目盲/耳聋',
  'Blur': '朦胧术',
  'Cloud_of_Daggers': '匕首云雾',
  'Darkness': '黑暗术',
  'Darkvision': '黑暗视觉',
  'Detect_Thoughts': '侦测思想',
  'Enhance_Ability': '增强属性',
  'Enlarge_Reduce': '放大/缩小',
  'Flaming_Sphere': '火焰法球',
  'Gentle_Repose': '温和安息',
  'Heat_Metal': '炽热金属',
  'Hold_Person': '人类定身',
  'Invisibility': '隐形术',
  'Knock': '敲击术',
  'Lesser_Restoration': '次级复原术',
  'Levitate': '浮空术',
  'Locate_Object': '定位物品',
  'Magic_Weapon': '魔化武器',
  'Mirror_Image': '镜影术',
  'Misty_Step': '迷雾步',
  'Pass_without_Trace': '行无踪迹',
  'Prayer_of_Healing': '治疗祷言',
  'Scorching_Ray': '灼热射线',
  'See_Invisibility': '识破隐形',
  'Shatter': '粉碎音波',
  'Silence': '沉默术',
  'Spiritual_Weapon': '灵体武器',
  'Suggestion': '暗示术',
  'Web': '蛛网术',
  
  // 3环法术 (部分常见的)
  'Animate_Dead': '活化死尸',
  'Aura_of_Vitality': '活力灵光',
  'Beacon_of_Hope': '希望信标',
  'Bestow_Curse': '降咒术',
  'Blink': '闪烁术',
  'Call_Lightning': '召雷术',
  'Clairvoyance': '千里眼',
  'Counterspell': '反制法术',
  'Create_Food_and_Water': '造粮造水',
  'Daylight': '昼明术',
  'Dispel_Magic': '解除魔法',
  'Fear': '恐惧术',
  'Fireball': '火球术',
  'Fly': '飞行术',
  'Gaseous_Form': '气化形体',
  'Haste': '加速术',
  'Hypnotic_Pattern': '催眠图纹',
  'Lightning_Bolt': '闪电束',
  'Magic_Circle': '法术环',
  'Major_Image': '高等幻象',
  'Mass_Healing_Word': '群体治疗之语',
  'Meld_into_Stone': '融身入石',
  'Plant_Growth': '植物滋长',
  'Protection_from_Energy': '能量防护',
  'Remove_Curse': '移除诅咒',
  'Revivify': '复生术',
  'Slow': '缓慢术',
  'Speak_with_Dead': '死者交谈',
  'Spirit_Guardians': '灵体守卫',
  'Tongues': '巧言术',
  'Vampiric_Touch': '吸血鬼之触',
  'Water_Breathing': '水下呼吸',
  'Water_Walk': '水上行走'
};

/**
 * 修正世界书文件中的中文名称
 */
function fixChineseNames(inputFile, outputFile) {
  try {
    console.log(`处理文件: ${inputFile}`);
    
    // 读取原始文件
    const content = fs.readFileSync(inputFile, 'utf-8');
    const worldBook = JSON.parse(content);
    
    // 遍历所有条目
    for (const [uid, entry] of Object.entries(worldBook.entries)) {
      if (entry.content) {
        try {
          const spells = JSON.parse(entry.content);
          
          // 修正每个法术的中文名称
          spells.forEach(spell => {
            if (SPELL_NAME_MAP[spell.id]) {
              const correctName = SPELL_NAME_MAP[spell.id];
              spell.name_zh = correctName;
              spell.raw_name = `${correctName}｜${spell.name_en}`;
              console.log(`修正: ${spell.id} -> ${correctName}`);
            }
          });
          
          // 更新内容
          entry.content = JSON.stringify(spells, null, 2);
          
        } catch (parseError) {
          console.error(`解析法术内容时出错:`, parseError);
        }
      }
    }
    
    // 保存修正后的文件
    fs.writeFileSync(outputFile, JSON.stringify(worldBook, null, 2), 'utf-8');
    console.log(`修正完成，保存到: ${outputFile}`);
    
  } catch (error) {
    console.error(`处理文件 ${inputFile} 时出错:`, error);
  }
}

/**
 * 批量处理所有世界书文件
 */
function fixAllWorldBooks() {
  const files = [
    'DND5e_戏法_WorldBook.json',
    'DND5e_一环法术_WorldBook.json',
    'DND5e_二环法术_WorldBook.json',
    'DND5e_三环法术_WorldBook.json',
    'DND5e_四环法术_WorldBook.json',
    'DND5e_五环法术_WorldBook.json',
    'DND5e_六环法术_WorldBook.json',
    'DND5e_七环法术_WorldBook.json',
    'DND5e_八环法术_WorldBook.json',
    'DND5e_九环法术_WorldBook.json',
    'DND5e_Complete_Spells_WorldBook.json'
  ];
  
  files.forEach(file => {
    if (fs.existsSync(file)) {
      const outputFile = file.replace('.json', '_Fixed.json');
      fixChineseNames(file, outputFile);
    } else {
      console.log(`文件不存在: ${file}`);
    }
  });
}

// 执行修正
if (require.main === module) {
  console.log('开始修正中文法术名称...\n');
  fixAllWorldBooks();
  console.log('\n所有文件修正完成！');
}
