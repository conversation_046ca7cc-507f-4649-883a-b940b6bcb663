/**
 * 法术解析脚本 - 从DND5e手册HTML文件中解析法术数据
 */

const fs = require('fs');
const path = require('path');

// 伤害类型映射
const DAMAGE_TYPE_MAP = {
  '火焰': 'fire',
  '寒冷': 'cold',
  '闪电': 'lightning',
  '雷鸣': 'thunder',
  '强酸': 'acid',
  '毒素': 'poison',
  '光耀': 'radiant',
  '黯蚀': 'necrotic',
  '力场': 'force',
  '穿刺': 'piercing',
  '挥砍': 'slashing',
  '钝击': 'bludgeoning',
  '心灵': 'psychic'
};

// 移除interface定义，使用普通对象

/**
 * 解析HTML文件中的法术数据
 */
function parseSpellsFromHTML(htmlContent, level) {
  const spells = [];
  
  // 使用正则表达式匹配法术条目
  const spellPattern = /<H4[^>]*id="([^"]*)"[^>]*>([^<]*)<\/H4>\s*<P><EM>([^<]*)<\/EM>[^<]*<BR><STRONG>施法时间：<\/STRONG>([^<]*)<BR><STRONG>施法距离：<\/STRONG>([^<]*)<BR><STRONG>法术成分：<\/STRONG>([^<]*)<BR><STRONG>持续时间：<\/STRONG>([^<]*)<BR>([^]*?)(?=<H4|$)/g;
  
  let match;
  while ((match = spellPattern.exec(htmlContent)) !== null) {
    const [, id, nameWithEn, schoolInfo, castingTime, range, components, duration, description] = match;
    
    // 解析中英文名称
    const nameMatch = nameWithEn.match(/^([^（]+)(?:（([^）]+)）)?/);
    if (!nameMatch) continue;
    
    const name_zh = nameMatch[1].trim();
    const name_en = nameMatch[2] || id.replace(/_/g, ' ');
    
    // 解析学派信息
    const schoolMatch = schoolInfo.match(/([^ ]+) /);
    const school = schoolMatch ? schoolMatch[1] : '未知';
    
    // 解析法术成分
    const componentsList = components.split('，').map(c => c.trim());
    
    // 解析描述，提取伤害、豁免等信息
    const spell: ParsedSpell = {
      name_zh,
      name_en,
      level,
      school,
      casting_time: castingTime.trim(),
      range: range.trim(),
      components: componentsList,
      duration: duration.trim(),
      description_short: extractShortDescription(description),
      description_long: cleanDescription(description)
    };
    
    // 解析伤害信息
    const damageMatch = description.match(/(\d+d\d+(?:\+\d+)?)/);
    if (damageMatch) {
      spell.damage = damageMatch[1];
    }
    
    // 解析伤害类型
    for (const [zh] of Object.entries(DAMAGE_TYPE_MAP)) {
      if (description.includes(zh)) {
        spell.damage_type = zh;
        break;
      }
    }
    
    // 解析攻击类型
    if (description.includes('远程法术攻击')) {
      spell.attack_type = '法术攻击 (远程)';
    } else if (description.includes('近战法术攻击')) {
      spell.attack_type = '法术攻击 (近战)';
    }
    
    // 解析豁免检定
    const saveMatch = description.match(/([^，]+)豁免/);
    if (saveMatch) {
      const saveAttr = saveMatch[1].trim();
      spell.save = {
        attribute: saveAttr,
        effect_on_success: description.includes('伤害减半') ? '伤害减半' : '无效果'
      };
    }
    
    // 解析升环效果
    const higherLevelMatch = description.match(/<STRONG>升环施法：<\/STRONG>([^<]*)/);
    if (higherLevelMatch) {
      spell.higher_level_cast = {
        per_slot_above_base: '每高于基础环位的法术位',
        effect: higherLevelMatch[1].trim()
      };
    }
    
    // 解析戏法升级
    if (level === 0) {
      const scalingMatch = description.match(/<STRONG>戏法强化：<\/STRONG>([^<]*)/);
      if (scalingMatch) {
        spell.scaling = parseCantripsScaling(scalingMatch[1]);
      }
    }
    
    spells.push(spell);
  }
  
  return spells;
}

/**
 * 提取简短描述
 */
function extractShortDescription(description: string): string {
  // 移除HTML标签
  const cleaned = description.replace(/<[^>]*>/g, '');
  // 取第一句话作为简短描述
  const firstSentence = cleaned.split('。')[0];
  return firstSentence.length > 100 ? firstSentence.substring(0, 100) + '...' : firstSentence + '。';
}

/**
 * 清理描述文本
 */
function cleanDescription(description: string): string {
  return description
    .replace(/<[^>]*>/g, '') // 移除HTML标签
    .replace(/&nbsp;/g, ' ') // 替换空格实体
    .replace(/\s+/g, ' ') // 合并多个空格
    .trim();
}

/**
 * 解析戏法升级信息
 */
function parseCantripsScaling(scalingText: string): Record<string, string> {
  const scaling: Record<string, string> = {};
  
  // 匹配类似 "5级：2d10，11级：3d10，17级：4d10" 的模式
  const matches = scalingText.matchAll(/(\d+)级：([^，。]+)/g);
  for (const match of matches) {
    scaling[match[1]] = match[2].trim();
  }
  
  return scaling;
}

/**
 * 主解析函数
 */
async function parseAllSpells() {
  const allSpells = [];
  const baseDir = 'DND5e_chm-main/DND5e_chm-main/玩家手册2024/法术详述';
  
  // 解析每个等级的法术文件
  for (let level = 0; level <= 9; level++) {
    const filename = `${level}环.htm`;
    const filepath = path.join(baseDir, filename);
    
    try {
      console.log(`正在解析 ${level}环法术...`);
      const htmlContent = fs.readFileSync(filepath, 'utf-8');
      const spells = parseSpellsFromHTML(htmlContent, level);
      
      console.log(`${level}环法术解析完成，共 ${spells.length} 个法术`);
      allSpells.push(...spells);
    } catch (error) {
      console.error(`解析 ${level}环法术时出错:`, error);
    }
  }
  
  console.log(`总共解析了 ${allSpells.length} 个法术`);
  return allSpells;
}

/**
 * 生成世界书格式的法术数据
 */
function generateWorldBookEntries(spells) {
  const entries = {};
  let uid = 1000; // 从1000开始，避免与现有条目冲突

  // 按等级分组法术
  const spellsByLevel = {};
  spells.forEach(spell => {
    if (!spellsByLevel[spell.level]) {
      spellsByLevel[spell.level] = [];
    }
    spellsByLevel[spell.level].push(spell);
  });
  
  // 为每个等级创建法术包条目
  for (const [level, levelSpells] of Object.entries(spellsByLevel)) {
    const levelNum = parseInt(level);
    const levelName = levelNum === 0 ? '戏法' : `${levelNum}环法术`;
    
    entries[uid] = {
      uid: uid,
      key: [`SPELLS_LEVEL_${levelNum}`, `${levelName}`, `LEVEL_${levelNum}_SPELLS`],
      keysecondary: [],
      comment: `${levelName}合集 (${levelSpells.length}个法术)`,
      content: JSON.stringify(levelSpells, null, 2),
      constant: true,
      vectorized: false,
      selective: true,
      selectiveLogic: 0,
      addMemo: true,
      order: 100,
      position: 0,
      disable: false,
      excludeRecursion: false,
      preventRecursion: false,
      delayUntilRecursion: false,
      probability: 100,
      useProbability: true,
      depth: 4,
      group: "",
      groupOverride: false,
      groupWeight: 100,
      scanDepth: null,
      caseSensitive: null,
      matchWholeWords: null,
      useGroupScoring: null,
      automationId: "",
      role: null,
      sticky: 0,
      cooldown: 0,
      delay: 0,
      displayIndex: uid
    };
    uid++;
  }
  
  // 创建完整法术库条目
  entries[uid] = {
    uid: uid,
    key: ["SPELLS_ALL", "所有法术", "ALL_SPELLS", "法术库"],
    keysecondary: [],
    comment: `完整法术库 (${spells.length}个法术)`,
    content: JSON.stringify(spells, null, 2),
    constant: true,
    vectorized: false,
    selective: true,
    selectiveLogic: 0,
    addMemo: true,
    order: 100,
    position: 0,
    disable: false,
    excludeRecursion: false,
    preventRecursion: false,
    delayUntilRecursion: false,
    probability: 100,
    useProbability: true,
    depth: 4,
    group: "",
    groupOverride: false,
    groupWeight: 100,
    scanDepth: null,
    caseSensitive: null,
    matchWholeWords: null,
    useGroupScoring: null,
    automationId: "",
    role: null,
    sticky: 0,
    cooldown: 0,
    delay: 0,
    displayIndex: uid
  };
  
  return { entries };
}

// 如果直接运行此脚本
if (require.main === module) {
  parseAllSpells().then(spells => {
    const worldBook = generateWorldBookEntries(spells);

    // 保存到文件
    const outputPath = '../../../DND5e_Spells_WorldBook.json';
    fs.writeFileSync(outputPath, JSON.stringify(worldBook, null, 2), 'utf-8');
    console.log(`世界书文件已生成: ${outputPath}`);
  }).catch(console.error);
}
