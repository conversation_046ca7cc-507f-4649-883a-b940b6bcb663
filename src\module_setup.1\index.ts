import './index.scss';
import { onMounted } from './ui';

// SillyTavern / TavernHelper injected function type declarations
// These are declared in other modules where they are used (e.g. utils.ts, module_creation.ts)
// We might not need to redeclare them here if they are not directly used in this file.
// However, it's good practice to ensure they are available if any top-level logic here needs them.
declare function triggerSlash(command: string): Promise<string | undefined>;
declare const toastr: {
  success: (message: string, title?: string) => void;
  info: (message: string, title?: string) => void;
  error: (message: string, title?: string) => void;
  warning: (message: string, title?: string) => void;
};
declare const $: any;


// Ensure globals are available from parent if in iframe
if (typeof window !== 'undefined' && typeof parent !== 'undefined' && parent !== window) {
  const apiKeysToCopy = ['$', 'toastr', 'triggerSlash'];
  apiKeysToCopy.forEach(key => {
    if (typeof (window as any)[key] === 'undefined' && typeof (parent as any)[key] !== 'undefined') {
      // console.log(`[ModuleSetup] Copying ${key} from parent to window.`);
      (window as any)[key] = (parent as any)[key];
    }
  });
}

// Script entry point
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  onMounted();
} else {
  document.addEventListener('DOMContentLoaded', onMounted);
}
