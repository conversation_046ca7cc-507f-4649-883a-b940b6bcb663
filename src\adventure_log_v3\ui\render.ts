import {
  PlayerState,
  AdventureSceneJSON,
  PlayerChoiceJSON,
  Attribute,
  NarrativeEntry,
  EnemyStateJSON,
  EquipmentItem,
  InventoryItem,
  WeaponTemplate,
  MagicEffect,
  SpellTemplate,
} from '../types';
import { getEffectivePlayerAC, getEffectiveWeaponProperties, safeToastr } from '../utils'; // Import the new AC calculation function
import { weaponTemplates, currentSceneData } from '../core/state'; // Import weaponTemplates and currentSceneData
import { getSpellTemplates, canCastSpell } from '../spells'; // Import spell functions

import {
  mainNarrativeArea,
  actionChoicesArea,
  healthDisplay,
  locationDisplay,
  timeDisplay,
  charNameDisplay,
  charRaceClassDisplay,
  charLevelDisplay,
  acDisplay,
  attrStrDisplay,
  attrDexDisplay,
  attrConDisplay,
  attrIntDisplay,
  attrWisDisplay,
  attrChaDisplay,
  // currencyGoldDisplay, // Removed
  expDisplay,
  exhaustionDisplay,
  proficienciesDisplay,
  skillsDisplay,
  spellSlotsDisplay,
  equippedSpellsDisplay,
  // equipmentDisplay, // Removed
  // inventoryDisplay, // Removed
  activeQuestsDisplay,
  // New backpack elements
  backpackCurrencyGold,
  backpackCurrencySilver,
  backpackCurrencyCopper,
  backpackEquippedItemsArea, // Corrected: this is the div container for equipped items
  backpackInventoryItemsArea, // Corrected: this is the div container for inventory items
  // Spellbook elements
  spellbookSpellSlotsDisplay,
  spellbookPreparedSpellsList,
  spellbookAvailableSpellsList,
  spellDetailName,
  spellDetailLevel,
  spellDetailSchool,
  spellDetailCastingTime,
  spellDetailRange,
  spellDetailComponents,
  spellDetailDuration,
  spellDetailDescription,
  spellSlotLevelSelect,
  spellTargetTypeSelect,
  spellTargetCustomInput,
  customTargetInputDiv,
} from './domElements';

// --- New Rendering Functions for Backpack ---

export function renderCurrencyDisplay(playerState: PlayerState): void {
  if (backpackCurrencyGold) backpackCurrencyGold.textContent = `${playerState.currency?.gold || 0}`;
  if (backpackCurrencySilver) backpackCurrencySilver.textContent = `${playerState.currency?.silver || 0}`;
  if (backpackCurrencyCopper) backpackCurrencyCopper.textContent = `${playerState.currency?.copper || 0}`;
}

export function renderEquippedItemsDisplay(playerState: PlayerState): void {
  if (!backpackEquippedItemsArea) return; // Use the area container

  const equipped = playerState.equipment.filter(item => item.equipped);

  if (equipped.length > 0) {
    let html = '<ul id="backpack-equipped-list">'; // Ensure the ul has the ID
    equipped.forEach((item: EquipmentItem) => {
      const itemId = item.id || `equipped_${Date.now()}_${Math.random().toString(36).substring(2,7)}`;
      html += `<li class="equipment-item" data-item-id="${itemId}">`;
      html += `<div>`; // Wrapper for item name and buttons
      html += `<span class="item-name-toggle" data-item-id="${itemId}" role="button" tabindex="0"><strong>${item.name}</strong> (${item.type})</span>`;
      html += `<button class="backpack-button unequip-button" data-item-id="${itemId}">卸下</button>`;
      html += `</div>`;
      
      // Details content - initially hidden
      html += `<div class="item-details-content" id="details-equipped-${itemId}" style="display: none;">`;
      
      let detailsContent = '';
      if (item.baseItemName) {
        const template = weaponTemplates.find(wt => wt.name_zh === item.baseItemName || wt.name_en === item.baseItemName);
        if (template) {
          detailsContent += `<p><em>模板: ${item.baseItemName} (${template.damage} ${template.damageType})</em></p>`;
          if (template.properties && template.properties.length > 0) {
            detailsContent += `<p>&nbsp;&nbsp;<em>基础属性: ${template.properties.join(', ')}</em></p>`;
          }
        }
      }
      if (!detailsContent.includes('基础:') && item.damage && item.damageType) {
         detailsContent += `<p><em>基础: ${item.damage} ${item.damageType}</em></p>`;
      }
      if (item.properties && item.properties.length > 0 && item.type === "武器" && !detailsContent.includes("基础属性")) {
        detailsContent += `<p><em>属性: ${item.properties.join(', ')}</em></p>`;
      }

      if (item.details) {
        detailsContent += `<p><span class="item-description">描述: ${item.details.replace(/\n/g, '<br />&nbsp;&nbsp;')}</span></p>`;
      }

      if (item.type === "武器") {
         const effectiveProps = getEffectiveWeaponProperties(item, weaponTemplates);
         if (effectiveProps.length > 0) {
             detailsContent += `<p><em>有效属性: ${effectiveProps.join(', ')}</em></p>`;
         }
      }

      if (item.magicEffects && item.magicEffects.length > 0) {
        detailsContent += `<p>魔法效果:<ul>`;
        item.magicEffects.forEach((effect: MagicEffect) => {
          detailsContent += `<li class="magic-effect"><em>${effect.notes || effect.type}:</em> `;
          if (typeof effect.value === 'object' && effect.value !== null) {
            detailsContent += Object.entries(effect.value)
                          .map(([key, val]) => `${key}: ${val}`)
                          .join(', ');
          } else {
            detailsContent += `${effect.value}`;
          }
          if (effect.condition) detailsContent += ` <span class="effect-condition">(${effect.condition})</span>`;
          detailsContent += `</li>`;
        });
        detailsContent += `</ul></p>`;
      }
      html += detailsContent || '<p>无更多详情。</p>'; // Fallback if no details generated
      html += `</div></li>`; // Close details-content and li
    });
    html += '</ul>';
    backpackEquippedItemsArea.innerHTML = html;
  } else {
    backpackEquippedItemsArea.innerHTML = '<ul id="backpack-equipped-list"><li class="placeholder">无已装备物品。</li></ul>';
  }
}

export function renderInventoryDisplay(playerState: PlayerState): void {
  if (!backpackInventoryItemsArea) return;
  if (playerState.inventory && playerState.inventory.length > 0) {
    let html = '<ul id="backpack-inventory-list">';
    playerState.inventory.forEach((item: InventoryItem) => {
      const itemId = item.id || `inventory_${Date.now()}_${Math.random().toString(36).substring(2,7)}`;
      const isEquippable = item.type && !['消耗品', '杂物', '材料', '容器'].includes(item.type);

      html += `<li class="inventory-item" data-item-id="${itemId}">`;
      html += `<div>`; // Wrapper for item name and buttons
      html += `<span class="item-name-toggle" data-item-id="${itemId}" role="button" tabindex="0">${item.name} x${item.quantity}</span>`;
      if (isEquippable) {
        html += `<button class="backpack-button equip-button" data-item-id="${itemId}">装备</button>`;
      }
      // Placeholder for "Use" button for consumables - future
      // else if (item.type === '消耗品') {
      //   html += `<button class="backpack-button use-button" data-item-id="${itemId}" disabled>使用</button>`;
      // }
      html += `</div>`;

      // Details content - initially hidden
      html += `<div class="item-details-content" id="details-inventory-${itemId}" style="display: none;">`;
      let detailsContent = '';
      if (item.description) {
        detailsContent += `<p><span class="item-description">描述: ${item.description.replace(/\n/g, '<br />&nbsp;&nbsp;')}</span></p>`;
      }
      if (item.baseItemName) {
        const template = weaponTemplates.find(wt => wt.name_zh === item.baseItemName || wt.name_en === item.baseItemName);
        if (template) {
          detailsContent += `<p><em>模板: ${item.baseItemName} (${template.damage} ${template.damageType})</em></p>`;
          if (template.properties && template.properties.length > 0) {
            detailsContent += `<p>&nbsp;&nbsp;<em>基础属性: ${template.properties.join(', ')}</em></p>`;
          }
        }
      }
      if (item.properties && item.properties.length > 0) {
        detailsContent += `<p><em>属性: ${item.properties.join(', ')}</em></p>`;
      }
      if (item.magicEffects && item.magicEffects.length > 0) {
        detailsContent += `<p>魔法效果:<ul>`;
        item.magicEffects.forEach((effect: MagicEffect) => {
          detailsContent += `<li class="magic-effect"><em>${effect.notes || effect.type}:</em> `;
          if (typeof effect.value === 'object' && effect.value !== null) {
            detailsContent += Object.entries(effect.value).map(([key, val]) => `${key}: ${val}`).join(', ');
          } else {
            detailsContent += `${effect.value}`;
          }
          if (effect.condition) detailsContent += ` <span class="effect-condition">(${effect.condition})</span>`;
          detailsContent += `</li>`;
        });
        detailsContent += `</ul></p>`;
      }
      html += detailsContent || '<p>无更多详情。</p>';
      html += `</div></li>`; // Close details-content and li
    });
    html += '</ul>';
    backpackInventoryItemsArea.innerHTML = html;
  } else {
    backpackInventoryItemsArea.innerHTML = '<ul id="backpack-inventory-list"><li class="placeholder">物品栏为空。</li></ul>';
  }
}


// --- Original updatePlayerStatusDisplay modified ---
export function updatePlayerStatusDisplay(playerState: PlayerState): void {
  if (!playerState) return;
  if (charNameDisplay) charNameDisplay.textContent = `角色名: ${playerState.name || 'N/A'}`;
  if (charRaceClassDisplay)
    charRaceClassDisplay.textContent = `种族/职业: ${playerState.race || 'N/A'} / ${playerState.class || 'N/A'}`;
  if (charLevelDisplay) charLevelDisplay.textContent = `等级: ${playerState.level || 0}`;
  if (healthDisplay)
    healthDisplay.textContent = `生命值: ${playerState.hp?.current || '?'}/${playerState.hp?.max || '?'}`;
  if (acDisplay) acDisplay.textContent = `AC: ${getEffectivePlayerAC(playerState)}`;
  if (timeDisplay) timeDisplay.textContent = `时间: ${playerState.time || 'N/A'}`;
  if (locationDisplay) locationDisplay.textContent = `地点: ${playerState.currentLocation || 'N/A'}`;

  const formatAttr = (attr: Attribute | undefined) =>
    attr ? `${attr.final}(${attr.mod >= 0 ? '+' : ''}${attr.mod})` : 'N/A';

  if (attrStrDisplay) attrStrDisplay.textContent = `力量: ${formatAttr(playerState.attributes?.strength)}`;
  if (attrDexDisplay) attrDexDisplay.textContent = `敏捷: ${formatAttr(playerState.attributes?.dexterity)}`;
  if (attrConDisplay) attrConDisplay.textContent = `体质: ${formatAttr(playerState.attributes?.constitution)}`;
  if (attrIntDisplay) attrIntDisplay.textContent = `智力: ${formatAttr(playerState.attributes?.intelligence)}`;
  if (attrWisDisplay) attrWisDisplay.textContent = `感知: ${formatAttr(playerState.attributes?.wisdom)}`;
  if (attrChaDisplay) attrChaDisplay.textContent = `魅力: ${formatAttr(playerState.attributes?.charisma)}`;

  // Removed currencyGoldDisplay direct update here
  if (expDisplay) expDisplay.textContent = `${playerState.exp || 0}`;
  if (exhaustionDisplay) exhaustionDisplay.textContent = `${playerState.exhaustion || 0}`;

  if (proficienciesDisplay)
    proficienciesDisplay.innerHTML = playerState.proficiencies?.map(p => `<li>${p}</li>`).join('') || '<li>无</li>';

  if (skillsDisplay)
    skillsDisplay.innerHTML =
      playerState.skills
        ?.filter(s => s.proficient)
        .map(s => `<li>${s.name} (${s.finalValue >= 0 ? '+' : ''}${s.finalValue})</li>`)
        .join('') || '<li>无熟练技能</li>';

  if (spellSlotsDisplay) {
    let html = '';
    if (playerState.spellSlots && Object.keys(playerState.spellSlots).length > 0) {
      for (const level in playerState.spellSlots) {
        const slots = playerState.spellSlots[level];
        html += `<div>${level}环: ${slots.current}/${slots.max}</div>`;
      }
    }
    spellSlotsDisplay.innerHTML = html || '无';
  }

  if (equippedSpellsDisplay)
    equippedSpellsDisplay.innerHTML =
      playerState.equippedSpells?.map(s => `<li>${s.name} (${s.level}环)</li>`).join('') || '<li>无</li>';

  // Removed equipmentDisplay and inventoryDisplay direct updates here

  if (activeQuestsDisplay)
    activeQuestsDisplay.innerHTML = playerState.activeQuests?.map(q => `<li>${q}</li>`).join('') || '<li>无</li>';

  // Backpack module rendering is now handled by app.ts when the backpack interface is toggled
}

export function renderNarrative(scene: AdventureSceneJSON): void {
  if (!mainNarrativeArea) return;
  mainNarrativeArea.innerHTML = ''; // Clear previous content

  scene.narrative.forEach((entry: NarrativeEntry) => {
    if (!mainNarrativeArea) return; // Additional check
    const p = document.createElement('p');
    let contentHtml = entry.content.replace(/\n/g, '<br>');

    switch (entry.type) {
      case 'description':
        p.innerHTML = contentHtml;
        break;
      case 'dialogue':
        p.innerHTML = `<strong>${entry.speaker || '某人'}${
          entry.emotion ? ` (${entry.emotion})` : ''
        }:</strong> ${contentHtml}`;
        break;
      case 'systemMessage':
        p.className = 'system-message';
        p.innerHTML = `<em>${contentHtml}</em>`;
        break;
      case 'actionDescription':
        p.innerHTML = `<em>${entry.actor || '某物'} ${contentHtml}</em>`;
        break;
      case 'thought':
        p.className = 'thought-message';
        p.innerHTML = `<i>"${contentHtml}"</i>`;
        break;
      default:
        p.innerHTML = contentHtml;
    }
    mainNarrativeArea.appendChild(p);
  });

  if (scene.combatLog && scene.combatLog.length > 0) {
    const combatLogDiv = document.createElement('div');
    combatLogDiv.className = 'combat-log';
    combatLogDiv.innerHTML =
      '<h4>战斗记录:</h4>' + scene.combatLog.map(log => `<p>${log.replace(/\n/g, '<br>')}</p>`).join('');
    mainNarrativeArea.appendChild(combatLogDiv);
  }

  if (scene.enemies && scene.enemies.length > 0) {
    const enemiesDiv = document.createElement('div');
    enemiesDiv.className = 'enemies-display';
    let enemiesHtml = '<h4>当前人物:</h4><ul>';
    scene.enemies.forEach((enemy: EnemyStateJSON) => {
      enemiesHtml += `<li><strong>${enemy.name} (ID: ${enemy.id})</strong> - HP: ${enemy.hp.current}/${enemy.hp.max}, AC: ${enemy.ac}`;
      if (enemy.intent) enemiesHtml += `, 意图: ${enemy.intent}`;
      if (enemy.statusEffects && enemy.statusEffects.length > 0)
        enemiesHtml += `, 状态: ${enemy.statusEffects.join(', ')}`;
      enemiesHtml += `</li>`;
    });
    enemiesHtml += '</ul>';
    enemiesDiv.innerHTML = enemiesHtml;
    mainNarrativeArea.appendChild(enemiesDiv);
  }
}

export function renderActionChoices(
  choices: PlayerChoiceJSON[] | undefined,
  handleChoiceCallback: (choice: PlayerChoiceJSON) => Promise<void>,
): void {
  if (!actionChoicesArea) return;
  actionChoicesArea.innerHTML = '';
  if (choices && choices.length > 0) {
    choices.forEach(choice => {
      if (!actionChoicesArea) return; // Additional check
      const button = document.createElement('button');
      button.id = `choice-${choice.id}`;
      button.textContent = choice.text;
      button.dataset.action = choice.actionCommand;
      button.addEventListener('click', () => handleChoiceCallback(choice));
      actionChoicesArea.appendChild(button);
    });
  } else {
    actionChoicesArea.innerHTML = '<p>暂无行动选项。</p>';
  }
}

// --- Spellbook Rendering Functions ---

/**
 * 更新法术目标选择选项
 */
export function updateSpellTargetOptions(): void {
  if (!spellTargetTypeSelect) return;

  // safeToastr('info', '更新法术目标选择选项', 'Debug - 目标选择');

  // 清除现有选项（除了自己和自定义目标）
  const existingOptions = Array.from(spellTargetTypeSelect.options);
  existingOptions.forEach(option => {
    if (option.value !== 'self' && option.value !== 'custom') {
      spellTargetTypeSelect?.removeChild(option);
    }
  });

  // 获取场景中的NPC/敌人
  const npcsInScene = currentSceneData?.enemies || [];
  // 只在有NPC时显示调试信息
  if (npcsInScene.length > 0) {
    safeToastr('info', `场景中有 ${npcsInScene.length} 个可选目标`, 'Debug - 目标选择');
  }

  // 在"自定义目标"选项之前插入NPC选项
  const customOption = spellTargetTypeSelect.querySelector('option[value="custom"]');

  npcsInScene.forEach(npc => {
    const option = document.createElement('option');
    option.value = `npc:${npc.id}`;
    option.textContent = `${npc.name} (${npc.hp.current}/${npc.hp.max} HP)`;
    if (customOption) {
      spellTargetTypeSelect?.insertBefore(option, customOption);
    } else {
      spellTargetTypeSelect?.appendChild(option);
    }
  });

  // safeToastr('success', `已添加 ${npcsInScene.length} 个NPC目标选项`, 'Debug - 目标选择');
}

/**
 * 处理目标类型选择变化
 */
export function handleTargetTypeChange(): void {
  if (!spellTargetTypeSelect || !customTargetInputDiv) return;

  const selectedValue = spellTargetTypeSelect.value;
  // safeToastr('info', `目标类型选择变化: ${selectedValue}`, 'Debug - 目标选择');

  if (selectedValue === 'custom') {
    customTargetInputDiv.style.display = 'block';
    // safeToastr('info', '显示自定义目标输入框', 'Debug - 目标选择');
  } else {
    customTargetInputDiv.style.display = 'none';
    // safeToastr('info', '隐藏自定义目标输入框', 'Debug - 目标选择');
  }
}

/**
 * 获取当前选择的目标名称
 */
export function getSelectedTargetName(): string {
  if (!spellTargetTypeSelect) return '';

  const selectedValue = spellTargetTypeSelect.value;
  // safeToastr('info', `获取选择的目标: ${selectedValue}`, 'Debug - 目标选择');

  if (selectedValue === 'self') {
    return '自己';
  } else if (selectedValue === 'custom') {
    const customName = spellTargetCustomInput?.value.trim() || '';
    // 只在有自定义目标时显示调试信息
    if (customName) {
      safeToastr('info', `自定义目标: ${customName}`, 'Debug - 目标选择');
    }
    return customName;
  } else if (selectedValue.startsWith('npc:')) {
    const npcId = selectedValue.substring(4);
    const npc = currentSceneData?.enemies?.find(e => e.id === npcId);
    const npcName = npc?.name || '';
    // safeToastr('info', `NPC目标名称: ${npcName}`, 'Debug - 目标选择');
    return npcName;
  }

  return '';
}

export function renderSpellbookInterface(playerState: PlayerState): void {
  // safeToastr('info', '开始渲染法术书界面', 'Debug - 法术书界面');
  renderSpellSlotsDisplay(playerState);
  renderPreparedSpellsList(playerState);
  renderAvailableSpellsList(playerState);
  // safeToastr('success', '法术书界面渲染完成', 'Debug - 法术书界面');
}

export function renderSpellSlotsDisplay(playerState: PlayerState): void {
  if (!spellbookSpellSlotsDisplay) return;

  let html = '';
  if (playerState.spellSlots && Object.keys(playerState.spellSlots).length > 0) {
    for (const level in playerState.spellSlots) {
      const slots = playerState.spellSlots[level];
      const levelName = level === '0' ? '戏法' : `${level}环`;
      html += `<div class="spell-slot-level">
        <span class="spell-slot-label">${levelName}:</span>
        <span class="spell-slot-count">${slots.current}/${slots.max}</span>
      </div>`;
    }
  } else {
    html = '<div class="no-spell-slots">无法术槽</div>';
  }

  spellbookSpellSlotsDisplay.innerHTML = html;
}

export function renderPreparedSpellsList(playerState: PlayerState): void {
  if (!spellbookPreparedSpellsList) return;

  if (!playerState.equippedSpells) {
    spellbookPreparedSpellsList.innerHTML = '<li class="placeholder">无已准备法术。</li>';
    safeToastr('warning', '玩家没有equippedSpells数组', 'Debug - 已准备法术');
    return;
  }

  // safeToastr('info', `玩家有 ${playerState.equippedSpells.length} 个已准备法术`, 'Debug - 已准备法术');

  if (playerState.equippedSpells.length > 0) {
    let html = '';
    playerState.equippedSpells.forEach((spell) => {
      const canCast = canCastSpell(spell.name, playerState);
      const spellClass = canCast ? 'prepared-spell' : 'prepared-spell disabled';
      // safeToastr('info', `已准备法术: ${spell.name} (${spell.level}环) - ${canCast ? '可施放' : '无法施放'}`, 'Debug - 已准备法术');
      html += `<li class="${spellClass}" data-spell-name="${spell.name}">
        <span class="spell-name">${spell.name}</span>
        <span class="spell-level">${spell.level === 0 ? '戏法' : `${spell.level}环`}</span>
        ${canCast ? '<button class="cast-prepared-spell-button">施放</button>' : '<span class="no-slots">无法术槽</span>'}
      </li>`;
    });
    spellbookPreparedSpellsList.innerHTML = html;
  } else {
    spellbookPreparedSpellsList.innerHTML = '<li class="placeholder">无已准备法术。</li>';
    // safeToastr('info', '已准备法术列表为空', 'Debug - 已准备法术');
  }
}

export function renderAvailableSpellsList(playerState: PlayerState): void {
  if (!spellbookAvailableSpellsList) return;

  // 检查角色是否有已装备的法术
  if (!playerState.equippedSpells || playerState.equippedSpells.length === 0) {
    spellbookAvailableSpellsList.innerHTML = '<li class="placeholder">角色没有已学会的法术。</li>';
    return;
  }

  const spellTemplates = getSpellTemplates();
  if (!spellTemplates || spellTemplates.length === 0) {
    spellbookAvailableSpellsList.innerHTML = '<li class="placeholder">法术模板未加载。</li>';
    safeToastr('warning', '法术模板未加载或为空', 'Debug - 法术列表');
    return;
  }

  // 只获取角色实际拥有的法术的模板信息
  let playerSpells = playerState.equippedSpells.map(equippedSpell => {
    const template = spellTemplates.find(t =>
      t.name_zh === equippedSpell.name || t.name_en === equippedSpell.name
    );
    return template ? { ...template, equippedLevel: equippedSpell.level } : null;
  }).filter(spell => spell !== null);

  // Apply level filter
  const levelFilter = document.getElementById('spell-level-filter') as HTMLSelectElement;
  if (levelFilter && levelFilter.value !== 'all') {
    const targetLevel = parseInt(levelFilter.value);
    playerSpells = playerSpells.filter(spell => spell!.level === targetLevel);
  }

  // Apply school filter
  const schoolFilter = document.getElementById('spell-school-filter') as HTMLSelectElement;
  if (schoolFilter && schoolFilter.value !== 'all') {
    playerSpells = playerSpells.filter(spell => spell!.school === schoolFilter.value);
  }

  if (playerSpells.length > 0) {
    let html = '';
    playerSpells.forEach(spell => {
      if (!spell) return;

      const canCast = canCastSpell(spell.name_zh, playerState);
      const spellClass = canCast ? 'available-spell' : 'available-spell disabled';
      html += `<li class="${spellClass}" data-spell-name="${spell.name_zh}">
        <div class="spell-header">
          <span class="spell-name">${spell.name_zh}</span>
          <span class="spell-level">${spell.level === 0 ? '戏法' : `${spell.level}环`}</span>
          <span class="spell-school">${spell.school}</span>
        </div>
        <div class="spell-summary">${spell.description_short}</div>
        <div class="spell-actions">
          <button class="view-spell-button">查看详情</button>
          ${canCast ? '<button class="quick-cast-spell-button">快速施放</button>' : '<span class="no-slots">无法术槽</span>'}
        </div>
      </li>`;
    });
    spellbookAvailableSpellsList.innerHTML = html;
  } else {
    spellbookAvailableSpellsList.innerHTML = '<li class="placeholder">无符合条件的法术。</li>';
  }
}

export function showSpellDetailModal(spellName: string): void {
  const spellTemplates = getSpellTemplates();
  if (!spellTemplates || spellTemplates.length === 0) {
    safeToastr('error', '法术模板未加载', 'Debug - 法术详情');
    return;
  }

  const spell = spellTemplates.find(s => s.name_zh === spellName || s.name_en === spellName);
  if (!spell) {
    safeToastr('error', `找不到法术: ${spellName}`, 'Debug - 法术详情');
    return;
  }

  // safeToastr('info', `显示法术详情: ${spellName}`, 'Debug - 法术详情');

  if (spellDetailName) spellDetailName.textContent = spell.name_zh;
  if (spellDetailLevel) spellDetailLevel.textContent = spell.level === 0 ? '戏法' : `${spell.level}环`;
  if (spellDetailSchool) spellDetailSchool.textContent = spell.school;
  if (spellDetailCastingTime) spellDetailCastingTime.textContent = spell.casting_time;
  if (spellDetailRange) spellDetailRange.textContent = spell.range;
  if (spellDetailComponents) spellDetailComponents.textContent = spell.components.join(', ');
  if (spellDetailDuration) spellDetailDuration.textContent = spell.duration;
  if (spellDetailDescription) spellDetailDescription.textContent = spell.description_long;

  // Populate spell slot selection
  if (spellSlotLevelSelect) {
    spellSlotLevelSelect.innerHTML = '';
    if (spell.level === 0) {
      spellSlotLevelSelect.innerHTML = '<option value="0">戏法 (无需法术槽)</option>';
    } else {
      for (let level = spell.level; level <= 9; level++) {
        spellSlotLevelSelect.innerHTML += `<option value="${level}">${level}环法术槽</option>`;
      }
    }
  }

  // Update target selection options
  updateSpellTargetOptions();

  // Show the modal
  const modal = document.getElementById('spell-detail-modal');
  if (modal) {
    modal.style.display = 'flex';
    // safeToastr('success', '法术详情模态框已显示', 'Debug - 法术详情');
  }
}
