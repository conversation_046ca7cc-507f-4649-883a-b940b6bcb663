export let startScreenContainer: HTMLElement | null = null;
export let startNewGameButton: HTMLButtonElement | null = null;
export let adventureLogContainer: HTMLElement | null = null;
export let playerStatusArea: HTMLElement | null = null;
export let mainNarrativeArea: HTMLElement | null = null;
export let actionChoicesArea: HTMLElement | null = null;
export let healthDisplay: HTMLElement | null = null;
export let locationDisplay: HTMLElement | null = null;
export let timeDisplay: HTMLElement | null = null;
export let charNameDisplay: HTMLElement | null = null;
export let charRaceClassDisplay: HTMLElement | null = null;
export let charLevelDisplay: HTMLElement | null = null;
export let acDisplay: HTMLElement | null = null;
// Old backpack module elements (embedded in char sheet) are removed.
// New independent backpack interface elements
export let toggleBackpackButton: HTMLButtonElement | null = null;
export let backpackInterface: HTMLElement | null = null;
export let closeBackpackButton: HTMLButtonElement | null = null;
export let backpackCurrencyGold: HTMLElement | null = null;
export let backpackCurrencySilver: HTMLElement | null = null;
export let backpackCurrencyCopper: HTMLElement | null = null;
export let backpackEquippedList: HTMLElement | null = null; // The <ul> element for equipped items
export let backpackEquippedItemsArea: HTMLElement | null = null; // The <div> container for equipped items section
export let backpackInventoryList: HTMLElement | null = null; // The <ul> element for inventory items
export let backpackInventoryItemsArea: HTMLElement | null = null; // The <div> container for inventory items section
// End new independent backpack interface elements

// Spellbook interface elements
export let toggleSpellbookButton: HTMLButtonElement | null = null;
export let spellbookInterface: HTMLElement | null = null;
export let closeSpellbookButton: HTMLButtonElement | null = null;
export let spellbookSpellSlotsDisplay: HTMLElement | null = null;
export let spellbookPreparedSpellsList: HTMLElement | null = null;
export let spellLevelFilter: HTMLSelectElement | null = null;
export let spellSchoolFilter: HTMLSelectElement | null = null;
export let spellbookAvailableSpellsList: HTMLElement | null = null;
// Spell detail modal elements
export let spellDetailModal: HTMLElement | null = null;
export let closeSpellDetailButton: HTMLButtonElement | null = null;
export let spellDetailName: HTMLElement | null = null;
export let spellDetailLevel: HTMLElement | null = null;
export let spellDetailSchool: HTMLElement | null = null;
export let spellDetailCastingTime: HTMLElement | null = null;
export let spellDetailRange: HTMLElement | null = null;
export let spellDetailComponents: HTMLElement | null = null;
export let spellDetailDuration: HTMLElement | null = null;
export let spellDetailDescription: HTMLElement | null = null;
export let spellSlotLevelSelect: HTMLSelectElement | null = null;
export let spellTargetTypeSelect: HTMLSelectElement | null = null;
export let spellTargetCustomInput: HTMLInputElement | null = null;
export let customTargetInputDiv: HTMLElement | null = null;
export let castSpellButton: HTMLButtonElement | null = null;
export let cancelSpellButton: HTMLButtonElement | null = null;
export let attrStrDisplay: HTMLElement | null = null;
export let attrDexDisplay: HTMLElement | null = null;
export let attrConDisplay: HTMLElement | null = null;
export let attrIntDisplay: HTMLElement | null = null;
export let attrWisDisplay: HTMLElement | null = null;
export let attrChaDisplay: HTMLElement | null = null;
// currencyGoldDisplay is removed as it's replaced by backpack specific ones
export let expDisplay: HTMLElement | null = null;
export let exhaustionDisplay: HTMLElement | null = null;
export let toggleCharSheetButton: HTMLButtonElement | null = null;
export let detailedCharacterSheet: HTMLElement | null = null;
export let proficienciesDisplay: HTMLElement | null = null;
export let skillsDisplay: HTMLElement | null = null;
export let spellSlotsDisplay: HTMLElement | null = null;
export let equippedSpellsDisplay: HTMLElement | null = null;
// equipmentDisplay and inventoryDisplay are removed as they are replaced by new areas
export let activeQuestsDisplay: HTMLElement | null = null;

export function initializeDomElements(): void {
  startScreenContainer = document.getElementById('start-screen-container');
  startNewGameButton = document.getElementById('start-new-game-button') as HTMLButtonElement;
  adventureLogContainer = document.getElementById('adventure-log-container');
  playerStatusArea = document.getElementById('player-status-area');
  mainNarrativeArea = document.getElementById('main-narrative-area');
  actionChoicesArea = document.getElementById('action-choices-area');
  healthDisplay = document.getElementById('health');
  locationDisplay = document.getElementById('location');
  timeDisplay = document.getElementById('time');
  charNameDisplay = document.getElementById('char-name-display');
  charRaceClassDisplay = document.getElementById('char-race-class-display');
  charLevelDisplay = document.getElementById('char-level-display');
  acDisplay = document.getElementById('ac-display');
  attrStrDisplay = document.getElementById('attr-str-display');
  attrDexDisplay = document.getElementById('attr-dex-display');
  attrConDisplay = document.getElementById('attr-con-display');
  attrIntDisplay = document.getElementById('attr-int-display');
  attrWisDisplay = document.getElementById('attr-wis-display');
  attrChaDisplay = document.getElementById('attr-cha-display');
  // currencyGoldDisplay is removed
  expDisplay = document.getElementById('exp-display');
  exhaustionDisplay = document.getElementById('exhaustion-display');
  toggleCharSheetButton = document.getElementById('toggle-char-sheet-button') as HTMLButtonElement;
  detailedCharacterSheet = document.getElementById('detailed-character-sheet');
  
  // Initialize new independent backpack interface elements
  toggleBackpackButton = document.getElementById('toggle-backpack-button') as HTMLButtonElement;
  backpackInterface = document.getElementById('backpack-interface');
  closeBackpackButton = document.getElementById('close-backpack-button') as HTMLButtonElement;
  backpackCurrencyGold = document.getElementById('backpack-currency-gold');
  backpackCurrencySilver = document.getElementById('backpack-currency-silver');
  backpackCurrencyCopper = document.getElementById('backpack-currency-copper');
  backpackEquippedItemsArea = document.getElementById('backpack-equipped-items-area');
  backpackEquippedList = document.getElementById('backpack-equipped-list');
  backpackInventoryItemsArea = document.getElementById('backpack-inventory-items-area');
  backpackInventoryList = document.getElementById('backpack-inventory-list');

  // Initialize spellbook interface elements
  toggleSpellbookButton = document.getElementById('toggle-spellbook-button') as HTMLButtonElement;
  spellbookInterface = document.getElementById('spellbook-interface');
  closeSpellbookButton = document.getElementById('close-spellbook-button') as HTMLButtonElement;
  spellbookSpellSlotsDisplay = document.getElementById('spellbook-spell-slots-display');
  spellbookPreparedSpellsList = document.getElementById('spellbook-prepared-spells-list');
  spellLevelFilter = document.getElementById('spell-level-filter') as HTMLSelectElement;
  spellSchoolFilter = document.getElementById('spell-school-filter') as HTMLSelectElement;
  spellbookAvailableSpellsList = document.getElementById('spellbook-available-spells-list');

  // Initialize spell detail modal elements
  spellDetailModal = document.getElementById('spell-detail-modal');
  closeSpellDetailButton = document.getElementById('close-spell-detail-button') as HTMLButtonElement;
  spellDetailName = document.getElementById('spell-detail-name');
  spellDetailLevel = document.getElementById('spell-detail-level');
  spellDetailSchool = document.getElementById('spell-detail-school');
  spellDetailCastingTime = document.getElementById('spell-detail-casting-time');
  spellDetailRange = document.getElementById('spell-detail-range');
  spellDetailComponents = document.getElementById('spell-detail-components');
  spellDetailDuration = document.getElementById('spell-detail-duration');
  spellDetailDescription = document.getElementById('spell-detail-description');
  spellSlotLevelSelect = document.getElementById('spell-slot-level') as HTMLSelectElement;
  spellTargetTypeSelect = document.getElementById('spell-target-type') as HTMLSelectElement;
  spellTargetCustomInput = document.getElementById('spell-target-custom') as HTMLInputElement;
  customTargetInputDiv = document.getElementById('custom-target-input');
  castSpellButton = document.getElementById('cast-spell-button') as HTMLButtonElement;
  cancelSpellButton = document.getElementById('cancel-spell-button') as HTMLButtonElement;

  proficienciesDisplay = document.getElementById('proficiencies-display');
  skillsDisplay = document.getElementById('skills-display');
  spellSlotsDisplay = document.getElementById('spell-slots-display');
  equippedSpellsDisplay = document.getElementById('equipped-spells-display');
  // equipmentDisplay and inventoryDisplay are removed
  activeQuestsDisplay = document.getElementById('active-quests-display');
}
