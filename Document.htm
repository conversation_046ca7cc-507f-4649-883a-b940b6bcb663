<!DOCTYPE html>
<html lang="en"><head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8"><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1"><title>Document</title><style type="text/css">@import url(https://static.zeoseven.com/zsft/59/main/result.css);
.QQ_chat_page {
  padding-top: 10px;
  background-color: rgb(244, 245, 246);
  width: 100%;
  height: 100%;
  /* 填满视口高度 */
  /* background-image: url("http://sharkpan.xyz/f/eWhZ/00017-2763077315.png"); */
  background-size: cover;
  background-repeat: no-repeat;
  background-position: top center;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  /* 允许内容区域垂直滚动 */
  box-sizing: border-box;
  /* 防止内边距溢出 */
  border-radius: var(--yj);
  /* padding-bottom: 60px; */

  position: relative;
  overflow-x: hidden;

  /* z-index: 9999; */
  /* font-family: "字魂萌宠天地体"; */
}

.input-container {
  max-width: 100%;
  position: absolute;
  /* bottom: 20px; */
  bottom: 0px;
  left: 0;
  width: 100%;
  background-color: rgb(255, 255, 255, 0.2);
  border-bottom-right-radius: 10px;
  border-bottom-left-radius: 10px;
  box-sizing: border-box;
  padding: 10px 0;
  border-top: 1px solid black;
}

.msgcontent {
  flex: 1;
  overflow-y: auto;
  box-sizing: border-box;
  /* margin-top: 20px; */
  margin-bottom: 50px;
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}

.userInput {
  font: inherit;
  height: 30px;
  border-radius: 30px;
  letter-spacing: 0.7px;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;
  border: 1px solid rgb(0, 0, 0, 0.5);
}

/* 聊天设置弹窗样式 */
.chat-setting-popup {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 350px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  display: none;
}

.chat-setting-header {
  padding: 15px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-setting-header span {
  font-weight: bold;
  font-size: 16px;
}

.close-setting-btn {
  cursor: pointer;
}

.chat-setting-content {
  padding: 15px;
}

.setting-item {
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap:10px;
}

.setting-item span {
  font-size: 14px;
}

.setting-item input[type='text'] {
  flex:1;
  padding: 5px;
  border: 1px solid #ccc;
  border-radius: 5px;
}

.color-picker {
  width: 30px;
  height: 30px;
  padding: 0;
  border: 1px solid #ccc;
  border-radius: 3px;
  cursor: pointer;
}

.chat-setting-footer {
  padding: 10px 15px;
  text-align: right;
  border-top: 1px solid #eee;
}

.chat-setting-footer button {
  padding: 6px 15px;
  margin-left: 10px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

#save-setting-btn {
  background-color: #019aff;
  color: white;
}

#cancel-setting-btn {
  background-color: #f5f5f5;
  color: #333;
}

/* 弹窗遮罩层 */
.popup-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 35px;
  z-index: 999;
  display: none;
}

.QQ_chat_fakeimg{
  width: 100%; 
  background-color: white;
  aspect-ratio: 1 / 1;
  padding: 5px 18px 0 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 1.3em;
  /* overflow-y: auto; */
  -ms-overflow-style: none !important;
  scrollbar-width: none !important;
  text-align:center;
}

.QQ_chat_fakeimg::-webkit-scrollbar {
  display: none !important;
}

.QQ_chat_fakeimg div{
  max-height: 100%;
  display: flex;
  align-items: flex-start;
  overflow-y: auto;
  -ms-overflow-style: none !important;
  scrollbar-width: none !important;
}

.QQ_chat_fakeimg div::-webkit-scrollbar {
  display: none !important;
}


        .discord {
            background-color: #f2f3f5;
            width: 100%;
            height: 100%;
            line-height: 1.4;
            letter-spacing: 1px;
        }

        .discord-top {
            background-color: white;
            width: 100%;
            height: 80px;
            padding-top: 30px;
            padding-left: 10px;
            display: flex;
            align-items: center;
        }

        .discord-top-title {
            font-size: 20px;
            text-align: center;
            margin-top: -3px;
        }

        .discord-top-button {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .sortbutton {
            display: flex;
            align-items: center;
            gap: 4px;
            margin-bottom: -1px;
            height: 32px;
            background-color: #e8e9eb;
            padding: 8px 8px 8px 12px;
            width: fit-content;
            border-radius: 24px;
            margin-top: 10px;
            margin-left: 10px;
        }

        .tagbutton {
            display: flex;
            align-items: center;
            margin-left: auto;
            margin-right: 10px;
            background-color: #e8e9eb;
            padding: 8px 8px 8px 12px;
            width: fit-content;
            height: 32px;
            border-radius: 24px;
            margin-top: 10px;
        }

        .discord-cardlist {
            display: flex;
            flex-direction: column;
            width: 100%;
            height: 100%;
            overflow-y: auto;
            padding: 0 10px 150px 10px;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .discord-cardlist::-webkit-scrollbar {
            display: none !important;
        }

        .discord-card {
            width: 100%;
            height: auto;
            background-color: white;
            border-radius: 15px;
            margin-top: 15px;
            padding-left: 13px;
            padding-right: 13px;
            padding-bottom: 10px;
            border: 1px solid #dfdfe1;
        }

        .discord-card:active {
            background-color: rgb(244, 243, 243);
        }

        .discord-card-tags{
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            padding-top:10px;
        }

        .discord-card-tag {
            background-color: #eeeff1;
            color: #4f5055;
            width: fit-content;
            padding: 5px 15px;
            border-radius: 24px;
            margin-top: 5px;
            margin-left: -1px;
            font-size: 15px;
            text-align: center;
            font-weight: bold;
        }

        .discord-card-author {
            color: #9d8ef3;
            margin-top: 8px;
            font-size: 18px;
            display: inline-block;
            font-weight: bold;
        }

        .discord-card-title {
            font-weight: bold;
            font-size: 19px;
            margin-top: 5px;

            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;

            /* 文本溢出时显示省略号 */
            overflow: hidden;
            text-overflow: ellipsis;

            /* 确保文本换行 */
            white-space: normal;
            word-break: break-word;
            /* 避免长单词溢出 */
        }

        .discord-card-content {
            margin-top: 2px;
            color: #313133;
            font-size: 16px;

            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;

            /* 文本溢出时显示省略号 */
            overflow: hidden;
            text-overflow: ellipsis;

            /* 确保文本换行 */
            white-space: normal;
            word-break: break-word;
            /* 避免长单词溢出 */
        }

        .discord-card-interact {
            display: flex;
            align-items: center;
            margin-top: 15px;
        }

        .discord-card-interact-icon {
            margin-left: auto;
            color: #333237;
            background-color: #ececec;
            border-radius: 10px;
            font-size: 16px;
            padding: 2px 8px 2px 6px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .discord-card-fakeimg {
            text-align: center;
            margin-top: 9px;
            font-size: 1.2rem;
            background-color: rgb(239 237 238);
            padding: 30px 10px;
            width: 100%;
            border: 1px solid #dfdfe1;
            border-radius: 5px;
            text-align: center;
            text-wrap: balance;
        }

        .discord-thread {
            width: 100%;
            height: 100%;
            background-color: white;
            padding-top: 30px;
            position: relative;
            /* overflow-x: hidden; */
        }

        .discord-thread-top {
            display: grid;
            grid-template-columns: 32px 43px 1fr;
            align-items: center;
            width: 100%;
            height: 50px;
            padding: 0 10px;
        }

        .discord-thread-title {
            font-weight: bold;
            font-size: 20px;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 100%;
            white-space: nowrap;
        }

        .discord-thread-content {
            overflow-y: auto;
            padding: 0 10px;
            width: 100%;
            height: auto;
        }

        .discord-thread-content-title {
            font-weight: bold;
            font-size: 24px;
            margin-top: 10px;
            margin-bottom: 3px;
        }

        .discord-thread-content-tags {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            gap: 5px;

            .discord-card-tag {
                margin-top: 5px;
            }
        }

        .discord-thread-content-original {
            display: grid;
            grid-template-columns: 55px 1fr;
            align-items: start;
            margin-top: 20px;
            margin-bottom: 10px;
        }

        .discord-thread-content-original-author-head {
            width: 45px;
            height: 45px;
            background-size: cover;
            border-radius: 50%;
        }

        .discord-thread-content-original-author-name {
            color: #9d8ef3;
            font-size: 18px;
            display: inline-block;
            font-weight: bold;
        }

        .discord-thread-content-original-content {
            white-space: pre-wrap;
            margin-top:3px;
        }

        .discord-thread-content-likes {
            width: fit-content;
            padding: 5px 8px 5px 8px;
            text-align: center;
            background-color: #f2f3f5;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 5px;
            color: #525358;
            font-size: 16px;
            height: 34px;
        }

        .discord-thread-content-reaction {
            background-color: #eeeeef;
            padding: 5px;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            display: flex;
            margin-left: 6px;
            height: 34px;
        }

        .discord-thread-comment-list {
            padding: 0 10px;
            width: 100%;
        }

        .discord-thread-comment {
            display: grid;
            grid-template-columns: 50px 1fr;
            margin-top: 20px;
        }

        .discord-thread-comment-head {
            width: 40px;
            height: 40px;
            background-size: cover;
            border-radius: 50%;
            /* margin-top: 5px; */
        }

        .discord-name-creator {
            color: #9d8ef3;
            font-size: 16px;
            display: inline-block;
            font-weight: bold;
            position: relative;
        }

        .discord-thread-comment-name {
            font-size: 16px;
            display: inline-block;
            font-weight: bold;
            position: relative;
        }

        .discord-thread-comment-content{
            margin-top:3px;
        }

        .discord-name-creator::after {
            position: absolute;
            top: 0%;
            left: 100%;
            margin-left:3px;
            height: 20px;
            width: 20px;
            display: inline-block;
            content: "";
            background-size: contain;
            background-image:url('data:image/webp;base64,UklGRkoCAABXRUJQVlA4WAoAAAAQAAAAHQAAHwAAQUxQSIEAAAABcFpt27K8L91pkm0KTxwGIMEO7pYcGolE9BUs0VxHkEqy3/+fl0qKiAkAAEAABEIESdtoYr1SrlBkurFzB8MSPlSiHPsT7Px17MvmHmUXRQmnJRgKU/pBjOknQ3rvo8jHsKRTNYixzZIyAZCOMCRsVkZ7oXk2fRJgrCyWpGOU+CkAVlA4IKIBAABwCQCdASoeACAAPmEoj0WkIqGb/VQAQAYEswBhsa8KA3gFgAdKgvwVsdYAxwUyDx3fSu/J9AD9dBN4HzXUGnIffZmZ+Xi6UOW2PLIrM//asS4J9gAA+nzKAwpZUP5tD9q71GP2da5pzrXFQhw7pRv+CMeoEN+kh+aUE4VmdB7sPfZs8IsDkQx5uoj5AjWh089yG//EwIyr/se6feMyVX1jaa8+MnQrGmsfLaVAoTq9RrJ37OhatyX20m/e/5Ph5E/2P/FpTDWXvb7Ta8uM487hoffTtj8mtyn8Fc4n6KteA//0CAaACYby7EAYe8857OPwrIv+/3fyVUAr4ga3+Uc/qEdrqwZ67A7Y0t4qBlDcm8Yk4xtY7wVHEKsF/rNmiezTq+r0Ysk1j3xNln94b5jV3f2/9VUZXqbv1fNiMWWdlm8cV5lf86F2H81wUKVQuoPeG7hAZhvU3NJ6DDmTY3fijNc29rpXd5tHHovD4H816Ukg7x4+L7ulAwyBuE8T3FhuuXF690Bq9sa/4l27s0Q8DeM8DKDkFM9s0CKf8J5QMYAA');
            /* background-image: url('https://cdn.discordapp.com/role-icons/1336817752844796016/da610f5548f174d9e04d49b1b28c3af1.webp?size=32&amp;quality=lossless'); */
        }

        .discord-scroll-container {
            width: 100%;
            height: 100%;
            overflow-y: auto;
            padding-bottom: 130px;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .discord-scroll-container::-webkit-scrollbar {
            display: none !important;
        }

        .discord-thread-input {
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 50px;
            border-top: 2px solid #e4e4e4;
            background-color: white;
            display: flex;
            align-items: center;
            padding: 0 5px;
            gap: 5px;
        }

        .discord-thread-input-svgbackground {
            width: 35px;
            min-width: 35px;
            height: 35px;
            border-radius: 50%;
            background-color: #ececec;
            display: flex;
            align-items: center;
            justify-content: center;

            svg {
                width: 22px;
                height: 22px;
            }
        }

        .discord-thread-input-userinput {
            height: 30px;
            flex: 1;
            border-radius: 24px;
            border: 0px;
            background-color: #ececec;
            padding: 0 5px 0 10px;
            box-sizing: border-box;
            display: block;
            min-width: 50px;
            outline: none;
        }

        .discord-thread-input-userinput:focus {
            outline: none;
        }

        .discord-thread-input-userinput::placeholder {
            display: inline-block;
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            direction: ltr;
        }

        .discord-cardget{
            background-color: #e8e9eb;
            padding: 10px 20px;
            border-radius: 100px;
            margin: auto;
            font-size: 1.5rem;
        }
body{--yj: 22px;overflow:auto !important;-ms-overflow-style:none !important;scrollbar-width:none !important;letter-spacing:.7px;font-family:"JiangChengYuanTi";font-weight:normal}.head{width:40px;height:40px;border-radius:50%;min-width:40px}.user_avatar{width:40px;height:40px;border-radius:50%;background-size:cover}body::-webkit-scrollbar{display:none !important}.card{max-width:min(350px,95%);width:min(350px,95%);position:relative;height:auto;aspect-ratio:1/2;background:#1b1717;border-radius:35px;border:2px solid #810000;padding:7px;box-shadow:2px 3px 3px rgba(0, 0, 0, 0.25);margin-left:5px;box-sizing:border-box}.card-int{background-size:200% 200%;background-position:0% 0%;height:100%;border-radius:25px;transition:all .6s ease-out;overflow:hidden;box-sizing:border-box;position:relative}.card:hover .card-int{background-position:100% 100%}#App_Page{background-size:200% 200%;background-position:0% 0%;height:100%;border-radius:25px;transition:all .6s ease-out;overflow:hidden;box-sizing:border-box;position:relative}.top{position:absolute;top:0px;right:50%;-webkit-transform:translate(50%, 0%);transform:translate(50%, 0%);width:35%;height:22px;background-color:#1b1717;border-bottom-left-radius:13px;border-bottom-right-radius:13px;z-index:9999999}.speaker{position:absolute;top:2px;right:50%;-webkit-transform:translate(50%, 0%);transform:translate(50%, 0%);width:40%;height:2px;border-radius:2px;background-color:rgb(20, 20, 20)}.camera{position:absolute;top:6px;right:84%;-webkit-transform:translate(50%, 0%);transform:translate(50%, 0%);width:6px;height:6px;border-radius:50%;background-color:rgba(255, 255, 255, 0.048)}.int{position:absolute;width:3px;height:3px;border-radius:50%;top:50%;right:50%;-webkit-transform:translate(50%, -50%);transform:translate(50%, -50%);background-color:rgba(0, 0, 255, 0.212)}.btn1,.btn2,.btn3,.btn4{position:absolute;width:2px}.btn1,.btn2,.btn3{height:45px;top:30%;right:-4px;background-color:#ce1212}.btn2,.btn3{-webkit-transform:scale(-1);transform:scale(-1);left:-4px}.btn2,.btn3{-webkit-transform:scale(-1);transform:scale(-1);height:30px}.btn2{top:26%}.btn3{top:36%}.hidden{display:block;opacity:0;transition:all .3s ease-in}.card:hover .hidden{opacity:1}.card:hover .hello{-webkit-transform:translateY(-20px);transform:translateY(-20px)}.btn{position:absolute;width:2px}.backdiv{background-color:white;height:100%;width:100%;position:relative;background-size:cover;background-position:top center;background-repeat:no-repeat;background-image:url("http://sharkpan.xyz/f/6osR/00005-660646865.png");box-sizing:border-box}.dibu{position:absolute;bottom:40px;left:3%;right:3%;background-color:black;height:auto;display:-webkit-flex;display:flex;-webkit-align-items:center;align-items:center;-webkit-justify-content:space-around;justify-content:space-around;padding:0 5px 0 5px;border-radius:30px;--a: 10px;background-color:rgba(255, 255, 255, 0.2);backdrop-filter:blur(var(--a));-webkit-backdrop-filter:blur(var(--a))}.dibu svg{-webkit-transform:scale(0.85);transform:scale(0.85);-webkit-transform-origin:center;transform-origin:center}.dinbu{position:absolute;top:10px;left:7%;right:2%;color:#626367;display:-webkit-flex;display:flex;-webkit-align-items:center;align-items:center;-webkit-justify-content:space-between;justify-content:space-between;z-index:9999999}.zhong{position:absolute;border-radius:20px;--a: 3px;background-color:rgba(255, 255, 255, 0.2);backdrop-filter:blur(var(--a));-webkit-backdrop-filter:blur(var(--a));top:30%;left:4%;right:4%;color:black;padding:10px 15px 10px 10px;font-size:32px;display:-webkit-flex;display:flex;-webkit-align-items:center;align-items:center;-webkit-justify-content:space-between;justify-content:space-between;letter-spacing:2px}.app{position:absolute;bottom:120px;left:4%;right:4%;display:-webkit-flex;display:flex;-webkit-align-items:center;align-items:center;-webkit-justify-content:space-evenly;justify-content:space-evenly;color:black;box-sizing:border-box;max-width:92%;-webkit-flex-wrap:wrap;flex-wrap:wrap}.app-svg-div{max-width:20%;-webkit-transform:scale(0.8);transform:scale(0.8);-webkit-transform-origin:center;transform-origin:center}.QQ_home_head{border-radius:50%;margin-right:10px;width:40px;height:40px;font-size:0;min-width:40px;background-size:cover}.QQ_home_usermsg{--head_size: 40px;width:100%;display:-webkit-flex;display:flex;font-size:14px;-webkit-align-items:center;align-items:center;height:60px;align-items:center;position:relative}.QQ_home_usermsg>*{position:relative;z-index:1}.QQ_home_usermsg:active::before{content:"";position:absolute;top:0;left:-10px;right:-10px;height:100%;background-color:#d5d5d5}.QQ_home_usermsg_new{background-color:red;border-radius:50%;width:17px;height:17px;color:white;display:-webkit-flex;display:flex;-webkit-align-items:center;align-items:center;-webkit-justify-content:center;justify-content:center;font-size:13px;justify-self:end;padding:2px}.QQ_home_usermsg_new_hidden{background-color:#dbdbdb;border-radius:10px;width:2rem;height:17px;color:white;display:-webkit-flex;display:flex;-webkit-align-items:center;align-items:center;-webkit-justify-content:center;justify-content:center;font-size:13px;justify-self:end;padding:2px}.QQ_bottom_nav{position:absolute;bottom:0px;width:100%;height:40px;display:-webkit-flex;display:flex;-webkit-align-items:center;align-items:center;-webkit-justify-content:space-evenly;justify-content:space-evenly;font-size:10px;background-color:white}body div{box-sizing:border-box !important}.page_button{display:-webkit-flex;display:flex;-webkit-align-items:center;align-items:center;-webkit-justify-content:space-evenly;justify-content:space-evenly;width:100%;z-index:99;position:absolute;bottom:0px;left:0;height:22px;box-sizing:border-box;padding-bottom:4px}@media(max-width: 768px){.zhong{font-size:20px}}#space_page{width:100%;height:820px;background-color:#fff;display:none}.user_moment_title{--head_size: 40px;width:100%;display:-webkit-flex;display:flex;font-size:14px;-webkit-align-items:start;align-items:start;margin-bottom:5px}.user_moment{padding-left:7px;padding-right:7px;position:relative}.moment_button{display:-webkit-flex;display:flex;-webkit-align-items:center;align-items:center;gap:5px;margin-left:auto;-webkit-transform:scale(0.9);transform:scale(0.9);-webkit-transform-origin:center;transform-origin:center}.moment_msg{background-color:#fff;margin-top:5px;max-width:60%;border-radius:7px;font-size:15px;padding:6px;width:-webkit-fit-content;width:fit-content;white-space:normal;letter-spacing:1px;line-height:1.4;margin-bottom:10px;box-shadow:1px 1px 2px rgba(32, 32, 32, 0.2)}#Home_page{width:100%;height:100%;background-image:url("http://sharkpan.xyz/f/nBUl/%E6%89%8B%E6%9C%BA%E5%A3%81%E7%BA%B82.jpg");background-size:cover;background-position:top center;background-repeat:no-repeat}.QQ_chat_head{width:40px;height:40px;border-radius:50%;background-size:cover;min-width:40px}.user_avatar{width:40px;height:40px;border-radius:50%;background-size:cover}.QQ_chat_msg{font-size:14px;margin-right:5px;margin-left:5px}.QQ_chat_mymsg{max-width:70%;margin-left:auto;display:grid;grid-template-columns:1fr 50px;grid-template-rows:auto auto;justify-items:end;text-align:right;-webkit-align-items:start;align-items:start;margin-bottom:20px;margin-right:11px}.QQ_chat_charmsg{max-width:80%;display:grid;grid-template-columns:47px 1fr;grid-template-rows:auto auto;-webkit-align-items:start;align-items:start;margin-bottom:20px;margin-left:10px}.QQ_chat_msgdiv{box-shadow:2px 2px 2px rgba(15, 15, 15, 0.3);letter-spacing:1px;line-height:1.4;font-size:15px;width:-webkit-fit-content;width:fit-content;background-color:rgb(239, 206, 242, 0.65);color:black;border-radius:7px;padding:7px;text-align:left;box-sizing:border-box;margin-left:-2px}.QQ_chat_name{font-size:13px;color:black}.QQ_chat_msgimg{width:100%;background-color:white}.msgimg{max-width:100%;height:auto;display:block}.QQ_chat_time{display:-webkit-flex;display:flex;-webkit-align-items:center;align-items:center;-webkit-justify-content:center;justify-content:center;width:100%;height:10px;margin-bottom:10px;color:#a1a1a1}.mimictwitter hr{background-color:rgb(240, 244, 245);border:none;height:1px}.twitterhead{width:45px;height:45px;border-radius:50%;margin-left:4px;min-width:45px}.interactsvg{width:15.5px;height:15.5px}.twitter-content{-webkit-flex:1;flex:1;overflow-y:auto;box-sizing:border-box;padding-top:10px;padding-right:3px}.mimictwitter{background-color:white;width:100%;max-width:100%;border-radius:10px;height:100%;box-sizing:border-box;padding-top:10px;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;position:relative;overflow-x:hidden}.twitter{overflow-x:hidden;font-size:16px;height:100%}.twitter_moment{display:grid;grid-template-columns:50px 1fr 0px;grid-template-rows:auto auto}img{box-sizing:border-box !important}*{box-sizing:border-box}.user_leave_message{font-size:13px;margin-top:7px;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;span{line-height:1.5}}.space_contents{scrollbar-width:none;-ms-overflow-style:none}.give_money{margin-top:10px;border-radius:8px;overflow:hidden}#QQ_home_chars{background-color:#fefefe;width:100%;-webkit-flex:1;flex:1;border-radius:20px 20px 0 0;padding:5px 10px 20px 10px;box-sizing:border-box;overflow:auto !important;-ms-overflow-style:none !important;scrollbar-width:none !important;margin-bottom:40px}#QQ_home_chars::-webkit-scrollbar{display:none !important}#QQ_home_page{display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;height:100%}span{word-break:break-word}.QQ_chat_music{background-color:#fefefe;border-radius:10px;padding-top:10px;padding-right:10px;padding-left:10px;padding-bottom:5px;width:100%}.hide-title{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.music-play-button{position:absolute;border-radius:50%;background-color:hsla(0,0%,100%,.2862745098);height:32px;width:32px;top:50%;left:50%;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);z-index:2;border:2px solid white;display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center;-webkit-align-items:center;align-items:center}.space_fakeimg{text-align:center;margin-top:5px;font-size:1.2rem;background-color:rgb(239 237 238);padding:30px 10px;width:100%;border:1px solid black;border-radius:5px;text-align:center}.QQ_home_lasttime{overflow:hidden;white-space:nowrap;text-overflow:ellipsis;max-width:4rem}
</style><style>
        .button_image {
    /* 基础样式 */
    padding: 3px 4px;
    font-size: 13px;
    font-weight: 600;
    color: #ffffff;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border: none;
    border-radius: 2px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(99, 102, 241, 0.2);
    
    /* 文本和图标布局 */
    display: inline-flex;
    align-items: center;
    gap: 8px;

    /* 防止文本换行 */
    white-space: nowrap;
    
    /* 去除默认按钮样式 */
    outline: none;
    -webkit-appearance: none;
    -moz-appearance: none;
     }
      </style><style type="text/css">@font-face { font-family: TencentSans; src: url("moz-extension://31195eee-e8ff-44c2-9336-ba5b312126b2/static/fonts/TencentSans.woff2"); }</style></head><body><div class="card"><div class="btn1"></div><div class="btn2"></div><div class="btn3"></div><div class="btn4"></div><div class="dinbu"><span id="time">17:24</span><div style="display:flex;align-items:center;justify-self:end;gap:2px"><svg viewBox="0 0 1024 1024" width="16" height="15"><path d="M926.634667 294.912a32 32 0 1 1-39.936 50.005333C780.512 260.128 649.824 213.333333 512.053333 213.333333c-137.813333 0-268.544 46.826667-374.752 131.669334a32 32 0 1 1-39.936-50.005334C214.784 201.194667 359.562667 149.333333 512.053333 149.333333c152.437333 0 297.173333 51.818667 414.581334 145.578667z m-235.413334 298.133333a32 32 0 0 1-38.442666 51.178667A233.418667 233.418667 0 0 0 512.021333 597.333333c-51.541333 0-100.48 16.629333-140.8 46.912a32 32 0 1 1-38.442666-51.157333A297.408 297.408 0 0 1 512.021333 533.333333c65.504 0 127.893333 21.184 179.2 59.722667z m128-149.344a32 32 0 0 1-38.442666 51.168C703.829333 437.066667 610.378667 405.333333 512.032 405.333333c-98.368 0-191.850667 31.754667-268.8 89.578667a32 32 0 1 1-38.453333-51.157333C292.736 377.664 399.669333 341.333333 512.032 341.333333c112.32 0 219.242667 36.309333 307.189333 102.368zM512 853.333333a64 64 0 1 1 0-128 64 64 0 0 1 0 128z" fill="currentColor"></path></svg> <span>78%</span> <svg viewBox="0 0 1024 1024" width="20" height="20" style="justify-self:end;margin-right:13px"><path d="M984.2 434.8c-5-2.9-8.2-8.2-8.2-13.9v-99.3c0-53.6-43.9-97.5-97.5-97.5h-781C43.9 224 0 267.9 0 321.5v380.9C0 756.1 43.9 800 97.5 800h780.9c53.6 0 97.5-43.9 97.5-97.5v-99.3c0-5.8 3.2-11 8.2-13.9 23.8-13.9 39.8-39.7 39.8-69.2v-16c0.1-29.6-15.9-55.5-39.7-69.3zM912 702.5c0 12-6.2 19.9-9.9 23.6-3.7 3.7-11.7 9.9-23.6 9.9h-781c-11.9 0-19.9-6.2-23.6-9.9-3.7-3.7-9.9-11.7-9.9-23.6v-381c0-11.9 6.2-19.9 9.9-23.6 3.7-3.7 11.7-9.9 23.6-9.9h780.9c11.9 0 19.9 6.2 23.6 9.9 3.7 3.7 9.9 11.7 9.9 23.6v381z" p-id="4287" fill="currentColor"></path><path d="M736 344v336c0 8.8-7.2 16-16 16H112c-8.8 0-16-7.2-16-16V344c0-8.8 7.2-16 16-16h608c8.8 0 16 7.2 16 16z" p-id="4288" fill="currentColor"></path></svg></div></div><div class="card-int"><div class="page_button" style="background-color:none;display:none" id="page_button"><div style="width:45px;display:flex;justify-content:center;height:20px" onclick="gotopage(1)"><svg viewBox="0 0 1024 1024" width="20" height="20"><path d="M810.666667 213.333333a42.666667 42.666667 0 0 1 42.368 37.674667L853.333333 256v274.304c0 79.274667-50.944 147.498667-120.490666 152.106667L725.333333 682.666667H273.706667l97.792 97.834666a42.666667 42.666667 0 0 1-56.32 63.872l-4.010667-3.541333-170.666667-170.666667a42.666667 42.666667 0 0 1 0-60.330666l170.666667-170.666667a42.666667 42.666667 0 0 1 63.872 56.32l-3.541333 4.010667L273.706667 597.333333H725.333333c19.584 0 39.936-24.618667 42.410667-59.861333l0.256-7.168V256a42.666667 42.666667 0 0 1 42.666667-42.666667z" fill="#000000" p-id="13502"></path></svg></div><div style="width:45px;display:flex;justify-content:center" onclick="gotopage(0)"><svg t="1736242335450" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8709" width="20" height="20"><path d="M762.3 565.3c0-19 15.7-34.1 34.7-34.1 19 0 34.2 15.1 34.2 34.1v336.3c0 19-15.2 34.4-34.2 34.4H229.6c-19.1 0-34.7-15.4-34.7-34.4V565.3c0-19 15.7-34.1 34.7-34.1 18.6 0 34.2 15.1 34.2 34.1v301.9h498.5V565.3z m-638.2 9.3l388.8-387.8 389.3 387.8c13.2 13.2 35.2 13.2 48.4 0 13.4-13.2 13.4-35.1 0-48.3L538 114.1l-0.7-0.5c-13.4-13.2-35.2-13.2-48.6 0l-413 412.6c-13.6 13.2-13.6 35.1 0 48.3 13.2 13.2 35.2 13.2 48.4 0.1z" fill="#231815" p-id="8710"></path></svg></div><div style="width:45px;display:flex;justify-content:center" onclick="gotopage(-1)"><svg t="1736242697753" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9742" width="20" height="20"><path d="M622.650611 284.901749 447.745069 284.901749 447.745069 142.823869 63.980685 334.705038l383.76336 191.882192L447.744046 384.834762l189.391465 0c149.914358 0 224.855164 62.789045 224.855164 188.368158 0 129.928165-77.435627 194.876386-232.338602 194.876386L187.952184 768.079306l0 99.93199L634.146433 868.011296c211.184817 0 316.777737-95.104031 316.777737-285.311071C950.924169 384.178823 841.510224 284.901749 622.650611 284.901749z" fill="#272636" p-id="9743"></path></svg></div></div><div id="Home_page" style="display:none"><div class="dibu"><svg viewBox="0 0 1024 1024" style="width:4.1rem;height:4.1rem"><path d="M510.798639 124.660184c-213.447347 0-386.480238 173.029822-386.480238 386.480238 0 213.446323 173.029822 386.475122 386.480238 386.475122 213.446323 0 386.475122-173.029822 386.475122-386.475122C897.274784 297.690006 724.244962 124.660184 510.798639 124.660184L510.798639 124.660184 510.798639 124.660184zM510.798639 864.019379c-194.883549 0-352.884073-158.001547-352.884073-352.879979 0-194.883549 158.000524-352.884073 352.884073-352.884073 194.878432 0 352.883049 158.000524 352.883049 352.884073C863.678618 706.017832 705.677071 864.019379 510.798639 864.019379L510.798639 864.019379 510.798639 864.019379zM588.978209 546.374902c-17.681708 0-31.070646 15.915481-47.113017 15.915481-15.910365 0-85.756129-68.836785-85.756129-85.761246 0-16.920368 15.914458-25.258267 15.914458-42.813085 0-12.632715-47.107901-89.925079-66.432015-89.925079-19.328207 0-66.436108 34.479279-66.436108 66.433038 0 92.455715 170.506349 269.653463 277.230022 269.653463 29.427216 0 66.437132-31.070646 66.437132-66.437132C683.071214 600.178295 606.532003 546.374902 588.978209 546.374902" fill="#09B245" p-id="6003"></path></svg> <svg viewBox="0 0 1066 1024" style="width:3.3rem;height:3.3rem"><path d="M548.48 0c282.752 0 512 229.248 512 512s-229.248 512-512 512c-282.794667 0-512-229.248-512-512s229.205333-512 512-512z m0 42.666667c-259.242667 0-469.333333 210.133333-469.333333 469.333333s210.090667 469.333333 469.333333 469.333333c259.2 0 469.333333-210.133333 469.333333-469.333333s-210.133333-469.333333-469.333333-469.333333z m0 173.952c168.234667 0 304.597333 118.186667 304.597333 263.936 0 145.792-136.362667 263.978667-304.64 263.978666a348.757333 348.757333 0 0 1-73.130666-7.68c-36.010667 22.357333-109.866667 70.528-109.866667 70.528s-0.341333-76.672 0-115.797333c-73.813333-48.128-121.6-124.757333-121.6-211.029333 0-145.749333 136.362667-263.936 304.64-263.936z m-129.706667 223.658666a40.448 40.448 0 0 0-40.533333 40.32 40.448 40.448 0 0 0 40.533333 40.32c22.4 0 40.533333-18.005333 40.533334-40.32a40.448 40.448 0 0 0-40.533334-40.32z m135.850667 0a40.448 40.448 0 0 0-40.576 40.32 40.448 40.448 0 0 0 40.533333 40.32 40.448 40.448 0 0 0 40.618667-40.32 40.490667 40.490667 0 0 0-40.576-40.32z m135.850667 0a40.490667 40.490667 0 0 0-40.576 40.32 40.448 40.448 0 0 0 40.576 40.32 40.448 40.448 0 0 0 40.533333-40.32 40.448 40.448 0 0 0-40.533333-40.32z" fill="#2397FF" p-id="9807"></path></svg> <svg viewBox="0 0 1024 1024" style="width:4rem;height:4rem"><path d="M441.05 164.53c49.52-9.62 101.09-8.57 150.18 3.06 43.01 10.18 84.04 28.57 120.29 53.84 65.19 45.14 114.56 112.69 137.32 188.68 20.42 67.17 20.07 140.33-0.41 207.44l-0.13 0.46c-23.01-53.51-74.79-93.34-132.27-102.5l-0.01-0.11c0.47-40.52-11.14-81.12-33.35-115.06-22.75-35.1-56.37-63.06-95.1-78.89-39.94-16.49-85.1-19.84-127.09-9.72-39.19 9.32-75.35 30.59-102.8 60.04-28.81 30.73-47.9 70.45-53.65 112.2-6.29 44.14 2.08 90.27 23.8 129.23 19.43 35.16 49.34 64.4 84.89 83.08 30.24 16.01 64.53 24.1 98.72 23.74h0.11c7.41 46.72 35.01 89.65 73.93 116.41 9 6.11 18.38 11.95 28.55 15.85l-0.56 0.16c-69.43 21.22-145.37 20.78-214.4-1.82-75.89-24.48-142.72-75.63-186.49-142.25-23.43-35.39-40.37-75.04-49.76-116.43-11.55-50.94-11.75-104.42-0.53-155.44 14.59-67.14 49.51-129.62 98.75-177.51 49.09-48.12 112.46-81.52 180.01-94.46z" fill="#5C5CEF" p-id="22508"></path><path d="M596.41 537.54c35.61-21.21 78.78-28.95 119.61-22.14l0.01 0.11c-0.55 37.38-11.1 74.74-31.06 106.43-16.48 26.73-39.2 49.58-65.79 66.26-31.95 20.42-69.77 31.29-107.63 31.82h-0.11c-5.28-31.73-1.94-64.9 9.96-94.81 14.36-36.47 41.23-67.8 75.01-87.67zM848.3 618.01l0.13-0.46c12.06 27.01 17.34 56.99 15.05 86.49-3.04 42.37-22.11 83.28-52.48 112.96-17.34 17.04-38.17 30.6-60.92 39.23-43.64 17.04-93.93 15.35-136.61-3.79l0.56-0.16c37.37-11.79 72.97-29.37 104.52-52.68 11.87-9.03 23.82-18.11 34.23-28.85 2.98-3 6.62-5.3 9.3-8.61 4.52-5.81 10.34-10.43 14.84-16.26 32.42-37 56.69-80.95 71.38-127.87z" fill="#19FFDC" p-id="22509"></path><path d="M716.03 515.51c57.48 9.16 109.26 48.99 132.27 102.5-14.69 46.92-38.96 90.87-71.38 127.87-4.5 5.83-10.32 10.45-14.84 16.26-2.68 3.31-6.32 5.61-9.3 8.61-10.41 10.74-22.36 19.82-34.23 28.85-31.55 23.31-67.15 40.89-104.52 52.68-10.17-3.9-19.55-9.74-28.55-15.85-38.92-26.76-66.52-69.69-73.93-116.41 37.86-0.53 75.68-11.4 107.63-31.82 26.59-16.68 49.31-39.53 65.79-66.26 19.96-31.69 30.51-69.05 31.06-106.43z" fill="#09EFDA" p-id="22510"></path></svg> <svg viewBox="0 0 1024 1024" style="width:3.7rem;height:3.7rem"><path d="M517.116019 967.737602c-120.532167 0-233.850026-46.937009-319.079152-132.165112C112.80774 750.341317 65.870732 637.025505 65.870732 516.492314c0-120.532167 46.938032-233.850026 132.166135-319.079152C283.26497 112.184035 396.583852 65.246003 517.116019 65.246003c120.53319 0 233.850026 46.938032 319.079152 132.166135s132.166135 198.546985 132.166135 319.079152c0 120.53319-46.937009 233.849002-132.166135 319.079152C750.966045 920.79957 637.64921 967.737602 517.116019 967.737602zM517.116019 108.790752c-108.901269 0-211.284077 42.407855-288.287869 119.41267S109.41548 407.591045 109.41548 516.492314s42.407855 211.283054 119.41267 288.286846c77.003791 77.005838 179.3866 119.413694 288.287869 119.413694s211.284077-42.407855 288.287869-119.413694c77.005838-77.003791 119.41267-179.385577 119.41267-288.286846s-42.406832-211.284077-119.41267-288.288892C728.400097 151.198607 626.017288 108.790752 517.116019 108.790752z" fill="#2c2c2c" p-id="39954"></path><path d="M693.052031 355.056552l-58.175981 0c0-32.129768-26.049283-58.177004-58.179051-58.177004L460.345038 296.879548c-32.127721 0-58.177004 26.047236-58.177004 58.177004l-58.179051 0c-32.127721 0-58.175981 26.046213-58.175981 58.173934l0 232.69983c0 32.127721 26.048259 58.172911 58.175981 58.172911l349.063047 0c32.129768 0 58.178027-26.045189 58.178027-58.172911l0-232.69983c0-32.127721-26.047236-58.177004-58.178027-58.177004L693.052031 355.056552zM519.453251 614.775758c-49.109488 0-88.929402-39.814798-88.929402-88.922239 0-49.110511 39.819914-88.927355 88.929402-88.927355 49.111534 0 88.930425 39.816844 88.930425 88.927355C608.383676 574.959937 568.564785 614.775758 519.453251 614.775758L519.453251 614.775758zM519.453251 614.775758" fill="#2c2c2c" p-id="39955"></path></svg></div><div class="zhong"><div style="display:flex;align-items:center;gap:10px"><svg viewBox="0 0 1024 1024" style="width:3rem;height:3rem"><path d="M509.014999 512.046737m-262.543634 0a262.543634 262.543634 0 1 0 525.087268 0 262.543634 262.543634 0 1 0-525.087268 0Z" fill="#FFD54F" p-id="51646"></path><path d="M510.601314 0.11053c0 1.705715 67.222223 48.391131 67.222223 91.886861a62.224479 62.224479 0 0 1-67.222223 65.618851 62.224479 62.224479 0 0 1-67.222224-65.635908c0-43.495729 67.222223-93.575518 67.222224-91.869804z" fill="#FFD54F" p-id="51647"></path><path d="M869.21081 155.364698c-1.228115 1.194 11.940004 81.9596-19.342807 112.184868a62.224479 62.224479 0 0 1-93.933718-2.695029 62.224479 62.224479 0 0 1 0.460543-93.950776c31.282811-30.225268 114.044097-16.716006 112.815982-15.539063z" fill="#FFD54F" p-id="51648"></path><path d="M1022.571634 513.411308c-1.705715-0.034114-49.53396 66.40348-93.029689 65.670023a62.241536 62.241536 0 0 1-64.527194-68.347995 62.241536 62.241536 0 0 1 66.778738-66.113509c43.495729 0.750515 92.483861 68.825595 90.778145 68.791481z" fill="#FFD54F" p-id="51649"></path><path d="M869.176695 868.660546c-1.228115-1.176943-81.499057 14.754434-112.781867-15.453777a62.224479 62.224479 0 0 1-0.545829-93.984889 62.224479 62.224479 0 0 1 93.899604-2.763259c31.299868 30.225268 20.656207 113.395925 19.44515 112.218982z" fill="#FFD54F" p-id="51650"></path><path d="M510.601314 1023.948829c0-1.705715 67.222223-48.391131 67.222223-91.886861a62.224479 62.224479 0 0 0-67.222223-65.584737 62.224479 62.224479 0 0 0-67.222224 65.584737c0 43.495729 67.222223 93.558461 67.222224 91.886861z" fill="#FFD54F" p-id="51651"></path><path d="M154.891533 155.330584c1.228115 1.194-11.940004 81.976657 19.342807 112.201925a62.224479 62.224479 0 0 0 93.916661-2.729144 62.224479 62.224479 0 0 0-0.4776-93.933718c-31.299868-30.259382-113.975868-16.698949-112.781868-15.522006z" fill="#FFD54F" p-id="51652"></path><path d="M1.445423 513.34308c1.705715-0.034114 49.53396 66.40348 93.029689 65.670023a62.241536 62.241536 0 0 0 64.49308-68.347995 62.241536 62.241536 0 0 0-66.778738-66.113509c-43.529844 0.750515-92.415632 68.825595-90.744031 68.791481z" fill="#FFD54F" p-id="51653"></path><path d="M154.942705 868.660546c1.228115-1.176943 81.516114 14.720319 112.781867-15.522005a62.224479 62.224479 0 0 0 0.477601-93.933719 62.224479 62.224479 0 0 0-93.916662-2.729143c-31.299868 30.259382-20.536807 113.361811-19.342806 112.201924z" fill="#FFD54F" p-id="51654"></path></svg> <span>22°</span></div><div style="display:flex;flex-direction:column;justify-self:end;align-items:end;font-size:inherit;gap:7px"><span>日本市</span> <span id="day">1月4日</span></div></div><div class="app"><div style="display:flex;flex-direction:column;align-items:center" class="app-svg-div"><svg viewBox="0 0 1024 1024" width="64" height="64"><path d="M763.776 968.576H261.504C147.456 968.576 55.04 876.16 55.04 762.112V259.84c0-114.048 92.416-206.464 206.464-206.464h502.272C877.824 53.376 970.24 145.792 970.24 259.84v502.272c0 114.048-92.416 206.464-206.464 206.464z" fill="#009CF5" p-id="52731"></path><path d="M529.792 586.496h-100.48l75.648-130.56 7.68-13.312 51.84-89.088 9.984-17.408 21.504-36.864c9.472-16.128 9.728-36.864-1.024-52.096-9.216-13.056-22.4-18.304-35.456-18.304-15.36 0-30.336 7.936-38.784 22.272l-7.68 13.568-7.936-13.568c-8.448-14.336-23.552-22.272-38.784-22.272-7.68 0-15.36 1.792-22.528 5.888-21.248 12.288-28.544 39.808-16.128 61.056l33.408 57.728-135.168 233.216H238.08c-26.88 0.256-48.384 24.064-44.16 52.096 3.328 22.016 23.936 37.376 46.208 37.376h33.92l103.424-0.256H583.936c3.84 0 7.68-1.664 9.984-4.736 11.776-15.744 20.48-40.192-8.32-65.408-15.232-13.44-35.584-19.328-55.808-19.328z" fill="#FFFFFF" p-id="52732"></path><path d="M785.28 586.24h-86.016l-115.072-198.528c-2.048-3.456-6.656-4.608-9.856-2.304-16.128 11.648-25.6 27.904-30.464 45.824-8.192 30.208-1.408 62.592 14.208 89.728l24.704 42.88 13.056 22.784 51.84 89.344 5.376 8.96 49.792 86.016c8.448 14.336 23.296 22.272 38.528 22.272 7.68 0 15.616-2.048 22.784-6.144 21.248-12.288 28.544-39.552 16.128-61.056l-29.056-50.304h36.224c26.88 0 48.256-24.32 43.904-52.352-3.584-21.888-23.936-37.12-46.08-37.12zM319.616 687.616c-15.744-6.4-30.592-5.632-42.496-2.304-6.528 1.792-11.904 6.272-15.232 12.032l-16.64 28.416c-12.544 21.504-5.12 48.768 16.128 61.056 7.168 4.096 15.104 6.144 22.784 6.144 15.36 0 30.08-7.936 38.272-22.272l18.944-32.64c4.608-8.064 5.504-17.792 2.048-26.368-3.84-9.6-11.008-18.816-23.808-24.064z" fill="#FFFFFF" p-id="52733"></path></svg> <span>Store</span></div><div style="display:flex;flex-direction:column;align-items:center" class="app-svg-div" data-app="twitter"><svg viewBox="0 0 1024 1024" width="64" height="64"><path d="M849.92 51.2H174.08c-67.8656 0-122.88 55.0144-122.88 122.88v675.84c0 67.8656 55.0144 122.88 122.88 122.88h675.84c67.8656 0 122.88-55.0144 122.88-122.88V174.08c0-67.8656-55.0144-122.88-122.88-122.88z m-93.65504 336.5888a317.0816 317.0816 0 0 1 0.4352 16.11776c0 165.16096-126.8224 355.53792-358.656 355.53792-71.14752 0-137.4208-20.72064-193.24416-56.05888a248.6272 248.6272 0 0 0 30.04928 1.7664 254.80704 254.80704 0 0 0 156.56448-53.45792c-55.13728-1.08544-101.67808-37.28384-117.71392-86.79424 7.67488 1.37216 15.616 2.29888 23.74656 2.29888a124.6208 124.6208 0 0 0 33.13152-4.50048c-57.61024-11.47904-101.08416-61.94176-101.08416-122.54208v-1.46432c17.02912 9.216 36.48512 14.96064 57.15456 15.59552-33.85856-22.49728-56.064-60.66688-56.064-104.03328 0-22.81472 6.14912-44.43648 17.06496-62.90432a359.2192 359.2192 0 0 0 259.80416 130.62144 125.37344 125.37344 0 0 1-3.29216-28.50816c0-68.97664 56.42752-124.91264 126.05952-124.91264 36.24448 0 68.96128 15.0784 91.88864 39.40352 28.73344-5.5296 55.7312-16.0256 80.09728-30.30528-9.40032 29.12768-29.42464 53.7856-55.48032 69.24288 25.62048-3.1488 49.8944-9.82016 72.47872-19.82464a247.84384 247.84384 0 0 1-62.94016 64.72192z" fill="#03A9F4" p-id="54801"></path></svg> <span>推特</span></div><div style="display:flex;flex-direction:column;align-items:center;position:relative" data-app="QQ" class="app-svg-div"><svg viewBox="0 0 1024 1024" width="64" height="64"><path d="M887.85832 64.549132 136.14168 64.549132c-39.540552 0-71.591525 32.051997-71.591525 71.590502l0 751.718687c0 39.538505 32.051997 71.591525 71.591525 71.591525l751.71664 0c39.538505 0 71.592548-32.05302 71.592548-71.591525L959.450868 136.139633C959.449845 96.601128 927.396825 64.549132 887.85832 64.549132zM862.687034 644.674719l0 5.110391 0 4.786003-0.860601 5.754051-1.236154 9.869788-1.720178 8.9263-2.636037 8.067746-0.967024 3.307326-1.828648 3.925403-1.560542 2.77009-2.043542 3.413749-1.829671 2.125407-2.311649 2.769067-2.043542 2.204202-2.204202 1.936095-2.636037 1.157359-2.367931 1.155313-1.934049 0.644683-1.694595 0-1.264806 0-1.908466-0.644683-3.631714-1.775436-1.664919-1.182942-1.722224-1.290389-1.910513-1.694595-1.908466-1.908466-3.173273-3.496637-3.765767-5.000897-2.957355-5.109368-2.957355-4.14132-2.877537-4.787026-4.060479-8.496511-4.570085-8.846482-0.537236-0.297782-0.754177 0-1.88186 1.37123-1.075495 2.339278-1.774413 2.851954-3.120061 8.631588-4.679579 12.235672-6.02318 14.653745-4.516874 7.341199-4.76042 7.635911-5.621021 8.606005-5.969968 8.443299-3.064802 3.818979-3.710508 4.142343-8.525164 8.389064 0.754177 0.75213 1.154289 1.182942 4.249791 2.52859 17.748223 8.497535 7.745405 4.356214 7.366781 4.247744 7.313569 5.324262 6.508227 5.539156 3.174296 2.446725 2.366907 2.877537 2.418073 3.279696 2.044566 3.63069 1.077541 2.984984 1.342578 3.603061 0.539282 3.065825 0.64366 3.629667-0.64366 2.447748 0 2.419096-0.539282 2.581802-1.342578 2.312672-0.539282 1.827625-1.182942 2.205225-3.226484 4.383844-2.956332 3.523243-2.312672 2.63399-1.936095 1.936095-4.89345 3.603061-5.54018 3.092431-5.915733 2.984984-6.293333 2.849908-7.098675 2.743484-3.764744 1.181919-3.38919 0.969071-8.175193 1.908466-8.498558 1.801019-8.496511 1.829671-9.250688 1.479701-9.573029 0.402159-9.78997 1.182942-9.680477 0-10.218736 0-10.487865 0-10.970866-0.644683-10.219759-0.940418-10.970866-1.479701-10.917654-1.183965-11.509125-1.612731-11.134595-2.743484L592.539314 853.880461l-10.81123-3.065825-11.078313-3.926426-10.862395-3.307326-5.702885-1.909489-5.109368-1.828648-3.225461-1.237177-3.174296-0.644683-4.142343 0-4.89345 0-10.431583-0.75213-5.298679-0.538259-6.80294-0.751107-4.357238 3.924379-5.969968 3.738138-8.066723 3.952009-8.928347 4.894474-5.431709 2.63399-5.647627 2.125407-12.531408 5.109368-6.80294 1.693572-7.125281 1.936095-9.895371 1.882883-6.185886 0.537236-6.562463 0.537236-6.830569 0.754177-7.959276 0.322341-7.637957 0-7.959276 0-16.914228 0-18.3663-0.322341-17.801435-1.828648-9.035794-1.238201-8.712429-1.290389-8.497535-1.291412-8.497535-1.693572-7.879458-2.555196-7.878434-1.801019-7.099698-2.877537-6.722098-2.63399-6.239098-3.012613-5.431709-3.28072-5.432733-3.926426-1.828648-1.935072-2.420119-2.312672-1.829671-2.042519-1.666966-2.312672-1.50733-2.365884-1.182942-2.312672-1.694595-5.001921-0.618077-2.555196-0.754177-2.876514 0-2.636037 0.754177-2.983961 0-2.986007 0.618077-2.983961 0-1.802042 0-4.140297 0.295735-3.201925 1.39886-3.629667 1.182942-3.603061 2.153036-4.355191 1.558495-1.694595 1.291412-1.908466 3.387144-4.14132 2.689249-2.152013 2.52859-1.478677 2.341325-1.908466 3.736091-1.183965 2.958378-1.77646 3.710508-1.935072 4.248767-1.264806 4.247744-1.290389 4.894474-1.048889 4.678556-0.645706 5.541203-0.751107 5.968945-0.431835 1.586125-0.536212 0.322341 0 0.754177-0.644683 0-0.699941-1.076518-1.506307-3.199878-1.505283-7.958252-6.883781-5.324262-4.248767-6.185886-5.431709-6.239098-6.05081-6.508227-7.770987-7.421017-8.712429-2.796696-4.679579-3.711532-4.894474-3.173273-5.64558-2.850931-6.157233-3.873214-5.834892-2.52859-6.724145-2.984984-6.66684-2.984984-7.476275-2.205225-7.098675-2.043542-8.819876-0.644683-0.323365-0.6191 0-0.322341-0.645706-0.754177 0-1.263783 0.645706-0.645706 0.323365-0.858554 1.478677-0.323365 1.802042-0.644683 1.613754-1.12973 2.63399-3.657296 6.507204-1.882883 3.926426-2.984984 3.388167-3.199878 4.140297-3.496637 4.571109-3.710508 4.061502-4.357238 3.925403-4.059456 3.710508-4.437056 2.877537-4.894474 3.091408-4.678556 1.802042-5.539156 1.39886-5.431709 0.509606-0.537236 0-0.753153 0-1.237177-0.509606-0.969071-2.152013-1.612731-1.048889-2.097778-5.108345-1.290389-2.77009-1.39886-3.925403-1.156336-4.14132-0.6191-4.033873-1.721201-9.249665-0.644683-5.433756 0-5.538133 0-12.31549 0.644683-13.283538 1.076518-6.831592 1.263783-6.990205 1.156336-7.556093 2.043542-7.018857 2.312672-8.094352 2.366907-7.852852 3.174296-8.093329 2.984984-7.745405 4.113691-7.877411 3.873214-8.497535 4.678556-7.985882 5.51255-8.606005 5.621021-7.745405 5.862521-8.603959 4.894474-5.968945 6.265704-6.91141 6.589069-6.910387 3.065825-3.279696 3.736091-3.738138 5.406127-4.705162 5.54018-4.759397 9.03477-8.094352 6.830569-4.975315 2.420119-1.802042-1.344624-4.140297-1.075495-5.431709-0.753153-2.984984 0-3.738138 0-4.463662 0-4.03285 1.290389-4.787026 1.345648-5.109368 1.720178-5.297656 2.341325-5.942339 3.091408-6.184863 4.356214-6.184863 0-4.330632 0.429789-4.060479 0.645706-5.51255 1.88186-6.15621 1.721201-6.696516 1.37123-3.093455 1.586125-2.848884 2.043542-3.200902 2.205225-2.339278 0-4.356214 0-4.894474 0-6.076392 1.290389-7.852852 1.290389-9.143241 2.312672-10.970866 3.173273-11.348466 2.366907-6.290263 2.339278-6.803963 2.555196-6.372128 3.065825-6.80294 3.119037-7.448646 3.604084-6.990205 4.03285-7.476275 5.109368-7.42204 2.340301-4.247744 2.52859-3.523243 5.458315-7.878434 5.592368-7.851828 6.399757-8.067746 6.830569-7.906064 7.42204-7.743358 7.744381-7.959276 9.358135-8.604982 5.969968-5.216815 7.232728-5.646604 7.342222-5.001921 7.959276-4.786003 7.637957-4.248767 8.604982-3.495614 9.143241-4.356214 9.087982-2.984984 9.090029-3.092431 9.787924-3.065825 9.788947-2.339278 10.326183-1.909489 10.432607-1.721201 10.272971-1.370207 10.32516-1.39886 10.972912-0.6191 10.431583 0 10.812253 0 11.18576 0 10.812253 1.37123 10.862395 1.291412 11.078313 1.155313 10.273994 2.474354 10.863419 2.311649 10.19213 2.636037 11.160177 3.01159 10.219759 3.602038 9.7869 4.14132 10.434653 4.356214 9.5454 4.786003 9.250688 5.324262 9.0624 5.862521 7.851828 5.405103 3.604084 3.092431 3.820002 2.204202 7.259334 6.185886 6.132674 5.968945 6.076392 6.157233 5.969968 6.801916 4.894474 6.589069 5.808286 6.80294 4.088108 7.313569 4.113691 6.508227 4.383844 7.554046 3.603061 6.589069 6.131651 13.929244 3.010567 7.207146 2.341325 6.723122 2.339278 7.205099 2.097778 6.831592 1.506307 5.833869 1.88186 6.91141 3.334955 12.316513 2.124384 10.754948 1.370207 10.43363 1.183965 8.389064 1.883907 12.746302 0.429789 2.043542 1.505283 2.311649 4.034896 6.614651 2.579755 4.437056 2.313695 4.705162 2.957355 4.975315 2.580778 5.969968 1.666966 6.157233 1.828648 6.695492 1.39886 6.910387 0.645706 3.388167 0.537236 4.14132 0 3.629667-0.537236 3.63069 0 4.464685-1.076518 4.355191-1.934049 8.391111-2.206248 4.247744-1.611707 4.786003 0 1.182942 0.75213 1.480724 2.205225 3.521197 9.575076 14.144138 7.581676 10.595312 3.604084 7.15291 4.89345 7.743358 4.247744 8.498558 4.89652 9.141194 4.892427 10.219759 5.513574 11.588943 3.090385 7.126304 2.850931 6.883781 2.476401 7.367805 2.420119 6.560416 1.828648 6.939039 1.880837 6.480598 2.365884 12.664438 1.882883 12.961196 1.289366 11.45489L862.688057 644.674719z" fill="#1296db" p-id="63263"></path></svg> <span>QQ</span><div class="new_tips" style="position:absolute;right:-5px;top:-5px;background-color:red;color:#fff;width:25px;height:25px;border-radius:50%;display:none;justify-content:center;align-items:center;font-size:15px">1</div></div><div style="display:flex;flex-direction:column;align-items:center" class="app-svg-div"><svg viewBox="0 0 1024 1024" width="57" height="57"><path d="M860.16 0C950.272 0 1024 73.889684 1024 164.163368v531.509895s-32.768-4.122947-180.224-53.355789c-40.96-14.362947-96.256-34.896842-157.696-57.478737 36.864-63.595789 65.536-137.485474 86.016-215.444211h-202.752v-71.841684h247.808V256.512h-247.808V135.437474h-100.352c-18.432 0-18.432 18.458947-18.432 18.458947v104.663579H200.704v41.040842h249.856v69.793684H243.712v41.013895H645.12c-14.336 51.307789-34.816 98.519579-57.344 141.608421-129.024-43.115789-268.288-77.985684-356.352-55.403789-55.296 14.362947-92.16 38.992842-112.64 63.595789-96.256 116.978526-26.624 295.504842 176.128 295.504842 120.832 0 237.568-67.718737 327.68-178.526316C757.76 742.858105 1024 853.692632 1024 853.692632v6.144C1024 950.110316 950.272 1024 860.16 1024H163.84C73.728 1024 0 950.137263 0 859.836632V164.163368C0 73.889684 73.728 0 163.84 0h696.32zM268.126316 553.121684c93.049263-10.374737 180.062316 26.974316 283.270737 78.874948-74.886737 95.501474-165.941895 155.701895-256.970106 155.701894-157.830737 0-204.368842-126.652632-125.466947-197.200842 26.300632-22.851368 72.838737-35.301053 99.166316-37.376z" fill="#00A0EA" p-id="64921"></path></svg> <span>支付宝</span></div></div></div><div id="App_Page" style="height:100%"><div class="twitter" id="App_twitter" style="display:none"><div class="mimictwitter"><div style="margin-right:8px;margin-top:10px"><div style="display:grid;grid-template-columns:1fr 1fr 1fr;align-items:center"><div style="border-radius:50%;width:32px;height:32px;margin-left:7px" class="twitter-close-btn user_avatar"></div><svg viewBox="0 0 24 24" style="width:24.73px;height:54px;justify-self:center"><g><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"></path></g></svg> <svg style="margin-left:auto;width:24px;height:24px" viewBox="0 0 1024 1024" aria-hidden="true"><g><path d="M596.992 512q0 34.016-24 59.008-12 12-27.488 18.496T512.992 596q-34.016 0-59.008-24.992-4.992-4.992-8.51199999-10.016t-6.49600001-11.008-4.992-12.512-3.48799999-12.992-1.50400001-12.512q0-35.00800001 24.992-59.488t59.488-24.512 59.488 24q7.008 8 12.992 17.504t8.512 20.512T596.96 512z m0 332q0 35.00800001-24 59.008-8 8-17.504 13.504t-20.512 8.512-22.016 3.008q-34.016 0-59.008-24.992-6.016-4.992-10.496-12t-7.488-14.496-4.992-16-2.016-16.512q0-35.00800001 24.992-59.488t59.488-24.51199999 59.488 24.99199999q11.008 11.008 17.504 27.008t6.496 32z m0-664.992q0 35.00800001-24 59.008-8 8-17.504 13.504t-20.512 8.512-22.016 3.00800001q-34.016 0-59.008-24.51200001T428.96 179.04000001t24.992-59.00800001q12-12 27.488-18.496t31.488-6.496q35.00800001 0 60 24.992 11.008 11.008 17.504 27.008t6.496 32z" fill="#000000" p-id="1477"></path></g></svg></div><div style="margin-top:13px"></div><div style="display:grid;grid-template-columns:1fr 1fr;align-items:center;justify-items:center"><span style="color:#5f6f7b">为你推荐</span> <span><strong>正在关注</strong></span></div><div style="margin-top:8px;display:grid;grid-template-columns:1fr 1fr;align-items:center;justify-items:center"><div></div><div style="background-color:#1d9bf0;width:100%;height:4px;border-radius:50px"></div></div><div style="width:100%;height:1px;background-color:#f0f4f5;margin-top:2px"></div></div><div class="twitter-content"><hr></div></div></div><div class="QQ" id="App_QQ" style="background-color:#eff3ff;width:100%;height:100%;box-sizing:border-box;position:relative"><div id="QQ_home_page"><div style="width:100%;height:30px"></div><div style="display:flex;align-items:center;gap:5px;width:100%;box-sizing:border-box"><div class="user_avatar" style="width:35px;height:35px;border-radius:50%;margin-left:10px"></div><div style="display:flex;align-items:start;justify-content:center;flex-direction:column;margin-left:3px;gap:3px"><span style="font-size:15px" id="QQ_home_UserName"><strong>{{user}}</strong></span><div style="display:flex;align-items:center;height:10px"><svg viewBox="0 0 1024 1024" width="13" height="13" style="margin-right:2px"><path d="M957.539 464.339c-26.14-245.993-246.771-424.08-492.787-397.772C218.735 92.873 40.474 313.62 66.635 559.634 92.774 805.631 313.406 983.717 559.443 957.41c245.997-26.306 424.258-247.075 398.096-493.071z m-298.25-135.757c39.341 0 71.233 31.921 71.233 71.301 0 39.379-31.891 71.3-71.233 71.3-39.338 0-71.229-31.921-71.229-71.3 0-39.38 31.892-71.301 71.229-71.301z m-294.447 0c39.34 0 71.23 31.921 71.23 71.301 0 39.379-31.89 71.3-71.23 71.3s-71.228-31.921-71.228-71.3c0-39.38 31.887-71.301 71.228-71.301z m366.815 304.963c-21.735 96.663-119.97 163.563-219.591 163.563-103.222 0-200.969-68.713-220.487-167.453 0-26.686 20.351-36.26 32.887-36.26h370.156c9.115 0.017 43.978 3.176 37.035 40.15z" fill="#fbba13" p-id="5845"></path></svg> <span style="font-size:11px">Q我吧</span> <svg viewBox="0 0 1024 1024" width="10" height="10" style="margin-top:1px"><path d="M342.528 916.48c19.456 0 38.4-7.168 53.248-21.504l338.944-327.68c14.848-14.336 23.552-34.304 23.552-55.296s-8.192-40.96-23.552-55.296l-338.944-327.68c-30.72-29.696-79.36-28.672-108.544 2.048-29.696 30.72-28.672 79.36 2.048 108.544l281.6 272.384-281.6 272.384c-30.72 29.696-31.232 78.336-2.048 108.544 15.36 15.872 35.328 23.552 55.296 23.552z" fill="#000000" p-id="7578"></path></svg></div></div><svg viewBox="0 0 1024 1024" version="1.1" width="20" height="20" style="margin-left:auto;margin-right:10px"><path d="M512 0a42.666667 42.666667 0 0 1 42.666667 42.666667v426.666666h426.666666a42.666667 42.666667 0 0 1 0 85.333334H554.666667v426.666666a42.666667 42.666667 0 0 1-85.333334 0V554.666667H42.666667a42.666667 42.666667 0 0 1 0-85.333334h426.666666V42.666667a42.666667 42.666667 0 0 1 42.666667-42.666667z" fill="#191919" p-id="4261"></path></svg></div><div style="width:100%;height:10px"></div><div id="QQ_home_chars"><div style="width:100%;height:30px;background-color:#eff3ff;margin-top:5px;border-radius:5px;display:flex;align-items:center;justify-content:center;gap:2px"><svg viewBox="0 0 1024 1024" width="16" height="16"><path d="M446.112323 177.545051c137.567677 0.219798 252.612525 104.59798 266.162424 241.493333 13.562828 136.895354-78.778182 261.818182-213.617777 289.008485-134.852525 27.203232-268.386263-52.156768-308.945455-183.608889s25.018182-272.252121 151.738182-325.779394A267.235556 267.235556 0 0 1 446.112323 177.545051m0-62.060607c-182.794343 0-330.989899 148.195556-330.989899 330.989899s148.195556 330.989899 330.989899 330.989899 330.989899-148.195556 330.989899-330.989899-148.195556-330.989899-330.989899-330.989899z m431.321212 793.341415a30.849293 30.849293 0 0 1-21.94101-9.102223l-157.220202-157.220202c-11.752727-12.179394-11.584646-31.534545 0.37495-43.50707 11.972525-11.972525 31.327677-12.140606 43.494141-0.37495l157.220202 157.220202a31.036768 31.036768 0 0 1 6.723232 33.810101 31.004444 31.004444 0 0 1-28.651313 19.174142z m0 0" fill="#919095"></path></svg> <span style="color:#919095">搜索</span></div><div style="width:100%;height:13px"></div></div></div><div id="QQ_space_page" style="display:none;height:100%"><div style="width:100%;height:35px"></div><div style="display:flex"><span style="font-size:16px;margin-left:10px">动态</span> <svg viewBox="0 0 1024 1024" width="20" height="20" style="margin-left:auto"><path d="M593.420488 922.099512c24.553022 0 44.456585 19.903563 44.456585 44.456586v11.988292c0 24.553022-19.903563 44.456585-44.456585 44.456586h-159.843903c-24.553022 0-44.456585-19.903563-44.456585-44.456586v-11.988292c0-24.553022 19.903563-44.456585 44.456585-44.456586h159.843903zM515.996098 0c24.276293 0 43.957073 19.68078 43.957073 43.957073l0.002997 54.191079C735.392843 121.808047 870.649756 272.137241 870.649756 454.056585V740.277073h102.4c24.553022 0 44.456585 19.903563 44.456585 44.456586v11.988292c0 24.553022-19.903563 44.456585-44.456585 44.456586H870.649756v1.998048H152.35122v-1.998048H52.948293C28.39527 841.178537 8.491707 821.274974 8.491707 796.721951v-11.988292C8.491707 760.180636 28.39527 740.277073 52.948293 740.277073H152.35122V454.056585c0-181.229019 134.231914-331.106654 308.696538-355.632702L461.049756 43.957073c0-24.276293 19.68078-43.957073 43.957073-43.957073h10.989269z m25.620979 197.047571h-60.233178c-126.178779 0-228.706654 101.264109-230.744664 226.958361l-0.029971 3.816273-0.000999 312.454868h521.783446V427.822205c0-127.454533-103.3211-230.774634-230.774634-230.774634z" fill="#000000" p-id="3355"></path></svg> <svg viewBox="0 0 1065 1024" width="20" height="20" style="margin-right:10px;margin-left:15px"><path d="M1002.234305 330.02867V693.97133c0 53.148789-28.346021 101.741967-73.902125 128.063272l-315.349481 182.224419c-46.062284 26.321305-102.248146 26.321305-148.31043 0l-315.349481-182.224419c-46.062284-26.321305-73.902126-75.420662-73.902126-128.063272V330.02867c0-53.148789 28.346021-101.741967 73.902126-128.063272l315.349481-182.224419c46.062284-26.321305 102.248146-26.321305 148.31043 0l315.349481 182.224419c45.556105 26.321305 73.902126 74.914483 73.902125 128.063272z m-76.939199 0c0-25.308947-13.666831-49.099357-35.432526-61.753831l-315.349481-182.224419c-22.271873-12.654474-49.099357-12.654474-71.37123 0l-315.349481 182.224419c-22.271873 12.654474-35.432526 36.444884-35.432526 61.753831V693.97133c0 25.308947 13.666831 49.099357 35.432526 61.753831l315.349481 182.224419c22.271873 12.654474 49.099357 12.654474 71.37123 0l315.349481-182.224419c22.271873-12.654474 35.432526-36.444884 35.432526-61.753831V330.02867zM362.424123 508.709837C362.424123 410.004943 442.400395 329.522491 541.611468 329.522491s179.187346 80.482452 179.187346 179.187346-80.482452 179.187346-179.187346 179.187345-179.187346-79.470094-179.187345-179.187345z m76.939199 0c0 56.692042 46.062284 102.248146 102.248146 102.248146s102.248146-46.062284 102.248146-102.248146-46.062284-102.248146-102.248146-102.248146-102.248146 46.568463-102.248146 102.248146z" fill="#000000" p-id="13806"></path></svg></div><div style="width:100%;height:1px;background-color:#e6e6e6;margin-bottom:10px;margin-top:10px"></div><div class="space_contents" id="space_contents" style="overflow-y:auto;width:100%;height:100%"><div style="margin-bottom:150px"></div></div></div><div class="QQ_bottom_nav"><div id="QQ_message_nav" style="display:flex;align-items:center;justify-content:center;flex-direction:column;gap:1px"><svg class="icon" viewBox="0 0 1024 1024" width="20" height="20" style="transform:scale(1.15);transform-origin:center;fill:#019aff" id="QQ_message_svg"><path d="M822.016 61.44H196.864A155.88352 155.88352 0 0 0 40.96 216.94976v426.2144a155.88352 155.88352 0 0 0 155.904 155.50976h9.6256a1.97632 1.97632 0 0 1 1.96608 1.9456l0.97792 84.992A72.832 72.832 0 0 0 322.72896 945.152l211.37408-141.09184a31.9744 31.9744 0 0 1 17.80736-5.39648h270.1056A155.88352 155.88352 0 0 0 977.92 643.16416V216.94976A155.88352 155.88352 0 0 0 822.016 61.44z m85.06368 581.72416a85.05344 85.05344 0 0 1-85.06368 84.84864H551.936a102.69184 102.69184 0 0 0-57.21088 17.33632l-211.39456 141.09184a1.9712 1.9712 0 0 1-3.072-1.6128l-0.97792-85.02272A72.97024 72.97024 0 0 0 206.4896 728.0128h-9.6256a85.05344 85.05344 0 0 1-85.06368-84.84864V216.94976A85.05344 85.05344 0 0 1 196.864 132.096h625.152a85.05344 85.05344 0 0 1 85.06368 84.84864v426.2144z"></path><path d="M311.08608 381.76768a54.17472 54.17472 0 1 0 54.31296 54.17472 54.24128 54.24128 0 0 0-54.31296-54.17472z m207.80032 0a54.17472 54.17472 0 1 0 54.31296 54.17472 54.24128 54.24128 0 0 0-54.31296-54.17472z m212.52096 0a54.17472 54.17472 0 1 0 54.31296 54.17472 54.24128 54.24128 0 0 0-54.31296-54.17472z"></path></svg> <span>消息</span></div><div id="QQ_people_nav" style="display:flex;align-items:center;justify-content:center;flex-direction:column;gap:1px"><svg viewBox="0 0 1024 1024" style="fill:#000000" width="20" height="20" id="QQ_people_svg"><path d="M620.744191 538.879184c82.736353-40.523949 140.308583-124.785028 140.308583-222.936465 0-137.367601-111.714338-249.080915-249.02668-249.080915-137.367601 0-249.080915 111.713314-249.080915 249.080915 0 98.151437 57.57223 182.412516 140.363841 222.936465C235.330238 586.429153 111.796714 740.736565 111.796714 923.694503c0 18.464537 15.032368 33.443693 33.496905 33.443693 18.464537 0 33.497928-14.979156 33.497928-33.443693 0-183.774537 149.46001-333.343017 333.234547-333.343017 183.77556 0 333.234547 149.568481 333.234547 333.343017 0 18.464537 14.978133 33.443693 33.443693 33.443693 18.519796 0 33.496905-14.979156 33.496905-33.443693C912.20124 740.736565 788.668739 586.429153 620.744191 538.879184zM329.886801 315.942719c0-100.438527 81.70179-182.194552 182.140317-182.194552 100.384291 0 182.086082 81.756025 182.086082 182.194552 0 100.384291-81.702813 182.086082-182.086082 182.086082C411.587568 498.0288 329.886801 416.32701 329.886801 315.942719z"></path></svg> <span>联系人</span></div><div id="QQ_moment_nav" style="display:flex;align-items:center;justify-content:center;flex-direction:column;gap:1px;position:relative"><svg viewBox="0 0 1059 1024" width="20" height="20" id="QQ_moment_svg" style="fill:#000000"><path d="M206.566465 1008.20002c11.278404 7.517627 30.080982 15.035253 45.117545 15.035254 11.281023 0 26.324133-3.75554 37.605156-7.517627l240.646548-127.777391 240.646547 127.777391c11.278404 7.517627 22.558118 7.517627 37.602537 7.517627 15.037872 0 30.075744-3.75554 45.118854-15.035254 22.559427-15.030015 37.598609-45.097903 30.082292-71.402393l-45.124092-278.110282L1037.545084 459.503632c18.799959-18.790793 26.321514-48.85868 18.799959-75.163172-7.516317-26.311038-33.839141-45.103141-60.156726-48.85868L714.181074 290.382568 593.854527 42.338864C582.577432 16.027826 556.254609 0.99912 526.177555 0.99912c-30.080982 0-56.403806 15.028706-67.684829 41.339744l-120.32 248.043704L63.679182 331.723621c-26.316276 3.756849-52.640409 22.548951-60.161965 48.853443-7.516317 26.309729-3.758159 56.376307 18.801269 75.169719l199.285852 199.183713-45.120164 278.106353c-3.758159 30.065269 7.521555 60.133156 30.082291 75.163171zM63.679182 406.886793l274.492235-41.339744 48.878322-7.518936 22.559427-45.097903 116.568389-248.042394 120.318691 248.043703 22.562046 45.097903 48.88225 7.516317 274.485688 41.339744L789.383529 606.074435l-33.845688 33.822117 7.522865 45.103141 45.124092 278.103734-240.651785-127.777391-41.356767-26.311039-45.124093 22.555499-240.646547 131.532931 45.120164-278.103734 7.521555-45.103141-30.082292-33.823427L63.679182 406.888102z" p-id="37579"></path><path d="M526.176246 692.509463c-131.604951 0-176.725115-127.777391-176.725116-131.53424-3.763396-15.035253 3.758159-30.065269 18.796031-33.828665 15.044419-3.75554 30.080982 3.763396 33.845688 18.793411 0 3.762087 33.839141 93.953964 124.084706 93.953965 90.239018 0 124.078159-93.953964 124.078159-93.953965 3.763396-15.030015 18.801269-22.548951 33.845688-18.793411 15.036563 3.763396 22.558118 22.555499 18.79603 33.828665 0 3.756849-45.120164 131.53555-176.721186 131.535549z" p-id="37580"></path></svg> <span>动态</span><div class="new_tips" style="position:absolute;right:-8px;top:-8px;background-color:red;color:#fff;width:15px;height:15px;border-radius:50%;display:none;justify-content:center;align-items:center;font-size:12px">1</div></div></div><div id="QQ_friend_info" style="display:none;background-color:#fff;height:100%;width:100%;padding-top:50px;padding-left:10px"><span>自定义名称:</span><input name="" id="setcharname" placeholder="这里输入名称"><br><br><span>自定义头像:</span> <input type="file" id="QQ_head_input"><br><br><br><button onclick="Setfriendinfo(!1)">取消</button> <button onclick="Setfriendinfo(!0)">确定</button></div></div><div class="discord" id="App_discord" style="display:none;width:100%;height:100%"><div class="discord-homepage" style="width:100%;height:100%"><div class="discord-top"><svg t="1744127791511" class="discord-top-return" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3402" width="28" height="28"><path d="M440.070244 831.562927c11.813463 0 23.601951-4.49561 32.593171-13.486829a45.980098 45.980098 0 0 0 0-65.161366l-198.730927-198.730927 589.674146 0.499512c24.001561 0 43.507512-19.431024 43.40761-43.40761 0-24.101463-19.480976-43.63239-43.507512-43.63239l-592.221659-0.574439 201.378342-201.378341a45.980098 45.980098 0 0 0 0-65.161366 45.980098 45.980098 0 0 0-65.186342 0L131.29678 476.734439a45.955122 45.955122 0 0 0-13.53678 33.16761v0.574439c0 13.361951 5.994146 25.300293 15.409951 33.292488l274.307122 274.332097c8.99122 8.966244 20.804683 13.461854 32.593171 13.461854z" p-id="3403" fill="#505058"></path></svg> <svg aria-hidden="true" width="20" height="20" fill="none" viewBox="0 0 24 24" style="margin-left:15px;margin-right:10px"><path fill="#313237" fill-rule="evenodd" d="M18.09 1.63c.4-.7 1.43-.7 1.82 0l3.96 6.9c.38.66-.12 1.47-.91 1.47h-7.92c-.79 0-1.3-.81-.91-1.48l3.96-6.9Zm.46 1.87h.9c.3 0 .52.26.5.55l-.22 2.02c-.01.16-.17.26-.33.23a1.92 1.92 0 0 0-.8 0c-.16.03-.32-.07-.33-.23l-.21-2.02a.5.5 0 0 1 .5-.55ZM19 9a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z" clip-rule="evenodd" class=""></path><path fill="#313237" d="M14.8 3.34a.48.48 0 0 0-.24-.69A9.94 9.94 0 0 0 11 2c-4.97 0-9 3.58-9 8 0 1.5.47 2.91 1.28 **********.12.49-.06.67l-1.51 1.51A1 1 0 0 0 2.4 18h5.1a.5.5 0 0 0 .49-.5c0-2.86 1.62-5.3 3.97-6.56.28-.15.38-.51.25-.8a2.86 2.86 0 0 1 .18-2.61l2.4-4.19ZM18.91 12.98a5.45 5.45 0 0 1 2.18 6.2c-.1.33-.09.68.1.96l.83 1.32a1 1 0 0 1-.84 1.54h-5.5A5.6 5.6 0 0 1 10 17.5a5.6 5.6 0 0 1 5.68-5.5c1.2 0 2.32.36 3.23.98Z" class=""></path></svg><div class="discord-top-title"><strong>类脑ΟΔΥΣΣΕΙΑ</strong></div><div style="display:flex;align-items:center;justify-content:center;background-color:#ebebeb;border-radius:50%;height:32px;width:32px;margin-left:auto;margin-right:10px"><svg viewBox="0 0 1024 1024" width="16" height="16"><path d="M991.418182 972.8L791.272727 772.654545c79.127273-83.781818 130.327273-195.490909 130.327273-316.50909 0-251.345455-200.145455-451.490909-446.836364-451.49091C232.727273 0 32.581818 204.8 32.581818 451.490909s200.145455 451.490909 446.836364 451.490909c97.745455 0 190.836364-32.581818 265.309091-88.436363l200.145454 204.8 46.545455-46.545455zM102.4 451.490909c0-209.454545 167.563636-381.672727 377.018182-381.672727s377.018182 172.218182 377.018182 381.672727-172.218182 386.327273-381.672728 386.327273c-204.8 0-372.363636-172.218182-372.363636-386.327273z" fill="#55565c" p-id="12001"></path></svg></div></div><div style="width:100%;height:1px;background-color:#dcdcdc"></div><div style="width:100%;height:1px;background-color:#d8d9db"></div><div class="discord-top-button"><div class="sortbutton"><svg aria-hidden="true" role="img" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24"><path fill="#515256" d="M16.3 21.7a1 1 0 0 0 1.4 0l4-4a1 1 0 0 0-1.4-1.4L18 18.58V3a1 1 0 1 0-2 0v15.59l-2.3-2.3a1 1 0 0 0-1.4 1.42l4 4ZM6.3 2.3a1 1 0 0 1 1.4 0l4 4a1 1 0 0 1-1.4 1.4L8 5.42V21a1 1 0 1 1-2 0V5.41l-2.3 2.3a1 1 0 0 1-1.4-1.42l4-4Z" class=""></path></svg><div style="font-size:14px;color:#515256">排序 &amp; 查看</div><svg aria-hidden="true" role="img" xmlns="http://www.w3.org/2000/svg" width="20" fill="none" viewBox="0 0 24 24"><path fill="#515256" d="M5.3 9.3a1 1 0 0 1 1.4 0l5.3 5.29 5.3-5.3a1 1 0 1 1 1.4 1.42l-6 6a1 1 0 0 1-1.4 0l-6-6a1 1 0 0 1 0-1.42Z" class=""></path></svg></div><div class="tagbutton"><div style="font-size:14px;color:#515256">标签</div><svg aria-hidden="true" role="img" xmlns="http://www.w3.org/2000/svg" width="20" fill="none" viewBox="0 0 24 24"><path fill="#515256" d="M5.3 9.3a1 1 0 0 1 1.4 0l5.3 5.29 5.3-5.3a1 1 0 1 1 1.4 1.42l-6 6a1 1 0 0 1-1.4 0l-6-6a1 1 0 0 1 0-1.42Z" class=""></path></svg></div></div><div style="width:100%;height:1px;background-color:#dcdcdc"></div><div style="width:100%;height:1px;background-color:#d8d9db"></div><div class="discord-cardlist"><div class="discord-cardget"><span>查看帖子</span></div></div></div><div class="discord-thread-list" style="width:100%;height:100%;display:none"></div></div></div></div><div class="top" style="display:flex;align-items:center;justify-content:center"><svg t="1735485675807" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9713" width="16" height="16"><path d="M716.8 805.823147 716.8 384.709973c0-95.68256-77.544107-173.2608-173.216427-173.2608L56.664747 211.449173c-3.754667 0-6.795947 3.024213-6.795947 6.741333L49.8688 805.819733c0 3.71712 3.04128 6.72768 6.795947 6.72768l130.061653 0c3.754667 0 6.792533-3.027627 6.792533-6.765227L193.518933 344.562347c0-3.744427 3.04128-6.782293 6.795947-6.782293l279.68512 0c52.52096 0 95.112533 42.571093 95.112533 95.09888l0 372.8896c0 3.741013 3.037867 6.761813 6.772053 6.761813l128.146773 0c3.72736 0 6.772053-3.017387 6.772053-6.72768M454.15424 805.778773c0 3.744427-3.01056 6.76864-6.76864 6.76864L317.354667 812.547413c-3.754667 0-6.795947-3.027627-6.795947-6.76864L310.55872 452.348587c0-3.741013 3.04128-6.77888 6.795947-6.77888l130.030933 0c3.75808 0 6.76864 3.037867 6.76864 6.77888l0 353.447253L454.15424 805.778773zM974.134613 805.778773c0 3.744427-3.044693 6.76864-6.79936 6.76864l-130.000213 0c-3.764907 0-6.826667-3.027627-6.826667-6.76864L830.508373 218.251947c0-3.75808 3.06176-6.792533 6.826667-6.792533l130.000213 0c3.75808 0 6.79936 3.034453 6.79936 6.792533L974.134613 805.778773z" fill="#ffffff" p-id="9714"></path></svg></div></div><script></script><script>var __webpack_exports__ = {};

;// ./src/界面/chat/chat_page.html
// Module
var code = "<div data-name=\"${name}\" class=\"QQ_chat_page\" style=\"width:100%;height:100%;padding-top:0\"> <div style=\"padding-top:10px;backdrop-filter:blur(1px);background-color:rgb(255,255,255,.1)\"> <div style=\"width:100%;height:20px;display:grid;top:0;left:0;grid-template-columns:auto 1fr auto;align-items:center;margin-top:20px\"> <svg class=\"QQ-close-btn\" viewBox=\"0 0 1024 1024\" style=\"height:18px;width:18px;margin-left:8px\"> <path d=\"M769.137778 153.372444L444.984889 512l324.152889 358.684444c36.408889 35.043556 36.408889 91.989333 0 126.976a95.744 95.744 0 0 1-131.868445 0l-377.571555-417.678222c-1.536-1.365333-3.584-1.877333-5.063111-3.299555A87.438222 87.438222 0 0 1 227.555556 512c-0.341333-23.324444 8.533333-46.876444 27.079111-64.739556 1.479111-1.422222 3.527111-1.934222 5.063111-3.185777L637.269333 26.339556a95.744 95.744 0 0 1 131.868445 0c36.408889 35.043556 36.408889 91.932444 0 127.032888z\" fill=\"#666666\" p-id=\"1570\"></path> </svg> <div style=\"display:flex;align-items:center\"> <div class=\"new_tips\" style=\"background-color:gray;color:#fff;width:20px;height:20px;border-radius:50%;display:none;justify-content:center;align-items:center;font-size:12px\"> 1 </div> <span style=\"margin-left:8px;color:#000;font-size:16px\" id=\"QQ_chat_username\">${name}</span> </div> <svg id=\"QQ_chat_page_setting\" viewBox=\"0 0 1024 1024\" style=\"height:20px;width:20px;margin-right:14px\"> <path d=\"M901.632 896H122.368c-30.72 0-55.808-25.088-55.808-55.808v-1.536c0-30.72 25.088-55.808 55.808-55.808h779.776c30.72 0 55.808 25.088 55.808 55.808v1.536c-0.512 30.72-25.6 55.808-56.32 55.808zM901.632 568.32H122.368c-30.72 0-55.808-25.088-55.808-55.808v-1.536c0-30.72 25.088-55.808 55.808-55.808h779.776c30.72 0 55.808 25.088 55.808 55.808v1.536c-0.512 30.72-25.6 55.808-56.32 55.808zM901.632 240.64H122.368c-30.72 0-55.808-25.088-55.808-55.808v-1.536c0-30.72 25.088-55.808 55.808-55.808h779.776c30.72 0 55.808 25.088 55.808 55.808v1.536c-0.512 30.72-25.6 55.808-56.32 55.808z\" p-id=\"4301\" fill=\"#666666\"></path> </svg> </div> <div style=\"width:100%;height:.5px;background-color:#eee;margin-top:10px;border-top:1px solid #9a9a9a\"></div> </div> <div class=\"input-container\"> <div style=\"display:flex;align-items:center;width:100%\"> <svg viewBox=\"0 0 1024 1024\" width=\"25\" height=\"25\" style=\"margin-left:10px\"> <path d=\"M512 62a184.09090869 184.09090869 0 0 1 184.09090869 184.09090869v204.54545478a184.09090869 184.09090869 0 1 1-368.18181738 1e-8v-204.54545479A184.09090869 184.09090869 0 0 1 512 62z m0 65.45454521a118.63636348 118.63636348 0 0 0-118.63636348 118.63636348v204.54545479a118.63636348 118.63636348 0 1 0 237.27272695 0v-204.54545479A118.63636348 118.63636348 0 0 0 512 127.45454521z\" p-id=\"6838\"></path> <path d=\"M192.90909131 471.09090869a319.09090869 319.09090869 0 0 0 638.18181738 0 32.72727305 32.72727305 0 1 0-65.45454521 0 253.63636348 253.63636348 0 0 1-507.27272695 0 32.72727305 32.72727305 0 1 0-65.45454522 0z\" p-id=\"6839\"></path> <path d=\"M479.27272695 757.45454521v131.85a32.72727305 32.72727305 0 1 0 65.4545461 0V757.45454521a32.72727305 32.72727305 0 1 0-65.4545461 0z\" p-id=\"6840\"></path> <path d=\"M409.72727305 953.81818174h206.87727216a32.72727305 32.72727305 0 1 0 0-65.45454522H409.72727305a32.72727305 32.72727305 0 1 0 0 65.45454522z\" p-id=\"6841\"></path> </svg> <div style=\"flex-grow:1;margin-left:5px;margin-right:2%\"> <input class=\"userInput\" type=\"text\" name=\"\" style=\"box-sizing:border-box;background-color:transparent\"/> </div> <div style=\"margin-right:7px\" id=\"QQ_chat_send-btn\"> <button style=\"background-color:#fff;border-radius:30px;height:31px;display:block;width:3.5rem;background-color:transparent;border:1px solid rgb(0,0,0,.5)\"> 发送 </button> </div> </div> </div> <div class=\"msgcontent\" style=\"padding-top:15px;padding-bottom:0\"></div> </div>";
// Exports
/* harmony default export */ var chat_page = (code);
;// ./src/界面/chat/chat_page_setting.html
// Module
var chat_page_setting_code = "<div class=\"chat-setting-popup\"> <div class=\"chat-setting-header\"> <span>聊天设置</span> <svg class=\"close-setting-btn\" viewBox=\"0 0 1024 1024\" width=\"20\" height=\"20\"> <path d=\"M512 421.490332 331.092592 240.582924C307.952518 217.442849 270.568889 217.442849 247.428814 240.582924 224.288739 263.722999 224.288739 301.106628 247.428814 324.246702L428.336222 505.154112 247.428814 686.061521C224.288739 709.201596 224.288739 746.585225 247.428814 769.7253 270.568889 792.865374 307.952518 792.865374 331.092592 769.7253L512 588.817891 692.907408 769.7253C716.047482 792.865374 753.431111 792.865374 776.571186 769.7253 799.711261 746.585225 799.711261 709.201596 776.571186 686.061521L595.663778 505.154112 776.571186 324.246702C799.711261 301.106628 799.711261 263.722999 776.571186 240.582924 753.431111 217.442849 716.047482 217.442849 692.907408 240.582924L512 421.490332Z\" fill=\"#666666\"></path> </svg> </div> <div class=\"chat-setting-content\"> <div class=\"setting-item\"> <span>气泡颜色</span> <div style=\"flex:1\"> <input style=\"width:100%\" type=\"text\" id=\"bubble-color-input\" placeholder=\"颜色值\"/> </div> <input type=\"color\" id=\"bubble-color\" class=\"color-picker\"/> </div> <div class=\"setting-item\"> <span>字体颜色</span> <div style=\"flex:1\"> <input style=\"width:100%\" type=\"text\" id=\"text-color-input\" placeholder=\"颜色值\"/> </div> <input type=\"color\" id=\"text-color\" class=\"color-picker\"/> </div> <div class=\"setting-item\"> <span>聊天壁纸</span> <input type=\"text\" id=\"chat-bg\" placeholder=\"输入图片URL\"/> </div> <div class=\"preview\" style=\"margin:0 auto\"> <div class=\"QQ_chat_msgdiv\" id=\"chat-setting-preview\" data-name=\"${username}\" style=\"margin:0 auto\"> <span>这是预览文本</span> </div> </div> </div> <div class=\"chat-setting-footer\"> <button id=\"randomcolor-setting-btn\" style=\"background-color:#919bec;color:#fff\">随机</button> <button id=\"save-setting-btn\">保存</button> <button id=\"cancel-setting-btn\">取消</button> </div> </div>";
// Exports
/* harmony default export */ var chat_page_setting = (chat_page_setting_code);
;// ./src/界面/chat/chat_list_item.html
// Module
var chat_list_item_code = "<div data-name=\"${name}\" class=\"QQ_home_usermsg\"> <div class=\"QQ_home_head\" style=\"background-image:url('${head}')\"></div> <div style=\"width:100%;display:grid;grid-template-columns:1fr auto;grid-template-rows:1fr 1fr;row-gap:4px\"> <span class=\"QQ_home_name\"><strong>${name}</strong></span> <span class=\"QQ_home_lasttime\" style=\"color:#626367;justify-self:end\"></span> <span class=\"QQ_home_lastmsg\" style=\"color:#626367;white-space:nowrap;overflow:hidden;text-overflow:ellipsis\">...</span> <div class=\"QQ_home_usermsg_new\" style=\"display:none\"></div> </div> </div>";
// Exports
/* harmony default export */ var chat_list_item = (chat_list_item_code);
;// ./src/界面/chat/chat_user_message.html
// Module
var chat_user_message_code = "<div class=\"QQ_chat_mymsg\"> <div style=\"width:auto;height:auto;margin-top:4px\"> ${content} </div> <div class=\"user_avatar Chat_MyHead\"></div> </div>";
// Exports
/* harmony default export */ var chat_user_message = (chat_user_message_code);
;// ./src/界面/chat/chat_char_msg.html
// Module
var chat_char_msg_code = " <% if (isgroup) { %> <div class=\"QQ_chat_charmsg\"> <div data-name=\"${name}\" class=\"QQ_chat_head head\"></div> <div style=\"width:auto;height:auto;margin-top:4px\"> <span class=\"name\">${name}</span><br/> ${content} </div> </div> <% } else { %> <div class=\"QQ_chat_charmsg\"> <div data-name=\"${name}\" class=\"QQ_chat_head head\"></div> <div style=\"width:auto;height:auto;margin-top:4px\">${content}</div> </div> <% } %>";
// Exports
/* harmony default export */ var chat_char_msg = (chat_char_msg_code);
;// ./src/界面/chat/chat_normal_message.html
// Module
var chat_normal_message_code = "<div class=\"QQ_chat_msgdiv\" data-name=\"${username}\" <% if (isgroup) { %> style=\"margin-top:6px\" <% } %>> <span>${message}</span> </div>";
// Exports
/* harmony default export */ var chat_normal_message = (chat_normal_message_code);
;// ./src/界面/chat/chat_emoji_message.html
// Module
var chat_emoji_message_code = "<div class=\"QQ_chat_msgdiv\" data-name=\"${username}\" <% if (isgroup) { %> style=\"margin-top:6px\" <% } %>> <div style=\"width:100%;background-color:#fff\"> <img class=\"msgimg\" loading=\"lazy\" src=\"<%= emojiUrl %>\" alt=\"加载失败\"/> </div> <% if (additionalText) { %> <span style=\"margin-top:5px;display:block\"> <%= additionalText %> </span> <% } %> </div>";
// Exports
/* harmony default export */ var chat_emoji_message = (chat_emoji_message_code);
;// ./src/界面/chat/chat_fakeimg_message.html
// Module
var chat_fakeimg_message_code = "<div class=\"QQ_chat_msgdiv\" data-name=\"${username}\" <% if (isgroup) { %> style=\"margin-top:6px\" <% } %>> <div class=\"QQ_chat_fakeimg\" style=\"text-align:center\"> <div>${content}</div> </div> ${additionalText} </div>";
// Exports
/* harmony default export */ var chat_fakeimg_message = (chat_fakeimg_message_code);
;// ./src/界面/chat/chat_music_message.html
// Module
var chat_music_message_code = "<div class=\"QQ_chat_music\"> <div class=\"music-container\" style=\"display:grid;grid-template-columns:1fr 64px;width:100%\"> <div style=\"display:flex;flex-direction:column;margin-top:8px;overflow:hidden;max-width:100%\"> <div class=\"hide-title music-name\" style=\"font-size:1.2rem\"><strong>${musicname}</strong></div> <div class=\"hide-title music-author\" style=\"color:#8d8c8d;margin-left:3px;margin-top:10px;font-size:1rem\">${musicauthor}</div> </div> <div style=\"margin-left:auto;position:relative;height:64px;width:64px\"> <div class=\"icon-container\" style=\"width:64px;width:64px;position:relative\"> <svg style=\"position:absolute;left:-3px;top:-5px\" class=\"icon\" viewBox=\"0 0 1024 1024\" width=\"72\" height=\"72\"><path d=\"M773.9392 301.8752m-200.0384 0a200.0384 200.0384 0 1 0 400.0768 0 200.0384 200.0384 0 1 0-400.0768 0Z\" fill=\"#d81e06\" p-id=\"51386\"></path><path d=\"M924.4672 706.2528a24.32 24.32 0 0 1-24.2688 24.2688h-145.7664a24.3712 24.3712 0 0 0-24.2688 24.32 24.2688 24.2688 0 0 0 24.2688 24.2688h48.5888a24.32 24.32 0 0 1 24.2688 24.32 24.32 24.32 0 0 1-24.2688 24.2688h-64.512A388.7616 388.7616 0 0 1 122.88 390.4512h-48.5888a24.32 24.32 0 0 1 0-48.5888h97.28a24.2688 24.2688 0 0 0 6.8096-47.5648l0.768-0.9728H122.88a24.32 24.32 0 1 1 0-48.5888h101.632a388.7616 388.7616 0 0 1 619.52 437.248h56.32a24.32 24.32 0 0 1 24.1152 24.2688z\" fill=\"#2c2c2c\" p-id=\"51387\"></path><path d=\"M565.8112 478.8736a3.84 3.84 0 0 0-4.5568 4.608c3.1744 14.7456 7.8848 40.96 9.984 60.0576 3.6352 32.2048-28.5696 62.3104-53.6064 70.7584-39.424 13.568-84.8896-18.688-95.1296-56.32-11.776-43.9808 12.6464-97.5872 53.2992-115.0464 4.5056-2.0992 9.0112-3.8912 13.568-5.9904 9.3184-4.2496 4.8128-11.1616 3.2768-17.5104-4.1984-17.4592-6.6048-34.9184 2.4064-51.2 13.568-24.4224 48.7936-43.6736 76.1856-31.9488a276.48 276.48 0 0 1 34.6624 16.5888 25.1904 25.1904 0 0 1 10.5472 28.2624c-5.7344 15.36-20.7872 18.9952-36.7616 9.6768a196.1984 196.1984 0 0 0-17.4592-9.9328 16.2304 16.2304 0 0 0-23.808 15.36 150.3744 150.3744 0 0 0 1.9456 18.0224 18.4832 18.4832 0 0 0 13.568 14.8992 200.3456 200.3456 0 0 1 29.3888 8.9088c38.8096 17.152 66.56 45.1584 79.5136 87.04 24.064 77.6704-21.0944 155.0848-87.9616 184.6272a182.016 182.016 0 0 1-116.8384 13.2096 185.6 185.6 0 0 1-126.464-104.8064c-18.0736-40.96-22.8864-82.7904-8.7552-127.0784 18.9952-59.2384 56.9344-99.6352 114.7392-121.9072a23.4496 23.4496 0 0 1 28.0064 9.728 21.1456 21.1456 0 0 1-6.0416 29.5424 259.8912 259.8912 0 0 1-25.2928 13.568c-31.8976 16.2304-51.7632 42.752-64.4096 75.8784-24.7296 64.4096 9.3184 139.1104 65.6384 168.3456 51.7632 27.0848 122.5728 16.2304 160.8192-28.3136A106.2912 106.2912 0 0 0 619.52 542.72c-5.12-25.6-34.5088-59.2896-53.7088-63.8464z m-53.4016 5.9904a6.144 6.144 0 0 0-7.9872-4.5056c-33.28 11.4688-47.5136 47.2576-31.9488 76.4416a28.0064 28.0064 0 0 0 30.1056 13.824c14.1824-4.1984 24.1152-14.4384 22.016-27.6992-2.9696-19.5072-7.936-38.8096-12.1856-58.0608z\" fill=\"#d81e06\" p-id=\"51388\"></path></svg> <div class=\"music-img\" style=\"width:64px;height:64px;position:absolute;left:0;top:0;background-size:cover;z-index:1;background-image:url('https://y.qq.com/music/photo_new/T002R800x800M000004Z85XP1c25b7.jpg');display:none\"> </div> </div> <div class=\"music-play-button\"> <svg class=\"icon-music-play\" viewBox=\"0 0 1024 1024\" width=\"24\" height=\"24\"> <path d=\"M902.420317 544.833016l-585.142857-357.587302v715.174603l585.142857-357.587301z\" p-id=\"42990\" fill=\"#ffffff\"></path> </svg> <svg class=\"icon-music-stop\" t=\"1743245033745\" class=\"icon\" viewBox=\"0 0 1024 1024\" width=\"19\" height=\"19\" style=\"display:none\"><path d=\"M290.133333 128h-39.808C206.336 128 170.666667 178.944 170.666667 241.792v540.416C170.666667 845.056 206.336 896 250.325333 896H290.133333c43.946667 0 79.658667-50.944 79.658667-113.792V241.792C369.792 178.944 334.08 128 290.133333 128zM773.674667 128H733.866667c-43.989333 0-79.658667 50.944-79.658667 113.792v540.416c0 62.848 35.669333 113.792 79.658667 113.792h39.808C817.664 896 853.333333 845.056 853.333333 782.208V241.792C853.333333 178.944 817.664 128 773.674667 128z\" fill=\"#ffffff\" p-id=\"43402\"></path></svg> </div> </div> </div> <div style=\"width:100%;height:2px;background-color:#efefef;margin-bottom:5px;margin-top:5px\"></div> <div style=\"display:flex;align-items:center;gap:5px\"> <svg viewBox=\"0 0 1024 1024\" style=\"width:.9rem;height:.9rem\"> <path d=\"M0 0m184.32 0l655.36 0q184.32 0 184.32 184.32l0 655.36q0 184.32-184.32 184.32l-655.36 0q-184.32 0-184.32-184.32l0-655.36q0-184.32 184.32-184.32Z\" fill=\"#EA3E3C\" p-id=\"1500\"></path> <path d=\"M527.616 849.43872a373.6064 373.6064 0 0 1-162.54976-39.00416c-112.36352-55.16288-180.00896-176.29184-172.55424-308.67456 7.41376-130.34496 85.10464-237.4656 202.752-279.552a35.85024 35.85024 0 0 1 24.15616 67.51232c-107.66336 38.49216-150.81472 136.86784-155.29984 216.13568-5.86752 103.51616 46.08 197.79584 132.34176 240.13824 124.69248 60.30336 216.91392 22.35392 260.82304-5.64224 59.8016-38.16448 97.86368-100.01408 96.95232-157.55264-1.024-63.72352-24.064-120.99584-63.27296-157.14304a145.408 145.408 0 0 0-65.5872-35.28704q2.82624 9.76896 5.64224 19.32288c13.38368 45.63968 24.94464 85.05344 25.6 114.40128a134.26688 134.26688 0 0 1-37.69344 97.76128 139.1104 139.1104 0 0 1-100.6592 40.45824 140.10368 140.10368 0 0 1-100.47488-42.24 169.12384 169.12384 0 0 1-46.2848-122.76736c1.19808-85.12512 80.11776-153.28256 162.816-175.104a324.80256 324.80256 0 0 1-6.71744-67.05152 92.0576 92.0576 0 0 1 69.18144-91.81184c46.21312-12.53376 104.448 5.19168 124.66176 37.888a35.84 35.84 0 0 1-11.70432 49.31584 35.84 35.84 0 0 1-49.26464-11.65312 62.34112 62.34112 0 0 0-48.45568-5.21216c-4.32128 1.71008-12.35968 4.90496-12.76928 23.10144a270.87872 270.87872 0 0 0 6.73792 58.51136 217.4976 217.4976 0 0 1 133.56032 57.6512c53.57568 49.38752 85.0432 125.46048 86.35392 208.71168 1.29024 81.85856-49.7664 167.86432-130.048 219.136a310.14912 310.14912 0 0 1-168.2432 48.65024z m23.6544-457.55392c-56.77056 15.6672-107.4688 63.03744-108.07296 106.42432a98.304 98.304 0 0 0 25.6512 71.43424 68.0448 68.0448 0 0 0 49.36704 20.87936 67.24608 67.24608 0 0 0 49.44896-18.944 63.19104 63.19104 0 0 0 17.23392-46.08c-0.4096-19.79392-11.7248-58.368-22.67136-95.6928-3.61472-12.42112-7.35232-25.14944-10.9568-38.02112z\" fill=\"#FFFFFF\" p-id=\"1501\"></path> </svg> <div style=\"font-size:.9rem;color:#8d8c8c\">网易云音乐</div> </div> </div>";
// Exports
/* harmony default export */ var chat_music_message = (chat_music_message_code);
;// ./src/界面/chat/chat_transfer_message.html
// Module
var chat_transfer_message_code = "<div class=\"give_money\"> <div style=\"background-color:#4396f7;height:80px;width:100%;color:#fff;padding-top:20px;padding-left:12px;display:flex;flex-direction:column;gap:9px;position:relative\"> <span><%= amount %></span> <span>已转入你的余额</span> <div style=\"border-radius:50%;background-color:#9fccf1;width:50px;height:50px;position:absolute;right:10px;display:flex;align-items:center;justify-content:center;top:15px\"> <svg t=\"1738258225266\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"4449\" width=\"33\" height=\"33\"> <path d=\"M954.014 510.695c-31.51 0.303-185.995-88.917-190.874-93.568-11.734-11.188-7.3-25.115 4.673-27.02s79.203-9.898 79.203-9.898c-41.318-110.437-133.518-198.636-254.527-227.223-193.138-45.643-392.87 83.827-437.013 281.424-0.38 1.685-3.047 21.83-19.045 21.191-10.83-0.432-46.309-14.539-59.136-20.556-14.644-6.868-18.601-5.094-14.16-27.201C113.37 156.643 370.632-12.653 617.554 45.272c204.919 48.063 347.27 225.878 362.91 428.477 2.619 27.151-11.203 36.797-26.451 36.944z m-322.516 55.059c20.284 0 36.729 16.427 36.729 36.727 0 20.265-16.445 36.729-36.729 36.729h-73.456v45.909c0 25.359-20.551 45.911-45.911 45.911-25.358 0-45.91-20.551-45.91-45.911V639.21h-73.456c-20.284 0-36.727-16.465-36.727-36.729 0-20.302 16.444-36.727 36.727-36.727h73.456v-36.73h-73.456c-20.284 0-36.727-16.461-36.727-36.726 0-20.3 16.444-36.729 36.727-36.729h73.456v-2.512l-81.508-81.491c-14.445-14.455-14.445-37.875 0-52.313s37.858-14.437 52.304 0l74.497 74.478 74.786-74.784c14.444-14.419 37.859-14.419 52.303 0 14.446 14.454 14.446 37.877 0 52.313l-80.558 80.559v3.749h73.456c20.284 0 36.729 16.429 36.729 36.729 0 20.267-16.445 36.726-36.729 36.726h-73.456v36.73h73.456z m-368.471 51.828c16.112 10.302 12.229 30.994-5.477 32.836s-73.153 6.725-73.153 6.725C228.353 760.157 317.44 841.379 432.63 868.585c193.137 45.623 390.651-77.582 434.806-275.179 0.906-4.054 0.877-3.418 2.457-12.122s4.139-21.945 21.142-16.052 58.475 18.085 71.394 21.539 11.465 8.548 9.846 20.553c-0.493 3.651-1.238 6.994-2.017 10.509-57.81 256.921-309.451 417.196-562.049 357.924-204.677-48.01-346.947-225.429-362.865-427.724-2.159-34.762 22.4-41.857 47.505-28.577 19.75 10.447 154.064 87.822 170.176 98.124z\" p-id=\"4450\" fill=\"#4090ee\"></path> </svg> </div> </div> <div style=\"background-color:#fff;width:100%;height:30px;color:#959598;font-size:13px;padding-left:10px;padding-top:8px\"> QQ转账 </div> </div> ";
// Exports
/* harmony default export */ var chat_transfer_message = (chat_transfer_message_code);
;// ./src/界面/chat/chat_char_voice_message.html
// Module
var chat_char_voice_message_code = "<div class=\"QQ_chat_msgdiv\" data-name=\"${username}\" <% if (isgroup) { %> style=\"margin-top:6px\" <% } %>> <div class=\"QQ_chat_voice\" style=\"width:100%;display:flex;gap:5px;align-items:center\"> <span style=\"margin-left:2px\">${time}\"</span> <span style=\"display:block\"> <svg style=\"transform:rotate(90deg);display:flex;align-items:center\" t=\"1742985508017\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"38914\" width=\"20\" height=\"20\"> <path d=\"M99.931429 457.782857c8.137143 8.137143 20.132571 7.716571 27.849142-0.420571 101.156571-107.574857 234.861714-164.150857 384.438858-164.150857 150.418286 0 284.562286 56.996571 385.28 164.571428 7.277714 7.296 18.870857 7.296 26.569142-0.859428l57.014858-56.996572c6.838857-7.277714 6.838857-16.713143 1.28-23.570286-96.859429-119.149714-279.003429-206.994286-470.144-206.994285S138.934857 257.206857 42.057143 376.356571c-5.997714 6.857143-5.577143 16.274286 1.28 23.588572z m171.428571 171.867429c8.576 8.996571 19.712 8.137143 27.849143-1.28 49.718857-55.296 130.285714-95.158857 213.010286-94.299429 83.565714-0.859429 163.712 40.283429 213.851428 95.579429 8.137143 8.557714 18.852571 8.557714 27.008-0.438857l63.853714-62.994286c6.857143-6.857143 7.716571-15.853714 1.28-23.149714-62.134857-76.708571-177.426286-133.284571-305.993142-133.284572-128.585143 0-243.858286 56.996571-306.011429 133.302857-6.418286 7.277714-5.558857 15.835429 1.28 23.131429z m240.859429 224.987428c8.996571 0 17.133714-4.717714 32.987428-20.132571l100.297143-96.438857c6.418286-5.997714 7.716571-15.414857 2.139429-22.710857-27.008-34.706286-77.568-64.713143-135.424-64.713143-59.154286 0-110.573714 31.286857-137.142858 67.291428-3.858286 5.997714-2.56 14.134857 3.84 20.132572l100.297143 96.438857c15.853714 15.414857 24.009143 20.132571 33.005715 20.132571z\" p-id=\"38915\" fill=\"currentColor\"></path> </svg> </span> <span style=\"display:block\" class=\"totext\"> <svg style=\"margin-left:30px;display:flex;align-items:center\" viewBox=\"0 0 1024 1024\" width=\"24\" height=\"24\"> <path d=\"M999.611733 387.697778a37.831111 37.831111 0 0 0-48.355555 22.641778v0.512l-1.592889 5.233777c-58.88-241.208889-302.648889-388.892444-543.857778-330.012444A446.805333 446.805333 0 0 0 124.546844 295.253333a39.139556 39.139556 0 0 0 13.653334 53.077334 39.310222 39.310222 0 0 0 18.944 5.233777c13.084444 0 25.201778-6.826667 32.540444-18.375111a374.158222 374.158222 0 0 1 359.480889-183.978666 381.952 381.952 0 0 1 337.351111 334.279111 384.568889 384.568889 0 0 1-3.128889 115.086222v2.616889a38.855111 38.855111 0 0 0 21.560889 48.355555c19.456 6.826667 41.528889-3.185778 48.355556-22.641777l68.835555-192.284445a39.310222 39.310222 0 0 0-22.584889-48.924444z m-148.764444 348.956444a38.115556 38.115556 0 0 0-31.004445 16.839111 373.077333 373.077333 0 0 1-355.214222 157.639111 381.212444 381.212444 0 0 1-323.185778-306.346666 396.344889 396.344889 0 0 1 0-140.856889v-1.592889a39.139556 39.139556 0 0 0-22.072888-48.355556 38.058667 38.058667 0 0 0-48.355556 22.584889l-68.835556 191.317334a38.513778 38.513778 0 0 0 23.608889 48.867555c19.456 7.395556 40.96-3.128889 48.355556-22.584889v-3.697777c58.88 241.208889 302.705778 388.892444 543.914667 330.012444A448.739556 448.739556 0 0 0 881.851733 798.151111a38.684444 38.684444 0 0 0-8.362666-54.158222 31.857778 31.857778 0 0 0-22.641778-7.338667z\" p-id=\"41017\" fill=\"currentColor\"></path><path d=\"M310.687289 693.020444a50.517333 50.517333 0 0 0-33.166222 19.456 40.96 40.96 0 0 0 0 31.573334c1.592889 4.721778 3.697778 9.443556 6.257777 14.165333a26.794667 26.794667 0 0 0 13.198223 11.036445 30.833778 30.833778 0 0 0 12.060444 2.104888h5.290667c6.826667-1.080889 13.653333-2.616889 20.48-4.721777 15.758222-5.290667 32.028444-11.605333 48.924444-18.432 16.782222-6.826667 34.133333-15.758222 49.322667-23.608889 15.246222-7.907556 32.597333-17.863111 46.250667-26.282667 14.222222-8.419556 24.177778-15.758222 35.271111-24.177778 24.632889 19.456 50.915556 36.238222 78.791111 49.891556 33.621333 17.351111 68.835556 31.004444 105.073778 42.097778a56.32 56.32 0 0 0 20.48 3.640889 33.905778 33.905778 0 0 0 34.190222-26.282667 39.025778 39.025778 0 0 0-1.592889-35.726222 49.322667 49.322667 0 0 0-26.225778-18.375111 540.842667 540.842667 0 0 1-85.162667-33.621334 603.932444 603.932444 0 0 1-63.601777-38.912c20.48-23.096889 37.831111-48.355556 52.565333-75.662222a465.294222 465.294222 0 0 0 38.343111-98.304h49.436445a38.456889 38.456889 0 0 0 26.282666-12.060444 46.762667 46.762667 0 0 0 7.850667-26.282667 44.032 44.032 0 0 0-8.931556-28.899556 37.432889 37.432889 0 0 0-26.282666-11.036444H568.6784a116.167111 116.167111 0 0 0-9.500444-17.863111c-10.467556-12.629333-22.584889-25.770667-34.702223-38.912a47.786667 47.786667 0 0 0-27.818666-18.375111h-4.209778a36.636444 36.636444 0 0 0-24.177778 8.419555 35.896889 35.896889 0 0 0-18.375111 29.923556 51.2 51.2 0 0 0 16.270222 33.109333l6.314667 6.314667H315.352178a34.133333 34.133333 0 0 0-26.282667 12.629333 45.852444 45.852444 0 0 0-6.826667 26.282667c-0.512 10.467556 2.104889 20.48 7.907556 28.899555 6.826667 6.826667 16.270222 11.036444 26.282667 11.036445h47.274666c5.233778 15.758222 10.524444 32.028444 16.839111 47.786666a419.271111 419.271111 0 0 0 50.403556 94.094223c8.419556 11.036444 17.351111 22.584889 26.282667 33.109333a389.461333 389.461333 0 0 1-64.625778 39.936c-26.282667 12.629333-54.101333 23.096889-81.976889 32.028444z m131.299555-246.954666h140.344889c-6.826667 23.096889-16.839111 45.169778-28.387555 66.730666a325.290667 325.290667 0 0 1-39.936 55.751112 319.146667 319.146667 0 0 1-39.424-52.622223l-2.616889-4.209777a388.778667 388.778667 0 0 1-29.980445-65.649778z\" p-id=\"41018\" fill=\"currentColor\"></path></svg> </span> </div> <div class=\"voice2text\" style=\"display:none\"> <div style=\"width:100%;height:1px;background-color:#000;margin-top:10px;margin-bottom:8px\"></div> <span>${content}</span> </div> </div>";
// Exports
/* harmony default export */ var chat_char_voice_message = (chat_char_voice_message_code);
;// ./src/界面/chat/chat_my_voice_message.html
// Module
var chat_my_voice_message_code = "<div class=\"QQ_chat_msgdiv ${username}\" <% if (isgroup) { %> style=\"margin-top:6px\" <% } %>> <div class=\"QQ_chat_voice\" style=\"width:100%;display:flex;gap:5px;align-items:center\"> <span style=\"display:block\" class=\"totext\"> <svg style=\"margin-right:30px;display:flex;align-items:center\" viewBox=\"0 0 1024 1024\" width=\"24\" height=\"24\"> <path d=\"M999.611733 387.697778a37.831111 37.831111 0 0 0-48.355555 22.641778v0.512l-1.592889 5.233777c-58.88-241.208889-302.648889-388.892444-543.857778-330.012444A446.805333 446.805333 0 0 0 124.546844 295.253333a39.139556 39.139556 0 0 0 13.653334 53.077334 39.310222 39.310222 0 0 0 18.944 5.233777c13.084444 0 25.201778-6.826667 32.540444-18.375111a374.158222 374.158222 0 0 1 359.480889-183.978666 381.952 381.952 0 0 1 337.351111 334.279111 384.568889 384.568889 0 0 1-3.128889 115.086222v2.616889a38.855111 38.855111 0 0 0 21.560889 48.355555c19.456 6.826667 41.528889-3.185778 48.355556-22.641777l68.835555-192.284445a39.310222 39.310222 0 0 0-22.584889-48.924444z m-148.764444 348.956444a38.115556 38.115556 0 0 0-31.004445 16.839111 373.077333 373.077333 0 0 1-355.214222 157.639111 381.212444 381.212444 0 0 1-323.185778-306.346666 396.344889 396.344889 0 0 1 0-140.856889v-1.592889a39.139556 39.139556 0 0 0-22.072888-48.355556 38.058667 38.058667 0 0 0-48.355556 22.584889l-68.835556 191.317334a38.513778 38.513778 0 0 0 23.608889 48.867555c19.456 7.395556 40.96-3.128889 48.355556-22.584889v-3.697777c58.88 241.208889 302.705778 388.892444 543.914667 330.012444A448.739556 448.739556 0 0 0 881.851733 798.151111a38.684444 38.684444 0 0 0-8.362666-54.158222 31.857778 31.857778 0 0 0-22.641778-7.338667z\" p-id=\"41017\" fill=\"currentColor\"></path><path d=\"M310.687289 693.020444a50.517333 50.517333 0 0 0-33.166222 19.456 40.96 40.96 0 0 0 0 31.573334c1.592889 4.721778 3.697778 9.443556 6.257777 14.165333a26.794667 26.794667 0 0 0 13.198223 11.036445 30.833778 30.833778 0 0 0 12.060444 2.104888h5.290667c6.826667-1.080889 13.653333-2.616889 20.48-4.721777 15.758222-5.290667 32.028444-11.605333 48.924444-18.432 16.782222-6.826667 34.133333-15.758222 49.322667-23.608889 15.246222-7.907556 32.597333-17.863111 46.250667-26.282667 14.222222-8.419556 24.177778-15.758222 35.271111-24.177778 24.632889 19.456 50.915556 36.238222 78.791111 49.891556 33.621333 17.351111 68.835556 31.004444 105.073778 42.097778a56.32 56.32 0 0 0 20.48 3.640889 33.905778 33.905778 0 0 0 34.190222-26.282667 39.025778 39.025778 0 0 0-1.592889-35.726222 49.322667 49.322667 0 0 0-26.225778-18.375111 540.842667 540.842667 0 0 1-85.162667-33.621334 603.932444 603.932444 0 0 1-63.601777-38.912c20.48-23.096889 37.831111-48.355556 52.565333-75.662222a465.294222 465.294222 0 0 0 38.343111-98.304h49.436445a38.456889 38.456889 0 0 0 26.282666-12.060444 46.762667 46.762667 0 0 0 7.850667-26.282667 44.032 44.032 0 0 0-8.931556-28.899556 37.432889 37.432889 0 0 0-26.282666-11.036444H568.6784a116.167111 116.167111 0 0 0-9.500444-17.863111c-10.467556-12.629333-22.584889-25.770667-34.702223-38.912a47.786667 47.786667 0 0 0-27.818666-18.375111h-4.209778a36.636444 36.636444 0 0 0-24.177778 8.419555 35.896889 35.896889 0 0 0-18.375111 29.923556 51.2 51.2 0 0 0 16.270222 33.109333l6.314667 6.314667H315.352178a34.133333 34.133333 0 0 0-26.282667 12.629333 45.852444 45.852444 0 0 0-6.826667 26.282667c-0.512 10.467556 2.104889 20.48 7.907556 28.899555 6.826667 6.826667 16.270222 11.036444 26.282667 11.036445h47.274666c5.233778 15.758222 10.524444 32.028444 16.839111 47.786666a419.271111 419.271111 0 0 0 50.403556 94.094223c8.419556 11.036444 17.351111 22.584889 26.282667 33.109333a389.461333 389.461333 0 0 1-64.625778 39.936c-26.282667 12.629333-54.101333 23.096889-81.976889 32.028444z m131.299555-246.954666h140.344889c-6.826667 23.096889-16.839111 45.169778-28.387555 66.730666a325.290667 325.290667 0 0 1-39.936 55.751112 319.146667 319.146667 0 0 1-39.424-52.622223l-2.616889-4.209777a388.778667 388.778667 0 0 1-29.980445-65.649778z\" p-id=\"41018\" fill=\"currentColor\"></path></svg> </span> <div style=\"display:flex;align-items:center;gap:5px;margin-left:auto\"> <span style=\"display:block\"> <svg style=\"transform:rotate(-90deg);display:flex;align-items:center\" t=\"1742985508017\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"38914\" width=\"20\" height=\"20\"> <path d=\"M99.931429 457.782857c8.137143 8.137143 20.132571 7.716571 27.849142-0.420571 101.156571-107.574857 234.861714-164.150857 384.438858-164.150857 150.418286 0 284.562286 56.996571 385.28 164.571428 7.277714 7.296 18.870857 7.296 26.569142-0.859428l57.014858-56.996572c6.838857-7.277714 6.838857-16.713143 1.28-23.570286-96.859429-119.149714-279.003429-206.994286-470.144-206.994285S138.934857 257.206857 42.057143 376.356571c-5.997714 6.857143-5.577143 16.274286 1.28 23.588572z m171.428571 171.867429c8.576 8.996571 19.712 8.137143 27.849143-1.28 49.718857-55.296 130.285714-95.158857 213.010286-94.299429 83.565714-0.859429 163.712 40.283429 213.851428 95.579429 8.137143 8.557714 18.852571 8.557714 27.008-0.438857l63.853714-62.994286c6.857143-6.857143 7.716571-15.853714 1.28-23.149714-62.134857-76.708571-177.426286-133.284571-305.993142-133.284572-128.585143 0-243.858286 56.996571-306.011429 133.302857-6.418286 7.277714-5.558857 15.835429 1.28 23.131429z m240.859429 224.987428c8.996571 0 17.133714-4.717714 32.987428-20.132571l100.297143-96.438857c6.418286-5.997714 7.716571-15.414857 2.139429-22.710857-27.008-34.706286-77.568-64.713143-135.424-64.713143-59.154286 0-110.573714 31.286857-137.142858 67.291428-3.858286 5.997714-2.56 14.134857 3.84 20.132572l100.297143 96.438857c15.853714 15.414857 24.009143 20.132571 33.005715 20.132571z\" p-id=\"38915\" fill=\"currentColor\"></path> </svg> </span> <span>${time}\"</span> </div> </div> <div class=\"voice2text\" style=\"display:none\"> <div style=\"width:100%;height:1px;background-color:#000;margin-top:10px;margin-bottom:8px\"></div> <span>${content}</span> </div> </div>";
// Exports
/* harmony default export */ var chat_my_voice_message = (chat_my_voice_message_code);
;// ./src/界面/chat/chat_head.css?raw
var chat_headraw_namespaceObject = ".head[data-name='${name}'] {\r\n  background-image: url('${head}') !important;\r\n}\r\n";
;// ./src/界面/moment/moment_page.html
// Module
var moment_page_code = "<div class=\"user_moment_title\" style=\"margin-bottom:7px\"> <div class=\"QQ_home_head head\" data-name=\"${userName}\"></div> <div style=\"width:100%;display:grid;grid-template-rows:1fr 1fr;row-gap:4px\"> <div style=\"display:flex;align-items:center\"> <span><strong class=\"moment_sender\">${userName}</strong></span> <svg viewBox=\"0 0 1024 1024\" width=\"18\" height=\"18\" style=\"margin-left:auto\"> <path d=\"M512.4 429.1c22.3 0 41.7 7.9 58.1 23.6 7.9 7.9 13.9 16.9 18.2 27.1 4.3 10.2 6.4 20.8 6.4 32 0 22.3-8.2 41.7-24.6 58.1-3.3 3.3-6.6 6.1-9.9 8.4s-6.9 4.4-10.8 6.4c-3.9 2-8 3.6-12.3 4.9-4.3 1.3-8.5 2.5-12.8 3.4-4.2 1-8.3 1.5-12.3 1.5-23 0-42.5-8.2-58.6-24.6s-24.1-35.9-24.1-58.6c0-22.6 7.9-42.2 23.6-58.6 5.3-4.6 11-8.9 17.2-12.8 6.2-3.9 13-6.7 20.2-8.4 7.2-1.6 14.4-2.4 21.7-2.4z m326.8 0c23 0 42.3 7.9 58.1 23.6 5.3 5.3 9.7 11 13.3 17.2 3.6 6.2 6.4 13 8.4 20.2 2 7.2 3 14.4 3 21.7 0 22.3-8.2 41.7-24.6 58.1-3.3 3.9-7.2 7.4-11.8 10.3-4.6 2.9-9.4 5.4-14.3 7.4s-10.2 3.6-15.8 4.9-11 2-16.3 2c-23 0-42.5-8.2-58.6-24.6-16.1-16.4-24.1-35.9-24.1-58.6 0-22.6 8.2-42.2 24.6-58.6 7.2-7.2 16.1-13 26.6-17.2 10.5-4.2 21-6.3 31.5-6.4z m-654.6 0c23 0 42.3 7.9 58.1 23.6 5.3 5.3 9.7 11 13.3 17.2 3.6 6.2 6.4 13 8.4 20.2s3 14.4 3 21.7c0 22.3-8 41.7-24.1 58.1s-35.6 24.6-58.6 24.6-42.3-8.2-58.1-24.6c-7.9-7.9-13.9-16.9-18.2-27.1-4.3-10.2-6.4-20.5-6.4-31 0-23 8.2-42.7 24.6-59.1 7.2-7.2 16.1-13 26.6-17.2 10.4-4.2 20.9-6.3 31.4-6.4z\" p-id=\"14923\" fill=\"#000000\"></path> </svg> </div> <span style=\"color:#626367;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:12px\">${timestamp}</span> </div> </div> <span style=\"font-size:15px;line-height:1.3\" class=\"moment_message\">${message}</span> ${imgcontent} <div style=\"display:flex;align-items:center;gap:3px;margin-top:5px\"> <svg t=\"1736250731870\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"16069\" width=\"16\" height=\"16\"> <path d=\"M512 416a96 96 0 1 1-96 96 96 96 0 0 1 96-96m0-64a160 160 0 1 0 160 160 160 160 0 0 0-160-160z\" fill=\"#a1a1a1\" p-id=\"16070\"></path> <path d=\"M512 298.88c188.64 0 288 113.92 366.72 213.12C800 611.36 700.64 725.12 512 725.12S224 611.36 145.28 512C224 412.64 323.36 298.88 512 298.88m0-64C264.64 234.88 147.52 406.56 64 512c83.52 105.44 200.64 277.12 448 277.12S876.48 617.44 960 512c-83.52-105.44-200.64-277.12-448-277.12z\" fill=\"#a1a1a1\" p-id=\"16071\"></path> </svg> <span style=\"color:#a1a1a1;font-size:13px\">浏览${additionalInfo}次</span> <div class=\"moment_button\"> <svg viewBox=\"0 0 1024 1024\" width=\"26\" height=\"26\"> <path d=\"M190.193225 471.411583c14.446014 0 26.139334-11.718903 26.139334-26.13831 0-14.44499-11.69332-26.164916-26.139334-26.164916-0.271176 0-0.490164 0.149403-0.73678 0.149403l-62.496379 0.146333c-1.425466-0.195451-2.90005-0.295735-4.373611-0.295735-19.677155 0-35.621289 16.141632-35.621289 36.114522L86.622358 888.550075c0 19.949354 15.96767 35.597753 35.670407 35.597753 1.916653 0 3.808746 0.292666 5.649674 0l61.022819 0.022513c0.099261 0 0.148379 0.048095 0.24764 0.048095 0.097214 0 0.146333-0.048095 0.24457-0.048095l0.73678 0 0-0.148379c13.413498-0.540306 24.174586-11.422144 24.174586-24.960485 0-13.55983-10.760065-24.441669-24.174586-24.981974l0-0.393973-50.949392 0 1.450025-402.275993L190.193225 471.409536z\" fill=\"#000000\" p-id=\"19734\"></path> <path d=\"M926.52241 433.948343c-19.283182-31.445176-47.339168-44.172035-81.289398-45.546336-1.77032-0.246617-3.536546-0.39295-5.380544-0.39295l-205.447139-0.688685c13.462616-39.059598 22.698978-85.58933 22.698978-129.317251 0-28.349675-3.193739-55.962569-9.041934-82.542948l-0.490164 0.049119c-10.638291-46.578852-51.736315-81.31498-100.966553-81.31498-57.264215 0-95.466282 48.15065-95.466282 106.126063 0 3.241834-0.294712 6.387477 0 9.532097-2.996241 108.386546-91.240027 195.548698-196.23636 207.513194l0 54.881958-0.785899 222.227314 0 229.744521 10.709923 0 500.025271 0.222057 8.746198-0.243547c19.35686 0.049119 30.239721-4.817726 47.803749-16.116049 16.682961-10.761088 29.236881-25.50079 37.490869-42.156122 2.260483-3.341095 4.028757-7.075139 5.106298-11.20111l77.018118-344.324116c1.056052-4.053316 1.348718-8.181333 1.056052-12.160971C943.643346 476.446249 938.781618 453.944769 926.52241 433.948343zM893.82573 486.837924l-82.983993 367.783411-0.099261-0.049119c-2.555196 6.141884-6.879688 11.596106-12.872169 15.427364-4.177136 2.727111-8.773827 4.351098-13.414521 4.964058-1.49812-0.195451-3.046383 0-4.620227 0l-477.028511-0.540306-0.171915-407.408897c89.323375-40.266076 154.841577-79.670527 188.596356-173.661202 0.072655 0.024559 0.124843 0.049119 0.195451 0.072655 2.99931-9.137101 6.313799-20.73423 8.697079-33.164331 5.551436-29.185716 5.258771-58.123792 5.258771-58.123792-4.937452-37.98001 25.940812-52.965306 44.364417-52.965306 25.304316 0.860601 50.263777 33.656541 50.263777 52.326762 0 0 5.600555 27.563776 5.649674 57.190537 0.048095 37.366026-4.6673 56.847729-4.6673 56.847729l-0.466628 0c-5.872754 30.879288-16.214287 60.138682-30.464849 86.964654l0.36839 0.342808c-2.358721 4.815679-3.709485 10.220782-3.709485 15.943111 0 19.922748 19.088754 21.742187 38.765909 21.742187l238.761895 0.270153c0 0 14.666024 0.465604 14.690584 0.465604l0 0.100284c12.132318-0.638543 24.221658 5.207605 31.100322 16.409738 5.504364 9.016351 6.437619 19.6045 3.486404 28.988218L893.82573 486.837924z\" fill=\"#000000\" p-id=\"19735\"></path> <path d=\"M264.827039 924.31872c0.319272 0.024559 0.441045 0.024559 0.295735-0.024559 0.243547-0.048095 0.367367-0.074701-0.295735-0.074701s-0.539282 0.026606-0.271176 0.074701C264.43409 924.343279 264.532327 924.343279 264.827039 924.31872z\" fill=\"#000000\" p-id=\"19736\"></path> </svg> <svg viewBox=\"0 0 1024 1024\" width=\"28\" height=\"28\"> <path d=\"M185.2 888.7c-16.6 0-30-13.4-30-30v-580c0-49.6 40.4-90 90-90h540c49.6 0 90 40.4 90 90v410c0 49.6-40.4 90-90 90h-429c-16.6 0-30-13.4-30-30s13.4-30 30-30h429c16.5 0 30-13.5 30-30v-410c0-16.5-13.5-30-30-30h-540c-16.5 0-30 13.5-30 30v580c0 16.5-13.5 30-30 30z m490.1-430.5H347c-16.6 0-30-13.4-30-30s13.4-30 30-30h328.3c16.6 0 30 13.4 30 30s-13.4 30-30 30zM494 598.2H345.7c-16.6 0-30-13.4-30-30s13.4-30 30-30H494c16.6 0 30 13.4 30 30s-13.4 30-30 30zM194.2 883.7c-9.8 0-19.3-4.8-25.1-13.5-9.1-13.9-5.2-32.5 8.6-41.5l160-105c13.9-9.1 32.5-5.2 41.5 8.6 9.1 13.9 5.2 32.5-8.6 41.5l-160 105c-5 3.3-10.8 4.9-16.4 4.9z\" fill=\"#202020\" p-id=\"31300\"></path> </svg> <svg viewBox=\"0 0 1024 1024\" width=\"28\" height=\"28\"> <path d=\"M168.021333 751.104a53.333333 53.333333 0 0 1-81.450666-55.893333c48-219.733333 177.493333-333.184 381.205333-332.458667l8.490667-0.213333V186.026667c0-8.213333 2.602667-16.213333 7.445333-22.826667 1.237333-1.621333 1.237333-1.621333 2.602667-3.136a33.92 33.92 0 0 1 47.936-1.621333l370.197333 346.005333a32 32 0 0 0 1.066667 0.938667c7.914667 6.784 12.437333 16.576 12.437333 26.901333v3.498667c0 10.325333-4.522667 20.117333-12.373333 26.837333l-0.896 0.768-0.256 0.213333-370.922667 323.968c-1.109333 0.938667-1.109333 0.938667-2.282667 1.834667-15.146667 11.093333-36.437333 7.786667-47.509333-7.36a38.656 38.656 0 0 1-7.466667-22.826667v-197.248l-7.253333-0.362666c-112.213333 1.237333-212.266667 31.04-300.970667 89.472z m301.290667-153.472l1.962667 0.021333 68.992 3.541334v195.52l301.44-263.274667-301.44-281.749333v173.12l-31.146667 0.853333-40.597333 1.066667c-165.504-0.554667-268.117333 82.773333-313.386667 256.896 93.76-56.192 198.698667-84.906667 314.176-85.994667z m384.64-65.322667v3.498667c0-0.597333 0-1.173333 0.042667-1.749333a28.714667 28.714667 0 0 1-0.042667-1.749334z\" fill=\"#000000\" p-id=\"33532\"></path> </svg> </div> </div> <div style=\"display:flex;align-items:center;gap:3px;margin-top:0\"> <svg t=\"1736250924867\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"17414\" width=\"16\" height=\"16\"> <path d=\"M736.653061 929.959184H287.346939c-45.97551 0-83.591837-37.616327-83.591837-83.591837V177.632653c0-45.97551 37.616327-83.591837 83.591837-83.591837h449.306122c45.97551 0 83.591837 37.616327 83.591837 83.591837v668.734694c0 45.97551-37.616327 83.591837-83.591837 83.591837zM287.346939 135.836735c-22.987755 0-41.795918 18.808163-41.795919 41.795918v668.734694c0 22.987755 18.808163 41.795918 41.795919 41.795918h449.306122c22.987755 0 41.795918-18.808163 41.795919-41.795918V177.632653c0-22.987755-18.808163-41.795918-41.795919-41.795918H287.346939z\" fill=\"#a1a1a1\" p-id=\"17415\"></path> <path d=\"M616.489796 815.020408H407.510204c-11.493878 0-20.897959-9.404082-20.897959-20.897959s9.404082-20.897959 20.897959-20.897959h208.979592c11.493878 0 20.897959 9.404082 20.897959 20.897959s-9.404082 20.897959-20.897959 20.897959z\" fill=\"#a1a1a1\" p-id=\"17416\"></path> </svg> <span style=\"color:#a1a1a1;font-size:13px\">${randomPhone}</span> </div> <div style=\"display:flex;align-items:center;gap:3px;margin-top:5px;margin-bottom:5px\"> <svg class=\"icon\" viewBox=\"0 0 1024 1024\" width=\"13\" height=\"13\"> <path d=\"M773.6 912.7h-1.2c-37.2-0.4-74.5-0.4-111.8-0.4h-56.9c-38 0-76 0-114.1-0.5-21.1-0.6-41.9-5-61.5-13-33.3-13-52.3-42-52.2-79.7l0.1-141.4c0-78.3 0-156.7 0.7-235 0.1-21.3 13.8-41.3 25.5-51.8 45.3-41.4 94.5-93 115.1-162.6 5.7-19.4 7.9-40.8 10.2-63.4 4.6-45 33.8-74.3 72.8-74.3 15.3 0 30.6 4.6 45.6 13.5 30.1 18.1 50.2 46.5 61.3 87 17.8 64.3 8.7 126.7-1.3 180.2v0.2c-2.3 12.5 7.1 24.1 19.9 24.1h130c22.4 0 54.5 2.8 74.3 26.8 14.4 17.5 18.5 41.1 12.4 72.3-18.6 95.9-41.4 192.6-63.2 282.7-6.8 28.1-18.1 54.1-29 79.3l-4.7 10.8c-12.4 29.2-38 45.2-72 45.2zM216.1 903.3h-11.9c-43 0-78.2-35.2-78.2-78.2V476.6c0-43 35.2-78.2 78.2-78.2h11.9c43 0 78.2 35.2 78.2 78.2V825c0.1 43.1-35.1 78.3-78.2 78.3z\" fill=\"#000000\" p-id=\"34839\"></path> </svg> <span style=\"color:#474545;font-size:14px\">${extraContent}人已赞</span> </div> <div class=\"user_leave_message_list\"></div> <div style=\"display:flex;align-items:center;gap:3px;margin-top:10px\"> <div class=\"user_avatar\" style=\"width:30px;min-width:30px;height:30px;border-radius:50%\"></div> <div style=\"display:flex;align-items:center;width:100%;background-color:#f5f5f5;padding-right:5px;margin-left:5px\"> <input type=\"text\" placeholder=\"说点什么吧...\" style=\"height:30px;width:100%;margin-left:5px;border-radius:4px;background-color:#f5f5f5;padding:6px;border:none\"/> <svg viewBox=\"0 0 1024 1024\" width=\"20\" height=\"20\" class=\"moment_comment\"> <path d=\"M871.04 89.770667L120.064 380.16a51.2 51.2 0 0 0-1.792 94.762667l303.36 130.56 131.072 303.957333a51.2 51.2 0 0 0 94.805333-1.877333l289.792-751.573334a51.2 51.2 0 0 0-66.261333-66.133333z m-41.130667 107.392l-231.978666 601.642666-97.962667-227.114666-3.584-7.338667a85.333333 85.333333 0 0 0-41.045333-37.248l-226.56-97.536 601.173333-232.405333z\" fill=\"#a1a1a1\" p-id=\"18561\"></path> </svg> </div> </div> <div style=\"width:110%;height:8px;background-color:#e9e9e9;margin-left:-7px;margin-top:10px;margin-bottom:10px\"> </div>";
// Exports
/* harmony default export */ var moment_page = (moment_page_code);
;// ./src/界面/discord/discord_card.html
// Module
var discord_card_code = "<div class=\"discord-card\" data-id=\"${id}\"> <div class=\"discord-card-tags\">${tag}</div> <span class=\"discord-card-author\">${name}</span> <span style=\"color:#67686a;font-size:15px;margin-left:8px\">${time}</span> <div class=\"discord-card-title\">${cardtilte}</div> <div class=\"discord-card-content\">${content}</div> <div class=\"discord-card-fakeimg\">${img}</div> <div class=\"discord-card-interact\"> <svg aria-hidden=\"true\" role=\"img\" xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" fill=\"none\" viewBox=\"0 0 24 24\"> <path fill=\"#4d5059\" d=\"M12 22a10 10 0 1 0-8.45-4.64c.13.19.11.44-.04.61l-2.06 2.37A1 1 0 0 0 2.2 22H12Z\" class=\"\"> </path> </svg> <div style=\"color:#333237;margin-left:5px\">${messages}</div> <div class=\"discord-card-interact-icon\"> <div>👍</div> <div>${likes}</div> </div> </div> </div>";
// Exports
/* harmony default export */ var discord_discord_card = (discord_card_code);
;// ./src/界面/discord/discord_thread.html
// Module
var discord_thread_code = "<div class=\"discord-thread\" data-id=\"${id}\"> <div class=\"discord-thread-top\"> <svg class=\"discord-thread-top-return\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"3402\" width=\"28\" height=\"28\"> <path d=\"M440.070244 831.562927c11.813463 0 23.601951-4.49561 32.593171-13.486829a45.980098 45.980098 0 0 0 0-65.161366l-198.730927-198.730927 589.674146 0.499512c24.001561 0 43.507512-19.431024 43.40761-43.40761 0-24.101463-19.480976-43.63239-43.507512-43.63239l-592.221659-0.574439 201.378342-201.378341a45.980098 45.980098 0 0 0 0-65.161366 45.980098 45.980098 0 0 0-65.186342 0L131.29678 476.734439a45.955122 45.955122 0 0 0-13.53678 33.16761v0.574439c0 13.361951 5.994146 25.300293 15.409951 33.292488l274.307122 274.332097c8.99122 8.966244 20.804683 13.461854 32.593171 13.461854z\" p-id=\"3403\" fill=\"#505058\"></path> </svg> <svg viewBox=\"0 0 18.33 18.33\" style=\"width:20px;height:20px;margin-left:15px\"> <g> <path style=\"fill:#323338;stroke-width:0\" d=\"m9.17,18.33H.83c-.31,0-.59-.17-.74-.45-.14-.27-.12-.6.05-.86l1.66-2.41C.64,13.04,0,11.13,0,9.17,0,4.11,4.11,0,9.17,0s9.17,4.11,9.17,9.17-4.11,9.17-9.17,9.17Z\"/> </g> </svg> <div style=\"display:grid;grid-template-columns:1fr 18px;align-items:center\"> <div class=\"discord-thread-title\">${threadtilte}</div> <svg width=\"18\" height=\"18\" fill=\"none\" viewBox=\"0 0 24 24\"> <path fill=\"currentColor\" d=\"M9.3 5.3a1 1 0 0 0 0 1.4l5.29 5.3-5.3 5.3a1 1 0 1 0 1.42 1.4l6-6a1 1 0 0 0 0-1.4l-6-6a1 1 0 0 0-1.42 0Z\" class=\"\"></path> </svg> <div style=\"color:#5e5f63;margin-top:-3px\">类脑ΟΔΥΣΣΕΙΑ</div> </div> </div> <div style=\"width:100%;height:1px;background-color:#d7d7d7\"></div> <div style=\"width:100%;height:1px;background-color:#e3e3e3\"></div> <div class=\"discord-scroll-container\"> <div class=\"discord-thread-content\"> <div style=\"background-color:#f2f3f5;display:flex;align-items:center;justify-content:center;border-radius:50%;width:64px;height:64px;margin-top:20px;margin-left:5px\"> <svg viewBox=\"0 0 1024 1024\" width=\"32\" height=\"32\"> <path fill=\"#050608\" d=\"M524.531863 943.841235c-69.997795 0-137.060198-15.354355-199.606615-45.837266-15.354355-6.548181-91.674531-2.032194-162.575524 9.709372-11.515766 2.032194-23.483131-2.257993-31.160308-11.289967-7.677178-8.806174-10.16097-21.225138-6.548181-32.515105 15.805954-49.675854 26.192723-104.770893 22.805733-120.125248a452.434135 452.434135 0 0 1-78.35237-255.379052c0-251.088864 204.348401-455.437266 455.437265-455.437266S979.969129 237.315105 979.969129 488.403969 775.620728 943.841235 524.531863 943.841235z m-235.28291-116.512459c26.870121 0 50.804851 2.483793 65.481808 9.709372a382.955678 382.955678 0 0 0 169.801102 39.063285c213.831974 0 387.697464-173.865491 387.697464-387.697464S738.363837 100.706505 524.531863 100.706505 136.834399 274.571996 136.834399 488.403969c0 78.57817 23.483131 154.220948 67.739802 218.799559 17.16075 25.063727 11.289967 76.094377-0.903198 125.996031 27.095921-3.161191 58.03043-5.870783 85.57795-5.870783z m-140.672988-81.73936z\"> </path> </svg> </div> <div class=\"discord-thread-content-title\">${threadtilte}</div> <div class=\"discord-thread-content-tags\"> ${tag} </div> <div class=\"discord-thread-content-original\"> <div class=\"discord-thread-content-original-author-head head${isuser}\" data-name=\"${name}\"></div> <div> <div style=\"display:flex;align-items:center\"> <div class=\"discord-thread-content-original-author-name discord-name-creator\">${name}</div> </div> <div class=\"discord-thread-content-original-content\">${content}</div> <div class=\"discord-card-fakeimg\">${img}</div> </div> </div> </div> <div style=\"width:100%;height:1px;background-color:#d7d7d7\"></div> <div style=\"width:100%;height:1px;background-color:#e3e3e3\"></div> <div class=\"discord-thread-content\"> <div style=\"width:100%;display:flex;align-items:center;margin-top:10px\"> <div class=\"discord-thread-content-likes\"> <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 36 36\" width=\"24\" heigth=\"24\"> <path fill=\"#FFDB5E\" d=\"M34.956 17.916c0-.503-.12-.975-.321-1.404-1.341-4.326-7.619-4.01-16.549-4.221-1.493-.035-.639-1.798-.115-5.668.341-2.517-1.282-6.382-4.01-6.382-4.498 0-.171 3.548-4.148 12.322-2.125 4.688-6.875 2.062-6.875 6.771v10.719c0 1.833.18 3.595 2.758 3.885C8.195 34.219 7.633 36 11.238 36h18.044c1.838 0 3.333-1.496 3.333-3.334 0-.762-.267-1.456-.698-2.018 1.02-.571 1.72-1.649 1.72-2.899 0-.76-.266-1.454-.696-2.015 1.023-.57 1.725-1.649 1.725-2.901 0-.909-.368-1.733-.961-2.336.757-.611 1.251-1.535 1.251-2.581z\"/> <path fill=\"#EE9547\" d=\"M23.02 21.249h8.604c1.17 0 2.268-.626 2.866-1.633.246-.415.109-.952-.307-1.199-.415-.247-.952-.108-1.199.307-.283.479-.806.775-1.361.775h-8.81c-.873 0-1.583-.71-1.583-1.583s.71-1.583 1.583-1.583H28.7c.483 0 .875-.392.875-.875s-.392-.875-.875-.875h-5.888c-1.838 0-3.333 1.495-3.333 3.333 0 1.025.475 1.932 1.205 2.544-.615.605-.998 1.445-.998 2.373 0 1.028.478 1.938 1.212 2.549-.611.604-.99 1.441-.99 2.367 0 1.12.559 2.108 1.409 2.713-.524.589-.852 1.356-.852 2.204 0 1.838 1.495 3.333 3.333 3.333h5.484c1.17 0 2.269-.625 2.867-1.632.247-.415.11-.952-.305-1.199-.416-.245-.953-.11-1.199.305-.285.479-.808.776-1.363.776h-5.484c-.873 0-1.583-.71-1.583-1.583s.71-1.583 1.583-1.583h6.506c1.17 0 2.27-.626 2.867-1.633.247-.416.11-.953-.305-1.199-.419-.251-.954-.11-1.199.305-.289.487-.799.777-1.363.777h-7.063c-.873 0-1.583-.711-1.583-1.584s.71-1.583 1.583-1.583h8.091c1.17 0 2.269-.625 2.867-1.632.247-.415.11-.952-.305-1.199-.417-.246-.953-.11-1.199.305-.289.486-.799.776-1.363.776H23.02c-.873 0-1.583-.71-1.583-1.583s.709-1.584 1.583-1.584z\"/> </svg> <div style=\"color:#5f606a\">${likes}</div> </div> <div class=\"discord-thread-content-reaction\" style=\"width:34px\"> <svg class=\"icon_f8896c largeIcon_f8896c\" aria-hidden=\"true\" role=\"img\" xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" fill=\"none\" viewBox=\"0 0 24 24\"> <path fill=\"#5f606a\" fill-rule=\"evenodd\" d=\"M12 23a11 11 0 1 0 0-22 11 11 0 0 0 0 22ZM6.5 13a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Zm11 0a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Zm-9.8 1.17a1 1 0 0 1 1.39.27 3.5 3.5 0 0 0 5.82 0 1 1 0 0 1 1.66 1.12 5.5 5.5 0 0 1-9.14 0 1 1 0 0 1 .27-1.4Z\" clip-rule=\"evenodd\" class=\"\"></path> </svg> </div> <div class=\"discord-thread-content-reaction\" style=\"margin-left:auto\"> <svg aria-hidden=\"true\" width=\"18\" height=\"18\" fill=\"none\" viewBox=\"0 0 24 24\"> <path fill=\"#5f606a\" d=\"M9.7 2.89c.18-.07.32-.24.37-.43a2 2 0 0 1 3.86 0c.**********.38.43A7 7 0 0 1 19 9.5v2.09c0 .***********.33l1.1 1.22a3 3 0 0 1 .77 2.01v.28c0 .67-.34 1.29-.95 1.56-1.31.6-4 1.51-8.05 1.51-4.05 0-6.74-.91-8.05-1.5-.61-.28-.95-.9-.95-1.57v-.28a3 3 0 0 1 .77-2l1.1-1.23a.5.5 0 0 0 .13-.33V9.5a7 7 0 0 1 4.7-6.61ZM9.18 19.84A.16.16 0 0 0 9 20a3 3 0 1 0 6 0c0-.1-.09-.17-.18-.16a24.86 24.86 0 0 1-5.64 0Z\" class=\"\"></path> </svg> <div style=\"color:#5f606a;margin-right:1px\">关注</div> </div> <div class=\"discord-thread-content-reaction\" style=\"width:34px\"> <svg aria-hidden=\"true\" role=\"img\" xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" fill=\"none\" viewBox=\"0 0 24 24\"> <path fill=\"#5f606a\" d=\"M16.32 14.72a1 1 0 0 1 0-1.41l2.51-2.51a3.98 3.98 0 0 0-5.62-5.63l-2.52 2.51a1 1 0 0 1-1.41-1.41l2.52-2.52a5.98 5.98 0 0 1 8.45 8.46l-2.52 2.51a1 1 0 0 1-1.41 0ZM7.68 9.29a1 1 0 0 1 0 1.41l-2.52 2.51a3.98 3.98 0 1 0 5.63 5.63l2.51-2.52a1 1 0 0 1 1.42 1.42l-2.52 2.51a5.98 5.98 0 0 1-8.45-8.45l2.51-2.51a1 1 0 0 1 1.42 0Z\" class=\"\"></path> <path fill=\"#5f606a\" d=\"M14.7 10.7a1 1 0 0 0-1.4-1.4l-4 4a1 1 0 1 0 1.4 1.4l4-4Z\" class=\"\"> </path> </svg> </div> </div> </div> <div style=\"width:100%;height:3px;background-color:#f2f3f5;margin-top:10px\"></div> <div style=\"width:100%;height:2px;background-color:#f0f4f7\"></div> <div style=\"width:100%;height:3px;background-color:#f2f3f5\"></div> <div class=\"discord-thread-comment-list\"> </div> </div> <div class=\"discord-thread-input\"> <div class=\"discord-thread-input-button\"> <div style=\"display:flex;align-items:center;gap:5px;height:30px\"> <div class=\"discord-thread-input-svgbackground\"> <svg t=\"1744279170454\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"2274\" width=\"32\" height=\"32\"> <path d=\"M836 476H548V188c0-19.8-16.2-36-36-36s-36 16.2-36 36v288H188c-19.8 0-36 16.2-36 36s16.2 36 36 36h288v288c0 19.8 16.2 36 36 36s36-16.2 36-36V548h288c19.8 0 36-16.2 36-36s-16.2-36-36-36z\" fill=\"#333333\" p-id=\"2275\"></path> </svg> </div> <div class=\"discord-thread-input-svgbackground\"> <svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" viewBox=\"0 0 96 96\" width=\"28\" height=\"28\" preserveAspectRatio=\"xMidYMid meet\" style=\"transform:translate3d(0,0,0);content-visibility:visible\"> <defs> <clipPath id=\"__lottie_element_2133\"> <rect width=\"96\" height=\"96\" x=\"0\" y=\"0\"></rect> </clipPath> <clipPath id=\"__lottie_element_2135\"> <path d=\"M0,0 L96,0 L96,96 L0,96z\"></path> </clipPath> </defs> <g clip-path=\"url(#__lottie_element_2133)\"> <g clip-path=\"url(#__lottie_element_2135)\" transform=\"matrix(2.700000047683716,0,0,2.700000047683716,-79.60000610351562,-81.35110473632812)\" opacity=\"1\" style=\"display:block\"> <g transform=\"matrix(0.9999997019767761,0,0,0.9999997019767761,32.022003173828125,32.64699935913086)\" opacity=\"1\" style=\"display:block\"> <g opacity=\"1\" transform=\"matrix(1,0,0,1,7.179999828338623,7.181000232696533)\"> <path fill=\"#333333\" fill-opacity=\"1\" d=\" M-6.554999828338623,1.6410000324249268 C-6.929999828338623,3.0420000553131104 -6.099999904632568,4.480999946594238 -4.698999881744385,4.85699987411499 C-4.698999881744385,4.85699987411499 1.6410000324249268,6.554999828338623 1.6410000324249268,6.554999828338623 C3.0409998893737793,6.931000232696533 4.48199987411499,6.098999977111816 4.85699987411499,4.698999881744385 C4.85699987411499,4.698999881744385 6.554999828338623,-1.6410000324249268 6.554999828338623,-1.6410000324249268 C6.929999828338623,-3.0420000553131104 6.098999977111816,-4.480999946594238 4.698999881744385,-4.85699987411499 C4.698999881744385,-4.85699987411499 -1.6410000324249268,-6.556000232696533 -1.6410000324249268,-6.556000232696533 C-3.0409998893737793,-6.931000232696533 -4.48199987411499,-6.099999904632568 -4.85699987411499,-4.698999881744385 C-4.85699987411499,-4.698999881744385 -6.554999828338623,1.6410000324249268 -6.554999828338623,1.6410000324249268z\"> </path> </g> </g> <g transform=\"matrix(1,0,0,1,47.44300079345703,32.419002532958984)\" opacity=\"1\" style=\"display:block\"> <g opacity=\"1\" transform=\"matrix(1,0,0,1,7.802000045776367,7.021999835968018)\"> <path fill=\"#333333\" fill-opacity=\"1\" d=\" M-6.478000164031982,2.4040000438690186 C-7.552000045776367,4.372000217437744 -6.126999855041504,6.771999835968018 -3.884999990463257,6.771999835968018 C-3.884999990463257,6.771999835968018 3.885999917984009,6.771999835968018 3.885999917984009,6.771999835968018 C6.127999782562256,6.771999835968018 7.552000045776367,4.372000217437744 6.479000091552734,2.4040000438690186 C6.479000091552734,2.4040000438690186 2.5929999351501465,-4.718999862670898 2.5929999351501465,-4.718999862670898 C1.4739999771118164,-6.771999835968018 -1.4730000495910645,-6.771999835968018 -2.5929999351501465,-4.718999862670898 C-2.5929999351501465,-4.718999862670898 -6.478000164031982,2.4040000438690186 -6.478000164031982,2.4040000438690186z\"> </path> </g> </g> <g transform=\"matrix(1,0,0,1,32.119998931884766,49.04499816894531)\" opacity=\"1\" style=\"display:block\"> <g opacity=\"1\" transform=\"matrix(1,0,0,1,7.081999778747559,7.046999931335449)\"> <path fill=\"#333333\" fill-opacity=\"1\" d=\" M-0.9409999847412109,-6.26800012588501 C-0.42100000381469727,-6.796999931335449 0.41999998688697815,-6.796999931335449 0.9409999847412109,-6.26800012588501 C0.9409999847412109,-6.26800012588501 2.056999921798706,-5.132999897003174 2.056999921798706,-5.132999897003174 C2.2699999809265137,-4.916999816894531 2.5480000972747803,-4.78000020980835 2.8459999561309814,-4.744999885559082 C2.8459999561309814,-4.744999885559082 4.4120001792907715,-4.561999797821045 4.4120001792907715,-4.561999797821045 C5.140999794006348,-4.4770002365112305 5.665999889373779,-3.805999994277954 5.585000038146973,-3.061000108718872 C5.585000038146973,-3.061000108718872 5.410999774932861,-1.4630000591278076 5.410999774932861,-1.4630000591278076 C5.377999782562256,-1.1579999923706055 5.447000026702881,-0.8510000109672546 5.605999946594238,-0.5910000205039978 C5.605999946594238,-0.5910000205039978 6.441999912261963,0.7720000147819519 6.441999912261963,0.7720000147819519 C6.831999778747559,1.406999945640564 6.644999980926514,2.24399995803833 6.0229997634887695,2.6440000534057617 C6.0229997634887695,2.6440000534057617 4.690999984741211,3.502000093460083 4.690999984741211,3.502000093460083 C4.436999797821045,3.6649999618530273 4.24399995803833,3.9119999408721924 4.144000053405762,4.201000213623047 C4.144000053405762,4.201000213623047 3.621000051498413,5.7179999351501465 3.621000051498413,5.7179999351501465 C3.378000020980835,6.423999786376953 2.619999885559082,6.796999931335449 1.9259999990463257,6.551000118255615 C1.9259999990463257,6.551000118255615 0.43799999356269836,6.021999835968018 0.43799999356269836,6.021999835968018 C0.15399999916553497,5.921999931335449 -0.1550000011920929,5.921999931335449 -0.43799999356269836,6.021999835968018 C-0.43799999356269836,6.021999835968018 -1.9270000457763672,6.551000118255615 -1.9270000457763672,6.551000118255615 C-2.619999885559082,6.796999931335449 -3.378000020980835,6.423999786376953 -3.621999979019165,5.7179999351501465 C-3.621999979019165,5.7179999351501465 -4.144999980926514,4.201000213623047 -4.144999980926514,4.201000213623047 C-4.24399995803833,3.9119999408721924 -4.436999797821045,3.6649999618530273 -4.690999984741211,3.502000093460083 C-4.690999984741211,3.502000093460083 -6.02400016784668,2.6440000534057617 -6.02400016784668,2.6440000534057617 C-6.644999980926514,2.24399995803833 -6.831999778747559,1.406999945640564 -6.442999839782715,0.7720000147819519 C-6.442999839782715,0.7720000147819519 -5.605999946594238,-0.5910000205039978 -5.605999946594238,-0.5910000205039978 C-5.447000026702881,-0.8510000109672546 -5.377999782562256,-1.1579999923706055 -5.4120001792907715,-1.4630000591278076 C-5.4120001792907715,-1.4630000591278076 -5.585000038146973,-3.061000108718872 -5.585000038146973,-3.061000108718872 C-5.665999889373779,-3.805999994277954 -5.142000198364258,-4.4770002365112305 -4.4120001792907715,-4.561999797821045 C-4.4120001792907715,-4.561999797821045 -2.8469998836517334,-4.744999885559082 -2.8469998836517334,-4.744999885559082 C-2.5480000972747803,-4.78000020980835 -2.2699999809265137,-4.916999816894531 -2.056999921798706,-5.132999897003174 C-2.056999921798706,-5.132999897003174 -0.9409999847412109,-6.26800012588501 -0.9409999847412109,-6.26800012588501z\"> </path> </g> </g> <g transform=\"matrix(-0.9999998211860657,0,0,-0.9999998211860657,62.69399642944336,63.31999969482422)\" opacity=\"1\" style=\"display:block\"> <g opacity=\"1\" transform=\"matrix(1,0,0,1,7.247000217437744,7.247000217437744)\"> <path fill=\"#333333\" fill-opacity=\"1\" d=\" M1.5130000114440918,-5.5920000076293945 C0.9929999709129333,-6.997000217437744 -0.9940000176429749,-6.997000217437744 -1.5140000581741333,-5.5920000076293945 C-1.5140000581741333,-5.5920000076293945 -2.190999984741211,-3.760999917984009 -2.190999984741211,-3.760999917984009 C-2.4600000381469727,-3.0339999198913574 -3.0339999198913574,-2.4600000381469727 -3.76200008392334,-2.190999984741211 C-3.76200008392334,-2.190999984741211 -5.5920000076293945,-1.5130000114440918 -5.5920000076293945,-1.5130000114440918 C-6.997000217437744,-0.9940000176429749 -6.997000217437744,0.9929999709129333 -5.5920000076293945,1.5130000114440918 C-5.5920000076293945,1.5130000114440918 -3.76200008392334,2.190999984741211 -3.76200008392334,2.190999984741211 C-3.0339999198913574,2.4600000381469727 -2.4600000381469727,3.0339999198913574 -2.190999984741211,3.760999917984009 C-2.190999984741211,3.760999917984009 -1.5140000581741333,5.5920000076293945 -1.5140000581741333,5.5920000076293945 C-0.9940000176429749,6.997000217437744 0.9929999709129333,6.997000217437744 1.5130000114440918,5.5920000076293945 C1.5130000114440918,5.5920000076293945 2.190000057220459,3.760999917984009 2.190000057220459,3.760999917984009 C2.4600000381469727,3.0339999198913574 3.0329999923706055,2.4600000381469727 3.760999917984009,2.190999984741211 C3.760999917984009,2.190999984741211 5.5920000076293945,1.5130000114440918 5.5920000076293945,1.5130000114440918 C6.997000217437744,0.9929999709129333 6.997000217437744,-0.9940000176429749 5.5920000076293945,-1.5130000114440918 C5.5920000076293945,-1.5130000114440918 3.760999917984009,-2.190999984741211 3.760999917984009,-2.190999984741211 C3.0329999923706055,-2.4600000381469727 2.4600000381469727,-3.0339999198913574 2.190000057220459,-3.760999917984009 C2.190000057220459,-3.760999917984009 1.5130000114440918,-5.5920000076293945 1.5130000114440918,-5.5920000076293945z\"> </path> </g> </g> </g> </g> </svg> </div> <div class=\"discord-thread-input-svgbackground\"> <svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" viewBox=\"0 0 24 24\" width=\"30\" height=\"30\" preserveAspectRatio=\"xMidYMid meet\" style=\"transform:translate3d(0,0,0);content-visibility:visible\"> <defs> <clipPath id=\"__lottie_element_2044\"> <rect width=\"24\" height=\"24\" x=\"0\" y=\"0\"></rect> </clipPath> <clipPath id=\"__lottie_element_2046\"> <path d=\"M0,0 L600,0 L600,600 L0,600z\"></path> </clipPath> </defs> <g clip-path=\"url(#__lottie_element_2044)\"> <g clip-path=\"url(#__lottie_element_2046)\" transform=\"matrix(0.03999999910593033,0,0,0.03999999910593033,0,0)\" opacity=\"1\" style=\"display:block\"> <g transform=\"matrix(25,0,0,25,300,300)\" opacity=\"1\" style=\"display:block\"> <g opacity=\"1\" transform=\"matrix(1,0,0,1,0,0)\"> <path fill=\"#333333\" fill-opacity=\"1\" d=\" M-7,10 C-8.104999542236328,10 -9,9.104999542236328 -9,8 C-9,8 -9,2.5 -9,2.5 C-9,2.2239999771118164 -8.776000022888184,2 -8.5,2 C-8.5,2 -1.5,2 -1.5,2 C-1.2239999771118164,2 -1,2.2239999771118164 -1,2.5 C-1,2.5 -1,9.5 -1,9.5 C-1,9.776000022888184 -1.2239999771118164,10 -1.5,10 C-1.5,10 -7,10 -7,10z M1,9.5 C1,9.776000022888184 1.2239999771118164,10 1.5,10 C1.5,10 7,10 7,10 C8.104999542236328,10 9,9.104999542236328 9,8 C9,8 9,2.5 9,2.5 C9,2.2239999771118164 8.776000022888184,2 8.5,2 C8.5,2 1.5,2 1.5,2 C1.2239999771118164,2 1,2.2239999771118164 1,2.5 C1,2.5 1,9.5 1,9.5z\"> </path> </g> </g> <g transform=\"matrix(25,0,0,25,300,300)\" opacity=\"1\" style=\"display:block\"> <g opacity=\"1\" transform=\"matrix(1,0,0,1,0,0)\"> <path fill=\"#333333\" fill-opacity=\"1\" d=\" M-10,-2 C-10,-3.1050000190734863 -9.104999542236328,-4 -8,-4 C-8,-4 8,-4 8,-4 C9.104999542236328,-4 10,-3.1050000190734863 10,-2 C10,-2 10,-0.5 10,-0.5 C10,-0.2240000069141388 9.776000022888184,0 9.5,0 C9.5,0 -9.5,0 -9.5,0 C-9.776000022888184,0 -10,-0.2240000069141388 -10,-0.5 C-10,-0.5 -10,-2 -10,-2z\"> </path> </g> </g> <g transform=\"matrix(25,0,0,25,300,300)\" opacity=\"1\" style=\"display:block\"> <path stroke-linecap=\"butt\" stroke-linejoin=\"round\" fill-opacity=\"0\" stroke=\"#333333\" stroke-opacity=\"1\" stroke-width=\"2\" d=\" M7,-6 C7,-7.6570000648498535 5.6570000648498535,-9 4,-9 C4,-9 3.9110000133514404,-9 3.9110000133514404,-9 C2.49399995803833,-9 1.2589999437332153,-8.03600025177002 0.9150000214576721,-6.660999774932861 C0.9150000214576721,-6.660999774932861 0,-3 0,-3 C0,-3 4,-3 4,-3 C5.6570000648498535,-3 7,-4.3429999351501465 7,-6 C7,-6 7,-6 7,-6z\"> </path> <g opacity=\"1\" transform=\"matrix(1,0,0,1,0,0)\"></g> </g> <g transform=\"matrix(25,0,0,25,300,300)\" opacity=\"1\" style=\"display:block\"> <path stroke-linecap=\"butt\" stroke-linejoin=\"round\" fill-opacity=\"0\" stroke=\"#333333\" stroke-opacity=\"1\" stroke-width=\"2\" d=\" M-7,-6 C-7,-7.6570000648498535 -5.6570000648498535,-9 -4,-9 C-4,-9 -3.9110000133514404,-9 -3.9110000133514404,-9 C-2.49399995803833,-9 -1.2589999437332153,-8.03600025177002 -0.9150000214576721,-6.660999774932861 C-0.9150000214576721,-6.660999774932861 0,-3 0,-3 C0,-3 -4,-3 -4,-3 C-5.6570000648498535,-3 -7,-4.3429999351501465 -7,-6 C-7,-6 -7,-6 -7,-6z\"> </path> <g opacity=\"1\" transform=\"matrix(1,0,0,1,0,0)\"></g> </g> </g> </g> </svg> </div> </div> </div> <div class=\"discord-thread-input-focus-button\" style=\"display:none\"> <svg width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\" style=\"margin-top:2px\"> <path fill=\"#5f606a\" d=\"M9.3 5.3a1 1 0 0 0 0 1.4l5.29 5.3-5.3 5.3a1 1 0 1 0 1.42 1.4l6-6a1 1 0 0 0 0-1.4l-6-6a1 1 0 0 0-1.42 0Z\" class=\"\"></path> </svg> </div> <div style=\"display:flex;align-items:center;flex:1;min-width:50px;background-color:#ececec;border-radius:24px;height:35px\"> <input class=\"discord-thread-input-userinput\" type=\"text\" placeholder='在\"${threadtilte}\"中发送一则消息'> <svg style=\"margin-right:8px\" aria-hidden=\"true\" role=\"img\" xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" fill=\"none\" viewBox=\"0 0 24 24\"> <path fill=\"#5f606a\" fill-rule=\"evenodd\" d=\"M12 23a11 11 0 1 0 0-22 11 11 0 0 0 0 22ZM6.5 13a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Zm11 0a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Zm-9.8 1.17a1 1 0 0 1 1.39.27 3.5 3.5 0 0 0 5.82 0 1 1 0 0 1 1.66 1.12 5.5 5.5 0 0 1-9.14 0 1 1 0 0 1 .27-1.4Z\" clip-rule=\"evenodd\" class=\"\"></path> </svg> </div> <div class=\"discord-thread-input-svgbackground\"> <svg viewBox=\"0 0 1024 1024\" width=\"32\" height=\"32\" class=\"discord-thread-input-button\"> <path d=\"M511.752 70.5c-86.605 0-156.835 69.734-156.835 155.747l0 273.812c0 86.013 70.23 155.748 156.835 155.748 86.602 0 156.832-69.735 156.832-155.748L668.584 226.247C668.584 140.234 598.354 70.5 511.752 70.5L511.752 70.5 511.752 70.5zM243.854 461.102c-18.051 0-32.649 14.496-32.649 32.451 0 2.269 0.197 4.436 0.689 6.506l-0.689 0c0 151.605 113.922 276.578 261.386 295.713l0 80.687-52.275 0c-21.702 0-39.257 17.458-39.257 38.964 0 21.499 17.555 38.957 39.257 38.957l182.969 0c21.701 0 39.256-17.458 39.256-38.957 0-21.506-17.555-38.964-39.256-38.964L551.01 876.459l0-80.687c143.119-18.543 254.383-137.002 260.691-282.688 0.396-2.072 0.695-4.243 0.695-6.512 0-0.79-0.197-1.479-0.197-2.167 0-1.483 0.197-2.86 0.197-4.345l-0.695 0c-3.058-14.795-16.172-25.94-32.057-25.94-15.782 0-28.999 11.145-32.057 25.94l-0.688 0c0 129.019-105.344 233.572-235.249 233.572-129.903 0-235.249-104.554-235.249-233.572l-0.689 0c0.396-2.07 0.689-4.237 0.689-6.506C276.503 475.598 261.906 461.102 243.854 461.102L243.854 461.102 243.854 461.102zM243.854 461.102\" fill=\"#333333\" p-id=\"3351\"></path> </svg> <svg class=\"discord-thread-input-focus-button discord_thread-input-sendbutton\" style=\"width:100%;height:100%;display:none\" id=\"_图层_2\" data-name=\"图层 2\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 161.74 161.74\"> <defs> <style>.cls-1{fill:#5865f1}.cls-1,.cls-2{stroke-width:0}.cls-2{fill:#fff}</style> </defs> <g id=\"_图层_1-2\" data-name=\"图层 1\"> <circle class=\"cls-1\" cx=\"80.87\" cy=\"80.87\" r=\"80.87\"/> </g> <g id=\"_图层_2-2\" data-name=\"图层 2\"> <path class=\"cls-2\" d=\"m127.88,79.87c0-2.3-1.22-4.6-3.66-5.73L52.7,39.96c-3.5-1.84-7.33,1.81-5.67,5.39l10.16,25.41c.37.96,1.2,1.68,2.21,1.91l30.68,5.54c.93.17,1.39.92,1.39,1.67s-.46,1.5-1.39,1.67l-30.68,5.54c-1.01.23-1.84.94-2.21,1.91l-10.16,25.41c-1.67,3.58,2.17,7.23,5.67,5.39l71.53-34.18c2.44-1.13,3.66-3.43,3.66-5.73Z\"/> </g> </svg> </div> </div> </div>";
// Exports
/* harmony default export */ var discord_discord_thread = (discord_thread_code);
;// ./src/界面/discord/discord_thread_comment.html
// Module
var discord_thread_comment_code = "<div class=\"discord-thread-comment\"> <div class=\"discord-thread-comment-head head${isuser}\" data-name=\"${name}\"></div> <div> <div class=\"discord-thread-comment-name${creator}\">${name}</div> <div class=\"discord-thread-comment-content\">${content}</div> </div> </div>";
// Exports
/* harmony default export */ var discord_discord_thread_comment = (discord_thread_comment_code);
;// ./src/界面/worldbook/1-格式开头.txt?raw
var _1_raw_namespaceObject = "<线上格式>\r\n当用户要求查看内容时,仅输出对应格式,禁止输出剧情旁白\r\n以下是各格式具体介绍";
;// ./src/界面/worldbook/2-QQ聊天.txt?raw
var _2_QQ_raw_namespaceObject = "<QQ聊天格式介绍>\r\n格式示例如:\r\nmsg_start\r\n<{{user}}和xxx的私聊>\r\n发言人--内容--HH:MM\r\n发言人--特殊消息类型--HH:MM\r\n</{{user}}和xxx的私聊>\r\n\r\n<群聊:群名字>\r\n<成员>成员A,成员B</成员>\r\n<聊天内容>\r\n发言人--内容--HH:MM\r\n发言人--特殊消息类型--HH:MM\r\n</聊天内容>\r\n</群聊:群名字>\r\n\r\nmsg_end\r\n\r\n特殊消息类型:\r\n\r\n【表情包相关】\r\n角色会根据当前情绪和对话内容使用适当的表情包：\r\n- 表情包的选择应当符合角色当前心理和角色性格\r\n- 表情包使用频率应适中，平均每3-5条消息可使用一次\r\n- 输出格式为[bqb-表情包内容]（仅使用列表中存在的表情包，不可自创或篡改）\r\n- 表情包作为独立的一条消息，一条消息只能包含一个表情包\r\n- 示例:路人a--[bqb-摸小猫下巴]--12:00\r\n<表情包列表>\r\n\r\n</表情包列表>\r\n\r\n【转账消息相关】\r\n- 格式：[zz-金额元]\r\n- 必须独立成行，示例：路人a--[zz-520元]--12:00\r\n- 可用范围：仅私聊\r\n- 不可用范围：群聊，QQ空间\r\n\r\n【语音消息相关】\r\n- 格式：[yy-语音内容]\r\n- 必须独立成行，示例：路人a--[yy-想你了]--12:00\r\n- 可用范围：私聊，群聊\r\n- 不可用范围：QQ空间\r\n\r\n【音乐分享消息相关】\r\n- 格式：[music-歌名$歌手]\r\n- 必须独立成行，示例：路人a--[music-富士山下$陈奕迅]--12:00\r\n- 可用范围：私聊，群聊\r\n- 不可用范围：QQ空间\r\n\r\n【图片或视频消息相关】\r\n- 格式：[img-内容]\r\n- 示例：路人a--[img-一张自拍]--12:00\r\n- 可用范围：私聊，群聊，QQ空间\r\n- 在群聊和私聊时必须独立成行\r\n- 在QQ空间时前面可带其他文字内容\r\n- 注意：图片和视频都是使用这个格式\r\n\r\n格式解释:\r\n私聊：{{user}}和对方的私聊,聊天内容只有双方知道,标签名字顺序一定是{{user}}和xxx的私聊,而不是xxx和{{user}}的私聊\r\n群聊：包含多个成员的群组对话，所有群成员可见消息\r\n确保私聊和群聊的标签闭合\r\n特殊消息类型：聊天过程中可使用的消息类型,前后依然要加发言人和时间\r\n发言内容中如果需要换行,使用<br>\r\n若群聊中需要生成一些随机路人,禁止使用路人A,匿名用户等敷衍网名\r\n格式前后带上msg_start和msg_end标识符\r\n请勿生成多个msg_start,msg_end标识\r\n\r\n</QQ聊天格式介绍>";
;// ./src/界面/worldbook/3-QQ空间.txt?raw
var _3_QQ_raw_namespaceObject = "<QQ空间格式介绍>\r\n\r\n{{user}}和角色名都会使用聊天软件QQ\r\nQQ空间是聊天软件QQ中带的一个个人空间,可以在里面发布动态,所有人都能看到\r\n\r\n输出格式:\r\nmoment_start\r\n发言人--发言内容--发言时间--已浏览人数--已点赞人数\r\n发言人--评论内容\r\n发言人--评论内容\r\n发言人--发言内容--发言时间--已浏览人数--已点赞人数\r\n发言人--评论内容\r\n发言人--评论内容\r\nmoment_end\r\n\r\nQQ空间仅会有主要角色发布的动态,不会有路人动态\r\n发言内容中如果需要换行,使用<br>\r\n动态如果有配图,使用[img-内容]这个格式\r\n如{{user}}--我好看吗[img-一张自拍]--12:00--67--32\r\n但是角色发布的动态可以有路人参与评论\r\n路人必须生成具体网名,不可以使用\"匿名网友\"之类敷衍名字\r\n每条动态2-4条评论\r\n使用moment_start和moment_end标识符包裹\r\n请勿生成多个moment_start,moment_end标识\r\n\r\n</QQ空间格式介绍>";
;// ./src/界面/worldbook/4-discord论坛.txt?raw
var _4_discord_raw_namespaceObject = "<discord论坛格式介绍>\r\ndiscord帖子按以下格式输出:\r\n\r\ndiscord_start\r\n<随机六位数字字母>/* 此处仅生成六位数字字母替代被<>包裹的文本内容 */\r\n<正文>\r\n发帖人:\r\n帖子标题:\r\n帖子正文:\r\n帖子配图描述:\r\n帖子标签:\r\n距离发帖过去时间:\r\n帖子总评论数:\r\n帖子总点赞数:\r\n</正文>\r\n<评论>\r\n评论人--评论内容\r\n评论人--评论内容\r\n评论人--评论内容\r\n</评论>\r\n</随机六位数字字母>/* 此处仅生成六位数字字母 */ \r\ndiscord_end\r\n\r\n参考示例:\r\ndiscord_start\r\n<j324na>/* 此处仅生成六位数字字母替代被<>包裹的文本内容 */\r\n<正文>\r\n发帖人:柏柏\r\n帖子标题:4.8修bug 多人带群聊的同层手机界面\r\n帖子正文:正文内容\r\n帖子配图描述:界面的截图\r\n帖子标签:前端/UI美化\r\n距离发帖过去时间:2小时前\r\n帖子总评论数:9999\r\n帖子总点赞数:308\r\n</正文>\r\n<评论>\r\n唐初稚--伟大!\r\n</评论>\r\n</j324na>/* 此处仅生成六位数字字母 */\r\n<jtsn65>/* 此处仅生成六位数字字母替代被<>包裹的文本内容 */\r\n<正文>\r\n发帖人:本熊本的熊本熊\r\n帖子标题:我们是正经搞学术的，信我\r\n帖子正文:正文内容\r\n帖子配图描述:是一张配图\r\n帖子标签:聊天\r\n距离发帖过去时间:2小时前\r\n帖子总评论数:9999\r\n帖子总点赞数:308\r\n</正文>\r\n<评论>\r\n阿瓜--伟大!\r\n雀里--伟大!\r\n</评论>\r\n</jtsn65>/* 此处仅生成六位数字字母 */\r\ndiscord_end\r\n\r\n发帖人和评论人既可以是主要角色,也可以是路人\r\n每次生成帖子应有8到10个人回复\r\n路人必须生成具体网名,不可以使用\"匿名网友\"之类敷衍名字\r\n帖子有多个标签使用/分割\r\n内容如果需要换行使用<br>\r\n\r\n必须有MiPhone_start<br />discord_start包裹和discord_end<br />MiPhone_end包裹\r\n请勿生成多个discord_start,discord_end标识\r\n\r\n</discord论坛格式介绍>";
;// ./src/界面/worldbook/999-格式结尾.txt?raw
var _999_raw_namespaceObject = "以上格式必须全部一起包裹在MiPhone_start和MiPhone_end标识符里\r\n且要确保以下几点:\r\n1. 确保本轮回复中只存在一个MiPhone格式！\r\n2. 确保不同角色的聊天和群聊记录都位于同一个MiPhone格式中！\r\n3. 确保不在MiPhone格式内输出角色心理或旁白内容！\r\n4. **以MiPhone_end标识符收尾**\r\n\r\n[手机正确格式]\r\nMiPhone_start /* 此处必须完整生成所有字符 */\r\n此处为具体数据\r\n此处为更详细的数据\r\nMiPhone_end /* 此处必须完整生成所有字符 */\r\n\r\n</线上格式>";
;// ./src/界面/worldbook/worldlist.txt?raw
var worldlistraw_namespaceObject = "[手机-格式1-格式开头]\r\n深度=0\r\n类型=绿灯\r\n覆盖=真\r\n关键词=/查看(.+?)消息/, 发送消息, 回复, /给(.+?)发消息/, 在群聊, 动态, 空间, discord, dc, 论坛, 帖子, QQ, qq, 手机\r\n顺序=5560\r\n[手机-格式2-QQ聊天]\r\n深度=0\r\n类型=绿灯\r\n覆盖=真\r\n关键词=/查看(.+?)消息/, 发送消息, 回复, /给(.+?)发消息/, 在群聊, 动态, 空间, discord, dc, 论坛, 帖子, QQ, qq, 手机\r\n顺序=5561\r\n[手机-格式3-QQ空间]\r\n深度=0\r\n类型=绿灯\r\n覆盖=真\r\n关键词=动态, 空间\r\n顺序=5562\r\n[手机-格式4-discord论坛]\r\n深度=0\r\n类型=绿灯\r\n覆盖=真\r\n关键词=discord, dc, 论坛, 帖子\r\n顺序=5563\r\n[手机-格式999-格式结尾]\r\n深度=0\r\n类型=绿灯\r\n覆盖=真\r\n关键词=/查看(.+?)消息/, 发送消息, 回复, /给(.+?)发消息/, 在群聊, 动态, 空间, discord, dc, 论坛, 帖子, QQ, qq, 手机\r\n顺序=5564\r\n[手机-界面基本设置]\r\n深度=0\r\n类型=绿灯\r\n覆盖=假\r\n关键词=此世界书永不触发\r\n顺序=100\r\n[手机-角色]\r\n深度=0\r\n类型=绿灯\r\n覆盖=假\r\n关键词=此世界书永不触发\r\n顺序=100\r\n[手机-随机头像]\r\n深度=0\r\n类型=绿灯\r\n覆盖=假\r\n关键词=此世界书永不触发\r\n顺序=100\r\n[手机-表情包存放]\r\n深度=0\r\n类型=绿灯\r\n覆盖=假\r\n关键词=此世界书永不触发\r\n顺序=100";
;// ./src/界面/worldbook/随机头像.txt?raw
var _raw_namespaceObject = "http://sharkpan.xyz/f/EJeUD/Image_1737026320652.jpg\r\nhttp://sharkpan.xyz/f/Y8pt1/Image_1737026296736.jpg\r\nhttp://sharkpan.xyz/f/QWWc6/Image_1737026308451.jpg\r\nhttp://sharkpan.xyz/f/Z8LIW/Image_1737026306601.jpg\r\nhttp://sharkpan.xyz/f/W8lhW/Image_1737026313246.jpg\r\nhttp://sharkpan.xyz/f/0rJtX/Image_1737026292787.jpg\r\nhttp://sharkpan.xyz/f/yVxsN/Image_1737026293840.jpg\r\nhttp://sharkpan.xyz/f/vaBCL/Image_1737026286979.jpg\r\nhttp://sharkpan.xyz/f/pZ6hQ/Image_1737026285632.jpg\r\nhttp://sharkpan.xyz/f/11AH2/Image_1737026284351.jpg\r\nhttp://sharkpan.xyz/f/eXKUw/Image_1737026281715.jpg\r\nhttp://sharkpan.xyz/f/o31F4/Image_1737026277201.jpg\r\nhttp://sharkpan.xyz/f/8E2uj/Image_1737026279242.jpg\r\nhttp://sharkpan.xyz/f/GLmIl/Image_1737026275069.jpg\r\nhttp://sharkpan.xyz/f/zWZT5/Image_1737026271769.jpg\r\nhttp://sharkpan.xyz/f/7Zrij/Image_1737026269532.jpg\r\nhttp://sharkpan.xyz/f/AqYsZ/Image_1737026266131.jpg\r\nhttp://sharkpan.xyz/f/w4lFq/2406563368.jpeg\r\nhttp://sharkpan.xyz/f/MQNua/2403629154.jpeg\r\nhttp://sharkpan.xyz/f/3YZhe/2405854911.jpeg\r\nhttp://sharkpan.xyz/f/5QKhj/2312445144.jpeg\r\nhttp://sharkpan.xyz/f/k6JT6/2408434848.jpeg\r\nhttp://sharkpan.xyz/f/j6Bf6/2386328773.jpeg\r\nhttp://sharkpan.xyz/f/a8LHY/2386327598.jpeg\r\nhttp://sharkpan.xyz/f/r08C6/2386327604.jpeg\r\nhttp://sharkpan.xyz/f/DgECK/2331678725.jpeg\r\nhttp://sharkpan.xyz/f/LJrC7/2371251634.jpeg\r\nhttp://sharkpan.xyz/f/q1LF3/2329660869.gif\r\nhttp://sharkpan.xyz/f/X84sW/2328035526.jpeg\r\nhttp://sharkpan.xyz/f/2eDfQ/2326662447.jpeg\r\nhttp://sharkpan.xyz/f/ggetw/2326683821.jpeg\r\nhttp://sharkpan.xyz/f/J21ig/2323432137.gif\r\nhttp://sharkpan.xyz/f/BZjSa/%E5%A4%B4%E5%83%8F%20%282%29.jpg\r\nhttp://sharkpan.xyz/f/4rXIj/%E5%A4%B4%E5%83%8F%20%283%29.jpg\r\nhttp://sharkpan.xyz/f/Ndyhv/%E5%A4%B4%E5%83%8F%20%281%29.jpg\r\nhttp://sharkpan.xyz/f/VyJTY/%E5%A4%B4%E5%83%8F%20%284%29.jpg\r\nhttp://sharkpan.xyz/f/xlAhX/%E5%A4%B4%E5%83%8F%20%2820%29.jpg\r\nhttp://sharkpan.xyz/f/dDMI8/%E5%A4%B4%E5%83%8F%20%285%29.jpg\r\nhttp://sharkpan.xyz/f/lm7hx/%E5%A4%B4%E5%83%8F%20%286%29.jpg\r\nhttp://sharkpan.xyz/f/KkeHo/%E5%A4%B4%E5%83%8F%20%287%29.jpg\r\nhttp://sharkpan.xyz/f/EeZSD/%E5%A4%B4%E5%83%8F%20%288%29.jpg\r\nhttp://sharkpan.xyz/f/YyAi1/%E5%A4%B4%E5%83%8F%20%289%29.jpg\r\nhttp://sharkpan.xyz/f/QdaU6/%E5%A4%B4%E5%83%8F%20%2810%29.jpg\r\nhttp://sharkpan.xyz/f/ZKBuW/%E5%A4%B4%E5%83%8F%20%2811%29.jpg\r\nhttp://sharkpan.xyz/f/01AUX/%E5%A4%B4%E5%83%8F%20%2814%29.jpg\r\nhttp://sharkpan.xyz/f/ylpsN/%E5%A4%B4%E5%83%8F%20%2813%29.jpg\r\nhttp://sharkpan.xyz/f/vNpiL/%E5%A4%B4%E5%83%8F%20%2815%29.jpg\r\nhttp://sharkpan.xyz/f/pLQhQ/%E5%A4%B4%E5%83%8F%20%2816%29.jpg\r\nhttp://sharkpan.xyz/f/1QZh2/%E5%A4%B4%E5%83%8F%20%2817%29.jpg\r\nhttp://sharkpan.xyz/f/eqBSw/%E5%A4%B4%E5%83%8F%20%2818%29.jpg\r\nhttp://sharkpan.xyz/f/oDQI4/%E5%A4%B4%E5%83%8F%20%2819%29.jpg\r\nhttp://sharkpan.xyz/f/L5xwI7/p560183288.webp\r\nhttp://sharkpan.xyz/f/q0aJF3/p561777545.webp\r\nhttp://sharkpan.xyz/f/XdomSW/p561777547.webp\r\nhttp://sharkpan.xyz/f/24zATQ/p561777549.webp";
;// ./src/界面/worldbook/表情包列表.txt?raw
var _worldbook_raw_namespaceObject = "#在此存放表情包列表,加载一次界面后会自动修改格式世界书里的内容,请按示例格式存放\r\n此世界书同样需要一起绑定导出,否则无法使用表情包功能\r\n\r\n你敢顶嘴--http://sharkpan.xyz/f/vVBtL/mmexport1737057690899.png\r\n免礼,平身--http://sharkpan.xyz/f/pO6uQ/mmexport1737057701883.png\r\n你走吧--http://sharkpan.xyz/f/1vAc2/mmexport1737057678306.png\r\n我很满意--http://sharkpan.xyz/f/e8KUw/mmexport1737057664689.png\r\n揍你哦--http://sharkpan.xyz/f/oJ1i4/mmexport1737057862640.gif\r\n你是坏蛋--http://sharkpan.xyz/f/8r2Sj/mmexport1737057726579.png\r\n关心你--http://sharkpan.xyz/f/Gvmil/mmexport1737057801285.gif\r\n撞飞你--http://sharkpan.xyz/f/zMZu5/mmexport1737057848709.gif\r\n送你一个剪纸爱心--http://sharkpan.xyz/f/53nhj/345FFC998474F46C1A40B1567335DA03_0.gif\r\n飞奔过来--http://sharkpan.xyz/f/kDOi6/0A231BF0BFAB3C2B243F9749B64F7444_0.gif\r\n流口水--http://sharkpan.xyz/f/j36f6/3010464DF8BD77B4A99AB23730F2EE57_0.gif\r\n跟着音乐开心跳舞--http://sharkpan.xyz/f/aVwtY/0CBEE9105C7A98E0E6162A79CCD09EFA_0.gif\r\n开心扭动--http://sharkpan.xyz/f/rOpu6/9277120A65282CFEAB9E191B34474729_0.gif\r\n擦地板--http://sharkpan.xyz/f/DnJHK/F12BF133675BA34684A60CF38E17D328_0.gif\r\n打招呼，你好呀！--http://sharkpan.xyz/f/LgwT7/AC229A80203166B292155ADA057DE423_0.gif\r\n乖巧主动带上项圈--http://sharkpan.xyz/f/qJJI3/E7B02761D317A00B912F328AA9F02565_0.gif\r\n可怜兮兮--http://sharkpan.xyz/f/XgmcW/817B66DAB2414E1FC8D717570A602193_0.gif\r\n送你礼物--http://sharkpan.xyz/f/2aACQ/A491786010A6E595A84B9F4D4EE58B27_0.gif\r\n委屈哭泣--http://sharkpan.xyz/f/gVySw/D90D0B53802301FCDB1F0718DEB08C79_0.gif\r\n欢呼雀跃--http://sharkpan.xyz/f/JXeig/68FD6090F0D187FC88794909AA4E4C30_0.gif\r\n脏兮兮的狼狈样子--http://sharkpan.xyz/f/O6msy/897713F074EF610881EFD9A4D993B7DA_0.gif\r\n趴在枕头上休息一下--http://sharkpan.xyz/f/6Mzua/7AF42F3AE5EA01AEDBA5A3C7437339FA_0.gif\r\n开心吃面包--http://sharkpan.xyz/f/nX5sl/Camera_1040g34o313t1veosi60g4almcsqnoumhanf8f98.jpg\r\n害羞地跳舞--http://sharkpan.xyz/f/mqdcW/Camera_1040g34o313t1verti60g4almcsqnoumhepqc530.jpg\r\n认真看菜谱--http://sharkpan.xyz/f/BODsa/Camera_1040g0k0313t1vf2k1e004almcsqnoumhblfs9o0.jpg\r\n害羞想要表达喜欢--http://sharkpan.xyz/f/4Mdfj/IMG_20250131_212918.jpg\r\n激动亲亲--http://sharkpan.xyz/f/NOVuv/Camera_XHS_17383302852781040g2sg31a0ceua57idg4a11eo5c0eoqn8udogg.jpg\r\n水汪汪的大眼睛--http://sharkpan.xyz/f/VXnTY/Camera_XHS_17383302891351040g2sg31a0ceua57icg4a11eo5c0eoq7j77fu0.jpg\r\n嫁给我，最爱你--http://sharkpan.xyz/f/xkmFX/Camera_XHS_17383302941971040g2sg31a0ceua57ia04a11eo5c0eoql8q50vg.jpg\r\n乖巧站立--http://sharkpan.xyz/f/dlyH8/Camera_XHS_17383303028511040g00831aqhfp8fna405pf08cqh97tb5qhsf6o.jpg\r\n剪刀手比耶--http://sharkpan.xyz/f/lrNCx/Camera_XHS_17383303062101040g00831aqhfp8fna4g5pf08cqh97tbcoki8f8.jpg\r\n真棒！竖大拇指--http://sharkpan.xyz/f/K2Oto/Camera_XHS_17383303090201040g00831aqhfp8fna505pf08cqh97tbo91bmno.jpg\r\n小猫献上一盘鱼赔罪--http://sharkpan.xyz/f/Qm1H6/Camera_XHS_17383303303421040g2sg3189ite3a3ujg5ofl7bm417a4rcgd0bg.jpg\r\n小猫害羞捂嘴--http://sharkpan.xyz/f/ZOmuW/Camera_XHS_17383303329411040g2sg3189ite3a3uj05ofl7bm417a4kppd5v8.jpg\r\n小猫哭泣等待投喂--http://sharkpan.xyz/f/W2BfW/Camera_XHS_17383303352131040g2sg3189ite3a3uhg5ofl7bm417a4psbct30.jpg\r\n请多指教--http://sharkpan.xyz/f/0MjhX/Camera_XHS_17383303606011040g00831bogh2040u104ag8aht6f2mpkhq67r0.jpg\r\n保持联络哦--http://sharkpan.xyz/f/yk4HN/Camera_XHS_17383303659031040g00831bogh2040u2g4ag8aht6f2mpeus4ul8.jpg\r\n不要忘记哦--http://sharkpan.xyz/f/vBDIL/Camera_XHS_17383303686641040g00831bogh2040u304ag8aht6f2mppr4gni0.jpg\r\n开心！开心！--http://sharkpan.xyz/f/pg1IQ/Camera_XHS_17383303710381040g00831bogh2040u3g4ag8aht6f2mpf2epg78.jpg\r\n耶！--http://sharkpan.xyz/f/1MjS2/Camera_XHS_17383303739941040g00831bogh2040u4g4ag8aht6f2mpik7q0ag.jpg\r\n没关系，不要紧--http://sharkpan.xyz/f/erOSw/Camera_XHS_17383303793581040g00831bogh2040u604ag8aht6f2mpo9j26l0.jpg\r\n小老鼠喝饮料--http://sharkpan.xyz/f/8Mgij/Camera_XHS_17383305816431040g00831bv6at85gm6g5n6lbcu5s4aorps5mr8.jpg\r\n委屈地嘟起嘴--http://sharkpan.xyz/f/713sj/307F8B36E1F2A49573E6562193AA71BF_0.gif\r\n垂死梦中惊坐起--http://sharkpan.xyz/f/ADwCZ/04A8CC14F4C317F5E0DA84AD2A8BE1FF_0.gif\r\n小猫快速奔跑--http://sharkpan.xyz/f/wqghq/B8578FD25ED069B8AF1B0AC35F20770B_0.gif\r\n尴尬的动了动耳朵--http://sharkpan.xyz/f/M4OUa/DA8F0F3F2B2C1F567258724B9EA59623_0.gif\r\n激动地摇摆--http://sharkpan.xyz/f/30lHe/1507DB48EFC13593A4766C51F33BFC1C_0.gif\r\n双手叉腰--http://sharkpan.xyz/f/5xnSj/9B6915837A055D7EF9CE0DD18BC0E60F_0.jpg\r\n小猫偷看你--http://sharkpan.xyz/f/kXOI6/C0FC1927068E1F87D38FA09B7F51F830_0.gif\r\n小猫开心的跳跃起来--http://sharkpan.xyz/f/jq6H6/413DB04EE36F940E3381C99402CE2E44_0.gif\r\n开心的双手舞动--http://sharkpan.xyz/f/aJwtY/11BB0DE666912CED03486468EA5DB258_0.gif\r\n无理取闹，原地打滚--http://sharkpan.xyz/f/rmpI6/7D87F6F45B1AEDAABC0EF119E977732F_0.gif\r\n害怕地哭泣打滚--http://sharkpan.xyz/f/DXJcK/E419CF47415150B8CBADD767F09017C9_0.gif\r\n努力工作--http://sharkpan.xyz/f/LBwS7/6251D891E0E3FB87FCDC50BECCCD4559_0.gif\r\n加油！--http://sharkpan.xyz/f/qnJt3/19650FABB205847C0D505FF2B361B194_0.gif\r\n得意的跳舞--http://sharkpan.xyz/f/XmmcW/B7973C500D9E981A083A5F3E75CF198A_0.gif\r\n我准备好啦！--http://sharkpan.xyz/f/2ZAFQ/F0A74F31B23E7C20AB50B4A960344056_0.gif\r\n再坚持一下--http://sharkpan.xyz/f/g4yCw/CF6BE01FF89BB72BE179A537104984A5_0.gif\r\n尖叫--http://sharkpan.xyz/f/JneTg/3ED8B290F429F8FCC5D533CCC0568086_0.gif\r\n惊吓得一抖--http://sharkpan.xyz/f/OLmCy/AB70AB259C3BD064624A1349779B072C_0.gif\r\n失去所有力气和手段--http://sharkpan.xyz/f/6Azsa/3BEBF677C8FD51DFF757BB62DA22C0A8_0.jpg\r\n大吃一口美食--http://sharkpan.xyz/f/nJ5Ul/5E4285F661C54F06BEFB881AD07BC3FB_0.jpg\r\n不喜欢我，你真的没品--http://sharkpan.xyz/f/mgduW/4F3224BCDAF9298F644572715BDA7EB9_0.jpg\r\n一觉睡到自然死--http://sharkpan.xyz/f/BLDsa/EC0D48BDAAB071654E0112599A6FA57B_0.jpg\r\n红温了--http://sharkpan.xyz/f/4YdFj/C13A42E1CB05564DF9060B2D93617086_0.jpg\r\n扭屁股--http://sharkpan.xyz/f/NDVhv/EE91A0C1472A9D3D3F01DAD6E6BF6B3B_0.gif\r\n失败了 遗憾离场--http://sharkpan.xyz/f/V6niY/19CC4600A7EAE5CC33257376EA8E11E7_0.gif\r\n害羞脸红--http://sharkpan.xyz/f/xXmSX/8664EF605A192AC3B392E3FDE8DCB695_0.gif\r\n大声尖叫--http://sharkpan.xyz/f/dnyt8/A95DD11A432A74CAD0CF9E3B97DA96A2_0.gif\r\n我是厚脸皮--http://sharkpan.xyz/f/l2Nux/0C0EA6213E96C0992356DFC48500EE08_0.jpg\r\n你少看扁我--http://sharkpan.xyz/f/JnJIg/4344AAD53418BAE9569B06CEF9CFDDED_0.jpg\r\n得意地唱歌--http://sharkpan.xyz/f/OLjSy/AA21937C7A115FCF4B87DC646E13C572_0.jpg\r\n高兴地被喂饭--\r\nhttps://files.catbox.moe/8uz920.jpg\r\n高兴地转圈--\r\nhttps://files.catbox.moe/26rwpc.jpg\r\n开心--\r\nhttps://files.catbox.moe/9242jm.jpg\r\n大大的爱--\r\nhttps://files.catbox.moe/wabm2j.jpg\r\n喜欢--\r\nhttps://files.catbox.moe/l7j4vy.jpg\r\n幸福地看手机--\r\nhttps://files.catbox.moe/e2eqzc.jpg\r\n哦耶--\r\nhttps://files.catbox.moe/nff4qi.jpg\r\n给你爱心--\r\nhttps://files.catbox.moe/o3boww.jpg\r\n紧紧抱住--\r\nhttps://files.catbox.moe/2hon5k.jpg\r\n乖乖跟着--\r\nhttps://files.catbox.moe/ydtn6e.jpg\r\n亲亲--\r\nhttps://files.catbox.moe/l14x54.jpg\r\n宠爱地亲--\r\nhttps://files.catbox.moe/wqn5ki.jpg\r\n搂住你--\r\nhttps://files.catbox.moe/pxn8x6.jpg\r\n谄媚吻手--\r\nhttps://files.catbox.moe/cxdhkh.jpg\r\n恳求脸--\r\nhttps://files.catbox.moe/i5etcm.jpg\r\n舔一口--\r\nhttps://files.catbox.moe/p0vdxk.jpg\r\n抱抱--\r\nhttps://files.catbox.moe/qt4qzg.jpg\r\n扑过来--\r\nhttps://files.catbox.moe/czdmyn.jpg\r\n小狗飞扑--\r\nhttps://files.catbox.moe/9yz8nz.jpg\r\n满脸骄傲--\r\nhttps://files.catbox.moe/h1mf7j.jpg\r\n蹦蹦跳跳--\r\nhttps://files.catbox.moe/q6bahb.jpg\r\n泥里打滚--\r\nhttps://files.catbox.moe/ofy8ic.jpg\r\n蹑手蹑脚--\r\nhttps://files.catbox.moe/7mdkef.jpg\r\n等饭饭--\r\nhttps://files.catbox.moe/ppi7e4.jpg\r\n偷吃--\r\nhttps://files.catbox.moe/ur7vh9.jpg\r\n蹭裤腿--\r\nhttps://files.catbox.moe/dzoxjx.jpg\r\n打劫--\r\nhttps://files.catbox.moe/cz3umm.jpg\r\n用枪指着你--\r\nhttps://files.catbox.moe/zvm5n2.jpg\r\n咬咬咬咬--\r\nhttps://files.catbox.moe/ogs4t5.jpg\r\n无意义的吼叫--\r\nhttps://files.catbox.moe/0vetoe.jpg\r\n辣眼睛--\r\nhttps://files.catbox.moe/91c91h.jpg\r\n冷汗心虚笑--\r\nhttps://files.catbox.moe/nj0i5e.png\r\n犯错后心虚--\r\nhttps://files.catbox.moe/rejhsf.jpg\r\n装无辜--\r\nhttps://files.catbox.moe/pdjeef.jpg\r\n小心翼翼--\r\nhttps://files.catbox.moe/1mm5y1.jpg\r\n害羞--\r\nhttps://files.catbox.moe/rqtdfm.jpg\r\n要掉眼泪了--\r\nhttps://files.catbox.moe/cdaxhk.jpg\r\n委屈--\r\nhttps://files.catbox.moe/r89k3c.jpg\r\n眼含泪光--\r\nhttps://files.catbox.moe/yqwu7e.jpg\r\n哭哭--\r\nhttps://files.catbox.moe/a1518p.jpg\r\n急哭了--\r\nhttps://files.catbox.moe/wok7sq.jpg\r\n气哭了--\r\nhttps://files.catbox.moe/qfljha.jpg\r\n碗里没有饭--\r\nhttps://files.catbox.moe/i9682x.jpg\r\n老婆你回来吧--\r\nhttps://files.catbox.moe/ge0bjd.jpg\r\n害怕--\r\nhttps://files.catbox.moe/k8mfa6.jpg\r\n震惊猫猫--\r\nhttps://files.catbox.moe/bhytrl.jpg\r\n惊吓到模糊--\r\nhttps://files.catbox.moe/qdzxst.jpg\r\n期待--\r\nhttps://files.catbox.moe/mii353.jpg\r\n皱眉--\r\nhttps://files.catbox.moe/785has.jpg\r\n生气瞪你--\r\nhttps://files.catbox.moe/mzoekx.jpg\r\n有点生气--\r\nhttps://files.catbox.moe/evzdkv.jpg\r\n大脑空白--\r\nhttps://files.catbox.moe/jqnaxs.jpg\r\n大脑CPU烧了--\r\nhttps://files.catbox.moe/mrzvy0.gif\r\n探头探脑--\r\nhttps://files.catbox.moe/6p8w3k.gif\r\n已老实--\r\nhttps://files.catbox.moe/rv76c1.jpg\r\n平静地死了--\r\nhttps://files.catbox.moe/dcm5f9.jpg\r\n被打死惹--\r\nhttps://files.catbox.moe/qqibg1.jpg\r\n睡大觉--\r\nhttps://files.catbox.moe/pvbfg6.jpg\r\n土下座道歉--\r\nhttps://files.catbox.moe/qjnck3.jpg\r\n孤独低落狗--\r\nhttps://files.catbox.moe/z09102.jpg\r\n乖乖当狗--\r\nhttps://files.catbox.moe/bgid0b.jpg\r\n叼食盆小狗--\r\nhttps://files.catbox.moe/o196se.jpg\r\n战斗小狗--\r\nhttps://files.catbox.moe/c5ccwc.jpg\r\n汉堡小狗--\r\nhttps://files.catbox.moe/prowke.jpg";
;// ./src/界面/worldbook/界面基本设置.txt?raw
var src_worldbook_raw_namespaceObject_0 = "[下面是基本设置]\r\n外框颜色=#810000\r\n内框颜色=#1b1717\r\n气泡颜色=#ffffff\r\n侧边按钮=#ce1212\r\n聊天壁纸=http://sharkpan.xyz/f/mM2SW/Screenshot_20250317_001012.jpg\r\n手机壁纸=暂时设置不了手机壁纸\r\n发送模式=1\r\n世界书版本=509";
;// ./src/界面/index.ts

// 聊天界面













// 动态空间


// discord



// 世界书









class MyINI {
    sections;
    autoSave;
    save;
    constructor() {
        this.sections = {};
        this.autoSave = "";
    }
    loadLines(lines) {
        this.sections = {};
        let currentSection = "";
        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed.startsWith("[") && trimmed.endsWith("]")) {
                currentSection = trimmed.slice(1, -1);
            }
            else if (currentSection && trimmed.includes("=")) {
                const [key, ...values] = trimmed.split("=");
                const value = values.join("=").trim();
                if (!this.sections[currentSection]) {
                    this.sections[currentSection] = {};
                }
                this.sections[currentSection][key.trim()] = value;
            }
        }
        return Object.keys(this.sections).length > 0;
    }
    loadText(text) {
        // 添加参数校验
        if (typeof text !== "string") {
            console.error("Invalid text input");
            return false;
        }
        // 处理不同换行符并过滤空行
        const lines = text
            .replace(/\r\n/g, "\n") // 统一换行符
            .split(/\n+/)
            .map((line) => line.trim())
            .filter((line) => line.length > 0);
        console.log("Parsed lines:", lines.length);
        return this.loadLines(lines); // 使用this调用类方法
    }
    getAllSections() {
        return Object.keys(this.sections);
    }
    getAllKeys(section) {
        return this.sections[section] ? Object.keys(this.sections[section]) : [];
    }
    readValue(section, key) {
        return this.sections[section]?.[key] || "";
    }
    readValueDouble(section, key) {
        const value = parseFloat(this.readValue(section, key));
        return isNaN(value) ? -1 : value;
    }
    writeValue(section, key, value) {
        if (!key)
            return false;
        if (!this.sections[section]) {
            this.sections[section] = {};
        }
        this.sections[section][key] = value.toString();
        if (this.autoSave) {
            this.save(this.autoSave);
        }
        return true;
    }
    getKeyByValue(section, value) {
        const items = this.sections[section] || {};
        return Object.keys(items).find((key) => items[key] === value) || "";
    }
    containsKey(section) {
        return section in this.sections;
    }
    removeSection(section) {
        delete this.sections[section];
        return true;
    }
    getAllText() {
        let result = "";
        for (const section of Object.keys(this.sections)) {
            result += `\n[${section}]`;
            const keyvalue = this.sections[section];
            for (const key of Object.keys(keyvalue)) {
                const value = keyvalue[key];
                result += `\n${key}=${value}`;
            }
        }
        result = result.trim();
        return result;
    }
}
class worldbooktext {
    static list = (/* unused pure expression or super */ null && (`[1-格式开头]
深度=0
类型=绿灯
覆盖=真
关键词=/查看(.+?)消息/, 发送消息, 回复, /给(.+?)发消息/, 在群聊, 动态, 空间, discord, dc, 论坛, 帖子, QQ, qq, 手机
顺序=5560
[2-QQ聊天]
深度=0
类型=绿灯
覆盖=真
关键词=/查看(.+?)消息/, 发送消息, 回复, /给(.+?)发消息/, 在群聊, 动态, 空间, discord, dc, 论坛, 帖子, QQ, qq, 手机
顺序=5561
[3-QQ空间]
深度=0
类型=绿灯
覆盖=真
关键词=动态, 空间
顺序=5562
[4-discord论坛]
深度=0
类型=绿灯
覆盖=真
关键词=discord, dc, 论坛, 帖子
顺序=5563
[999-格式结尾]
深度=0
类型=绿灯
覆盖=真
关键词=/查看(.+?)消息/, 发送消息, 回复, /给(.+?)发消息/, 在群聊, 动态, 空间, discord, dc, 论坛, 帖子, QQ, qq, 手机
顺序=5564`));
}
let random_head_list = new Array();
let QQ_pages = new Array();
let QQ_emoji = new Map();
let QQ_CacheSendMsg = "";
// let QQ_SetNoteName = '';
let QQ_msgjson = {
    私聊: {},
    群聊: {},
};
let QQ_moment = (/* unused pure expression or super */ null && ([]));
let QQ_NewMsg = {};
let Npc_Settings = {};
let QQ_Groups = [];
let gening;
let worldbook;
let entries;
let newgen = true;
let QQ_CharSettings = new MyINI();
let Phone_Settings = new MyINI();
let QQ_Music = {
    audio: new Audio(),
    lastelement: undefined, // 允许 undefined
    isLoading: false,
    // 新增封面缓存
    cover: ""
};
let QQ_RandomHead = [];
let NpcCssValue = "";
let Variables = {};
let UserName;
let charAvatarPath;
let userAvatarPath;
let charcardname;
let User_LastMsgMap = {
    群聊: {},
    私聊: {},
};
let Char_LastMsgMap = {
    群聊: {},
    私聊: {},
};
const version = "509";
/**
 * 调用前端助手函数
 */
class ST {
    static async GetCurrentMessages() {
        const CurrentMessageId = getCurrentMessageId();
        const Messages = await getChatMessages(CurrentMessageId);
        if (!Messages) {
            console.log(`获取楼层记录失败`);
            return "";
        }
        let msg = Messages[0].message;
        return msg;
    }
    static async Gen(msg) {
        console.log(`触发生成  ${msg}`);
        let result;
        if (newgen) {
            result = await generate({ user_input: msg, should_stream: true });
        }
        else {
            result = await generate({ user_input: msg, should_stream: false });
        }
        console.log(`生成结果:${result}`);
        return result;
    }
}
/**
 * 获取消息中的名称
 * @param value 消息内容
 * @returns 名称列表
 */
function QQ_GetValueName(value) {
    let result = [];
    const lines = value.split(/\r?\n/);
    for (const line of lines) {
        let match = line.match(/在群聊(.+)中发送:(.+)/);
        if (match) {
            let obj = {
                name: match[1],
                value: match[2],
            };
            result.push(obj);
            continue;
        }
        match = line.match(/给(.+)发消息:(.+)/);
        if (match) {
            let obj = {
                name: match[1],
                value: match[2],
            };
            result.push(obj);
            continue;
        }
        match = line.match(/回复(.+):(.+)/);
        if (match) {
            let obj = {
                name: match[1],
                value: match[2],
            };
            result.push(obj);
            continue;
        }
    }
    return result;
}
/**
 * 生成消息
 * @param msg 消息内容
 * @returns 生成结果
 */
async function QQ_Gen(msg) {
    console.log(`触发生成  ${msg}`);
    let result;
    if (newgen) {
        result = await generate({ user_input: msg, should_stream: true });
    }
    else {
        result = await generate({ user_input: msg, should_stream: false });
    }
    console.log(`生成结果:${result}`);
    return result;
}
/**
 * 保存消息
 * @returns
 */
async function QQ_Save_Msg() {
    if (!QQ_msgjson) {
        return;
    }
    const CurrentMessageId = getCurrentMessageId();
    const Messages = await getChatMessages(CurrentMessageId);
    if (!Messages) {
        console.log(`获取楼层记录失败`);
        return;
    }
    let msg = Messages[0].message;
    const match = msg.match(/msg_start[\s\S]+?msg_end/);
    if (!match) {
        console.log(`匹配楼层记录失败`);
        return;
    }
    msg = msg.replace(match[0], `msg_start\n${QQ_Json2Text(QQ_msgjson)}\nmsg_end`);
    setChatMessage({ message: msg }, CurrentMessageId, { refresh: "none" });
}
function QQ_Json2Text(json) {
    console.log(`传进来的json:${JSON.stringify(json)}`);
    let result = "";
    for (const key in json.私聊) {
        if (json.私聊[key].length == 0) {
            continue;
        }
        let localkey = key.replace("的聊天", "的私聊");
        result += `<${localkey}>\n`;
        for (const msg of json.私聊[key]) {
            result += `${msg}\n`;
        }
        result += `</${localkey}>\n`;
    }
    for (const group in json.群聊) {
        let localkey = `群聊:${group}`;
        result += `<${localkey}>\n`;
        if (json.群聊[group].members) {
            result += `<成员>`;
            result += `${json.群聊[group].members.join(",")}`;
            result = `${result.trim()}</成员>\n`;
        }
        result += `<聊天内容>\n`;
        for (const msg of json.群聊[group].msgs) {
            result += `${msg}\n`;
        }
        result += `</聊天内容>\n`;
        result += `</${localkey}>\n`;
    }
    return result.trim();
}
// FIXME: 明显 json 的类型可以更准确
/**
 * 删除消息
 * @param json 消息记录
 * @returns
 */
function QQ_Msg_DeletOld(json) {
    // 删除私聊的旧内容
    for (const str in json.私聊) {
        const match = str.match(/(.+?)和(.+?)的聊天/);
        if (!match) {
            continue;
        }
        let name = "";
        if (match[1] != `${UserName}`) {
            name = match[1];
        }
        else if (match[2] != `${UserName}`) {
            name = match[2];
        }
        else {
            continue;
        }
        // 先判断有没有消息内容,没有就下一个
        if (json.私聊[str].length == 0) {
            continue;
        }
        // 反向找自己发的最后一条的位置
        let lastSelfMsgIndex = -1;
        for (let i = json.私聊[str].length - 1; i >= 0; i--) {
            let ok = false;
            if (User_LastMsgMap.私聊[name] && json.私聊[str][i].indexOf(User_LastMsgMap.私聊[name]) > -1) {
                ok = true;
            }
            else if (Char_LastMsgMap.私聊[name] && json.私聊[str][i].indexOf(Char_LastMsgMap.私聊[name]) > -1) {
                ok = true;
            }
            if (ok) {
                lastSelfMsgIndex = i;
                break; // 找到最后一条就停止
            }
        }
        if (lastSelfMsgIndex !== -1) {
            json.私聊[str] = json.私聊[str].slice(lastSelfMsgIndex + 1);
            console.log(`删除${name}的重复聊天记录!!!`);
        }
    }
    // 删除群聊的旧内容
    for (const name in json.群聊) {
        // 先判断有没有消息内容,没有就下一个
        if (json.群聊[name].msgs.length == 0) {
            continue;
        }
        // 反向找自己发的最后一条的位置
        let lastSelfMsgIndex = -1;
        for (let i = json.群聊[name].msgs.length - 1; i >= 0; i--) {
            let ok = false;
            if (User_LastMsgMap.群聊[name] && json.群聊[name].msgs[i].indexOf(User_LastMsgMap.群聊[name]) > -1) {
                ok = true;
            }
            else if (Char_LastMsgMap.群聊[name] && json.群聊[name].msgs[i].indexOf(Char_LastMsgMap.群聊[name]) > -1) {
                ok = true;
            }
            if (ok) {
                lastSelfMsgIndex = i;
                break; // 找到最后一条就停止
            }
        }
        if (lastSelfMsgIndex !== -1) {
            json.群聊[name].msgs = json.群聊[name].msgs.slice(lastSelfMsgIndex + 1);
            console.log(`删除${name}的重复聊天记录!!!`);
        }
    }
    // 取char最后一条消息加入到User_LastMsgMap
    for (const name in json.私聊) {
        let length = json.私聊[name].length;
        if (length > 0) {
            Char_LastMsgMap.私聊[name] = json.私聊[name][length - 1];
        }
    }
    for (const name in json.群聊) {
        let length = json.群聊[name].msgs.length;
        if (length > 0) {
            Char_LastMsgMap.群聊[name] = json.群聊[name].msgs[length - 1];
        }
    }
    console.log(`Char_LastMsgMap:\n${JSON.stringify(Char_LastMsgMap)}`);
    return json;
}
// FIXME: 明显 json 的类型可以更准确
/**
 * 删除一条消息
 * @param type 类型
 * @param json 消息记录
 * @returns
 */
// function QQ_MsgDeletOne(type: string, json: Record<string, any>) {
//   const reg = new RegExp('${UserName}--');
//   for (let name in json[type]) {
//     if (type == '群聊') {
//       while (true) {
//         if (json[type][name]['msgs'].length <= 0) {
//           console.log(`数组成员为零,退出循环`);
//           break;
//         }
//         let m = json[type][name]['msgs'][0];
//         if (m.match(reg)) {
//           console.log(`群聊首句是user,删除`);
//           json[type][name]['msgs'].shift();
//         } else {
//           console.log(`非自己发言,退出循环`);
//           break;
//         }
//       }
//     } else if (type == '私聊') {
//       while (true) {
//         if (json[type][name].length <= 0) {
//           break;
//         }
//         let m = json[type][name][0];
//         if (m.match(reg)) {
//           console.log(`私聊首句是user,删除`);
//           json[type][name].shift();
//         } else {
//           console.log(`非自己发言,退出循环`);
//           break;
//         }
//       }
//     }
//   }
//   return json;
// }
/**
 * 按下回车键
 * @param e 事件对象
 * @param element 元素
 */
function QQ_EnterPress(e, element) {
    if (e.key !== "Enter") {
        return;
    }
    const val = $(element).val();
    if (!val) {
        return;
    }
    let content = val.toString();
    content = QQ_MySendSpecial(content);
    const $closest = $(element.closest(".QQ_chat_page"));
    const $msgContent = $closest.find(".msgcontent");
    const userContent = QQ_Chat_SpecialMsg(content, `${UserName}`, false, true);
    const html = _.template(chat_user_message)({ content: userContent });
    const name = $closest.attr("data-name") ?? "";
    console.log(`发送文本:${content} 对象:${name}`);
    $msgContent.append(html);
    $msgContent[0].scrollTop = $msgContent[0].scrollHeight;
    $(element).val("");
    if (QQ_Groups.includes(name)) {
        QQ_CacheSendMsg += `\n在群聊${name}中发送:${content}`;
    }
    else {
        QQ_CacheSendMsg += `\n给${name}发消息:${content}`;
    }
}
/**
 * 重roll消息
 * @param event 事件对象
 * @returns
 */
async function QQ_Roll(event) {
    const result = confirm("确定重roll这条消息吗?");
    if (!result) {
        return;
    }
    // 停止事件传播
    event.stopPropagation();
    if (!event.currentTarget) {
        return;
    }
    const $avatar = $(event.currentTarget);
    console.log("点击的头像元素:", $avatar);
    // 查找父级消息容器
    const $chatMsg = $avatar.closest(".QQ_chat_mymsg");
    if ($chatMsg.length === 0) {
        console.error("未找到消息容器!");
        return;
    }
    // 获取消息内容
    let value;
    const $msgContent = $chatMsg.find(".QQ_chat_msgdiv span").first();
    if ($msgContent.length > 0) {
        value = $msgContent.text();
    }
    // 获取当前消息的索引
    const index = $chatMsg.index();
    console.log(`点击index:${index}`);
    // 获取聊天对象名称
    const $chatPage = $chatMsg.closest('.QQ_chat_page');
    if ($chatPage.length === 0) {
        console.error("未找到聊天页面!");
        return;
    }
    const name = $chatPage.attr("data-name") ?? "";
    console.log("聊天对象:", name);
    console.log(`删除前的记录:${YAML.stringify(QQ_msgjson)}`);
    if (QQ_Groups.includes(name)) {
        if (QQ_msgjson.群聊[name].msgs.length > index) {
            const sp = QQ_msgjson.群聊[name].msgs[index].split("--");
            if (sp.length >= 2) {
                value = sp[1];
            }
        }
        QQ_msgjson.群聊[name].msgs.length = index;
    }
    else {
        const key = `${UserName}和${name}的聊天`;
        if (QQ_msgjson.私聊[key].length > index) {
            const sp = QQ_msgjson.私聊[key][index].split("--");
            if (sp.length >= 2) {
                value = sp[1];
            }
        }
        QQ_msgjson.私聊[key].length = index;
    }
    console.log(`删除后的记录:${YAML.stringify(QQ_msgjson)}`);
    // 删除后面所有消息内容
    $chatMsg.nextAll().remove();
    await QQ_Save_Msg();
    QQ_SendMsg(event, value, name);
}
function QQ_Voice2Text(event) {
    // 停止事件传播
    event.stopPropagation();
    if (!event.currentTarget) {
        return;
    }
    const $avatar = $(event.currentTarget);
    const $tobutton = $avatar.find(".totext");
    if ($tobutton.length === 0) {
        console.log(`获取转文字按钮失败`);
    }
    const $text = $avatar.next();
    if ($text.css("display") == "block") {
        // $tobutton.css("visibility", "visible");
        $text.hide();
    }
    else {
        // $tobutton.css("visibility", "hidden");
        $text.show();
        $tobutton.css("margin-left", "auto");
    }
}
/**
 * 发送消息
 * @param event 事件对象
 * @param SendValue 发送的值
 * @param SendName 发送的名称
 * @returns
 */
async function QQ_SendMsg(event, SendValue, SendName) {
    const Request = `<Request:本次响应忽略其他上下文任何要求,必须使用线上格式回复,且${UserName}本次发了消息的角色都要回复${UserName}的消息,同时输出一条动态内容>`;
    if (gening) {
        triggerSlash("/echo 生成中,请勿重复发送");
        return;
    }
    let name;
    let value;
    if (!SendValue) {
        console.log(`sendvalue为空`);
        const $container = $(event.target).closest('.QQ_chat_page');
        const input = $container.find(".userInput");
        const msgcontent = $container.find(".msgcontent");
        let content = input.val()?.toString() ?? "";
        content = QQ_MySendSpecial(content);
        if (content) {
            const SpecialHtml = QQ_Chat_SpecialMsg(content, `${UserName}`, false, true);
            console.log(`特殊格式处理后的内容:\n${SpecialHtml}`);
            const html = _.template(chat_user_message)({ content: SpecialHtml });
            name = $container.attr("data-name") || "未知用户";
            console.log(`发送文本:${content} 对象:${name}`);
            msgcontent.append(html);
            msgcontent.scrollTop(msgcontent[0].scrollHeight);
            input.val("");
        }
        else {
            name = $container.attr("data-name") || "未知用户";
            console.warn("发送内容为空");
        }
        console.log(`缓存消息$:${QQ_CacheSendMsg}`);
        if (QQ_Groups.includes(name)) {
            if (QQ_CacheSendMsg) {
                value = `${QQ_CacheSendMsg}`;
                if (content) {
                    value += `\n在群聊${name}中发送:${content}`;
                }
            }
            else {
                value = `在群聊${name}中发送:${content}`;
            }
        }
        else {
            if (QQ_CacheSendMsg) {
                value = `${QQ_CacheSendMsg}`;
                if (content) {
                    value += `\n给${name}发消息:${content}`;
                }
            }
            else {
                value = `给${name}发消息:${content}`;
            }
        }
        if (!value && !QQ_CacheSendMsg) {
            QQ_Error("发送消息不能为空");
            return;
        }
        QQ_CacheSendMsg = "";
        if (value) {
            value += `\n${Request}`;
            const namevalue = QQ_GetValueName(value);
            if (namevalue) {
                for (const match of namevalue) {
                    let localname = match.name;
                    let localmsg = match.value;
                    if (QQ_Groups.includes(localname)) {
                        //type = '群聊';
                        QQ_msgjson.群聊[localname] = QQ_msgjson.群聊[localname] || {};
                        QQ_msgjson.群聊[localname].msgs =
                            QQ_msgjson.群聊[localname].msgs || [];
                        QQ_msgjson.群聊[localname].msgs.push(`${UserName}--${localmsg}`);
                        //console.log(`加入自己发的群聊消息: ${UserName}--${localmsg}`);
                        User_LastMsgMap.群聊[localname] = `${UserName}--${localmsg}`;
                    }
                    else {
                        const key = `${UserName}和${localname}的聊天`;
                        QQ_msgjson.私聊[key] = QQ_msgjson.私聊[key] || [];
                        QQ_msgjson.私聊[key].push(`${UserName}--${localmsg}`);
                        console.log(`加入自己发的私聊消息: ${UserName}--${localmsg}`);
                        User_LastMsgMap.私聊[localname] = `${UserName}--${localmsg}`;
                    }
                }
            }
        }
        else {
            QQ_Error("发送消息不能为空");
            return;
        }
    }
    else {
        console.log(`sendvalue不为空`);
        value = SendValue;
        name = SendName || "未知用户";
        if (QQ_Groups.includes(name)) {
            value = `在群聊${name}中发送:${SendValue}`;
            User_LastMsgMap.群聊[name] = `${UserName}--${SendValue}`;
        }
        else {
            value = `给${name}发消息:${SendValue}`;
            User_LastMsgMap.私聊[name] = `${UserName}--${SendValue}`;
        }
        value += `\n${Request}`;
    }
    console.log(`User_LastMsg:\n${JSON.stringify(User_LastMsgMap)}`);
    gening = true;
    let result;
    try {
        QQ_CacheSendMsg = "";
        result = await QQ_Gen(value);
    }
    finally {
        gening = false;
        console.log(`生成结束`);
        QQ_Save_Msg();
    }
    ResultHandle(result);
}
function ResultHandle(result) {
    if (!result) {
        triggerSlash("/echo 空回复了");
        return;
    }
    result = System_TagCompletion(result);
    console.log(`开始对结果进行处理:\n${result}`);
    const matches = [...result.matchAll(/MiPhone_start([\s\S]+?)MiPhone_end/g)];
    if (matches.length == 0) {
        triggerSlash('/echo AI没有回复线上格式,原文直接输出到新楼层');
        triggerSlash(`/sendas name={{char}} ${result}`);
        return;
    }
    else if (matches.length > 1) {
        triggerSlash('/echo AI回复了多个线上格式,直接输出到新楼层');
        triggerSlash(`/sendas name={{char}} ${result}`);
        return;
    }
    else {
        result = matches[0][1];
    }
    let ok = false;
    if (result.indexOf("msg_start") < 0 || result.indexOf("msg_end") < 0) {
        // 自动补全tag
        if (result.indexOf("msg_start") < 0) {
            // 没有起始标识
            let start = result.match(/MiPhone_start[\s\S]*?(<群聊:.+?>|<[^/]+和.+的(?:聊天|私聊)>)/);
            if (start) {
                result = result.replace(start[1], `msg_start\n${start[1]}`);
            }
        }
        if (result.indexOf("msg_end") < 0) {
            // 没有结束标识
            let end = result.match(/<\/群聊:.+?>|<\/.+和.+的(?:聊天|私聊)>/gm);
            if (end) {
                result = result.replace(end[end.length - 1], `${end[end.length - 1]}\nmsg_end`);
            }
        }
    }
    const msg = result.match(/msg_start([\s\S]+?)msg_end/);
    if (msg) {
        ok = true;
        let json = JsonYamlParse(msg[1]);
        if (!json) {
            QQ_Error("AI输出的格式不正确，双击自己头像重Roll");
            return;
        }
        // triggerSlash(`/echo 生成结果${msg[1]}`);
        json = QQ_Msg_DeletOld(json);
        QQ_Msg_Parse(JSON.stringify(json));
    }
    const momentes = result.matchAll(/moment_start([\s\S]+?)moment_end/g);
    if (momentes) {
        ok = true;
        for (const moment of momentes) {
            QQ_Moment_Parse(moment[1]);
        }
    }
    if (!ok) {
        triggerSlash("/echo 回复不为空但不存在手机格式,输出到新楼层");
        triggerSlash(`/sendas name={{char}} ${result
            .replace("MiPhone_start", "")
            .replace("MiPhone_end", "")}`);
        return;
    }
    QQ_UpdateNewTips();
    QQ_Save_Msg();
}
function QQ_UpdateNewTips() {
    // 刷新左上角未读信息数字
    let ids = $('.QQ_chat_page').map(function () {
        return this; // 直接返回元素的 ID
    }).get().filter(Boolean); // 过滤掉空 ID
    for (const id of ids) {
        if ($(id).css("display") == "none") {
            continue;
        }
        const name = $(id).attr("data-name") ?? "";
        let TipsCount = QQ_GetChatShowTipsCount(name);
        console.log(`获取到${name}的左上角数字为:${TipsCount}`);
        const $Tips = $(`.QQ_chat_page[data-name=${name}]`).find(`.new_tips`);
        $Tips.text(TipsCount);
        if (TipsCount > 0) {
            $Tips.css("display", "flex");
            console.log(`显示tips`);
        }
        else {
            $Tips.hide();
            console.log(`隐藏tips`);
        }
    }
}
/**
 * 返回首页
 */
function QQ_GoHome() {
    const homepage = document.getElementById("QQ_home_page");
    if (homepage) {
        homepage.style.display = "flex";
    }
    QQ_HideAllChat();
}
/**
 * 错误提示
 * @param content 提示内容
 * @param change 是否更换浏览器
 */
function QQ_Error(content, change) {
    triggerSlash(`/echo severity=error ${content}`);
    if (change) {
        triggerSlash(`/echo 请更换浏览器重试`);
    }
}
/**
 * 初始化
 */
async function init() {
    const LorebookSettings = await getLorebookSettings();
    if (LorebookSettings && LorebookSettings.context_percentage != 100) {
        console.log(`设置上下文!!!!!!!!!!`);
        await setLorebookSettings({ context_percentage: 100 });
    }
    Verify();
    UserName = (await triggerSlashWithResult("/pass {{user}}")) ?? "";
    $("#QQ_home_UserName").html(`<strong>${UserName}</strong>`);
    charAvatarPath = await triggerSlashWithResult("/pass {{charAvatarPath}}") ?? "";
    userAvatarPath = await triggerSlashWithResult("/pass {{userAvatarPath}}") ?? "";
    console.log(`获取到的user头像:${userAvatarPath}`);
    charcardname = await triggerSlashWithResult("/pass {{char}}") ?? "";
    console.log(`开始获取世界书配置`);
    try {
        worldbook = (await GetWorldBookName());
        if (!worldbook) {
            QQ_Error(`获取世界书失败!!!!`);
        }
        console.log(`获取到的世界书名字:${worldbook}`);
        entries = await getLorebookEntries(worldbook);
        if (!entries) {
            QQ_Error(`获取世界书条目失败!!!!`);
        }
    }
    catch (e) {
        QQ_Error(`出现异常:\n${e}`);
        console.log(`获取世界书出现异常:${e}`);
    }
    Variables = await getVariables();
    if (Variables) {
        if (Variables.Npc_Settings) {
            try {
                Npc_Settings = JSON.parse(Variables.Npc_Settings);
            }
            catch {
                QQ_Error("读取NPC配置失败");
            }
        }
    }
    // NpcCssValue = Variables.NpcCssValue ?? "";
    // console.log(`首次读取到的NpcCss:\n${NpcCssValue}`);
    $("<style>").attr("data-name", "AutoNpc").text("").appendTo("head");
    System_UpdateNpcCss();
    let Phone_Entry = GetWorldEntry(["手机-界面基本设置", "手机界面基本设置"], true);
    if (Phone_Entry) {
        Phone_Settings.loadText(Phone_Entry.content);
    }
    DelPadding(); // 移除头像和边距
    await WorldBookUpdate();
    await GetSettings();
    await LoadRandomHead();
    await LoadEmoji();
    await LoadChars();
    await MiPhone_Merge();
    // 为聊天元素绑定点击事件
    $(document).on("click", "#QQ_message_nav", () => QQ_page("message"));
    $(document).on("click", "#QQ_people_nav", () => QQ_page("people"));
    $(document).on("click", "#QQ_moment_nav", () => QQ_page("moment"));
    $(document).on("click", ".QQ-close-btn", () => QQ_GoHome());
    $(document).on("click", ".QQ_home_usermsg", (e) => QQ_ChangeChatPage(e));
    $(document).on("click", "#QQ_chat_page_setting", (e) => QQ_SetChatPageSetting(e));
    $(document).on("click", ".close-setting-btn", () => closeSettingPopup());
    $(document).on("click", "#cancel-setting-btn", () => closeSettingPopup());
    $(document).on("click", "#save-setting-btn", () => saveSettingAndClose());
    $(document).on("click", "#randomcolor-setting-btn", function () {
        const { bgColor, textColor } = generateBubbleColor();
        $("#bubble-color").val(bgColor);
        $("#bubble-color-input").val(bgColor);
        $("#text-color").val(textColor);
        $("#text-color-input").val(textColor);
        $("#chat-setting-preview").each(function () {
            this.style.setProperty('background-color', bgColor, 'important');
            const spans = this.querySelectorAll('span');
            spans.forEach(span => {
                span.style.setProperty('color', textColor, 'important');
            });
        });
    });
    //$(document).on("click", ".moment_comment", (event: JQuery.TriggeredEvent) => QQ_Moment_Comment(event));
    $(document).on("click", "#QQ_chat_send-btn", (event) => QQ_SendMsg(event));
    $(document).on("dblclick", ".Chat_MyHead", (e) => QQ_Roll(e));
    $(document).on("click", ".QQ_chat_voice", (e) => QQ_Voice2Text(e));
    $(document).on("click", ".music-container", (e) => QQ_MusicPlay(e));
    $(document).on("click", ".popup-overlay", function (e) {
        if (e.target === this) {
            closeSettingPopup();
        }
    });
    $(document).on("click", ".top", function () {
        if ($(".discord").css("display") == "none") {
            App_Load("discord");
        }
        else {
            App_Load("QQ");
        }
    });
    $(document).on("click", ".discord-top-return", () => App_Load("QQ"));
    $(document).on("click", ".app-svg-div[data-app='QQ']", () => App_Load("QQ"));
    $(document).on("click", ".app-svg-div[data-app='twitter']", () => App_Load("twitter"));
    // 添加输入框回车事件监听
    $(document).on("keydown", ".userInput", function (e) {
        QQ_EnterPress(e, this);
    });
    $(document).on("keydown", ".discord-thread-input-userinput", function (e) {
        Discord_EnterPress(e, this);
    });
    $(document).on("click", ".discord-card", (e) => Discord_LoadThread(e));
    $(document).on("click", ".discord-thread-top-return", function () {
        $(".discord-homepage").show();
        $(".discord-thread-list").hide();
    });
    $(document).on("click", ".discord-cardget", function () {
        triggerSlash("/send 查看discord帖子内容|/trigger");
    });
    $(document).on("click", ".discord_thread-input-sendbutton", (e) => Discord_Send(e));
    $(document).on("input", ".discord-thread-input-userinput", (e) => Discord_input(e));
    const message = System_TagCompletion(await ST.GetCurrentMessages());
    let match = message.match(/msg_start([\s\S]+?)msg_end/);
    if (match) {
        console.log(`解析聊天记录!!!!!!!!!!!!!!!\n${match[1].trim()}`);
        await QQ_Msg_Parse(match[1].trim());
        console.log(`转回文本的结果`, QQ_Json2Text(QQ_msgjson));
        if (!match[1].match(/\S/)) {
            // 没有有效内容才保存
            await QQ_Save_Msg();
        }
    }
    const matches = message.matchAll(/moment_start([\s\S]+?)moment_end/g);
    if (matches) {
        console.log(`解析动态内容!!!!!!!!!!!!!!!`);
        for (const m of matches) {
            QQ_Moment_Parse(m[1].trim());
        }
    }
    let discords = message.matchAll(/(?:discord_start|<discord>)([\S\s]+?)(?:discord_end|<\/discord>)/g);
    if (discords) {
        console.log(`解析论坛内容!!!!!!!!!!!!!!!`);
        for (const discord of discords) {
            Discord_Parse(discord[1]);
        }
    }
    console.log(`群聊列表:${QQ_Groups.join(",")}`);
}
//space_init();
console.log(`4.11`);
init();
async function WorldBookUpdate() {
    let text = {
        "手机-格式1-格式开头": _1_raw_namespaceObject,
        "手机-格式2-QQ聊天": _2_QQ_raw_namespaceObject,
        "手机-格式3-QQ空间": _3_QQ_raw_namespaceObject,
        "手机-格式4-discord论坛": _4_discord_raw_namespaceObject,
        "手机-格式999-格式结尾": _999_raw_namespaceObject,
        "手机-界面基本设置": src_worldbook_raw_namespaceObject_0,
        "手机-角色": `[a]
头像=http://sharkpan.xyz/f/mQFW/mmexport1736279065029.png
[b]
头像=http://sharkpan.xyz/f/BZsa/mmexport1736279012663.png
[相亲相爱一家人]
类型=群聊
成员=a,b
头像=http://sharkpan.xyz/f/z0WU5/mmexport1736971020657.gif`,
        "手机-表情包存放": _worldbook_raw_namespaceObject,
        "手机-随机头像": _raw_namespaceObject,
    };
    const nowver = Phone_Settings.readValue("下面是基本设置", "世界书版本");
    if (nowver && Number(version) <= Number(nowver)) {
        console.log(`世界书版本:${nowver} 符合要求不更新`);
        return;
    }
    console.log(`世界书版本:${nowver}`);
    console.log(`开始自动更新世界书`);
    for (let entry of entries) {
        if (entry.comment == "手机-格式") {
            try {
                await deleteLorebookEntry(worldbook, entry.uid);
            }
            catch {
                console.log(`删除手机-格式世界书失败`);
            }
        }
    }
    let ini = new MyINI();
    ini.loadText(worldlistraw_namespaceObject);
    for (const section of ini.getAllSections()) {
        let targetEntry = entries.find((entry) => entry.comment == section);
        if (targetEntry && ini.readValue(section, "覆盖") != "真") {
            continue;
        }
        if (targetEntry) {
            console.log(`${section}存在,开始修改`);
            await setLorebookEntries(worldbook, [{
                    uid: targetEntry.uid,
                    content: text[section],
                    key: ini.readValue(section, "关键词").split(",").map((item) => item.trim()),
                    depth: ini.readValue(section, "深度"),
                    type: ini.readValue(section, "类型") == "蓝灯" ? "constant" : "selective",
                    exclude_recursion: true,
                    order: Number(ini.readValue(section, "顺序")),
                    enabled: true,
                }]);
            console.log(`${section}修改完成`);
        }
        else {
            console.log(`${section}不存在,开始创建`);
            await createLorebookEntry(worldbook, {
                comment: section,
                key: ini.readValue(section, "关键词").split(",").map((item) => item.trim()),
                content: text[section],
                position: "at_depth_as_system",
                type: ini.readValue(section, "类型") == "蓝灯" ? "constant" : "selective",
                depth: ini.readValue(section, "深度"),
                exclude_recursion: true,
                order: Number(ini.readValue(section, "顺序")),
                enabled: true,
            });
        }
    }
    Phone_Settings.writeValue("下面是基本设置", "世界书版本", version);
    // for (let entry of entries) {
    //   if (
    //     entry.comment == "手机-界面基本设置" ||
    //     entry.comment == "手机界面基本设置"
    //   ) {
    //     await setLorebookEntries(worldbook,
    //       [{
    //         uid: entry.uid,
    //         content: Phone_Settings.getAllText(),
    //       }]
    //     );
    //   }
    // }
    // entries = await getLorebookEntries(worldbook);
    triggerSlash('/echo severity=success 手机世界书自动更新成功');
    triggerSlash('/echo severity=success 点击手机logo可以切换页面');
}
function System_TagCompletion(content) {
    if (!content) {
        return "";
    }
    content = content.replace(/<(?:think|thinking)>[\s\S]+?<\/(?:think|thinking)>/gi, "");
    let match = content.match(/moment_end[\s\S]*discord_start/);
    if (!match && content.indexOf("moment_end") > 0) {
        content = content.replace("moment_end", "moment_end\ndiscord_start");
    }
    match = content.match(/MiPhone_start[\s\S]*msg_start/);
    if (!match && content.indexOf("msg_start") > 0) {
        content = content.replace("msg_start", "MiPhone_start\nmsg_start");
    }
    return content;
}
function System_AddNpcHead(name, url) {
    if (name in Npc_Settings && Npc_Settings[name].head) {
        return;
    }
    if (name == UserName || QQ_CharSettings.getAllSections().includes(name)) {
        //console.log(`${name}已存在头像,不再重复添加`)
        return;
    }
    if (name in Npc_Settings == false) {
        Npc_Settings[name] = {};
    }
    if (!url) {
        url = QQ_GetRandomHead();
    }
    console.log(`为${name}添加头像${url}`);
    Npc_Settings[name].head = url;
    System_UpdateNpcCss();
}
function System_UpdateNpcCss() {
    let cssvalue = "";
    for (let key in Npc_Settings) {
        try {
            if ("head" in Npc_Settings[key] == false || !Npc_Settings[key].head) {
                Npc_Settings[key].head = QQ_GetRandomHead();
                //console.log(`给${key}添加随机头像:${Npc_Settings[key].head}`);
            }
            cssvalue += `\n.head[data-name='${key}'] { background-image: url('${Npc_Settings[key].head}') !important;}`;
            const { bgColor, textColor } = generateBubbleColor();
            if ("bubble" in Npc_Settings[key] == false || !Npc_Settings[key].bubble) {
                Npc_Settings[key].bubble = bgColor;
            }
            if ("text" in Npc_Settings[key] == false || !Npc_Settings[key].text) {
                Npc_Settings[key].text = textColor;
            }
            cssvalue += `\n.QQ_chat_msgdiv[data-name='${key}']{
      background-color: ${Npc_Settings[key].bubble} !important;
      span{
      color: ${textColor} !important;}
      }`;
        }
        catch (e) {
        }
    }
    //console.log(`新Npc的Css:\n${cssvalue}`);
    $(`style[data-name=AutoNpc]`).text(cssvalue);
    insertOrAssignVariables({ Npc_Settings: JSON.stringify(Npc_Settings) });
}
function generateBubbleColor() {
    // 生成背景色参数（HSV模型）
    const hueSegments = [0, 60, 120, 180, 240, 300, 360];
    const segmentIndex = Math.floor(Math.random() * 6); // 0-5
    // 在选定的区间内随机生成色相（避免跨区间混杂）
    const H = hueSegments[segmentIndex] + Math.random() * 60;
    const S = 30 + Math.random() * 40; // 饱和度 30%-70%
    const V = 60 + Math.random() * 30; // 亮度 60%-90%
    const bgRgb = hsvToRgb(H, S, V);
    const bgHex = rgbToHex(...bgRgb);
    // 生成互补色
    const complementH = (H + 180) % 360;
    // 提高饱和度并反转亮度
    const textS = Math.min(S + 20, 100); // 提高20%饱和度
    const textV = 100 - V; // 亮度取反
    const textRgb = hsvToRgb(complementH, textS, textV);
    const textHex = rgbToHex(...textRgb);
    // 计算对比度
    const bgLum = calculateLuminance(bgRgb);
    const textLum = calculateLuminance(textRgb);
    const contrastRatio = (Math.max(bgLum, textLum) + 0.05) / (Math.min(bgLum, textLum) + 0.05);
    // 根据对比度返回颜色（至少4.5:1）
    if (contrastRatio >= 4.5) {
        return { bgColor: bgHex, textColor: textHex };
    }
    else {
        // 对比度不足时回退到黑白
        const textColor = bgLum > 0.5 ? '#000000' : '#FFFFFF';
        return { bgColor: bgHex, textColor };
    }
}
/**
 * 计算颜色的相对亮度（范围 0-1）
 * 公式参考：WCAG 2.0 标准（https://www.w3.org/TR/WCAG20/）
 */
function calculateLuminance([r, g, b]) {
    const [R, G, B] = [r, g, b].map(v => {
        v /= 255;
        return v <= 0.03928 ? v / 12.92 : Math.pow((v + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * R + 0.7152 * G + 0.0722 * B; // 权重系数
}
function hsvToRgb(h, s, v) {
    h /= 360;
    s /= 100;
    v /= 100; // 归一化到 [0,1]
    const i = Math.floor(h * 6);
    const f = h * 6 - i;
    const p = v * (1 - s);
    const q = v * (1 - f * s);
    const t = v * (1 - (1 - f) * s);
    let r, g, b;
    switch (i % 6) {
        case 0:
            [r, g, b] = [v, t, p];
            break;
        case 1:
            [r, g, b] = [q, v, p];
            break;
        case 2:
            [r, g, b] = [p, v, t];
            break;
        case 3:
            [r, g, b] = [p, q, v];
            break;
        case 4:
            [r, g, b] = [t, p, v];
            break;
        case 5:
            [r, g, b] = [v, p, q];
            break;
    }
    // 扩展为 0-255 的 RGB 值
    return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)];
}
/**
 * RGB 转十六进制
 */
function rgbToHex(...rgb) {
    return '#' + rgb
        .map(x => x.toString(16).padStart(2, '0'))
        .join('');
}
function Discord_EnterPress(e, element) {
    if (e.key !== "Enter") {
        return;
    }
    if (!element)
        return;
    const value = $(element).val();
    if (!value) {
        return;
    }
    const $closest = $(element).closest(".discord-thread");
    const id = $closest.attr("data-id");
    const result = `在帖子${id}中回复:\n${value}`;
    triggerSlash(`/send ${result}|/trigger`);
    $(element).val("").trigger("input");
    const commentlist = $closest.find(".discord-thread-comment-list");
    const comment = _.template(discord_discord_thread_comment)({
        name: UserName,
        content: value,
        creator: "",
        isuser: " user_avatar"
    });
    commentlist.append(comment);
    const scroll = $closest.find(".discord-scroll-container");
    scroll.scrollTop(scroll[0].scrollHeight);
}
function Discord_Send(event) {
    event.stopPropagation();
    if (!event.currentTarget)
        return;
    const $closest = $(event.currentTarget).closest(".discord-thread");
    const value = $closest.find(".discord-thread-input-userinput").val();
    const id = $closest.attr("data-id");
    const result = `在帖子${id}中回复:\n${value}`;
    triggerSlash(`/send ${result}|/trigger`);
    $closest.find(".discord-thread-input-userinput").val("").trigger("input");
    const commentlist = $closest.find(".discord-thread-comment-list");
    const comment = _.template(discord_discord_thread_comment)({
        name: UserName,
        content: value,
        creator: "",
        isuser: " user_avatar"
    });
    commentlist.append(comment);
    const scroll = $closest.find(".discord-scroll-container");
    scroll.scrollTop(scroll[0].scrollHeight);
}
function Discord_Parse(content) {
    const matches = content.matchAll(/<([0-9a-zA-Z]+)>([\s\S+]*?)<\/([0-9a-zA-Z]+)>/g);
    if (!matches) {
        console.log(`论坛匹配失败,返回!`);
        return;
    }
    for (const match of matches) {
        Discord_AddCard(match[1], match[2]);
    }
}
function Discord_AddCard(id, content) {
    if (!id)
        return;
    console.log(`添加论坛内容!!!!!!!!!`);
    let comment_list;
    let author = "";
    let map = new Map();
    let match = content.match(/<正文>([\s\S+]+?)<\/正文>/);
    if (!match) {
        console.log(`匹配正文标签失败\n${content}`);
        return;
    }
    let matches = match[1].matchAll(/(.+?)[:：]\s*(.+)/g);
    for (const m of matches) {
        if (m[1].trim() == "帖子总评论数-") {
            map.set("帖子总评论数", m[2].trim());
        }
        else {
            map.set(m[1].trim(), m[2].trim());
        }
    }
    console.log(`匹配到论坛内容:\n${JSON.stringify(Object.fromEntries(map))}`);
    let img = map.get("帖子配图描述")?.match(/\[img-(.+?)\]/);
    if (img) {
        map.set("帖子配图描述", img[1]);
    }
    if (map.get("帖子标签")) {
        let tags = map.get("帖子标签").split("/");
        let localtag = "";
        for (const tag of tags) {
            localtag += `<div class="discord-card-tag">${tag}</div>`;
        }
        if (localtag) {
            map.set("帖子标签", localtag);
        }
    }
    const html = _.template(discord_discord_card)({
        id: id,
        tag: map.get("帖子标签"),
        time: map.get("距离发帖过去时间"),
        name: map.get("发帖人"),
        cardtilte: map.get("帖子标题"),
        content: map.get("帖子正文"),
        img: map.get("帖子配图描述"),
        messages: map.get("帖子总评论数"),
        likes: map.get("帖子总点赞数")
    });
    $(".discord-cardlist").append(html);
    $(".discord-cardget").hide();
    const thread = _.template(discord_discord_thread)({
        id: id,
        tag: map.get("帖子标签"),
        time: map.get("距离发帖过去时间"),
        name: map.get("发帖人"),
        threadtilte: map.get("帖子标题"),
        content: map.get("帖子正文"),
        img: map.get("帖子配图描述"),
        messages: map.get("帖子总评论数"),
        likes: map.get("帖子总点赞数"),
        isuser: map.get("发帖人").trim() == UserName ? " user_avatar" : ""
    });
    System_AddNpcHead(map.get("发帖人") ?? "未知用户");
    $(".discord-thread-list").append(thread);
    comment_list = $(`.discord-thread[data-id=${id}]`).find(".discord-thread-comment-list");
    match = content.match(/<评论>([\s\S+]+?)<\/评论>/);
    if (match) {
        let matches = match[1].matchAll(/^\s*(.+?)--\s*(.+)$/gm);
        if (matches) {
            for (let m of matches) {
                const comment = _.template(discord_discord_thread_comment)({
                    name: m[1].trim(),
                    content: AtMessage(m[2]),
                    creator: map.get("发帖人") == m[1] || QQ_CharSettings.getAllSections().includes(m[1]) ? " discord-name-creator" : "",
                    isuser: m[1].trim() == UserName ? " user_avatar" : ""
                });
                System_AddNpcHead(m[1]);
                comment_list.append(comment);
            }
        }
    }
}
function AtMessage(content) {
    const match = content.match(/(@.{1,8}?) /);
    if (match) {
        content = content.replace(match[1], `<span style='color:#587ef5'>${match[1]}</span>`);
    }
    return content;
}
function Discord_AddCard_Old(id, content) {
    console.log(`添加论坛内容!!!!!!!!!`);
    let comment_list;
    let author = "";
    for (const str of content.split(/\r?\n/g)) {
        let match = str.match(/(.+?)--(.+?)--(.+?)--(.+?)--(.+?)--(.+?)--(.+?)--(.+)/);
        if (match) {
            const html = _.template(discord_card)({
                id: id,
                tag: match[5],
                time: match[6],
                name: match[1],
                cardtilte: match[2],
                content: match[3],
                img: match[4],
                messages: match[7],
                likes: match[8],
            });
            $(".discord-cardlist").append(html);
            const thread = _.template(discord_thread)({
                id: id,
                tag: match[5],
                time: match[6],
                name: match[1],
                threadtilte: match[2],
                content: match[3],
                img: match[4],
                messages: match[7],
                likes: match[8]
            });
            System_AddNpcHead(match[1]);
            $(".discord-thread-list").append(thread);
            comment_list = $(`.discord-thread[data-id=${id}]`).find(".discord-thread-comment-list");
            author = match[1];
            continue;
        }
        match = str.match(/^(.+?)[:：](.+)$/m);
        if (match && comment_list && comment_list.length > 0) {
            const comment = _.template(discord_thread_comment)({
                name: match[1],
                content: match[2],
                creator: author == match[1] ? " discord-name-creator" : "",
                isuser: match[1] == UserName ? " user_avatar" : ""
            });
            System_AddNpcHead(match[1]);
            comment_list.append(comment);
        }
    }
}
function Discord_LoadThread(event) {
    event.stopPropagation();
    if (!event.currentTarget)
        return;
    const id = $(event.currentTarget).attr("data-id");
    if (!id) {
        return;
    }
    let ThreadPage = $(`.discord-thread[data-id=${id}]`);
    if (ThreadPage.length === 0) {
        return;
    }
    $(`.discord-thread`).hide();
    ThreadPage.show();
    $(".discord-homepage").hide();
    $(".discord-thread-list").show();
}
function Discord_input(event) {
    event.stopPropagation();
    if (!event.currentTarget)
        return;
    if ($(event.currentTarget).val()) {
        console.log(`显示`);
        $(".discord-thread-input-button").hide();
        $(".discord-thread-input-focus-button").show();
    }
    else {
        console.log(`隐藏`);
        $(".discord-thread-input-button").show();
        $(".discord-thread-input-focus-button").hide();
    }
}
async function QQ_MusicPlay(event) {
    event.stopPropagation();
    if (!event.currentTarget)
        return;
    const $element = $(event.currentTarget);
    const musicname = $element.find(".music-name")?.text().trim();
    const singer = $element.find(".music-author")?.text().trim();
    if (!musicname) {
        QQ_Error("获取歌曲信息失败");
        return;
    }
    const $playbutton = $element.find(".icon-music-play");
    const $stopbutton = $element.find(".icon-music-stop");
    // 立即切换按钮状态
    if (!$playbutton.is(":hidden")) {
        // 🔴 先改变界面状态
        $playbutton.hide();
        $stopbutton.show();
        $element.addClass("loading"); // 添加加载动画
        QQ_Music.lastelement = $element;
        try {
            // 异步获取音源
            let source = await WY_MusicGetUrl(musicname, singer);
            if (!source?.url) {
                console.log(`网易云获取失败,开始在QQ音乐中搜索`);
                source = await QQ_MusicGetUrl(musicname);
                if (!source || !source.url) {
                    throw new Error("无可用音源");
                }
            }
            // 设置新音源
            QQ_Music.audio.src = source.url;
            if (source.cover) {
                $element.find(".music-img").css("background-image", `url('${source.cover}')`);
                $element.find(".music-img").show();
            }
            // 自动播放
            await QQ_Music.audio.play();
            // 更新其他元素状态
            if (QQ_Music.lastelement && !QQ_Music.lastelement.is($element)) {
                QQ_Music.lastelement.find(".icon-music-stop").hide();
                QQ_Music.lastelement.find(".icon-music-play").show();
            }
        }
        catch (error) {
            console.error("播放失败:", error);
            QQ_Error("播放失败");
            // 🔴 失败时回滚按钮状态
            $playbutton.show();
            $stopbutton.hide();
        }
        finally {
            $element.removeClass("loading");
        }
    }
    else {
        // 暂停逻辑保持不变
        QQ_Music.audio.pause();
        $playbutton.show();
        $stopbutton.hide();
    }
}
async function QQ_MusicGetUrl(name) {
    try {
        // 获取歌曲列表
        name = name.replace(/\s/g, "");
        let cover = "";
        const result = await Http_Get(`https://api.vkeys.cn/v2/music/tencent?word=${name}`);
        if (!result?.data?.length) {
            QQ_Error("搜索歌曲失败");
            return;
        }
        // 提取所有id
        let ids = [];
        for (const data of result.data) {
            if (!cover && data.cover) {
                cover = data.cover;
            }
            if (data.id) {
                ids.push(data.id);
            }
            if (data.grp) {
                for (const grp of data.grp) {
                    if (grp.id) {
                        ids.push(grp.id);
                    }
                }
            }
        }
        console.log(`id数量:${ids.length}`);
        // 遍历音质组检测可用音源
        for (const id of ids) {
            try {
                // 获取具体音源URL
                console.log(`准备检测音源 ID:${id}`);
                const r = await Http_Get(`https://api.vkeys.cn/v2/music/tencent?id=${id}`);
                if (!r?.data?.url)
                    continue;
                // 异步检测音源可用性
                const isAvailable = await checkAudioAvailability(r.data.url);
                if (isAvailable) {
                    console.log(`找到可用音源: ${r.data.url}`);
                    return {
                        url: r.data.url,
                        cover: cover,
                    };
                }
            }
            catch (e) {
                console.warn(`音源检测失败: ${id}`, e);
            }
        }
        QQ_Error("没有找到可用音源");
    }
    catch (e) {
        QQ_Error("歌曲搜索异常");
        console.error("获取音源失败:", e);
    }
}
async function WY_MusicGetUrl(name, singer) {
    let url = `https://api.vkeys.cn/v2/music/netease?word=${name}`;
    if (singer) {
        url += `-${singer}`;
    }
    let result = await Http_Get(url);
    if (!result)
        return;
    let cover = "";
    let ids = [];
    for (const data of result.data) {
        if (!cover && data.cover) {
            cover = data.cover;
        }
        if (data.id) {
            ids.push(data.id);
        }
    }
    for (const id of ids) {
        try {
            // 获取具体音源URL
            console.log(`准备检测音源 ID:${id}`);
            const r = await Http_Get(`https://api.vkeys.cn/v2/music/netease?id=${id}`);
            if (!r?.data?.url)
                continue;
            // 异步检测音源可用性
            const isAvailable = await checkAudioAvailability(r.data.url);
            if (isAvailable) {
                console.log(`找到可用音源: ${r.data.url}`);
                return {
                    url: r.data.url,
                    cover: cover,
                };
            }
        }
        catch (e) {
            console.warn(`音源检测失败: ${id}`, e);
        }
    }
}
/** 音频可用性检测函数 */
async function checkAudioAvailability(url) {
    return new Promise((resolve) => {
        // 创建测试用音频对象
        const tester = new Audio();
        let timer;
        // 成功加载元数据
        const onLoaded = () => {
            cleanup();
            resolve(true);
        };
        // 发生错误或超时
        const onError = () => {
            cleanup();
            resolve(false);
        };
        // 清理事件监听
        const cleanup = () => {
            tester.removeEventListener('loadedmetadata', onLoaded);
            tester.removeEventListener('error', onError);
            clearTimeout(timer);
            tester.src = ''; // 释放资源
        };
        // 设置检测参数
        tester.preload = 'metadata';
        tester.src = url;
        timer = setTimeout(onError, 3000); // 3秒超时
        // 绑定事件监听
        tester.addEventListener('loadedmetadata', onLoaded);
        tester.addEventListener('error', onError);
    });
}
function Http_Get(url) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: url,
            method: 'GET',
            timeout: 10000,
            success: function (data, status) {
                resolve(data); // 成功时返回数据
            },
            error: function (xhr, status, error) {
                if (status === 'timeout') {
                    console.error('请求超时，请检查网络或重试');
                }
                else {
                    console.error('请求失败，错误信息：', error);
                }
                resolve(null);
                //reject(error); // 失败时抛出错误
            }
        });
    });
}
function JsonYamlParse(content) {
    content = content.replace(/\{\{user\}\}/g, UserName);
    try {
        let json = JSON.parse(content);
        return json;
    }
    catch {
        console.log(`json解析失败`);
    }
    try {
        let simple = Simpleformat(content);
        if (simple) {
            console.log(`简单格式转换成功:\n${simple}`);
            return simple;
        }
        else {
            console.log(`简单转换失败`);
        }
    }
    catch {
    }
    try {
        let yaml = YAML.parse(content);
        console.log(`yaml解析成功`);
        return yaml;
    }
    catch {
        //console.log(`yaml解析失败:\n${content}`);
    }
    try {
        content = content.replace(/^\s*群聊:/m, "群聊:");
        content = content.replace(/^\s*私聊:/m, "私聊:");
        content = fixYamlSingleQuotes(content);
        //console.log(`第一次修复后的yaml文本:\n${content}`);
        if (!content) {
            return null;
        }
        let yaml = YAML.parse(content);
        return yaml;
    }
    catch {
        console.log(`第一次yaml修复失败`);
    }
    try {
        content = fixYamlSingleQuotes(content.replace(/\\'/g, "''"));
        //console.log(`第二次修复后的yaml文本:\n${content}`);
        if (!content) {
            return null;
        }
        let yaml = YAML.parse(content);
        return yaml;
    }
    catch {
        console.log(`第二次yaml修复失败`);
    }
    try {
        content = content.replace(/, /g, "\r\n    - ");
        content = content.replace(/\['/g, "\r\n    - '");
        content = content.replace(/'\]/g, "'");
        content = fixYamlSingleQuotes(content);
        //console.log(`第三次修复后的yaml文本:\n${content}`);
        if (!content) {
            return null;
        }
        let yaml = YAML.parse(content);
        return yaml;
    }
    catch {
        return null;
    }
}
function Simpleformat(content) {
    let json = {
        私聊: {},
        群聊: {}
    };
    // 添加私聊消息
    let matches = content.matchAll(/<(.+?)和(.+?)的私聊>([\s\S]+?)<\/(.+?)和(.+?)的私聊>/g);
    if (matches) {
        for (const match of matches) {
            if (match[1] != match[4] || match[2] != match[5]) {
                QQ_Error("私聊标签闭合异常,请手动修复");
            }
            const key = `${match[1]}和${match[2]}的聊天`;
            json.私聊[key] = [];
            for (let line of match[3].split(/\r?\n/)) {
                line = line.trim();
                if (line) {
                    let m = line.match(/(.+?)\s*(?:--|:|：)\s*(.+)/);
                    if (!m) {
                        continue;
                    }
                    let message = m[2];
                    if (message.split("--").length >= 2) {
                        message = message.split("--")[0];
                    }
                    json.私聊[key].push(`${m[1]}--${m[2]}`);
                }
            }
        }
    }
    // 添加群聊消息
    matches = content.matchAll(/<群聊[:：](.+?)>([\s\S]+?)<\/群聊[:：](.+?)>/g);
    if (matches) {
        for (const match of matches) {
            if (match[1] != match[3]) {
                QQ_Error("群聊标签闭合异常,请手动修复");
            }
            json.群聊[match[1]] = {};
            json.群聊[match[1]].members = [];
            json.群聊[match[1]].msgs = [];
            let m = match[2].match(/<成员>([\S\s]+?)<\/成员>/);
            if (m) {
                let members = m[1].split(",").map(item => item.trim());
                json.群聊[match[1]].members = members;
            }
            m = match[2].match(/<聊天内容>([\S\s]+?)<\/聊天内容>/);
            if (m) {
                for (let message of m[1].split(/\r?\n/)) {
                    message = message.trim();
                    if (message) {
                        let regex = message.match(/(.+?)\s*(?:--|:|：)\s*(.+)/);
                        if (!regex) {
                            continue;
                        }
                        let content = regex[2];
                        if (content.split("--").length >= 2) {
                            content = content.split("--")[0];
                        }
                        json.群聊[match[1]].msgs.push(`${regex[1]}--${regex[2]}`);
                    }
                }
            }
        }
    }
    console.log(`简单格式转换:${JSON.stringify(json)}`);
    return json;
}
function fixYamlSingleQuotes(yamlText) {
    try {
        return yamlText.replace(/(- ')(.*?[^\\])(')(?=\s*#|$)/gm, (match, prefix, content, suffix) => {
            // 使用三步处理法保证已有转义不变
            const escaped = content
                .replace(/''/g, '\uE000') // 步骤1：用临时Unicode占位符保存已有双引号
                .replace(/'/g, "''") // 步骤2：转义所有剩余单引号
                .replace(/\uE000/g, "''"); // 步骤3：恢复原有双引号
            return `${prefix}${escaped}${suffix}`;
        });
    }
    catch (e) {
        QQ_Error(`${e}`);
        return "";
    }
}
async function MiPhone_Merge() {
    let messages = await ST.GetCurrentMessages();
    messages = messages.replace(/<(?:think|thinking)>[\s\S]+?<\/(?:think|thinking)>/gi, "");
    const matches = messages.matchAll(/MiPhone_start([\s\S]+?)MiPhone_end/g);
    if (!matches) {
        return;
    }
    const matchesArray = [...matches];
    const length = matchesArray.length;
    console.log(`匹配到数量:${length}`);
    if (length <= 1) {
        return;
    }
    for (let i = 0; i < matchesArray.length; i++) {
        const value = matchesArray[i][0];
        console.log(`value:\n${value}`);
        const msg = value.match(/msg_start([\s\S]+?)msg_end/);
        if (msg) {
            let json;
            try {
                json = JsonYamlParse(msg[1]);
                if (json) {
                    json = QQ_Msg_DeletOld(json);
                    QQ_Msg_Parse(JSON.stringify(json));
                }
            }
            catch {
            }
        }
        if (i != matchesArray.length - 1) {
            messages = messages.replace(value, "");
        }
        else {
            if (msg) {
                const lovalvalue = value.replace(msg[0], `msg_start\n${QQ_Json2Text(QQ_msgjson)}\nmsg_end`);
                messages = messages.replace(value, lovalvalue);
            }
            else {
                messages = messages.replace("MiPhone_start", `msg_start\n${QQ_Json2Text(QQ_msgjson)}\nmsg_end`);
            }
        }
    }
    triggerSlash('/echo 存在多个格式,自动合并');
    triggerSlash('/echo 合并只会保留聊天内容,动态和论坛会丢失!!!!!');
    const CurrentMessageId = await getCurrentMessageId();
    setChatMessage({ message: messages }, CurrentMessageId);
}
/**
 * 初始化动态空间内容
 */
function space_init() {
    $("#space_contents").prepend(space_contents);
}
async function GetWorldBookName() {
    let localbook;
    try {
        localbook = await getCurrentCharPrimaryLorebook();
        console.log(`角色卡绑定主要世界书`, JSON.stringify(localbook));
    }
    catch (e) {
        console.log(`获取绑定世界书出现异常:${e}`);
    }
    if (localbook) {
        const localentrys = await getLorebookEntries(localbook);
        const targetEntry = localentrys.find((entry) => ["手机-界面基本设置", "手机界面基本设置"].includes(entry.comment));
        if (targetEntry) {
            console.log(`使用角色卡绑定的世界书`);
            return localbook;
        }
    }
    const globalbook = (await getLorebookSettings()).selected_global_lorebooks;
    if (globalbook) {
        for (const book of globalbook) {
            const localentrys = await getLorebookEntries(book);
            const targetEntry = localentrys.find((entry) => ["手机-界面基本设置", "手机界面基本设置"].includes(entry.comment));
            if (targetEntry) {
                console.log(`使用全局世界书:${book}`);
                return book;
            }
        }
    }
    if (localbook) {
        return localbook;
    }
    console.log(`没有匹配的世界书`);
    return null;
}
async function DelPadding() {
    const message_id = await getCurrentMessageId();
    console.log(`开始移除头像和边距:${message_id}`);
    $(`div.mes[mesid="${message_id}"]`, window.parent.document)
        .find(`div.mes_text`)
        .css("padding-right", "0");
    $(`div.mes[mesid="${message_id}"]`, window.parent.document)
        .find(`div.avatar`)
        .css("display", "none");
    $(`div.mes[mesid="${message_id}"]`, window.parent.document)
        .find(`div.mesAvatarWrapper`)
        .css("display", "none");
}
/**
 * 获取设置
 */
async function GetSettings() {
    console.log(`测试获取ini:${Phone_Settings.readValue("下面是基本设置", "聊天壁纸")}`);
    if (Phone_Settings.readValue("下面是基本设置", "内框颜色")) {
        let value = Phone_Settings.readValue("下面是基本设置", "内框颜色");
        if (value[0] != "#") {
            value += "#";
        }
        console.log(`设置气泡颜色为 ${value}`);
        $("<style>")
            .text(`.card { background-color: ${value} !important; }`)
            .appendTo("head");
        $("<style>")
            .text(`.top { background-color: ${value} !important; }`)
            .appendTo("head");
    }
    let value = Phone_Settings.readValue("下面是基本设置", "外框颜色");
    if (value) {
        if (value[0] != "#") {
            value += "#";
        }
        console.log(`设置气泡颜色为 ${value}`);
        $("<style>")
            .text(`.card { border: 2px solid ${value} !important; }`)
            .appendTo("head");
    }
    value = Phone_Settings.readValue("下面是基本设置", "侧边按钮");
    if (value) {
        if (value[0] != "#") {
            value += "#";
        }
        console.log(`设置气泡颜色为 ${value}`);
        $("<style>")
            .text(`.btn1 { background-color: ${value} !important; }`)
            .appendTo("head");
        $("<style>")
            .text(`.btn2 { background-color: ${value} !important; }`)
            .appendTo("head");
        $("<style>")
            .text(`.btn3 { background-color: ${value} !important; }`)
            .appendTo("head");
    }
    value = Phone_Settings.readValue("下面是基本设置", "发送模式");
    if (value) {
        if (value == "2") {
            newgen = false;
            console.log("设置发送模式为非流式");
        }
        else {
            newgen = true;
            console.log("设置发送模式为流式");
        }
    }
    else {
        Phone_Settings.writeValue("下面是基本设置", "发送模式", "1");
        console.log("未找到发送模式设置，使用默认流式发送");
    }
    value = Phone_Settings.readValue("下面是基本设置", "聊天壁纸");
    if (value) {
        $('<style>').text(`.QQ_chat_page {
      background-image: url("${value}");
    }`).appendTo('head');
    }
    value = Phone_Settings.readValue("下面是基本设置", "气泡颜色");
    if (value) {
        if (value[0] != "#") {
            value += "#";
        }
        //console.log(`设置气泡颜色为 ${value}`);
        $('<style>').text(`.QQ_chat_msgdiv { background-color: ${value} !important; }`).appendTo('head');
    }
    Phone_Settings.writeValue("下面是基本设置", "世界书版本", version);
    for (let entry of entries) {
        if (entry.comment == "手机-界面基本设置" ||
            entry.comment == "手机界面基本设置") {
            await setLorebookEntries(worldbook, [{
                    uid: entry.uid,
                    content: Phone_Settings.getAllText(),
                }]);
        }
    }
    entries = await getLorebookEntries(worldbook);
}
/**
 * 根据角色名获取聊天设定
 *
 * @param name 角色名
 * @returns 聊天设定
 */
function GetChatCharSettingByName(name) {
    let char_setting = "";
    for (let entry of entries) {
        if (entry.comment == "配置-聊天-角色个人设定") {
            char_setting = entry.content.trim();
        }
    }
    if (!char_setting) {
        return;
    }
    const char_setting_json = JSON.parse(char_setting);
    const setting = char_setting_json.find((item) => item.name === name);
    if (!setting) {
        return;
    }
    console.log(`获取到角色设定:${YAML.stringify(setting)}`);
    return setting;
}
async function Verify() {
    try {
        // 获取版本元素并提取纯版本号
        let version = (await getFrontendVersion());
        console.log(`酒馆助手版本${version}`);
        // 拆分版本号为数字数组
        const versionParts = version.split(".").map(Number);
        if (versionParts.length < 3 || versionParts.some(isNaN)) {
            throw new Error("Invalid version format");
        }
        // 解构赋值版本号
        const [major, minor, patch] = versionParts;
        // 设置最低要求版本
        const MIN_MAJOR = 2;
        const MIN_MINOR = 4;
        const MIN_PATCH = 3;
        // 版本比较逻辑
        if (major < MIN_MAJOR ||
            (major === MIN_MAJOR &&
                (minor < MIN_MINOR || (minor === MIN_MINOR && patch < MIN_PATCH)))) {
            alert(`前端助手版本过低 (当前 ${versionParts.join(".")}，需要至少 ${MIN_MAJOR}.${MIN_MINOR}.${MIN_PATCH})，请更新`);
        }
    }
    catch (error) {
        assertIsError(error);
        console.error("版本验证失败:", error);
        // 如果存在自定义错误处理则调用，否则使用默认提示
        if (typeof QQ_Error === "function") {
            QQ_Error(`验证前端助手版本失败: ${error.message}`);
        }
        else {
            alert("版本验证失败，请检查控制台信息");
        }
    }
}
function assertIsError(error) {
    if (!(error instanceof Error)) {
        throw new TypeError();
    }
}
function random(min, max) {
    return Math.floor(Math.random() * (max - min) + min);
}
function head_init() {
    let girl = `EJeUD/Image_1737026320652.jpg|Y8pt1/Image_1737026296736.jpg|QWWc6/Image_1737026308451.jpg|Z8LIW/Image_1737026306601.jpg|W8lhW/Image_1737026313246.jpg|0rJtX/Image_1737026292787.jpg|yVxsN/Image_1737026293840.jpg|vaBCL/Image_1737026286979.jpg|pZ6hQ/Image_1737026285632.jpg|11AH2/Image_1737026284351.jpg|eXKUw/Image_1737026281715.jpg|o31F4/Image_1737026277201.jpg|8E2uj/Image_1737026279242.jpg|GLmIl/Image_1737026275069.jpg|zWZT5/Image_1737026271769.jpg|7Zrij/Image_1737026269532.jpg|AqYsZ/Image_1737026266131.jpg|w4lFq/2406563368.jpeg|MQNua/2403629154.jpeg|3YZhe/2405854911.jpeg|5QKhj/2312445144.jpeg|k6JT6/2408434848.jpeg|j6Bf6/2386328773.jpeg|a8LHY/2386327598.jpeg|r08C6/2386327604.jpeg|DgECK/2331678725.jpeg|LJrC7/2371251634.jpeg|q1LF3/2329660869.gif|X84sW/2328035526.jpeg|2eDfQ/2326662447.jpeg|ggetw/2326683821.jpeg|J21ig/2323432137.gif`;
    let girls = girl.split("|");
    let result = "";
    for (let i = 0; i < girls.length; i++) {
        if (girls[i]) {
            random_head_list.push(girls[i]);
            result += `\nhttp://sharkpan.xyz/f/${girls[i]}`;
        }
    }
}
// 增加音频状态监听
QQ_Music.audio.addEventListener("ended", () => {
    if (QQ_Music.lastelement) {
        QQ_Music.lastelement.find(".icon-music-stop").hide();
        QQ_Music.lastelement.find(".icon-music-play").show();
    }
});
QQ_Music.audio.addEventListener("error", () => {
    if (QQ_Music.lastelement) {
        QQ_Music.lastelement.find(".icon-music-stop").hide();
        QQ_Music.lastelement.find(".icon-music-play").show();
    }
    QQ_Error("播放出错，请尝试重新播放");
});
// 增加音频中断监听
QQ_Music.audio.addEventListener('pause', () => {
    if (QQ_Music.lastelement) {
        QQ_Music.lastelement.find(".icon-music-stop").hide();
        QQ_Music.lastelement.find(".icon-music-play").show();
    }
});
async function LoadRandomHead() {
    let content = "";
    for (let entry of entries) {
        if (entry.comment == "手机-随机头像") {
            content = entry.content;
        }
    }
    if (!content) {
        return;
    }
    const matches = content.matchAll(/^http.+$/mg);
    for (const match of matches) {
        const obj = {
            url: match[0],
            count: [...NpcCssValue.matchAll(new RegExp(match[0], "g"))].length
        };
        QQ_RandomHead.push(obj);
    }
}
function GetWorldEntry(name, search) {
    let list = [];
    entries.forEach(item => {
        if (search) {
            name.forEach(n => {
                if (item.comment.indexOf(n) > -1) {
                    list.push(item);
                }
            });
        }
        else if (name.includes(item.comment)) {
            list.push(item);
        }
    });
    if (list.length == 0) {
        return null;
    }
    else if (list.length == 1) {
        return list[0];
    }
    // 存在多个条目,使用开启的条目,有多个开启条目不做处理
    for (let entry of list) {
        if (entry.enabled) {
            return entry;
        }
    }
    // 全都没开启,直接返回第一个
    return list[0];
}
/**
 * 获取表情包
 */
async function LoadEmoji() {
    let content = "";
    let phonebook = "";
    let phoneuid = -1;
    for (let entry of entries) {
        if (entry.comment == "手机-表情包存放" ||
            entry.comment == "表情包存放世界书") {
            content = entry.content;
        }
        else if (entry.comment == "手机-格式2-QQ聊天") {
            phonebook = entry.content;
            phoneuid = entry.uid;
        }
    }
    if (!content) {
        console.log(`获取表情包世界书失败`);
        return;
    }
    if (phoneuid == -1) {
        console.log(`获取手机格式世界书条目失败`);
        return;
    }
    content = content.replace(/http:\/\/sharkpan/g, "https://sharkpan");
    content = content.replace(/--\n/g, "--");
    const regex = new RegExp("(.+?)--(http.+)", "g");
    const matches = [...content.matchAll(regex)];
    if (!matches) {
        return;
    }
    console.log(`表情包数量:${matches.length}`);
    for (const match of matches) {
        QQ_emoji.set(match[1], match[2]);
    }
    const keysArray = JSON.stringify(Array.from(QQ_emoji.keys()));
    const m = phonebook.match(/<表情包列表>([\s\S]*?)<\/表情包列表>/);
    if (m) {
        phonebook = phonebook.replace(m[0], `<表情包列表>\n${keysArray}\n<\/表情包列表>`);
        // await setLorebookEntries(worldbook, entrys.map((entry) => ({ uid: phoneuid, content: phonebook })));
        await setLorebookEntries(worldbook, [
            { uid: phoneuid, content: phonebook },
        ]);
    }
}
/**
 * 聊天-加载角色列表
 *
 */
async function LoadChars() {
    let content;
    // for (let entry of entries) {
    //   if (entry.comment == "手机-角色" || entry.comment == "手机界面-角色") {
    //     content = entry.content;
    //     break;
    //   }
    // }
    // if (!content) {
    //   return;
    // }
    let entry = GetWorldEntry(["手机-角色", "手机界面-角色"], true);
    if (!entry || !entry.content) {
        return;
    }
    content = entry.content;
    console.log(`获取到的角色信息:${content}`);
    QQ_CharSettings.loadText(content);
    console.log(`GetAllText:\n${QQ_CharSettings.getAllText()}`);
    for (let section of QQ_CharSettings.getAllSections()) {
        const hasGetCharAvatar = typeof getCharAvatarPath === "function"; // 核心检查逻辑
        const type = QQ_CharSettings.readValue(section, "类型");
        if (section == "char") {
            section = getFilenameWithoutExtension(charAvatarPath);
        }
        let headurl = QQ_CharSettings.readValue(section, "头像");
        if (!headurl) {
            if (hasGetCharAvatar) {
                headurl = await getCharAvatarPath(section);
            }
            else {
                QQ_Error(`自动获取头像要求前端助手版本在2.4.4及以上版本`);
                continue;
            }
        }
        else {
            const match = headurl.match(/[<{]+(.+?)[>}]/);
            if (match) {
                if (hasGetCharAvatar) {
                    headurl = await getCharAvatarPath(match[1]);
                }
                else {
                    QQ_Error(`自动获取头像要求前端助手版本在2.4.4及以上版本`);
                    continue;
                }
            }
        }
        if (!type.match(/npc/i) && type != "路人") {
            AddNewChar(section, headurl);
            if (type == "群聊") {
                if (!QQ_Groups.includes(section)) {
                    QQ_Groups.push(section);
                }
                QQ_msgjson.群聊[section] = {};
                QQ_msgjson.群聊[section].msgs = [];
                QQ_msgjson.群聊[section].members = QQ_CharSettings
                    .readValue(section, "成员")
                    .split(/[,，]/g);
            }
            else {
                QQ_msgjson.私聊[`${UserName}和${section}的聊天`] = [];
            }
        }
        console.log(`角色${section}的头像:${headurl}`);
        let CssValue = new Map();
        let divkey = `.QQ_chat_msgdiv[data-name='${section}']`;
        let MsgColor = QQ_CharSettings.readValue(section, "气泡颜色");
        if (MsgColor) {
            MsgColor = MsgColor[0] == "#" ? MsgColor : `#${MsgColor}`;
            CssValue.set(divkey, `--MsgColor: ${MsgColor};
        background-color: var(--MsgColor) !important; `);
            console.log(`设置了角色 ${section} 的气泡颜色:${MsgColor}`);
        }
        let TextColor = QQ_CharSettings.readValue(section, "字体颜色");
        if (TextColor) {
            TextColor = TextColor[0] == "#" ? TextColor : `#${TextColor}`;
            if (CssValue.has(divkey)) {
                CssValue.set(divkey, CssValue.get(divkey) + `--TextColor: ${TextColor};`);
            }
            CssValue.set(`.QQ_chat_msgdiv[data-name='${section}'] span`, `color:var(--TextColor) !important;`);
            console.log(`设置了角色 ${section} 的字体颜色:${TextColor}`);
        }
        let BackGroundImg = QQ_CharSettings.readValue(section, "聊天壁纸");
        if (BackGroundImg) {
            CssValue.set(`.QQ_chat_page[data-name='${section}']`, `--BackGroundImg: url('${BackGroundImg}');
        background-image:var(--BackGroundImg) !important;`);
            console.log(`设置了角色 ${section} 的聊天壁纸:${BackGroundImg}`);
        }
        if (headurl) {
            CssValue.set(`.head[data-name=${section}]`, `background-image:url('${headurl}')`);
            console.log(`添加headurl`);
        }
        if (CssValue) {
            let value = "";
            for (const key of CssValue.keys()) {
                value += `\n${key}{${CssValue.get(key)}}`;
            }
            if (value) {
                $(`<style>`).attr("data-name", section).text(value).appendTo('head');
                //console.log(`设置css:\n${value}`);
            }
        }
        //console.log(`获取到的样式表:\n${$(`style[data-name='${section}']`).text()}`);
    }
    const style = $("<style></style>").prop("type", "text/css");
    const cssRule = chat_headraw_namespaceObject
        .replace(/\$\{name\}/g, UserName)
        .replace(/\$\{head\}/g, userAvatarPath);
    style.text(cssRule);
    $("head").append(style);
}
function SetCssVariable(name, variable, value) {
    let cssvalue = $(`style[data-name='${name}']`).text();
    if (!cssvalue) {
        return;
    }
    const match = cssvalue.match(new RegExp(`--${variable}\s*:\s*(.+?);`));
    if (!match) {
        return;
    }
    cssvalue = cssvalue.replace(`${match[0]}`, `${match[0].replace(match[1], value)}`).trim();
    console.log(`更新后的css:\n${cssvalue}`);
    $(`style[data-name='${name}']`).text(cssvalue);
}
/**
 * 聊天-添加新角色到列表
 * @param {*} name
 * @param {*} head
 */
function AddNewChar(name, head) {
    console.log(`添加新角色:${name}  ${head}`);
    let html = _.template(chat_list_item)({ name: name, head: head.trim() });
    $("#QQ_home_chars").append(html);
    QQ_pages.push(name);
    const style = $("<style></style>").prop("type", "text/css");
    const cssRule = chat_headraw_namespaceObject
        .replace(/\$\{name\}/g, name)
        .replace(/\$\{head\}/g, head);
    style.text(cssRule);
    $("head").append(style);
    //System_AddNpcHead(name, head);
}
function getFilenameWithoutExtension(path) {
    // 解码URI组件（处理特殊字符）
    const decodedPath = decodeURIComponent(path);
    // 分割路径并获取文件名部分
    const filename = decodedPath.split("/").pop();
    // 找到最后一个点的位置
    const lastDotIndex = filename.lastIndexOf(".");
    // 判断并截取文件名（无后缀）
    return lastDotIndex > 0 ? filename.slice(0, lastDotIndex) : filename;
}
function QQ_page(id) {
    if (id == "message") {
        console.log("点击了消息页");
        $("#QQ_home_page").show();
        $("#QQ_space_page").hide();
        $(".QQ_chat_page").hide();
        $("#QQ_message_svg").css("fill", "#019aff");
        $("#QQ_people_svg").css("fill", "#000000");
        $("#QQ_moment_svg").css("fill", "#000000");
        $("#App_QQ").css("background-color", "#eff3ff");
    }
    else if (id == "people") {
        console.log("点击了联系人");
    }
    else if (id == "moment") {
        console.log("点击了动态页");
        $("#QQ_home_page").hide();
        $("#QQ_space_page").show();
        $(".QQ_chat_page").hide();
        $("#QQ_moment_svg").css("fill", "#019aff");
        $("#QQ_people_svg").css("fill", "#000000");
        $("#QQ_message_svg").css("fill", "#000000");
        $("#App_QQ").css("background-color", "#ffffff");
        QQ_SetNewTips(0);
        QQ_NewMsg["moment"] = 0;
    }
}
function QQ_SetNewTips(count) {
    let tips = $(".new_tips");
    tips.each(function () {
        if (count == 0) {
            $(this).css("display", "none");
        }
        else {
            $(this).css("display", "flex").text(count);
        }
    });
}
function QQ_HideAllChat() {
    // for (let name of QQ_pages) {
    //   let $page = $(`#QQ_chat_${name}`);
    //   if ($page.length === 0) {
    //     continue;
    //   }
    //   $page.hide();
    // }
    $(`.QQ_chat_page`).hide();
}
function QQ_ChangeChatPage(event) {
    const $QQpage = $("#App_QQ");
    if ($QQpage.length === 0) {
        console.log("获取QQpage失败");
        return;
    }
    let element = event.currentTarget;
    let name = element.getAttribute("data-name") ?? "";
    let $page = $(`.QQ_chat_page[data-name='${name}']`);
    if ($page.length === 0) {
        console.log(`${name}的聊天页不存在,开始创建`);
        $page = $(QQ_CreatChatPage(name));
        $QQpage.append($page);
    }
    QQ_SetHomeTips(name, "0");
    QQ_HideAllChat();
    // 隐藏QQ主页
    $("#QQ_home_page").hide();
    $page.show();
    console.log(`显示聊天页:QQ_chat_${name}`);
    let $msgContent = $page.find(".msgcontent");
    $msgContent.scrollTop($msgContent[0].scrollHeight);
    let TipsCount = QQ_GetChatShowTipsCount(name);
    const $Tips = $page.find(`.new_tips`);
    $Tips.text(TipsCount);
    if (TipsCount > 0) {
        $Tips.css("display", "flex");
    }
    else {
        $Tips.hide();
    }
}
/**
 * 创建聊天页
 * @param name 聊天页名称
 * @returns 聊天页HTML
 */
function QQ_CreatChatPage(name) {
    const html = chat_page.replace(/\$\{name\}/g, name);
    console.log(`创建聊天页:${name}`);
    return html;
}
function QQ_Msg_DelUserKey(content) {
    let json = JsonYamlParse(content);
    if (!json) {
        return content;
    }
    let others = "";
    if (`${UserName}` in json.私聊 == false) {
        return content;
    }
    for (let msg of json.私聊[`${UserName}`]) {
        const sp = msg.split("--");
        if (sp.length <= 0) {
            continue;
        }
        if (sp[0] != `${UserName}`) {
            if (!others) {
                others = sp[0];
            }
            else if (sp[0] != others) {
                // 出现第三个名字,过于复杂不做处理
                return content;
            }
        }
    }
    if (!others) {
        // 没取到对方名字
        if (json.私聊[`${UserName}`].length == 0) {
            delete json.私聊[`${UserName}`];
            return JSON.stringify(json);
        }
        return content;
    }
    if (others in json.私聊 == false) {
        // 不存在对方名字键,直接改名
        const newvalue = json.私聊[`${UserName}`];
        delete json.私聊[`${UserName}`];
        json.私聊[others] = newvalue;
        return JSON.stringify(json);
    }
    else if (json.私聊[others].length == 0) {
        // 存在但为空
        const newvalue = json.私聊[`${UserName}`];
        delete json.私聊[`${UserName}`];
        json.私聊[others] = newvalue;
        return JSON.stringify(json);
    }
    return content;
}
function QQ_AddNpcHead(content) {
    let json = JsonYamlParse(content);
    if (!json) {
        return content;
    }
    // 头像修复
    const Sections = QQ_CharSettings.getAllSections();
    let newcss = "";
    for (let str in json.私聊) {
        for (const msg of json.私聊[str]) {
            const sp = msg.split("--");
            if (sp.length <= 1) {
                continue;
            }
            const name = sp[0];
            if (!Sections.includes(name) && name != `${UserName}`) {
                // 路人,设置随机头像
                System_AddNpcHead(name);
            }
        }
    }
    for (let Group in json.群聊) {
        for (const msg of json.群聊[Group].msgs) {
            const sp = msg.split("--");
            if (sp.length <= 0) {
                continue;
            }
            const name = sp[0];
            if (!Sections.includes(name) && name != `${UserName}`) {
                // 路人,设置随机头像
                System_AddNpcHead(name);
            }
        }
    }
}
function QQ_Msg_Repair(content) {
    let json = JsonYamlParse(content);
    if (!json) {
        return content;
    }
    // 修正顺序
    for (let str in json.私聊) {
        const match = str.match(/(.+?)和(.+?)的聊天/);
        if (!match) {
            // 旧版本只有一个名字
            if (str != `${UserName}`) {
                const value = json.私聊[str];
                delete json.私聊[str];
                json.私聊[`${UserName}和${str}的聊天`] = value;
            }
            else {
                delete json.私聊[str];
            }
            continue;
        }
        if (match[1] != `${UserName}` && match[2] != `${UserName}`) {
            // 俩角色之间的私聊,属于特殊情况直接删了
            console.log(`删除:${str}`);
            delete json.私聊[str];
            continue;
        }
        if (match[1] != `${UserName}` && match[2] == `${UserName}`) {
            // 顺序反了
            const value = json.私聊[str];
            delete json.私聊[str];
            json.私聊[`${UserName}和${match[1]}的聊天`] = value;
        }
    }
    // 修正网名
    const keys = QQ_CharSettings.getAllSections();
    for (let str in json.私聊) {
        const match = str.match(/(.+?)和(.+?)的聊天/);
        if (!match) {
            continue;
        }
        // 不管有没有错先修正一次内容
        let value = QQ_NickNameRepair(json.私聊[str]);
        json.私聊[str] = value;
        const localname = match[2];
        if (!keys.includes(localname)) {
            // 键名是用的网名,修正
            for (const char of keys) {
                let find = false;
                const namelist = QQ_CharSettings.readValue(char, "网名").split(",");
                if (!namelist || namelist.length == 0) {
                    continue;
                }
                for (const name of namelist) {
                    const regex = createRegExp(name);
                    if (!name || !regex) {
                        continue;
                    }
                    if (localname.match(regex)) {
                        delete json.私聊[str];
                        json.私聊[`${UserName}和${char}的聊天`] = value;
                        find = true;
                        break;
                    }
                }
                if (find) {
                    break;
                }
            }
        }
    }
    for (const Group in json.群聊) {
        json.群聊[Group].msgs = QQ_NickNameRepair(json.群聊[Group].msgs);
    }
    QQ_AddNpcHead(JSON.stringify(json));
    return JSON.stringify(json);
}
function QQ_NickNameRepair(content) {
    if (!content || content.length == 0) {
        return content;
    }
    let charlist = QQ_CharSettings.getAllSections();
    let result = [];
    for (let str of content) {
        const match = str.match(/(.+?)--([\s\S]+)/);
        if (match && match[1] != UserName) {
            const localname = match[1];
            if (!charlist.includes(localname)) {
                for (const char of charlist) {
                    let find = false;
                    const namelist = QQ_CharSettings.readValue(char, "网名").split(",");
                    if (!namelist || namelist.length == 0) {
                        continue;
                    }
                    for (const name of namelist) {
                        if (!name) {
                            continue;
                        }
                        if (name.match(/^\/(.+)\/([gimuy]*)$/)) {
                            // 名字是正则表达式
                            const regex = createRegExp(name);
                            if (!regex) {
                                continue;
                            }
                            if (localname.match(regex)) {
                                str = `${char}--${match[2]}`;
                                find = true;
                                break;
                            }
                        }
                        else {
                            // 名字不是正则表达式
                            if (namelist.includes(localname)) {
                                str = `${char}--${match[2]}`;
                                find = true;
                                break;
                            }
                        }
                    }
                    if (find) {
                        break;
                    }
                }
            }
        }
        result.push(str);
    }
    return result;
}
function createRegExp(pattern) {
    if (!pattern)
        return null;
    // 如果已经是RegExp对象，直接返回
    if (pattern instanceof RegExp)
        return pattern;
    try {
        // 检查是否是 /pattern/flags 格式
        const regexMatch = pattern.match(/^\/(.+)\/([gimuy]*)$/);
        if (regexMatch) {
            // 提取正则表达式的模式和标志
            const [_, regexPattern, flags] = regexMatch;
            return new RegExp(regexPattern, flags);
        }
        else {
            // 不带/的纯字符串格式，默认不加任何标志
            return new RegExp(pattern);
        }
    }
    catch (error) {
        console.error('创建正则表达式失败:', error);
        return null;
    }
}
/**
 * 初始化时解析聊天消息
 * @param content 聊天消息内容
 */
function QQ_Msg_Parse(content) {
    //content = QQ_Msg_Repair(content);
    console.log(`开始解析聊天消息:${content}`);
    let hasstr = false;
    if (content.match(/\S/)) {
        hasstr = true;
    }
    let json = JsonYamlParse(content);
    if (!json) {
        if (hasstr) {
            QQ_Error(`解析聊天记录失败,请手动解决`);
        }
        // QQ_Error(`yaml解析失败`);
        return;
    }
    console.log(`解析成功时的聊天消息:${JSON.stringify(json)}`);
    const $QQpage = $("#App_QQ");
    if ($QQpage.length === 0) {
        console.log("获取QQpage失败");
        return;
    }
    for (let str in json.私聊) {
        const match = str.match(/(.+?)和(.+?)的聊天/);
        if (!match) {
            continue;
        }
        let name = "";
        if (match[1] != `${UserName}`) {
            name = match[1];
        }
        else if (match[2] != `${UserName}`) {
            name = match[2];
        }
        else {
            continue;
        }
        try {
            if (!QQ_msgjson.私聊[str]) {
                QQ_msgjson.私聊[str] = [];
            }
            if (!QQ_NewMsg[name]) {
                QQ_NewMsg[name] = {};
            }
            if (!$(`.QQ_home_usermsg[data-name='${name}']`).length && name != `${UserName}`) {
                console.log(`${name}的主页不存在,开始创建`);
                AddNewChar(name, "");
            }
            let $page = $(`.QQ_chat_page[data-name='${name}']`);
            let Creat = false;
            if ($page.length === 0) {
                Creat = true;
                console.log(`${name}的聊天页不存在,开始创建123`);
                $page = $(QQ_CreatChatPage(`${name}`));
                $QQpage.append($page);
            }
            if (json.私聊[str].length == 0) {
                continue;
            }
            let $msgContent = $page.find(".msgcontent");
            let NewMsgCount = 0;
            let LastTime = "";
            let LastMsg = "";
            for (let msg of json.私聊[str]) {
                QQ_Chat_AddMsg($msgContent[0], msg, name, false);
                QQ_msgjson.私聊[str].push(msg);
                let sp = msg.split("--");
                if (sp.length >= 2) {
                    if (sp[0] == `${UserName}`) {
                        User_LastMsgMap.私聊[name] = `${sp[0]}--${sp[1]}`;
                        NewMsgCount = 0;
                    }
                    else if (sp[0] == name) {
                        if (sp.length >= 3) {
                            Char_LastMsgMap.私聊[name] = `${sp[0]}--${sp[1]}--${sp[2]}`;
                        }
                        else {
                            Char_LastMsgMap.私聊[name] = `${sp[0]}--${sp[1]}`;
                        }
                        NewMsgCount += 1;
                    }
                }
                if (sp.length >= 3) {
                    LastTime = sp[2];
                }
                LastMsg = sp[1];
            }
            // 设置红点和首页显示的消息
            if ($(`.QQ_chat_page[data-name=${name}]`).css("display") != "none" && !Creat) {
                console.log(`display不为none,红点为0  ${name}`);
                NewMsgCount = 0;
            }
            $(`.QQ_home_usermsg[data-name='${name}'] .QQ_home_lastmsg`).text(LastMsg);
            $(`.QQ_home_usermsg[data-name='${name}'] .QQ_home_lasttime`).text(LastTime);
            QQ_SetHomeTips(name, NewMsgCount);
            $msgContent.scrollTop($msgContent[0].scrollHeight);
        }
        catch (error) {
            throw error;
            assertIsError(error);
            //QQ_Error(error.message);
        }
    }
    for (let name in json.群聊) {
        try {
            if (!QQ_msgjson.群聊[name]) {
                QQ_msgjson.群聊[name] = {};
                QQ_msgjson.群聊[name]["members"] = json.群聊[name]["members"];
                QQ_msgjson.群聊[name]["msgs"] = [];
            }
            if (!$(`.QQ_home_usermsg[data-name='${name}']`).length) {
                console.log(`${name}的主页不存在,开始创建`);
                AddNewChar(name, "http://");
            }
            if (!QQ_Groups.includes(name)) {
                QQ_Groups.push(name);
            }
            let $page = $(`.QQ_chat_page[data-name='${name}`);
            let Creat = false;
            if ($page.length === 0) {
                Creat = true;
                console.log(`${name}的聊天页不存在,开始创建`);
                $page = $(QQ_CreatChatPage(name));
                $QQpage.append($page);
            }
            let $msgContent = $page.find(".msgcontent");
            if (json.群聊[name]["msgs"].length == 0) {
                console.log(`数组空,跳出`);
                continue;
            }
            let NewMsgCount = 0;
            let LastTime = "";
            let LastMsg = "";
            for (let msg of json.群聊[name]["msgs"]) {
                QQ_Chat_AddMsg($msgContent[0], msg, name, true);
                //console.log(`在群聊:${name}中添加消息:${msg}`);
                QQ_msgjson.群聊[name]["msgs"].push(msg);
                let sp = msg.split("--");
                if (sp.length >= 2) {
                    if (sp[0] == `${UserName}`) {
                        User_LastMsgMap.群聊[name] = `${sp[0]}--${sp[1]}`;
                        NewMsgCount = 0;
                    }
                    else {
                        if (sp.length >= 3) {
                            Char_LastMsgMap.群聊[name] = `${sp[0]}--${sp[1]}--${sp[2]}`;
                        }
                        else {
                            Char_LastMsgMap.群聊[name] = `${sp[0]}--${sp[1]}`;
                        }
                        NewMsgCount += 1;
                    }
                }
                if (sp.length >= 3) {
                    LastTime = sp[2];
                }
                LastMsg = `${sp[0]}:${sp[1]}`;
            }
            // 设置红点和首页显示的消息
            if ($(`.QQ_chat_page[data-name=${name}]`).css("display") != "none" && !Creat) {
                NewMsgCount = 0;
            }
            $(`.QQ_home_usermsg[data-name='${name}'] .QQ_home_lastmsg`).text(LastMsg);
            $(`.QQ_home_usermsg[data-name='${name}'] .QQ_home_lasttime`).text(LastTime);
            QQ_SetHomeTips(name, NewMsgCount);
            console.log(`群聊的未读消息数量:${NewMsgCount}`);
            $msgContent.scrollTop($msgContent[0].scrollHeight);
            let TipsCount = QQ_GetChatShowTipsCount(name);
            const $Tips = $(`.QQ_chat_page[data-name=${name}]`).find(`.new_tips`);
            $Tips.text(TipsCount);
            if (TipsCount > 0) {
                $Tips.css("display", "flex");
            }
            else {
                $Tips.hide();
            }
        }
        catch (error) {
            assertIsError(error);
            QQ_Error(error.message);
        }
    }
    console.log(`User_LastMsgMap:\n${JSON.stringify(User_LastMsgMap)}\nChar_LastMsgMap:\n${JSON.stringify(Char_LastMsgMap)}`);
}
function QQ_SetHomeTips(name, count) {
    $(`.QQ_home_usermsg[data-name='${name}'] .QQ_home_usermsg_new`).text(count);
    if (count == 0) {
        $(`.QQ_home_usermsg[data-name='${name}'] .QQ_home_usermsg_new`).hide();
    }
    else {
        $(`.QQ_home_usermsg[data-name='${name}'] .QQ_home_usermsg_new`).show();
    }
    console.log(`设置${name}的首页红点:${count}`);
    QQ_NewMsg[name] = count;
}
function QQ_GetChatShowTipsCount(name) {
    let count = 0;
    for (const n in QQ_NewMsg) {
        if (n != name) {
            count += Number(QQ_NewMsg[n]);
        }
    }
    return count;
}
async function QQ_Moment_Comment(event) {
    const $closest = $(event.currentTarget).closest(`.user_moment`);
    const message = $closest.find(".moment_message").text();
    const input = $closest.find("input[type='text']");
    const name = $closest.find(".moment_sender").text();
    const list = $closest.find(".user_leave_message_list");
    const value = input.val();
    if (!value || !message) {
        return;
    }
    let messageDiv = $("<div>", { class: "user_leave_message" });
    messageDiv.html(`<span><strong>${UserName}</strong>：${value}</span>`);
    list.append(messageDiv);
    input.val("");
    const sendvalue = `回复${name}的动态"${message}"\n回复内容:${value}\n<Request:只是在动态里回复,而不是给他发私信>`;
    console.log(sendvalue);
    const result = await QQ_Gen(sendvalue);
    ResultHandle(result);
}
function QQ_Moment_Parse(content) {
    const lines = content.split(/\r?\n/g);
    let momentdiv;
    let list;
    let count = QQ_NewMsg["moment"] || 0;
    for (const line of lines) {
        //console.log(`line:${line}`);
        let match = line.match(/\s*(.+?)--(.+?)--(.+?)--(.+?)--(.+)/);
        if (match) {
            // 是新动态内容
            // const str = `${match[1]}--${match[2]}`;
            // if (str! in QQ_momentjson) {
            //   QQ_momentjson[str] = [];
            // }
            console.log(`匹配到动态内容:${match[0]}`);
            if (momentdiv) {
                count += 1;
                $("#space_contents").prepend(momentdiv);
            }
            let fakeimg = "";
            let message = match[2];
            const matches = message.matchAll(/\[img-(.+?)\]/g);
            if (matches) {
                for (const m of matches) {
                    message = message.replace(m[0], "");
                    fakeimg += `\n<div class="space_fakeimg">${m[1]}</div>`;
                }
            }
            momentdiv = $("<div>", { class: "user_moment" });
            momentdiv.html(_.template(moment_page)({
                userName: match[1],
                message: message,
                timestamp: match[3],
                additionalInfo: match[4],
                randomPhone: QQ_CharSettings.readValue(match[1], "手机型号") ? QQ_CharSettings.readValue(match[1], "手机型号") : QQ_GetRandomPhone(),
                extraContent: match[5],
                imgcontent: fakeimg
            }));
            list = momentdiv.find(".user_leave_message_list");
            continue;
        }
        match = line.match(/^\s*(.+?)(?::|：|--)(.+)$/m);
        if (match && momentdiv && list) {
            // 是评论内容
            //console.log(`评论人:${match[1]}  评论内容:${match[2]}`)
            let messageDiv = $("<div>", { class: "user_leave_message" });
            messageDiv.html(`<span><strong>${match[1]}</strong>：${match[2]}</span>`);
            list.append(messageDiv);
        }
    }
    if (momentdiv) {
        count += 1;
        $("#space_contents").prepend(momentdiv);
    }
    QQ_NewMsg["moment"] = count;
    QQ_SetNewTips(count);
}
function QQ_GetRandomHead() {
    // return `http://sharkpan.xyz/f/${random_head_list[Math.floor(Math.random() * random_head_list.length)]
    //   }`;
    if (QQ_RandomHead.length === 0)
        return null; // 处理空数组
    let minCount = Infinity;
    const candidates = [];
    // 一次遍历找到最小值和候选对象
    for (const obj of QQ_RandomHead) {
        if (obj.count < minCount) {
            minCount = obj.count;
            candidates.length = 0; // 清空数组，重置候选
            candidates.push(obj);
        }
        else if (obj.count === minCount) {
            candidates.push(obj);
        }
    }
    // 随机选择一个（即使只有一个元素也适用）
    const selected = candidates[Math.floor(Math.random() * candidates.length)];
    selected.count++; // 直接修改原对象
    return selected.url;
}
let Phone = [
    "小米",
    "华为",
    "苹果",
    "三星",
    "魅族",
    "一加",
    "oppo",
    "vivo",
    "真我",
    "红米",
];
let Phone_lvl = ["pro", "max", "mate", "ultra", "plus"];
function QQ_GetRandomPhone() {
    let name = Phone[Math.floor(Math.random() * Phone.length)];
    name += random(6, 19);
    let count = random(1, Phone_lvl.length);
    let a = [];
    for (let i = 0; i < count; i++) {
        let randomresult = Phone_lvl[random(0, Phone_lvl.length)];
        if (!a.includes(randomresult)) {
            a.push(randomresult);
        }
    }
    name += " " + a.join(" ");
    return name;
}
function QQ_Chat_AddMsg(element, msg, name, isgroup) {
    if (!QQ_NewMsg[name]) {
        QQ_NewMsg[name] = {};
    }
    const match = msg.match(/(.+?)(?:--|:|：)(.+)/);
    if (!match) {
        return;
    }
    System_AddNpcHead(match[1]);
    let message = match[2];
    if (message.split("--").length >= 2) {
        message = message.split("--")[0];
    }
    if (match[1] == `${UserName}`) {
        QQ_NewMsg[name].Count = 0;
        return QQ_Chat_AddUserMsg(element, `${match[1]}--${message}`);
    }
    QQ_NewMsg[name].Count += 1;
    const content = QQ_Chat_SpecialMsg(message, match[1], isgroup);
    const html = _.template(chat_char_msg)({
        name: match[1],
        content: content,
        isgroup: isgroup || false,
    });
    $(element).append(html);
}
function QQ_Chat_AddUserMsg(element, msg) {
    const match = msg.match(/(.+?)--(.+)/);
    if (!match) {
        return;
    }
    const content = QQ_Chat_SpecialMsg(match[2], `${UserName}`, false, true);
    const html = _.template(chat_user_message)({ content });
    $(element).append(html);
}
/**
 * 处理特殊格式消息
 * @param msg 消息内容
 * @param isgroup 是否是群聊
 * @returns 处理后的消息
 */
function QQ_Chat_SpecialMsg(msg, username, isgroup, mysend) {
    const match = msg.match(/\[(.+?)[\|-](.+?)\]/);
    const xxx = msg.match(/(.+?)\[/);
    const xx = msg.match(/\](.+)/);
    let additionalText = "";
    if (xxx) {
        additionalText = xxx[1];
    }
    if (xx) {
        console.log(`前后都有`);
        additionalText = additionalText ? additionalText + `<br>${xx[1]}` : xx[1];
    }
    if (!match) {
        // 使用普通消息模板
        //console.log(`自己的消息,使用的普通模板`);
        return _.template(chat_normal_message)({
            message: msg,
            isgroup: isgroup || false,
            username: username
        });
    }
    const type = match[1];
    if (type == "bqb") {
        // 使用表情包消息模板
        console.log(`表情包链接:${QQ_GetEmoji(match[2])}`);
        return _.template(chat_emoji_message)({
            emojiUrl: QQ_GetEmoji(match[2]),
            additionalText: additionalText,
            isgroup: isgroup || false,
            username: username
        });
    }
    else if (type == "转账" || type == "zz") {
        // 使用转账消息模板
        return _.template(chat_transfer_message)({
            amount: match[2],
        });
    }
    else if (type == "yy") {
        let file = chat_char_voice_message;
        if (mysend) {
            file = chat_my_voice_message;
        }
        return _.template(file)({
            content: match[2],
            time: Math.ceil((match[2].length / 4)).toString(),
            isgroup: isgroup || false,
            username: username
        });
    }
    else if (["img", "image", "video", "imgs", "images", "file", "files", "图片", "视频", "tp"].includes(type)) {
        return _.template(chat_fakeimg_message)({
            isgroup: isgroup || false,
            username: username,
            content: match[2],
            additionalText: additionalText ? `<span style="margin-top: 5px; display: block">${additionalText}</span>` : ""
        });
    }
    else if (["music", "音乐"].includes(type)) {
        let sp = match[2].split("$");
        let musicname = "";
        let musicauthor = "";
        if (sp.length >= 2) {
            musicname = sp[0];
            musicauthor = sp[1];
        }
        //console.log(`加入音乐:${musicname}----${musicauthor}`);
        return _.template(chat_music_message)({
            isgroup: isgroup || false,
            username: username,
            musicname: musicname,
            musicauthor: musicauthor
        });
    }
    return "";
}
function QQ_MySendSpecial(content) {
    let match = content.match(/\[?(.+?)-(.+?)]?$/m);
    if (match) {
        let type = match[1];
        if (["yy", "语音",].includes(type)) {
            content = `[yy-${match[2]}]`;
        }
        else if (["表情包", "表情", "bqb", "bq"].includes(type)) {
            content = `[bqb-${match[2]}]`;
        }
        else if (["img", "image", "video", "imgs", "images", "file", "files", "图片", "视频", "tp"].includes(type)) {
            content = `[img-${match[2]}]`;
        }
    }
    return content;
}
/**
 * 获取表情包
 * @param name 表情包名称
 * @returns 表情包URL
 */
function QQ_GetEmoji(name) {
    if (!QQ_emoji.has(name)) {
        return;
    }
    return QQ_emoji.get(name);
}
// 当前正在设置的聊天对象
let currentSettingChatName = "";
/**
 * 设置聊天页设置
 * @param event 事件对象
 */
function QQ_SetChatPageSetting(event) {
    console.log("点击了设置聊天页设置");
    let element = event.currentTarget;
    // 获取当前聊天页的名称
    const chatPageElement = $(element).closest('.QQ_chat_page');
    if (chatPageElement.length === 0) {
        console.error("无法获取当前聊天页");
        return;
    }
    currentSettingChatName = chatPageElement.attr("data-name") ?? "";
    console.log(`打开聊天设置页面，当前聊天对象: ${currentSettingChatName}`);
    // 添加遮罩层和弹窗到页面
    if ($(".popup-overlay").length === 0) {
        $(".card").append('<div class="popup-overlay"></div>');
    }
    if ($(".chat-setting-popup").length === 0) {
        $(".card").append(chat_page_setting.replace("${username}", currentSettingChatName));
        const bubblecolorPicker = document.getElementById('bubble-color');
        if (bubblecolorPicker) {
            console.log(`开始监听`);
            bubblecolorPicker.addEventListener('input', (event) => {
                const color = event.target.value;
                $("#bubble-color-input").val(color || "#ffffff");
                $("#chat-setting-preview").each(function () {
                    this.style.setProperty('background-color', color, 'important');
                });
            });
        }
        const TextcolorPicker = document.getElementById('text-color');
        if (TextcolorPicker) {
            console.log(`开始监听`);
            TextcolorPicker.addEventListener('input', (event) => {
                const color = event.target.value;
                $("#text-color-input").val(color || "#ffffff");
                $("#chat-setting-preview").each(function () {
                    const spans = this.querySelectorAll('span');
                    spans.forEach(span => {
                        span.style.setProperty('color', color, 'important');
                    });
                });
            });
        }
    }
    // 填充已有设置
    loadCurrentSettings(currentSettingChatName);
    // 显示弹窗和遮罩层
    $(".popup-overlay").show();
    $(".chat-setting-popup").show();
}
/**
 * 加载当前设置到弹窗
 * @param chatName 聊天对象名称
 */
function loadCurrentSettings(chatName) {
    const setting = GetChatCharSettingByName(chatName);
    // 填充表单
    let bubbleColor = QQ_CharSettings.readValue(chatName, "气泡颜色");
    let TextColor = QQ_CharSettings.readValue(chatName, "字体颜色");
    let chatBg = QQ_CharSettings.readValue(chatName, "聊天壁纸");
    if (bubbleColor && bubbleColor[0] !== "#") {
        bubbleColor = "#" + bubbleColor;
    }
    if (TextColor && TextColor[0] !== "#") {
        TextColor = "#" + TextColor;
    }
    $("#bubble-color").val(bubbleColor || "#ffffff");
    $("#bubble-color-input").val(bubbleColor || "#ffffff");
    $("#text-color").val(TextColor || "#ffffff");
    $("#text-color-input").val(TextColor || "#ffffff");
    $("#chat-bg").val(chatBg || "");
    $("#chat-setting-preview").each(function () {
        this.style.setProperty('background-color', bubbleColor, 'important');
        const spans = this.querySelectorAll('span');
        spans.forEach(span => {
            span.style.setProperty('color', TextColor, 'important');
        });
    });
}
function closeSettingPopup() {
    $(".popup-overlay").hide();
    $(".chat-setting-popup").hide();
}
/**
 * 保存设置并关闭弹窗
 */
async function saveSettingAndClose() {
    if (!currentSettingChatName) {
        console.error("未知的聊天对象");
        return;
    }
    // 获取设置值
    const bubbleColor = $("#bubble-color-input").val() || "";
    const TextColor = $("#text-color-input").val() || "";
    const chatBg = $("#chat-bg").val() || "";
    console.log(`保存聊天设置: ${currentSettingChatName}`, {
        bubbleColor,
        TextColor,
        chatBg,
    });
    QQ_CharSettings.writeValue(currentSettingChatName, "气泡颜色", bubbleColor);
    QQ_CharSettings.writeValue(currentSettingChatName, "字体颜色", TextColor);
    QQ_CharSettings.writeValue(currentSettingChatName, "聊天壁纸", chatBg);
    const result = QQ_CharSettings.getAllText();
    for (let entry of entries) {
        if (entry.comment == "手机-角色" || entry.comment == "手机界面-角色") {
            await setLorebookEntries(worldbook, [
                { uid: entry.uid, content: result },
            ]);
            break;
        }
    }
    SetCssVariable(currentSettingChatName, "MsgColor", bubbleColor);
    SetCssVariable(currentSettingChatName, "TextColor", TextColor);
    SetCssVariable(currentSettingChatName, "BackGroundImg", `url('${chatBg}')`);
    // 关闭弹窗
    closeSettingPopup();
}
function updateTime() {
    const timeElement = document.getElementById("time");
    if (!timeElement) {
        return;
    }
    const now = new Date();
    const hours = String(now.getHours()).padStart(2, "0");
    const minutes = String(now.getMinutes()).padStart(2, "0");
    //const seconds = String(now.getSeconds()).padStart(2, '0');
    timeElement.textContent = `${hours}:${minutes}`;
    // const dayElement = document.getElementById("day");
    // dayElement.textContent = `${now.getMonth() + 1}月${now.getDate()}日`;
}
function App_Load(App_Name) {
    $("#Home_page").hide();
    $("#App_page").show();
    App_HideAll();
    $(`#App_${App_Name}`).show();
}
function App_HideAll() {
    const AppNames = ["QQ", "twitter", "discord"];
    for (const name of AppNames) {
        $(`#App_${name}`).hide();
    }
}
// 每秒更新一次时间
setInterval(updateTime, 1000);
updateTime();</script></body><div style="all: initial;"><div style="all: initial;" id="__hcfy__"></div></div><div id="transmart-crx-shadow-root" style="all: initial;"></div></html>