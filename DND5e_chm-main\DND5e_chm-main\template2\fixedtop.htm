﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<title></title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<style>
html,div { margin: 0; padding: 0;}

body {
	padding: 0px;
	margin: 0px;
	overflow: auto;
	height: 100%;
}

#winchm_template_button{
	float: right;
	width: 93px;
	top: 7px;
	position: relative;
	text-align: right;
	right: 5px;
	height: auto;
}

#winchm_template_top{
	padding: 0px;
	margin: 0px;
	border-bottom: 1px solid #9B9B9B;
	background-color: #B1CEFE;
}

#winchm_template_navigation{
	margin: 0px;
	padding-top: 7px;
	padding-left: 7px;
	padding-bottom: 3px;
	padding-right: 0px;
	font-size: 8.5pt;
	font-family: Arial, Helvetica, sans-serif;
	font-weight: normal;
	color: #585858;
}

#winchm_template_title{
	margin: 0px;
	padding-top: 4px;
	padding-left: 7px;
	padding-bottom: 7px;
	padding-right: 0px;
	font-size: 18px; 
	font-family: Verdana, Geneva, sans-serif;
	color: #363636;
}

#winchm_template_content{
	margin-top: 20px;
	margin-left: 15px;
	margin-bottom: 20px;
	margin-right: 15px;
	width: auto  !important;
	width: 100%;
}

#winchm_template_footer{
	border-width: 1px;
	border-color: #B1CEFE;
	border-top-style: solid;
	margin-top: 15px;
	margin-left: 15px;
	margin-bottom: 20px;
	margin-right: 15px;
	padding-top: 7px;
	padding-left: 0px;
	padding-bottom: 0px;
	padding-right: 0px;
	font-family: arial, helvetica, sans-serif;
	font-size: 8.5pt;
	color: #696969;
	width: auto;
	text-align: left;
}


#winchm_template_container{
	margin: 0px;
	padding: 0px;
	position: static;
	padding-bottom: 3px;
	overflow: auto;
	background-color: #FFFFFF;
}


#winchm_template_navigation A:link	{text-decoration: none; color:#004080}
#winchm_template_navigation A:visited  {text-decoration: none; color: #004080}
#winchm_template_navigation A:active {text-decoration: none; color: #004080 }
#winchm_template_navigation A:hover {text-decoration: none;color: #0080FF}

A:link	{text-decoration: underline; color:#0033CC}
A:visited  {text-decoration: underline; color: #0033CC}
A:active {text-decoration: underline; color: #0033CC }
A:hover {text-decoration: underline;color: #FF0000;}
</style>
<script type="text/javascript">
function isMobile(){
Agent = window.navigator.userAgent;
if (Agent.indexOf("iPhone")>=1 || Agent.indexOf("iPad")>=1 || Agent.indexOf("iPod")>=1 || Agent.indexOf("Android")>=1){
return true;
}else{
return false;	
}

}
function d_onresize(){
if (window.navigator.userAgent.indexOf("MSIE")>=1){
document.getElementById('winchm_template_container').style.pixelWidth = document.body.offsetWidth - 3;
document.getElementById('winchm_template_container').style.pixelHeight = document.body.offsetHeight - document.getElementById('winchm_template_top').offsetHeight - 4;
}
document.getElementById('winchm_template_container').style.top = document.getElementById('winchm_template_top').offsetHeight + 'px';
}

function d_onbeforeprint(){
if (window.navigator.userAgent.indexOf("MSIE")>=1){
document.getElementById('winchm_template_container').style.height = 'auto';
}
}

function d_onafterprint(){
d_onresize();
}

if(!isMobile()){

window.onload = d_onresize;
window.onresize = d_onresize;
window.onbeforeprint = d_onbeforeprint;
window.onafterprint = d_onafterprint;

document.write("<style>\n");
document.write("body {overflow: hidden;}\n");
document.write("#winchm_template_container {position: absolute;overflow: auto;top : 0px;right: 0px;bottom: 0px;left: 0px;}\n");
document.write("</style>\n");
}

</script>
<style>
@media print
{
body {
	padding: 0px;
	margin: 0px;
	overflow: auto;
	height: auto;
}
#winchm_template_container{
	position: static;	
	margin: 0px;
	padding: 5px;
	
	width: auto;
	height: auto;
	overflow: hidden;
}
#winchm_template_button{
visibility:hidden;
}
}
</style>
</head>

<body>

<div id="winchm_template_top">
	<div id="winchm_template_button"><IMG alt="Previous topic" src="btn_prev_n.gif" ><IMG alt="Next topic" src="btn_next_n.gif" ></div>
	<div id="winchm_template_navigation">Help &gt; 
($navigation$)</div>
	<div id="winchm_template_title">($title$)</div>
</div>
<div id="winchm_template_container">
	<div id="winchm_template_content">($content$)</div>	
	<div id="winchm_template_footer">Copyright &copy; 2023. All rights 
reserved. (To change the copyright info, just edit it in template.)</div>
</div>

</body>

</html>
