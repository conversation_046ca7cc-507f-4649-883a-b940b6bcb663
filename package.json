{"scripts": {"build:dev": "webpack --mode development", "build": "webpack --mode production", "watch": "webpack --mode development --watch --progress"}, "browserslist": ["defaults and partially supports es6-module"], "devDependencies": {"@types/jquery": "^3.5.32", "@types/jqueryui": "^1.12.24", "@types/lodash": "^4.17.16", "@types/toastr": "^2.1.43", "@types/webpack": "^5.28.5", "autoprefixer": "^10.4.21", "css-loader": "^7.1.2", "html-inline-css-loader": "^0.2.1", "html-inline-css-webpack-plugin": "^1.11.2", "html-inline-script-webpack-plugin": "^3.2.1", "html-loader": "^5.1.0", "html-webpack-inline-svg-plugin": "^2.3.0", "html-webpack-plugin": "^5.6.3", "mini-css-extract-plugin": "^2.9.2", "postcss": "^8.5.3", "postcss-cli": "^11.0.1", "postcss-loader": "^8.1.1", "postcss-nested": "^7.0.2", "sass": "^1.87.0", "sass-loader": "^16.0.5", "socket.io": "^4.8.1", "terser-webpack-plugin": "^5.3.14", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths-webpack-plugin": "^4.2.0", "tslib": "^2.8.1", "typescript": "^5.8.3", "webpack": "^5.99.8", "webpack-cli": "^6.0.1", "yaml": "^2.7.1"}, "pnpm": {"onlyBuiltDependencies": ["@parcel/watcher"]}}