import { safeToastr, saveContentToLorebook } from '../utils';
import type { CharacterGenerationData, AIResponseFormat, ModuleSetupEquipmentItem, MagicEffect, EffectType, PredefinedMagicEffectOption, WeaponTemplate, ArmorTemplate } from '../types';
// Import the new function and potentially a way to get all selectable weapons
import { 
    createFullEquipmentRow, 
    createDynamicInventoryItem, 
    createMagicEffectEditRow, 
    predefinedMagicEffectOptions, 
    armorTemplates, 
    weaponTemplates 
} from './equipment_inventory';
import { createSpellItem, updateSpellAttackAndDc, populateSpellDatalists, setupDynamicSpellLists, setupSpellcastingCalculations } from './spells';
import { calculateAttributeFinal, getAttributeModifier, setupAttributeCalculations } from './attributes';
import { updateAllSkillFinalValues, setupSkillCalculations } from './skills';


// SillyTavern / TavernHelper injected function type declarations
declare function triggerSlash(command: string): Promise<string | undefined>;

export function populateCharacterFormFromJSON(characterData: CharacterGenerationData) {
  safeToastr('info', `正在使用AI生成的数据填充角色表单: ${characterData.name}`, '角色填充');

  const form = document.getElementById('character-sheet-form') as HTMLFormElement;
  if (!form) return;

  const setFormValue = (elementId: string, value: string | number | undefined, isSelect: boolean = false) => {
    const element = document.getElementById(elementId) as HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement;
    if (element && value !== undefined) {
      if (isSelect && 'options' in element) {
        let found = false;
        for (let i = 0; i < element.options.length; i++) {
          if (element.options[i].value === value.toString() || element.options[i].text === value.toString()) {
            element.selectedIndex = i;
            found = true;
            break;
          }
        }
        if (!found) {
          const newOption = new Option(value.toString(), value.toString(), true, true);
          element.add(newOption);
        }
      } else {
        (element as HTMLInputElement).value = value.toString();
      }
      if (
        elementId.startsWith('attr-') &&
        (elementId.includes('-base') || elementId.includes('-race') || elementId.includes('-mod'))
      ) {
        // Trigger input event to recalculate final attribute and modifier
        element.dispatchEvent(new Event('input'));
      }
    }
  };

  setFormValue('char-name', characterData.name);
  setFormValue('char-race', characterData.race, true);
  setFormValue('char-class-1', characterData.class, true);
  setFormValue('char-level-1', characterData.level);
  setFormValue('char-alignment', characterData.alignment);
  setFormValue('char-background', characterData.background);
  setFormValue('char-personality-traits', characterData.personalityTraits);
  setFormValue('char-ideals', characterData.ideals);
  setFormValue('char-bonds', characterData.bonds);
  setFormValue('char-flaws', characterData.flaws);
  setFormValue('char-appearance', characterData.appearance);
  setFormValue('char-story', characterData.story);
  setFormValue('char-age', characterData.age);
  setFormValue('char-gender', characterData.gender, true);
  setFormValue('char-faith', characterData.faith);
  setFormValue('char-height', characterData.height);
  setFormValue('char-weight', characterData.weight);
  setFormValue('char-xp', characterData.exp);
  setFormValue('char-subclass-1', characterData.subclass);
  setFormValue('spell-ability', characterData.spellcastingAbility, true);
  setFormValue('char-tool-proficiencies-text', characterData.toolProficienciesText);

  if (characterData.currency) {
    setFormValue('char-currency-gold', characterData.currency.gold);
    setFormValue('char-currency-silver', characterData.currency.silver);
    setFormValue('char-currency-copper', characterData.currency.copper);
  }

  const attributes = ['str', 'dex', 'con', 'int', 'wis', 'cha'];
  const attrFullNames = {
    str: 'strength',
    dex: 'dexterity',
    con: 'constitution',
    int: 'intelligence',
    wis: 'wisdom',
    cha: 'charisma',
  };
  attributes.forEach(attr => {
    const attrKey = attrFullNames[attr as keyof typeof attrFullNames] as keyof CharacterGenerationData['attributes'];
    const attrData = characterData.attributes[attrKey];
    if (attrData) {
      setFormValue(`attr-${attr}-base`, attrData.base);
      setFormValue(`attr-${attr}-race`, attrData.race_bonus);
      setFormValue(`attr-${attr}-mod`, attrData.modifier_bonus);
      // The 'input' event triggered by setFormValue should handle recalculation
    }
  });
  
  // Populate skills
  characterData.skills?.forEach(skillData => {
    const skillCheckbox = document.querySelector(`input[data-skill-name="${skillData.name}"]`) as HTMLInputElement;
    const skillModifierInput = skillCheckbox?.closest('.skill-item')?.querySelector('.skill-modifier-input') as HTMLInputElement;
    if (skillCheckbox) {
      skillCheckbox.checked = skillData.proficient;
    }
    if (skillModifierInput) {
      skillModifierInput.value = skillData.modifierValue.toString();
    }
  });
  // This needs to be called after attributes and skills raw data is populated
  // setupAttributeCalculations(); // This should already be called once globally
  // setupSkillCalculations(); // This should already be called once globally
  updateAllSkillFinalValues(); 

  const equipmentListContainer = document.getElementById('equipment-list-container');
  if (equipmentListContainer) {
    equipmentListContainer.innerHTML = ''; // Clear existing items
    characterData.equipment?.forEach(savedEq => {
      // createFullEquipmentRow now accepts initialData to populate itself
      const equipmentRow = createFullEquipmentRow(savedEq);
      equipmentListContainer.appendChild(equipmentRow);
    });
  }

  const inventoryListContainer = document.getElementById('inventory-list-container');
  if (inventoryListContainer) {
    inventoryListContainer.innerHTML = '';
    characterData.inventory?.forEach(item => {
      inventoryListContainer.appendChild(createDynamicInventoryItem(item.name, item.description, item.quantity));
    });
  }

  const cantripsListDiv = document.getElementById('cantrips-list');
  if (cantripsListDiv) {
    cantripsListDiv.innerHTML = '';
    characterData.equippedSpells
      ?.filter(s => s.level === 0)
      .forEach(spell => {
        cantripsListDiv.appendChild(createSpellItem('cantrip', 'cantrips-list', 'cantrips-datalist', spell.name));
      });
    if (cantripsListDiv.children.length === 0 && characterData.equippedSpells?.filter(s => s.level === 0).length === 0) {
       // Only add placeholder if no spells of this level were added
      cantripsListDiv.appendChild(createSpellItem('cantrip', 'cantrips-list', 'cantrips-datalist'));
    }
  }

  const level1SpellsListDiv = document.getElementById('level1-spells-list');
  if (level1SpellsListDiv) {
    level1SpellsListDiv.innerHTML = '';
    characterData.equippedSpells
      ?.filter(s => s.level === 1)
      .forEach(spell => {
        level1SpellsListDiv.appendChild(
          createSpellItem('level1spell', 'level1-spells-list', 'level1-spells-datalist', spell.name),
        );
      });
     if (level1SpellsListDiv.children.length === 0 && characterData.equippedSpells?.filter(s => s.level === 1).length === 0) {
        level1SpellsListDiv.appendChild(createSpellItem('level1spell', 'level1-spells-list', 'level1-spells-datalist'));
    }
  }
  if (characterData.spellSlots?.['1']) {
    setFormValue('spell-slots-1', characterData.spellSlots['1'].max);
  }

  // Ensure all calculations are run after form population
  // setupAttributeCalculations(); // Called once globally
  updateSpellAttackAndDc(); // Call this after attributes and spell ability are set
  // setupSkillCalculations(); // Called once globally
  // updateAllSkillFinalValues(); // Called above after skills data is set

  safeToastr('success', `角色 "${characterData.name}" 的数据已填充到表单。`, 'AI角色生成');
}

export async function handleAiGenerateCharacter() {
  const outputMessageDiv = document.getElementById('output-message') as HTMLElement;
  const characterNameInput = document.getElementById('char-name') as HTMLInputElement;
  const aiCharacterPromptTextarea = document.getElementById('ai-character-prompt-textarea') as HTMLTextAreaElement;

  let userPromptForChar = aiCharacterPromptTextarea?.value.trim();
  if (!userPromptForChar) {
    userPromptForChar = (document.getElementById('module-content-textarea') as HTMLTextAreaElement)?.value.trim();
  }
  if (!userPromptForChar) {
    userPromptForChar = `请为D&D 5e生成一个详细的1级角色。`;
  }

  if (!outputMessageDiv) {
    safeToastr('error', '输出消息区域未找到 (ai generate character)!', '界面错误');
    return;
  }

  outputMessageDiv.textContent = '正在向AI发送请求以生成角色数据 (JSON格式)...';
  safeToastr('info', '向AI发送角色生成请求 (JSON)...', 'AI交互');

  const aiPrompt = `请根据以下用户提示为D&D 5e游戏生成一个角色。用户提示： "${userPromptForChar}". 请严格按照 "module_setup_ai_prompts_v2_json.md" 中定义的 "characterGeneration" JSON Schema返回结果，确保整个JSON响应被 "开始制作\\n##@@_MODULE_CONTENT_BEGIN_@@##" 和 "##@@_MODULE_CONTENT_END_@@##\\n结束制作" 包裹。`;

  try {
    if (typeof triggerSlash !== 'function') {
      safeToastr('error', 'triggerSlash API 不可用!', 'API 错误');
      outputMessageDiv.textContent = '错误: triggerSlash API 不可用。';
      return;
    }

    const aiRawResponseWithWrappers = await triggerSlash(`/gen ${aiPrompt}`);

    if (aiRawResponseWithWrappers?.trim()) {
      safeToastr('success', 'AI已返回角色数据!', 'AI交互完成');
      let coreJSONString: string | null = null;
      const newMarkerPatternMatch = aiRawResponseWithWrappers.match(
        /##@@_MODULE_CONTENT_BEGIN_@@##([\s\S]*?)##@@_MODULE_CONTENT_END_@@##/,
      );
      if (newMarkerPatternMatch && newMarkerPatternMatch[1]) {
        coreJSONString = newMarkerPatternMatch[1].trim();
      } else {
        console.warn(
          'New markers ##@@_MODULE_CONTENT_BEGIN_@@##...##@@_MODULE_CONTENT_END_@@## not found in AI response for character generation.',
        );
      }

      if (coreJSONString) {
        try {
          const parsedResponse = JSON.parse(coreJSONString) as AIResponseFormat<CharacterGenerationData>;
          if (parsedResponse.requestType === 'characterGeneration' && parsedResponse.data) {
            populateCharacterFormFromJSON(parsedResponse.data);
            outputMessageDiv.textContent = `AI角色数据已填充到表单。请检查并保存。`;
            if (characterNameInput && parsedResponse.data.name) {
              characterNameInput.value = parsedResponse.data.name;
            }
          } else {
            throw new Error('AI返回的JSON格式不符合预期的角色生成类型。');
          }
        } catch (e) {
          safeToastr('error', `AI返回的角色JSON解析失败: ${(e as Error).message}.`, 'JSON解析错误');
          outputMessageDiv.textContent = `AI返回的角色JSON解析失败。原始提取内容:\n${coreJSONString.substring(
            0,
            300,
          )}...`;
        }
      } else {
        safeToastr('warning', '未能从AI回复中提取有效的JSON角色数据。', '内容提取失败');
        outputMessageDiv.textContent = '未能从AI回复中提取有效的JSON角色数据。';
      }
    } else {
      safeToastr('warning', 'AI未能生成角色数据或返回为空。', 'AI交互失败');
      outputMessageDiv.textContent = 'AI未能生成角色数据或返回为空。';
    }
  } catch (error) {
    const errorMsg = `AI角色生成过程中发生错误: ${(error as Error).message}`;
    safeToastr('error', errorMsg, 'AI错误');
    outputMessageDiv.textContent = errorMsg;
  }
}

export async function handleSaveCharacter() {
  const outputMessageDiv = document.getElementById('output-message') as HTMLElement;
  const form = document.getElementById('character-sheet-form') as HTMLFormElement;

  if (!form || !outputMessageDiv) {
    safeToastr('error', '人物创建表单或输出区域未找到!', '界面错误');
    return;
  }

  const formData = new FormData(form);
  const getVal = (key: string) => formData.get(key)?.toString().trim() || '';
  const getNum = (key: string, defaultValue = 0) => {
    const val = formData.get(key)?.toString().trim();
    if (val === '' || val === null || val === undefined) {
      return defaultValue;
    }
    const num = parseInt(val, 10);
    return isNaN(num) ? defaultValue : num;
  };

  const characterName = getVal('charName');
  if (!characterName) {
    safeToastr('warning', '角色名不能为空!', '保存错误');
    outputMessageDiv.textContent = '错误: 角色名不能为空。';
    (document.getElementById('char-name') as HTMLInputElement)?.focus();
    return;
  }

  const characterDataToSave: Partial<CharacterGenerationData> = {
    name: characterName,
    race: getVal('charRace'),
    class: getVal('charClass1'),
    level: getNum('charLevel1', 1),
    alignment: getVal('charAlignment'),
    background: getVal('charBackground'),
    personalityTraits: getVal('charPersonalityTraits'),
    ideals: getVal('charIdeals'),
    bonds: getVal('charBonds'),
    flaws: getVal('charFlaws'),
    appearance: getVal('charAppearance'),
    story: getVal('charStory'),
    age: getVal('charAge'),
    gender: getVal('charGender'),
    faith: getVal('charFaith'),
    height: getVal('charHeight'),
    weight: getVal('charWeight'),
    exp: getNum('charXp'),
    subclass: getVal('charSubclass1'),
    spellcastingAbility: getVal('spellAbility'),
    toolProficienciesText: getVal('charToolProficienciesText'),
    attributes: {
      strength: { base: 0, race_bonus: 0, modifier_bonus: 0, final: 0, mod: 0 },
      dexterity: { base: 0, race_bonus: 0, modifier_bonus: 0, final: 0, mod: 0 },
      constitution: { base: 0, race_bonus: 0, modifier_bonus: 0, final: 0, mod: 0 },
      intelligence: { base: 0, race_bonus: 0, modifier_bonus: 0, final: 0, mod: 0 },
      wisdom: { base: 0, race_bonus: 0, modifier_bonus: 0, final: 0, mod: 0 },
      charisma: { base: 0, race_bonus: 0, modifier_bonus: 0, final: 0, mod: 0 },
    },
    hp: { current: 0, max: 0 },
    ac: 10,
    currency: {
      gold: getNum('charCurrencyGold', 0),
      silver: getNum('charCurrencySilver', 0),
      copper: getNum('charCurrencyCopper', 0),
    },
    proficiencies: [],
    skills: [], // Initialize as empty, will be populated below
    equippedSpells: [],
    spellSlots: {},
    equipment: [],
    inventory: [],
  };

  const attributeKeys = ['str', 'dex', 'con', 'int', 'wis', 'cha'];
  const attributeFullNames: Record<string, keyof CharacterGenerationData['attributes']> = {
    str: 'strength',
    dex: 'dexterity',
    con: 'constitution',
    int: 'intelligence',
    wis: 'wisdom',
    cha: 'charisma',
  };

  attributeKeys.forEach(attrKey => {
    const baseEl = document.getElementById(`attr-${attrKey}-base`) as HTMLInputElement;
    const raceEl = document.getElementById(`attr-${attrKey}-race`) as HTMLInputElement;
    const modEl = document.getElementById(`attr-${attrKey}-mod`) as HTMLInputElement;
    const base = baseEl ? parseInt(baseEl.value, 10) : NaN;
    const race_bonus = raceEl ? parseInt(raceEl.value, 10) : NaN;
    const modifier_bonus = modEl ? parseInt(modEl.value, 10) : NaN;
    const validBase = isNaN(base) ? 8 : base;
    const validRaceBonus = isNaN(race_bonus) ? 0 : race_bonus;
    const validModifierBonus = isNaN(modifier_bonus) ? 0 : modifier_bonus;
    const final = calculateAttributeFinal(validBase, validRaceBonus, validModifierBonus);
    const mod = getAttributeModifier(final);
    characterDataToSave.attributes![attributeFullNames[attrKey]] = {
      base: validBase,
      race_bonus: validRaceBonus,
      modifier_bonus: validModifierBonus,
      final,
      mod,
    };
  });

  if (characterDataToSave.attributes!.constitution && characterDataToSave.level) {
    const conMod = characterDataToSave.attributes!.constitution.mod;
    const classHitDice = characterDataToSave.class === '战士' ? 10 : characterDataToSave.class === '法师' ? 6 : 8; // Simplified
    let maxHp = classHitDice + conMod;
    if (characterDataToSave.level > 1) {
      maxHp += (characterDataToSave.level - 1) * (Math.floor(classHitDice / 2) + 1 + conMod);
    }
    characterDataToSave.hp = { current: maxHp > 0 ? maxHp : 1, max: maxHp > 0 ? maxHp : 1 };
  }
  if (characterDataToSave.attributes!.dexterity) {
    characterDataToSave.ac = 10 + characterDataToSave.attributes!.dexterity.mod;
  }

  characterDataToSave.skills = [];
  document.querySelectorAll('.skill-item').forEach(skillItemElement => {
    const checkbox = skillItemElement.querySelector('input[type="checkbox"]') as HTMLInputElement;
    const modifierInput = skillItemElement.querySelector('.skill-modifier-input') as HTMLInputElement;
    const finalValueInput = skillItemElement.querySelector('.skill-final-value') as HTMLInputElement;

    if (checkbox && modifierInput && finalValueInput) {
      characterDataToSave.skills!.push({
        name: checkbox.dataset.skillName || '未知技能',
        proficient: checkbox.checked,
        attribute: checkbox.dataset.attribute || 'unknown',
        modifierValue: parseInt(modifierInput.value, 10) || 0,
        finalValue: parseInt(finalValueInput.value, 10) || 0,
      });
    }
  });

  const toolProfs = getVal('charToolProficienciesText').split(',').map(s => s.trim()).filter(s => s);
  characterDataToSave.proficiencies = [...new Set([...(characterDataToSave.proficiencies || []), ...toolProfs])];

  characterDataToSave.equippedSpells = [];
  document.querySelectorAll<HTMLInputElement>('#cantrips-list div.spell-item input[type="text"]').forEach(input => {
    const spellName = input.value.trim();
    if (spellName) characterDataToSave.equippedSpells!.push({ name: spellName, level: 0, source: '习得' });
  });
  document
    .querySelectorAll<HTMLInputElement>('#level1-spells-list div.spell-item input[type="text"]')
    .forEach(input => {
      const spellName = input.value.trim();
      if (spellName) characterDataToSave.equippedSpells!.push({ name: spellName, level: 1, source: '习得' });
    });

  if (!characterDataToSave.spellSlots) {
    characterDataToSave.spellSlots = {};
  }
  const slots1Value = getNum('spellSlots1', 0); // Assuming 'spellSlots1' is the ID for level 1 spell slots input
  if (slots1Value >= 0 && characterDataToSave.spellSlots) { // Check if slots1Value is non-negative
    characterDataToSave.spellSlots['1'] = { current: slots1Value, max: slots1Value };
  }


  characterDataToSave.equipment = [];
  document.querySelectorAll('#equipment-list-container .full-equipment-row').forEach(row => {
    const typeSelect = row.querySelector('.equipment-type-select') as HTMLSelectElement;
    const customNameInput = row.querySelector('.equipment-custom-name') as HTMLInputElement;
    const templateSelect = row.querySelector('.equipment-template-select') as HTMLSelectElement | null; // Might not exist for "饰品"
    const customDescriptionTextarea = row.querySelector('.equipment-custom-description') as HTMLTextAreaElement;

    const itemType = typeSelect.value as ModuleSetupEquipmentItem['type'];
    if (!itemType) return; // Skip if no type selected

    const itemName = customNameInput.value.trim();
    const baseItemName = templateSelect?.value || undefined;
    
    // If itemName is empty, try to use the selected template's name_zh
    let finalItemName = itemName;
    if (!finalItemName && baseItemName) {
        if (itemType === "武器") {
            const template = weaponTemplates.find((wt: WeaponTemplate) => wt.name_en === baseItemName);
            if (template) finalItemName = template.name_zh;
        } else if (itemType === "防具") {
            const template = armorTemplates.find((at: ArmorTemplate) => at.name_en === baseItemName);
            if (template) finalItemName = template.name_zh;
        }
    }
    if (!finalItemName && itemType === "饰品") { // For trinkets without a template
        finalItemName = "未命名饰品"; // Default name or skip if validation is stricter
    }
    if (!finalItemName) return; // Skip if no name can be determined


    const collectedMagicEffects: MagicEffect[] = [];
    row.querySelectorAll('.magic-effect-edit-row').forEach(effectRow => {
      const effectTypeSelect = effectRow.querySelector('.magic-effect-type-select') as HTMLSelectElement;
      const selectedEffectType = effectTypeSelect.value as EffectType;
      if (!selectedEffectType) return;

      const effectSchema = predefinedMagicEffectOptions.find((opt: PredefinedMagicEffectOption) => opt.type === selectedEffectType);
      if (!effectSchema) return;

      const effectValue: any = {};
      let allValuesCollected = true;
      effectSchema.valueSchema.forEach(fieldSchema => { // fieldSchema type is inferred from PredefinedMagicEffectOption.valueSchema
        const inputElement = effectRow.querySelector(`[name="${fieldSchema.name}"]`) as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
        if (inputElement) {
          if (fieldSchema.type === 'number') {
            const numVal = parseFloat(inputElement.value);
            effectValue[fieldSchema.name] = isNaN(numVal) ? undefined : numVal; // Store as number or undefined
          } else {
            effectValue[fieldSchema.name] = inputElement.value;
          }
          if (effectValue[fieldSchema.name] === undefined && fieldSchema.type !== 'textarea' && fieldSchema.type !== 'text') { // Text/textarea can be empty
             // Basic validation: if a non-text field is empty, maybe skip this effect or part of it
             // For simplicity, we'll assume required fields are filled. More robust validation could be added.
          }
        } else {
          allValuesCollected = false; 
        }
      });
      
      // Handle single value effects like ATTACK_BONUS where valueSchema might be just {name: "bonus", ...}
      // and the value is not an object but a direct value.
      let finalEffectValue = effectValue;
      if (effectSchema.valueSchema.length === 1 && effectSchema.valueSchema[0].name === "bonus" && effectValue.hasOwnProperty("bonus")) {
          finalEffectValue = effectValue.bonus;
      } else if (effectSchema.valueSchema.length === 1 && effectSchema.valueSchema[0].name === "amount" && effectValue.hasOwnProperty("amount") && !effectValue.damageType) {
          // For DAMAGE_BONUS_STATIC if only amount is provided
          finalEffectValue = { amount: effectValue.amount };
      }


      if (allValuesCollected) {
        collectedMagicEffects.push({
          type: selectedEffectType,
          value: finalEffectValue,
          notes: effectSchema.notes || selectedEffectType, // Use predefined notes or type as fallback
        });
      }
    });

    const equipmentItem: ModuleSetupEquipmentItem = {
      name: finalItemName,
      type: itemType,
      baseItemName: baseItemName,
      description: customDescriptionTextarea.value.trim(),
      equipped: true, // Default to equipped
      magicEffects: collectedMagicEffects.length > 0 ? collectedMagicEffects : undefined,
      // Physical properties like damage, properties, ac_base would typically be derived from baseItemName
      // by the adventure_log_v3 client. We don't need to store them redundantly here if baseItemName is set.
      // If we allow overriding them, then we'd collect them from UI too.
    };
    characterDataToSave.equipment!.push(equipmentItem);
  });

  characterDataToSave.inventory = [];
  document.querySelectorAll('#inventory-list-container .dynamic-item-row').forEach(row => {
    const nameInput = row.querySelector('input[name="inventoryItemName[]"]') as HTMLInputElement;
    const detailsInput = row.querySelector('input[name="inventoryItemDetails[]"]') as HTMLInputElement;
    const quantityInput = row.querySelector('input[name="inventoryItemQuantity[]"]') as HTMLInputElement;
    if (nameInput && nameInput.value.trim()) {
      characterDataToSave.inventory!.push({
        name: nameInput.value.trim(),
        quantity: quantityInput ? parseInt(quantityInput.value, 10) || 1 : 1,
        description: detailsInput ? detailsInput.value.trim() : '',
      });
    }
  });

  const characterJsonString = JSON.stringify(characterDataToSave, null, 2);

  const moduleSetupContainer = document.getElementById('module-setup-container');
  const characterCreationContainer = document.getElementById('character-creation-container');
  if (moduleSetupContainer && characterCreationContainer) {
    characterCreationContainer.style.display = 'none';
    moduleSetupContainer.style.display = 'block';
  }

  await saveContentToLorebook(characterName, characterJsonString, outputMessageDiv);
}
