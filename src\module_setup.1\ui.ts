import { safeToastr } from './utils';
import { handleSaveCustomModule, handleAiGenerateContent } from './module_creation';
import { handleSave<PERSON><PERSON><PERSON>, handleAiGenerate<PERSON>haracter } from './character_creation/data_handling';
import { setupAttributeCalculations } from './character_creation/attributes';
import { setupSpellcastingCalculations, populateSpellDatalists, setupDynamicSpellLists, updateSpellAttackAndDc } from './character_creation/spells';
import { setupDynamicEquipmentList, setupDynamicInventoryList } from './character_creation/equipment_inventory';
import { setupSkillCalculations, updateAllSkillFinalValues } from './character_creation/skills';

// This function will be called by setupAttributeCalculations and setupSpellcastingCalculations
// We need to define it here or ensure it's available globally if they are to call it.
// For better modularity, it's better if the setup functions in attributes/spells
// don't directly call functions from skills/spells respectively.
// Instead, the main onMounted can coordinate these calls.

// Making them available for attributes.ts and spells.ts if they use `declare function`
(window as any).updateSpellAttackAndDc = updateSpellAttackAndDc;
(window as any).updateAllSkillFinalValues = updateAllSkillFinalValues;


export function onMounted() {
  const moduleSetupContainer = document.getElementById('module-setup-container');
  const characterCreationContainer = document.getElementById('character-creation-container');

  const saveCustomButton = document.getElementById('save-custom-module-button');
  const aiGenerateModuleButton = document.getElementById('ai-generate-content-button');
  const goToCharacterCreationButton = document.getElementById('go-to-character-creation-button');

  const backToModuleSetupButton = document.getElementById('back-to-module-setup-button');
  const saveCharacterButton = document.getElementById('save-character-button');
  const aiGenerateCharacterButton = document.getElementById('ai-generate-character-button');

  if (!moduleSetupContainer || !characterCreationContainer) {
    safeToastr('error', '核心界面容器未找到!', '界面初始化错误');
    return;
  }

  // Module Setup Event Listeners
  if (saveCustomButton) {
    saveCustomButton.addEventListener('click', handleSaveCustomModule);
  }
  if (aiGenerateModuleButton) {
    aiGenerateModuleButton.addEventListener('click', handleAiGenerateContent);
  }
  if (goToCharacterCreationButton) {
    goToCharacterCreationButton.addEventListener('click', () => {
      moduleSetupContainer.style.display = 'none';
      characterCreationContainer.style.display = 'block';
      safeToastr('info', '已切换到人物创建界面。', '界面切换');
    });
  }

  // Character Creation Event Listeners
  if (backToModuleSetupButton) {
    backToModuleSetupButton.addEventListener('click', () => {
      characterCreationContainer.style.display = 'none';
      moduleSetupContainer.style.display = 'block';
      safeToastr('info', '已返回模组设置界面。', '界面切换');
    });
  }
  if (saveCharacterButton) {
    saveCharacterButton.addEventListener('click', handleSaveCharacter);
  }
  if (aiGenerateCharacterButton) {
    aiGenerateCharacterButton.addEventListener('click', handleAiGenerateCharacter);
  }

  // Initialize Character Creation Features
  // Order can be important here
  setupAttributeCalculations(); // Sets up listeners that might call updateSpellAttackAndDc and updateAllSkillFinalValues
  setupSkillCalculations();     // Sets up skill listeners and initial calculation
  setupSpellcastingCalculations(); // Sets up spell listeners that might call updateAllSkillFinalValues
  
  populateSpellDatalists();
  setupDynamicSpellLists();
  setupDynamicEquipmentList();
  setupDynamicInventoryList();

  // Initial calculations after all setups
  // Some of these might have been called during their respective setup,
  // but a final call ensures everything is up-to-date based on initial form values.
  updateAllSkillFinalValues();
  updateSpellAttackAndDc();


  safeToastr('info', '模组创作与人物创建导航界面已初始化。', '初始化完成');
}
