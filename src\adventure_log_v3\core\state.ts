import {
  AdventureSceneJSON,
  EquipmentItem,
  InventoryItem,
  LoadGameResult,
  PlayerState,
  WeaponTemplate,
} from '../types';
import { safeToastr } from '../utils';

// --- 类型声明 ---
declare function triggerSlash(command: string): Promise<string | undefined>;
declare function getLastMessageId(): number | undefined;
declare function setChatMessages(
  messages: { message_id: number; message: string }[],
  options?: { refresh?: string },
): Promise<void>;

// --- 核心状态变量 ---
export let playerState: PlayerState = getDefaultPlayerState();
export let weaponTemplates: WeaponTemplate[] = [];
export let currentSceneData: AdventureSceneJSON | null = null;
export let currentHostMessageId: number | null = null;

// --- 状态相关常量 ---
export const PLAYER_STATE_START_TAG = '<!-- PLAYER_STATE_START -->';
export const PLAYER_STATE_END_TAG = '<!-- PLAYER_STATE_END -->';

// --- 状态初始化与管理函数 ---
export function getDefaultPlayerState(): PlayerState {
  const defaultInventory: InventoryItem[] = [
    { id: 'default_backpack_01', name: '背包', quantity: 1, description: '一个普通的背包。' },
    { id: 'default_tinderbox_01', name: '火绒盒', quantity: 1, description: '用于生火。' },
    { id: 'default_rations_01', name: '口粮', quantity: 2, description: '一天的食物' },
  ];
  const backpack = defaultInventory.find(i => i.id === 'default_backpack_01');
  if (backpack) backpack.type = '容器';
  const tinderbox = defaultInventory.find(i => i.id === 'default_tinderbox_01');
  if (tinderbox) tinderbox.type = '工具';
  const rations = defaultInventory.find(i => i.id === 'default_rations_01');
  if (rations) rations.type = '消耗品';

  return {
    name: '新冒险者',
    race: '人类',
    class: '平民',
    level: 1,
    exp: 0,
    hp: { current: 10, max: 10 },
    ac: 10,
    currency: { gold: 10, silver: 0, copper: 0 },
    attributes: {
      strength: { base: 10, race_bonus: 0, modifier_bonus: 0, final: 10, mod: 0 },
      dexterity: { base: 10, race_bonus: 0, modifier_bonus: 0, final: 10, mod: 0 },
      constitution: { base: 10, race_bonus: 0, modifier_bonus: 0, final: 10, mod: 0 },
      intelligence: { base: 10, race_bonus: 0, modifier_bonus: 0, final: 10, mod: 0 },
      wisdom: { base: 10, race_bonus: 0, modifier_bonus: 0, final: 10, mod: 0 },
      charisma: { base: 10, race_bonus: 0, modifier_bonus: 0, final: 10, mod: 0 },
    },
    proficiencies: ['匕首'],
    skills: [],
    spellSlots: {
      '1': { current: 2, max: 2 }, // 1环法术槽
      '2': { current: 1, max: 1 }, // 2环法术槽
      '3': { current: 1, max: 1 }, // 3环法术槽
    },
    equippedSpells: [
      { name: '火焰箭', level: 0 },
      { name: '光亮术', level: 0 },
      { name: '魔法飞弹', level: 1 },
      { name: '治疗轻伤', level: 1 },
      { name: '火球术', level: 3 },
    ],
    equipment: [
      { id: 'default_clothing_01', name: '平民服装', type: '衣物', equipped: true, details: '一套普通的平民服装。' },
      {
        id: 'default_dagger_01',
        name: '匕首',
        baseItemName: '匕首',
        type: '武器',
        equipped: true,
        properties: ['灵巧', '轻型'],
        details: '一把标准的匕首。',
      },
    ],
    inventory: defaultInventory,
    activeQuests: [],
    exhaustion: 0,
    time: '第一天 清晨',
    currentLocation: '旅途的起点',
    spellcastingAbility: 'INT',
    lastProcessedSceneUuid: null,
  };
}

export function applyVariableUpdate(targetObject: any, path: string, operation: string, value: any): void {
  const pathParts = path.split('.');
  let currentTarget = targetObject;
  for (let i = 0; i < pathParts.length - 1; i++) {
    if (currentTarget[pathParts[i]] === undefined || typeof currentTarget[pathParts[i]] !== 'object') {
      console.error(`[applyVariableUpdate] Path part ${pathParts[i]} not found or not an object in target.`);
      return;
    }
    currentTarget = currentTarget[pathParts[i]];
  }
  const finalPropertyKey = pathParts[pathParts.length - 1];

  switch (operation) {
    case '设置':
      if (typeof currentTarget === 'object' && currentTarget !== null) {
        currentTarget[finalPropertyKey] = value;
      }
      break;
    // ... (other cases remain the same) ...
    case '增加':
      if (
        typeof currentTarget === 'object' &&
        currentTarget !== null &&
        typeof currentTarget[finalPropertyKey] === 'number' &&
        typeof value === 'number'
      ) {
        currentTarget[finalPropertyKey] += value;
      }
      break;
    case '减少':
      if (
        typeof currentTarget === 'object' &&
        currentTarget !== null &&
        typeof currentTarget[finalPropertyKey] === 'number' &&
        typeof value === 'number'
      ) {
        currentTarget[finalPropertyKey] -= value;
      }
      break;
    case '添加元素':
      if (
        typeof currentTarget === 'object' &&
        currentTarget !== null &&
        Array.isArray(currentTarget[finalPropertyKey])
      ) {
        currentTarget[finalPropertyKey].push(value);
      }
      break;
    case '移除元素':
      if (
        typeof currentTarget === 'object' &&
        currentTarget !== null &&
        Array.isArray(currentTarget[finalPropertyKey])
      ) {
        const index = currentTarget[finalPropertyKey].indexOf(value);
        if (index > -1) currentTarget[finalPropertyKey].splice(index, 1);
      }
      break;
    case '物品获得':
      if (path === 'inventory' && Array.isArray(currentTarget.inventory) && typeof value === 'object' && value.name) {
        const quantityToAdd = typeof value.quantity === 'number' ? value.quantity : 1;
        const existingItem = currentTarget.inventory.find(
          (item: InventoryItem) => (item.id && value.id && item.id === value.id) || item.name === value.name,
        );
        if (existingItem) {
          existingItem.quantity += quantityToAdd;
          existingItem.description = value.details || value.description || existingItem.description;
          if (value.baseItemName) existingItem.baseItemName = value.baseItemName;
          if (value.properties) existingItem.properties = value.properties;
          if (value.magicEffects) existingItem.magicEffects = value.magicEffects;
          if (value.type) existingItem.type = value.type;
        } else {
          const newItem: InventoryItem = {
            id: value.id || `item_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`,
            name: value.name,
            quantity: quantityToAdd,
            description: value.details || value.description || '',
            baseItemName: value.baseItemName,
            properties: value.properties || [],
            magicEffects: value.magicEffects || [],
          };
          if (value.type) {
            newItem.type = value.type;
          }
          currentTarget.inventory.push(newItem);
        }
      } else {
        console.warn(
          '[applyVariableUpdate] "物品获得" 操作失败：value 无效或 inventory 不是数组。',
          value,
          currentTarget.inventory,
        );
      }
      break;
    case '物品失去':
      if (
        path === 'inventory' &&
        Array.isArray(currentTarget.inventory) &&
        typeof value === 'object' &&
        value.name &&
        (typeof value.quantity === 'number' || value.quantity === undefined)
      ) {
        const quantityToRemove = typeof value.quantity === 'number' ? value.quantity : 1;
        const itemIndex = value.id
          ? currentTarget.inventory.findIndex((item: InventoryItem) => item.id === value.id)
          : currentTarget.inventory.findIndex((item: InventoryItem) => item.name === value.name);
        if (itemIndex > -1) {
          currentTarget.inventory[itemIndex].quantity -= quantityToRemove;
          if (currentTarget.inventory[itemIndex].quantity <= 0) {
            currentTarget.inventory.splice(itemIndex, 1);
          }
        }
      } else {
        console.warn(
          '[applyVariableUpdate] "物品失去" 操作失败：value 无效或 inventory 不是数组。',
          value,
          currentTarget.inventory,
        );
      }
      break;
    default:
      console.warn(`[applyVariableUpdate] Unknown operation: ${operation}`);
  }
}

export async function persistGameState(): Promise<void> {
  if (currentHostMessageId === null || typeof setChatMessages !== 'function') return;
  let persistedString = `${PLAYER_STATE_START_TAG}\n${JSON.stringify(
    playerState,
    null,
    2,
  )}\n${PLAYER_STATE_END_TAG}\n\n`;
  if (currentSceneData) {
    persistedString += `查看系统\n##@@_ADVENTURE_BEGIN_@@##\n${JSON.stringify(
      currentSceneData,
    )}\n##@@_ADVENTURE_END_@@##\n关闭系统`;
  }
  try {
    await setChatMessages([{ message_id: currentHostMessageId, message: persistedString }], { refresh: 'affected' });
  } catch (e) {
    safeToastr('error', `Failed to persist game state: ${(e as Error).message}`, 'Persistence Error');
  }
}

export async function loadGameState(): Promise<LoadGameResult> {
  //safeToastr('info', 'loadGameState: Attempting to load game state...', 'Load Debug');
  if (typeof getLastMessageId !== 'function' || typeof triggerSlash !== 'function') {
    safeToastr('error', 'loadGameState: SillyTavern API functions not found.', 'Load Debug');
    return { playerStateLoadedFromMsg: false, sceneDataLoadedFromMsg: false };
  }
  const hostIdResult = getLastMessageId();
  currentHostMessageId = hostIdResult === undefined ? null : hostIdResult;
  //safeToastr('info', `loadGameState: currentHostMessageId is ${currentHostMessageId}`, 'Load Debug');

  if (currentHostMessageId === null) {
    safeToastr('warning', 'loadGameState: currentHostMessageId is null. Cannot load.', 'Load Debug');
    return { playerStateLoadedFromMsg: false, sceneDataLoadedFromMsg: false };
  }

  let rawPersistedState: string | undefined;
  try {
    rawPersistedState = await triggerSlash(`/messages ${currentHostMessageId}`);
    //safeToastr('info', `loadGameState: rawPersistedState length: ${rawPersistedState?.length || 0}`, 'Load Debug');
  } catch (e) {
    safeToastr('error', `loadGameState: Error fetching host message: ${(e as Error).message}`, 'Load Debug');
    return { playerStateLoadedFromMsg: false, sceneDataLoadedFromMsg: false };
  }

  if (!rawPersistedState?.trim()) {
    safeToastr('warning', 'loadGameState: rawPersistedState is empty or whitespace.', 'Load Debug');
    return { playerStateLoadedFromMsg: false, sceneDataLoadedFromMsg: false };
  }

  let playerStateSuccessfullyParsed = false;
  let sceneDataSuccessfullyParsed = false;
  currentSceneData = null;

  const playerStateRegex = new RegExp(`${PLAYER_STATE_START_TAG}\\s*([\\s\\S]*?)\\s*${PLAYER_STATE_END_TAG}`);
  const playerStateMatch = rawPersistedState.match(playerStateRegex);
  //safeToastr('info', `loadGameState: PlayerState regex match: ${playerStateMatch ? 'found' : 'NOT found'}`, 'Load Debug');

  if (playerStateMatch?.[1]) {
    const playerStateJSONString = playerStateMatch[1].trim();
    //safeToastr('info', `loadGameState: PlayerState JSON string (first 100 chars): ${playerStateJSONString.substring(0,100)}`, 'Load Debug');
    try {
      const tempState = JSON.parse(playerStateJSONString);
      if (tempState?.name && tempState.attributes) {
        playerState = tempState;
        playerStateSuccessfullyParsed = true;
        //safeToastr('success', 'loadGameState: PlayerState parsed successfully.', 'Load Debug');
      } else {
        safeToastr(
          'warning',
          'loadGameState: Parsed PlayerState lacks essential fields (name/attributes).',
          'Load Debug',
        );
      }
    } catch (e) {
      safeToastr('error', `loadGameState: Error parsing PlayerState JSON: ${(e as Error).message}`, 'Load Debug');
      console.error('[AdvLogV3 Load] Error parsing PlayerState JSON:', e, playerStateJSONString);
    }
  } else {
    //safeToastr('warning', 'loadGameState: PlayerState tags not found or no content between them.', 'Load Debug');
  }

  const sceneDataRegex = /##@@_ADVENTURE_BEGIN_@@##([\s\S]*?)##@@_ADVENTURE_END_@@##/;
  const plotMatch = rawPersistedState.match(sceneDataRegex);
  //safeToastr('info', `loadGameState: SceneData regex match: ${plotMatch ? 'found' : 'NOT found'}`, 'Load Debug');

  if (plotMatch && plotMatch[1]) {
    const latestSceneJSONString = plotMatch[1].trim();
    //safeToastr('info', `loadGameState: SceneData JSON string (first 100 chars): ${latestSceneJSONString.substring(0,100)}`, 'Load Debug');
    if (latestSceneJSONString) {
      const parsedLatestScene = parseAIResponse(latestSceneJSONString);
      if (parsedLatestScene) {
        currentSceneData = parsedLatestScene;
        sceneDataSuccessfullyParsed = true;
        //safeToastr('success', 'loadGameState: SceneData parsed successfully.', 'Load Debug');
      } else {
        safeToastr('warning', 'loadGameState: parseAIResponse returned null for SceneData.', 'Load Debug');
      }
    } else {
      safeToastr('warning', 'loadGameState: SceneData JSON string is empty after trim.', 'Load Debug');
    }
  } else {
    //safeToastr('warning', 'loadGameState: SceneData tags not found or no content between them.', 'Load Debug');
  }

  //safeToastr('info', `loadGameState: Returning - PlayerState loaded: ${playerStateSuccessfullyParsed}, SceneData loaded: ${sceneDataSuccessfullyParsed}`, 'Load Debug');
  return {
    playerStateLoadedFromMsg: playerStateSuccessfullyParsed,
    sceneDataLoadedFromMsg: sceneDataSuccessfullyParsed,
  };
}

export function parseAIResponse(jsonString: string): AdventureSceneJSON | null {
  try {
    const parsedData = JSON.parse(jsonString);
    if (
      parsedData &&
      parsedData.sceneType &&
      parsedData.sceneTitle &&
      parsedData.narrative &&
      parsedData.playerChoices
    ) {
      return parsedData as AdventureSceneJSON;
    }
    console.error('[AdvLog] Invalid AdventureSceneJSON structure:', parsedData);
    safeToastr('error', 'parseAIResponse: Invalid AdventureSceneJSON structure.', 'Parse Debug');
    return null;
  } catch (e) {
    console.error('[AdvLog] Failed to parse AdventureSceneJSON string:', jsonString, e);
    safeToastr('error', `parseAIResponse: JSON.parse error - ${(e as Error).message}`, 'Parse Debug');
    return null;
  }
}

export function generateInitialAdventureSceneJSON(ps: PlayerState): AdventureSceneJSON {
  return {
    sceneType: 'location',
    sceneTitle: '冒险的序章',
    currentLocation: ps.currentLocation || '未知起点',
    time: ps.time || '某个时刻',
    narrative: [{ type: 'description', content: '你已准备好踏上征程！请选择你的第一个行动，让传奇开始！' }],
    playerChoices: [
      { id: 'A', text: '根据我的模组设定，正式开始冒险！', actionCommand: 'start_adventure_module' },
      { id: 'B', text: '我应该先了解一下我所处的环境（基于模组）。', actionCommand: 'survey_environment_module' },
      { id: 'C', text: '查看我的角色状态。', actionCommand: 'check_character_status' },
    ],
  };
}

export async function loadCharacterDataFromLorebook(characterName: string): Promise<string | null> {
  // ... (rest of the function remains the same)
  if (typeof triggerSlash !== 'function') return null;
  const lorebookFileName = 'RPG_Modules_Test.json';
  try {
    const findEntryCommand = `/findentry file="${lorebookFileName}" "${characterName}"`;
    const uidResult = await triggerSlash(findEntryCommand);
    if (!uidResult?.trim() || uidResult.trim() === '[]') return null;
    let entryUid: string | null = null;
    try {
      const parsedUidResult = JSON.parse(uidResult);
      if (Array.isArray(parsedUidResult) && parsedUidResult.length > 0) entryUid = parsedUidResult[0].toString();
      else if (typeof parsedUidResult === 'string' && parsedUidResult.trim() !== '') entryUid = parsedUidResult.trim();
      else if (typeof parsedUidResult === 'number') entryUid = parsedUidResult.toString();
    } catch (e) {
      if (typeof uidResult === 'string' && uidResult.trim() !== '') entryUid = uidResult.trim();
    }
    if (!entryUid) return null;
    const getContentCommand = `/getentryfield file="${lorebookFileName}" field=content "${entryUid}"`;
    const content = await triggerSlash(getContentCommand);
    return content?.trim() ? content : null;
  } catch (error) {
    return null;
  }
}

export async function loadWeaponTemplates(): Promise<void> {
  // ... (rest of the function remains the same)
  const weaponTemplatesJsonString = `
[
  { "name_zh": "匕首", "name_en": "Dagger", "category": "简易近战", "damage": "1d4", "damageType": "穿刺", "properties": ["灵巧", "轻型", "投掷 (射程 20/60)"], "weight": "1磅", "cost": "2 GP", "mastery": "迅击" },
  { "name_zh": "短棒", "name_en": "Club", "category": "简易近战", "damage": "1d4", "damageType": "钝击", "properties": ["轻型"], "weight": "2磅", "cost": "1 SP", "mastery": "缓速" },
  { "name_zh": "手斧", "name_en": "Handaxe", "category": "简易近战", "damage": "1d6", "damageType": "挥砍", "properties": ["轻型", "投掷 (射程 20/60)"], "weight": "2磅", "cost": "5 GP", "mastery": "侵扰" },
  { "name_zh": "标枪", "name_en": "Javelin", "category": "简易近战", "damage": "1d6", "damageType": "穿刺", "properties": ["投掷 (射程 30/120)"], "weight": "2磅", "cost": "5 SP", "mastery": "缓速" },
  { "name_zh": "轻锤", "name_en": "Light Hammer", "category": "简易近战", "damage": "1d4", "damageType": "钝击", "properties": ["轻型", "投掷 (射程 20/60)"], "weight": "2磅", "cost": "2 GP", "mastery": "迅击" },
  { "name_zh": "硬头锤", "name_en": "Mace", "category": "简易近战", "damage": "1d6", "damageType": "钝击", "properties": [], "weight": "4磅", "cost": "5 GP", "mastery": "削弱" },
  { "name_zh": "长棍", "name_en": "Quarterstaff", "category": "简易近战", "damage": "1d6", "damageType": "钝击", "properties": ["多用 (1d8)"], "weight": "4磅", "cost": "2 SP", "mastery": "失衡" },
  { "name_zh": "矛", "name_en": "Spear", "category": "简易近战", "damage": "1d6", "damageType": "穿刺", "properties": ["投掷 (射程 20/60)", "多用 (1d8)"], "weight": "3磅", "cost": "1 GP", "mastery": "削弱" },
  { "name_zh": "轻弩", "name_en": "Light Crossbow", "category": "简易远程", "damage": "1d8", "damageType": "穿刺", "properties": ["弹药 (射程 80/320；弩矢)", "装填", "双手"], "weight": "5磅", "cost": "25 GP", "mastery": "缓速" },
  { "name_zh": "短弓", "name_en": "Shortbow", "category": "简易远程", "damage": "1d6", "damageType": "穿刺", "properties": ["弹药 (射程 80/320；箭矢)", "双手"], "weight": "2磅", "cost": "25 GP", "mastery": "侵扰" },
  { "name_zh": "投石索", "name_en": "Sling", "category": "简易远程", "damage": "1d4", "damageType": "钝击", "properties": ["弹药 (射程 30/120；弹丸)"], "weight": "—", "cost": "1 SP", "mastery": "缓速" },
  { "name_zh": "战斧", "name_en": "Battleaxe", "category": "军用近战", "damage": "1d8", "damageType": "挥砍", "properties": ["多用 (1d10)"], "weight": "4磅", "cost": "10 GP", "mastery": "失衡" },
  { "name_zh": "长剑", "name_en": "Longsword", "category": "军用近战", "damage": "1d8", "damageType": "挥砍", "properties": ["多用 (1d10)"], "weight": "3磅", "cost": "15 GP", "mastery": "削弱" },
  { "name_zh": "巨剑", "name_en": "Greatsword", "category": "军用近战", "damage": "2d6", "damageType": "挥砍", "properties": ["重型", "双手"], "weight": "6磅", "cost": "50 GP", "mastery": "擦掠" },
  { "name_zh": "刺剑", "name_en": "Rapier", "category": "军用近战", "damage": "1d8", "damageType": "穿刺", "properties": ["灵巧"], "weight": "2磅", "cost": "25 GP", "mastery": "侵扰" },
  { "name_zh": "短剑", "name_en": "Shortsword", "category": "军用近战", "damage": "1d6", "damageType": "穿刺", "properties": ["灵巧", "轻型"], "weight": "2磅", "cost": "10 GP", "mastery": "侵扰" },
  { "name_zh": "长弓", "name_en": "Longbow", "category": "军用远程", "damage": "1d8", "damageType": "穿刺", "properties": ["弹药 (射程 150/600；箭矢)", "重型", "双手"], "weight": "2磅", "cost": "50 GP", "mastery": "缓速" }
]
  `;
  try {
    const parsedTemplates = JSON.parse(weaponTemplatesJsonString);
    if (Array.isArray(parsedTemplates)) {
      weaponTemplates = parsedTemplates;
      // safeToastr('success', `成功加载 ${weaponTemplates.length} 个武器模板。`, '模板加载'); // Keep this toastr as it's useful
    } else {
      safeToastr('error', '武器模板数据不是一个有效的JSON数组。', '模板加载错误');
    }
  } catch (e) {
    safeToastr('error', `解析内联武器模板JSON失败: ${(e as Error).message}`, '模板加载错误');
  }
}

export function setPlayerState(newState: PlayerState): void {
  playerState = newState;
}
export function setCurrentSceneData(newSceneData: AdventureSceneJSON | null): void {
  currentSceneData = newSceneData;
}
export function setCurrentHostMessageId(newId: number | null): void {
  currentHostMessageId = newId;
}
export function setWeaponTemplates(newTemplates: WeaponTemplate[]): void {
  weaponTemplates = newTemplates;
}

// --- Equipment Management Functions ---
export function equipItem(itemId: string): boolean {
  const itemIndexInInventory = playerState.inventory.findIndex(item => item.id === itemId);
  if (itemIndexInInventory === -1) {
    safeToastr('warning', `物品 ${itemId} 在物品栏中未找到。`, '装备失败');
    return false;
  }
  const itemToEquip = { ...playerState.inventory[itemIndexInInventory] };
  if (!itemToEquip.type || ['消耗品', '杂物', '材料'].includes(itemToEquip.type)) {
    safeToastr('info', `物品 ${itemToEquip.name} (${itemToEquip.type}) 不是可装备类型。`, '操作提示');
    return false;
  }
  const getItemSlotType = (type: string): string => {
    if (type === '盔甲') return 'armor';
    if (type === '武器') return 'weapon';
    return type;
  };
  const newItemSlotType = getItemSlotType(itemToEquip.type);
  const equippedItemsCopy = [...playerState.equipment];
  for (const equippedItem of equippedItemsCopy) {
    if (equippedItem.id && getItemSlotType(equippedItem.type) === newItemSlotType) {
      if (!unequipItem(equippedItem.id)) {
        console.error(`[AdvLog] Failed to unequip ${equippedItem.name} while trying to equip ${itemToEquip.name}`);
        return false;
      }
      safeToastr('info', `已卸下 ${equippedItem.name} 以装备 ${itemToEquip.name}。`, '自动卸下');
    }
  }
  const currentItemIndexInInventory = playerState.inventory.findIndex(item => item.id === itemId);
  if (currentItemIndexInInventory === -1) {
    console.warn(
      `[AdvLog] Item ${itemId} no longer in inventory after attempting to unequip others. Proceeding with original itemToEquip data.`,
    );
  } else {
    if (playerState.inventory[currentItemIndexInInventory].quantity > 1) {
      playerState.inventory[currentItemIndexInInventory].quantity -= 1;
    } else {
      playerState.inventory.splice(currentItemIndexInInventory, 1);
    }
  }
  const newEquipmentItem: EquipmentItem = {
    id: itemToEquip.id,
    name: itemToEquip.name,
    type: itemToEquip.type || '未知类型',
    details: itemToEquip.description,
    baseItemName: itemToEquip.baseItemName,
    properties: itemToEquip.properties,
    magicEffects: itemToEquip.magicEffects,
    equipped: true,
  };
  if (!itemToEquip.type) {
    console.warn(
      `[AdvLog] Equipping item '${itemToEquip.name}' which was missing a 'type' in inventory. Defaulted to '未知类型'.`,
    );
  }
  playerState.equipment.push(newEquipmentItem);
  safeToastr('success', `${itemToEquip.name} 已装备。`, '装备成功');
  return true;
}

export function unequipItem(itemId: string): boolean {
  const itemIndexInEquipment = playerState.equipment.findIndex(item => item.id === itemId);
  if (itemIndexInEquipment === -1) {
    safeToastr('warning', `物品 ${itemId} 未装备。`, '卸装失败');
    return false;
  }
  const itemToUnequip = playerState.equipment.splice(itemIndexInEquipment, 1)[0];
  const existingInventoryItem = playerState.inventory.find(
    invItem => (invItem.id && invItem.id === itemToUnequip.id) || invItem.name === itemToUnequip.name,
  );
  if (existingInventoryItem) {
    existingInventoryItem.quantity += 1;
    if (itemToUnequip.type) existingInventoryItem.type = itemToUnequip.type;
    existingInventoryItem.description = itemToUnequip.details || existingInventoryItem.description;
    existingInventoryItem.baseItemName = itemToUnequip.baseItemName || existingInventoryItem.baseItemName;
    existingInventoryItem.properties = itemToUnequip.properties || existingInventoryItem.properties;
    existingInventoryItem.magicEffects = itemToUnequip.magicEffects || existingInventoryItem.magicEffects;
  } else {
    const newInventoryItem: InventoryItem = {
      id: itemToUnequip.id,
      name: itemToUnequip.name,
      description: itemToUnequip.details,
      baseItemName: itemToUnequip.baseItemName,
      properties: itemToUnequip.properties,
      magicEffects: itemToUnequip.magicEffects,
      quantity: 1,
    };
    if (itemToUnequip.type) {
      newInventoryItem.type = itemToUnequip.type;
    }
    playerState.inventory.push(newInventoryItem);
  }
  safeToastr('success', `${itemToUnequip.name} 已卸下并放入物品栏。`, '卸装成功');
  return true;
}
