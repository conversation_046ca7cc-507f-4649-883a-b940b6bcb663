// --- JSON Schema Interfaces ---
export interface NPCProfile {
  name: string;
  role: string;
  description: string;
  motivation?: string;
}

export interface LocationProfile {
  name: string;
  type: string;
  description: string;
  pointsOfInterest?: string[];
}

export interface ModuleCreationData {
  title: string;
  narrativeContent: string;
  themes?: string[];
  dmNotes?: string;
}

// --- Adventure Log v3 Weapon/Magic Effect Types ---
export interface WeaponTemplate {
  name_zh: string;
  name_en: string;
  category: string;
  damage: string;
  damageType: string;
  properties: string[];
  weight: string;
  cost: string;
  mastery?: string;
}

export interface ArmorTemplate {
  name_zh: string;
  name_en: string;
  category: "轻甲" | "中甲" | "重甲" | "盾牌";
  ac_base: number;
  ac_dex_bonus: boolean; // 是否加敏捷调整值到AC
  ac_dex_max?: number | null; // 敏捷调整值上限 (null表示无上限)
  strength_requirement?: number | null; // 力量需求
  stealth_disadvantage: boolean; // 是否隐匿检定劣势
  weight: string;
  cost: string;
}

// This interface is also defined and exported from equipment_inventory.ts
// but data_handling.ts tries to import it from here.
// To avoid circular dependencies or confusion, ensure it's defined or re-exported here if needed.
// For now, assuming equipment_inventory.ts is the source of truth for this specific helper type.
// If data_handling.ts *must* get it from types.ts, then it should be defined here.
// Let's define it here to satisfy the current import in data_handling.ts
export interface PredefinedMagicEffectOption {
  label: string;
  type: EffectType;
  valueSchema: Array<{ name: string, type: 'text' | 'number' | 'textarea' | 'select', label: string, options?: string[] }>;
  notes?: string;
}

export type EffectType =
  | "ATTACK_BONUS"
  | "DAMAGE_BONUS_STATIC"
  | "DAMAGE_BONUS_DICE"
  | "ON_HIT_EFFECT_SAVE"
  | "ON_HIT_EFFECT_CONDITION"
  | "CRITICAL_HIT_MODIFIER"
  | "WEAPON_PROPERTY_GRANT"
  | "WEAPON_PROPERTY_MODIFY"
  | "SPELL_LIKE_ABILITY"
  | "ATTRIBUTE_REQUIREMENT"
  | "AC_BONUS" // For armor/shields/etc.
  | "SAVE_BONUS" // For saving throws
  | "SKILL_BONUS" // For skill checks
  | "PASSIVE_EFFECT" // e.g., resistance, immunity, special senses
  | "MOVEMENT_MODIFIER"
  | "HEALING_EFFECT"
  | "RESOURCE_MODIFIER" // e.g., spell slots, ki points
  | string; // Allow for custom effect types

export interface MagicEffect {
  type: EffectType;
  value: any;
  condition?: string;
  notes?: string;
}

export interface ModuleSetupEquipmentItem {
  id?: string;
  name: string;                 // 物品的显示名称，例如 "焰舌长剑+1"
  type: "武器" | "盔甲" | "盾牌" | "戒指" | "护符" | "奇物" | string;
  baseItemName?: string;         // 指向 WeaponTemplate 的 name_zh 或 name_en
  description?: string;
  equipped?: boolean;
  
  // Weapon specific, inherited or overridden from template
  damage?: string;
  damageType?: string;
  properties?: string[];
  
  magicEffects?: MagicEffect[];
  // For module_setup, we might not need full details like cost/weight if baseItemName is present
  // but `details` was the old field, so we can keep it for generic items or notes.
  details?: string; 
}
// --- End Adventure Log v3 Types ---

export interface AttributeData {
  base: number;
  race_bonus: number;
  modifier_bonus: number;
  final: number;
  mod: number;
}

export interface SkillData {
  name: string;
  proficient: boolean;
  attribute: string; // e.g., "strength", "dexterity"
  modifierValue: number; // User-entered modifier for this skill
  finalValue: number; // Calculated final value
}

export interface CharacterGenerationData {
  name: string;
  race: string;
  class: string;
  level: number;
  attributes: {
    strength: AttributeData;
    dexterity: AttributeData;
    constitution: AttributeData;
    intelligence: AttributeData;
    wisdom: AttributeData;
    charisma: AttributeData;
  };
  hp?: { current: number; max: number };
  ac?: number;
  spellcastingAbility?: 'INT' | 'WIS' | 'CHA' | 'STR' | 'DEX' | 'CON' | string;
  alignment?: string;
  background?: string;
  personalityTraits?: string;
  ideals?: string;
  bonds?: string;
  flaws?: string;
  appearance?: string;
  story?: string;
  skills: SkillData[]; // Updated to store structured skill data
  proficiencies?: string[]; // For tools, weapons, armor, languages, saving throws (non-skill profs)
  equippedSpells?: { name: string; level: number; source?: string }[];
  spellSlots?: Record<string, { current: number; max: number }>;
  equipment?: ModuleSetupEquipmentItem[]; // Use the new detailed equipment item type
  inventory?: { name: string; quantity: number; description?: string; type?: string; baseItemName?: string; magicEffects?: MagicEffect[] }[]; // Also enhance inventory slightly
  currency?: { gold: number; silver: number; copper: number };
  exp?: number;
  age?: string;
  gender?: string;
  faith?: string;
  height?: string;
  weight?: string;
  subclass?: string;
  toolProficienciesText?: string;
}

export interface AIResponseFormat<T> {
  requestType: 'moduleCreation' | 'characterGeneration';
  data: T;
  error?: string;
}
