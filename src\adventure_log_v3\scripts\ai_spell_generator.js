/**
 * AI法术生成器 - 基于知识库生成完整的DND5e法术数据
 * 生成符合SpellTemplate接口的完整法术信息
 */

const fs = require('fs');

// 法术学派映射
const SCHOOLS = {
  'abjuration': '防护',
  'conjuration': '咒法', 
  'divination': '预言',
  'enchantment': '惑控',
  'evocation': '塑能',
  'illusion': '幻术',
  'necromancy': '死灵',
  'transmutation': '变化'
};

// 伤害类型映射
const DAMAGE_TYPES = {
  'acid': '强酸',
  'bludgeoning': '钝击',
  'cold': '寒冷',
  'fire': '火焰',
  'force': '力场',
  'lightning': '闪电',
  'necrotic': '黯蚀',
  'piercing': '穿刺',
  'poison': '毒素',
  'psychic': '心灵',
  'radiant': '光耀',
  'slashing': '挥砍',
  'thunder': '雷鸣'
};

// 属性映射
const ATTRIBUTES = {
  'strength': '力量',
  'dexterity': '敏捷', 
  'constitution': '体质',
  'intelligence': '智力',
  'wisdom': '感知',
  'charisma': '魅力'
};

/**
 * 基于AI知识库的完整法术数据
 * 包含DND5e中最常用和重要的法术
 */
const AI_SPELL_DATABASE = [
  // === 戏法 (0环) ===
  {
    name_zh: '酸液飞溅',
    name_en: 'Acid Splash',
    level: 0,
    school: '塑能',
    casting_time: '1 动作',
    range: '60尺',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '创造酸液球攻击附近的敌人。',
    description_long: '你向射程内一点投掷一颗酸液球。选择该点5尺范围内的一个或两个生物。每个目标必须成功通过一次敏捷豁免，否则受到1d6点强酸伤害。',
    save: { attribute: '敏捷', effect_on_success: '无伤害' },
    damage: '1d6',
    damage_type: '强酸',
    scaling: { '5': '2d6', '11': '3d6', '17': '4d6' },
    area_of_effect: { type: '球形', size: '5尺半径' }
  },
  {
    name_zh: '剑刃防护',
    name_en: 'Blade Ward',
    level: 0,
    school: '防护',
    casting_time: '1 动作',
    range: '自身',
    components: ['V', 'S'],
    duration: '1轮',
    description_short: '获得对物理伤害的抗性。',
    description_long: '你伸出手臂，手指张开，获得对钝击、穿刺和挥砍伤害的抗性，直到你下个回合开始。'
  },
  {
    name_zh: '颤栗之触',
    name_en: 'Chill Touch',
    level: 0,
    school: '死灵',
    casting_time: '1 动作',
    range: '120尺',
    components: ['V', 'S'],
    duration: '1轮',
    description_short: '幽灵之手攻击目标并阻止治疗。',
    description_long: '你创造一只幽灵骷髅手朝一个生物伸去。进行一次远程法术攻击检定。命中时，目标受到1d8黯蚀伤害，且直到你下个回合开始前无法恢复生命值。',
    attack_type: '法术攻击 (远程)',
    damage: '1d8',
    damage_type: '黯蚀',
    scaling: { '5': '2d8', '11': '3d8', '17': '4d8' }
  },
  {
    name_zh: '舞光术',
    name_en: 'Dancing Lights',
    level: 0,
    school: '塑能',
    casting_time: '1 动作',
    range: '120尺',
    components: ['V', 'S', 'M (一点磷光物质)'],
    duration: '专注，至多1分钟',
    description_short: '创造移动的光源。',
    description_long: '你创造至多四个火把大小的光源，它们在法术持续时间内看起来像火把、灯笼或发光球体。你也可以将它们合并成一个发光的中型人形。'
  },
  {
    name_zh: '德鲁伊伎俩',
    name_en: 'Druidcraft',
    level: 0,
    school: '变化',
    casting_time: '1 动作',
    range: '30尺',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '创造小型自然效果。',
    description_long: '你向自然精魂低语，在射程内创造下述效果之一：预测天气、让花朵绽放、创造感官效果、点燃或熄灭小火焰。'
  },
  {
    name_zh: '魔能爆',
    name_en: 'Eldritch Blast',
    level: 0,
    school: '塑能',
    casting_time: '1 动作',
    range: '120尺',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '发射魔能射线攻击目标。',
    description_long: '一道噼啪作响的能量束朝着射程内的一个生物射去。进行一次远程法术攻击检定。命中时，目标受到1d10力场伤害。',
    attack_type: '法术攻击 (远程)',
    damage: '1d10',
    damage_type: '力场',
    scaling: { '5': '2d10', '11': '3d10', '17': '4d10' }
  },
  {
    name_zh: '火焰箭',
    name_en: 'Fire Bolt',
    level: 0,
    school: '塑能',
    casting_time: '1 动作',
    range: '120尺',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '发射火焰射线攻击目标。',
    description_long: '你向一个生物或物体投掷一团火焰。进行一次远程法术攻击检定。命中时，目标受到1d10火焰伤害。可燃物体会被点燃。',
    attack_type: '法术攻击 (远程)',
    damage: '1d10',
    damage_type: '火焰',
    scaling: { '5': '2d10', '11': '3d10', '17': '4d10' }
  },
  {
    name_zh: '交友术',
    name_en: 'Friends',
    level: 0,
    school: '惑控',
    casting_time: '1 动作',
    range: '自身',
    components: ['S', 'M (少量化妆品)'],
    duration: '专注，至多1分钟',
    description_short: '获得对一个生物的魅力优势。',
    description_long: '在法术持续时间内，你在针对一个你选择的生物的所有魅力检定上具有优势。法术结束时，该生物意识到你使用了魔法影响它的情绪。'
  },
  {
    name_zh: '神导术',
    name_en: 'Guidance',
    level: 0,
    school: '预言',
    casting_time: '1 动作',
    range: '触及',
    components: ['V', 'S'],
    duration: '专注，至多1分钟',
    description_short: '为目标的属性检定提供指导。',
    description_long: '你触碰一个自愿的生物。一次在法术结束前，该目标可以在进行一次属性检定时掷一个d4并将结果加到检定中。'
  },
  {
    name_zh: '光亮术',
    name_en: 'Light',
    level: 0,
    school: '塑能',
    casting_time: '1 动作',
    range: '触及',
    components: ['V', 'M (一只萤火虫或磷光苔藓)'],
    duration: '1小时',
    description_short: '使物体发出明亮光芒。',
    description_long: '你触碰一个不大于10尺立方的物体。直到法术结束，该物体散发出20尺半径的明亮光芒，以及额外20尺半径的微光。'
  },
  {
    name_zh: '法师之手',
    name_en: 'Mage Hand',
    level: 0,
    school: '咒法',
    casting_time: '1 动作',
    range: '30尺',
    components: ['V', 'S'],
    duration: '1分钟',
    description_short: '创造幽灵之手操作物体。',
    description_long: '一只幽灵般的手出现在射程内的一个点上。这只手持续到法术结束或你用一个动作解除它。你可以用动作控制这只手操作物体、开门等。'
  },
  {
    name_zh: '修复术',
    name_en: 'Mending',
    level: 0,
    school: '变化',
    casting_time: '1 分钟',
    range: '触及',
    components: ['V', 'S', 'M (两块磁石)'],
    duration: '立即',
    description_short: '修复物体上的破损。',
    description_long: '这个法术修复物体上的单一破损或裂缝，比如断裂的链环、两半的钥匙、撕裂的斗篷或漏水的酒袋。只要破损不超过1尺，就能完全修复。'
  },
  {
    name_zh: '传讯术',
    name_en: 'Message',
    level: 0,
    school: '变化',
    casting_time: '1 动作',
    range: '120尺',
    components: ['V', 'S', 'M (一小段铜线)'],
    duration: '1轮',
    description_short: '向远处的生物传递消息。',
    description_long: '你指向射程内的一个生物并低声说出一条消息。目标（且只有目标）听到该消息，并可以用低语回复你。'
  },
  {
    name_zh: '心灵之屑',
    name_en: 'Mind Sliver',
    level: 0,
    school: '惑控',
    casting_time: '1 动作',
    range: '60尺',
    components: ['V'],
    duration: '1轮',
    description_short: '心灵攻击削弱目标防御。',
    description_long: '你向一个生物的心灵发动攻击。目标必须进行智力豁免。失败时受到1d6心灵伤害，且下次豁免检定减少1d4。',
    save: { attribute: '智力', effect_on_success: '无伤害' },
    damage: '1d6',
    damage_type: '心灵',
    scaling: { '5': '2d6', '11': '3d6', '17': '4d6' }
  },
  {
    name_zh: '次级幻象',
    name_en: 'Minor Illusion',
    level: 0,
    school: '幻术',
    casting_time: '1 动作',
    range: '30尺',
    components: ['S', 'M (一点羊毛)'],
    duration: '1分钟',
    description_short: '创造声音或静态幻象。',
    description_long: '你在射程内创造一个声音或一个物体的幻象，持续法术时间。如果创造声音，音量可以从低语到尖叫。如果创造物体幻象，必须不大于5尺立方。'
  },
  {
    name_zh: '毒气喷涌',
    name_en: 'Poison Spray',
    level: 0,
    school: '咒法',
    casting_time: '1 动作',
    range: '10尺',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '喷射毒气攻击附近敌人。',
    description_long: '你向射程内的一个生物伸出手掌，从掌心喷出一股毒气。目标必须成功通过一次体质豁免，否则受到1d12毒素伤害。',
    save: { attribute: '体质', effect_on_success: '无伤害' },
    damage: '1d12',
    damage_type: '毒素',
    scaling: { '5': '2d12', '11': '3d12', '17': '4d12' }
  },
  {
    name_zh: '魔法伎俩',
    name_en: 'Prestidigitation',
    level: 0,
    school: '变化',
    casting_time: '1 动作',
    range: '10尺',
    components: ['V', 'S'],
    duration: '至多1小时',
    description_short: '创造各种小型魔法效果。',
    description_long: '这个法术是施法者学会的小把戏。你在射程内创造下述魔法效果之一：感官效果、点燃或熄灭火焰、清洁或弄脏物体、调味食物、魔法印记或创造小物品。'
  },
  {
    name_zh: '燃火术',
    name_en: 'Produce Flame',
    level: 0,
    school: '咒法',
    casting_time: '1 动作',
    range: '自身',
    components: ['V', 'S'],
    duration: '10分钟',
    description_short: '在手中创造火焰，可投掷攻击。',
    description_long: '一团摇曳的火焰出现在你手中。火焰不伤害你或你的装备，并散发出明亮光芒。你可以用动作将火焰投向30尺内的目标。',
    attack_type: '法术攻击 (远程)',
    damage: '1d8',
    damage_type: '火焰',
    scaling: { '5': '2d8', '11': '3d8', '17': '4d8' }
  },
  {
    name_zh: '冷冻射线',
    name_en: 'Ray of Frost',
    level: 0,
    school: '塑能',
    casting_time: '1 动作',
    range: '60尺',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '寒冰射线造成伤害并减缓目标。',
    description_long: '一道蓝白色的寒冰射线朝着射程内的一个生物射去。进行一次远程法术攻击检定。命中时，目标受到1d8寒冷伤害，且速度降低10尺直到你的下个回合开始。',
    attack_type: '法术攻击 (远程)',
    damage: '1d8',
    damage_type: '寒冷',
    scaling: { '5': '2d8', '11': '3d8', '17': '4d8' }
  },
  {
    name_zh: '抵抗术',
    name_en: 'Resistance',
    level: 0,
    school: '防护',
    casting_time: '1 动作',
    range: '触及',
    components: ['V', 'S', 'M (一件小斗篷)'],
    duration: '专注，至多1分钟',
    description_short: '为目标的豁免检定提供加值。',
    description_long: '你触碰一个自愿的生物。一次在法术结束前，该目标可以在进行一次豁免检定时掷一个d4并将结果加到检定中。'
  },
  {
    name_zh: '圣火术',
    name_en: 'Sacred Flame',
    level: 0,
    school: '塑能',
    casting_time: '1 动作',
    range: '60尺',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '神圣火焰从天而降攻击目标。',
    description_long: '类似火焰的神圣能量从天而降，攻击射程内一个你能看见的生物。目标必须成功通过一次敏捷豁免，否则受到1d8光耀伤害。',
    save: { attribute: '敏捷', effect_on_success: '无伤害' },
    damage: '1d8',
    damage_type: '光耀',
    scaling: { '5': '2d8', '11': '3d8', '17': '4d8' }
  },

  // === 1环法术 ===
  {
    name_zh: '魔法飞弹',
    name_en: 'Magic Missile',
    level: 1,
    school: '塑能',
    casting_time: '1 动作',
    range: '120尺',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '自动命中的魔法飞弹。',
    description_long: '你创造出三支闪光的魔法飞弹。每支飞弹自动命中你指定的一个位于射程内的可见生物，造成1d4+1力场伤害。',
    attack_type: '自动命中',
    damage: '1d4+1',
    damage_type: '力场',
    num_projectiles: 3,
    higher_level_cast: {
      per_slot_above_base: '每高于1环的法术位',
      effect: '增加一支飞弹'
    }
  },
  {
    name_zh: '治疗轻伤',
    name_en: 'Cure Wounds',
    level: 1,
    school: '塑能',
    casting_time: '1 动作',
    range: '触及',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '恢复目标的生命值。',
    description_long: '你触碰的生物恢复1d8+你的施法属性调整值点生命值。这个法术对不死生物或构装体无效。',
    damage: '1d8',
    damage_type: '治疗',
    add_casting_modifier_to_damage: true,
    higher_level_cast: {
      per_slot_above_base: '每高于1环的法术位',
      effect: '增加1d8治疗'
    }
  },
  {
    name_zh: '祝福术',
    name_en: 'Bless',
    level: 1,
    school: '惑控',
    casting_time: '1 动作',
    range: '30尺',
    components: ['V', 'S', 'M (一滴圣水)'],
    duration: '专注，至多1分钟',
    description_short: '为最多三个生物提供攻击和豁免加值。',
    description_long: '你祝福最多三个射程内的生物。每当目标进行攻击检定或豁免检定时，可以掷一个d4并将结果加到检定中。',
    higher_level_cast: {
      per_slot_above_base: '每高于1环的法术位',
      effect: '额外影响一个目标'
    }
  },
  {
    name_zh: '护盾术',
    name_en: 'Shield',
    level: 1,
    school: '防护',
    casting_time: '1 反应',
    range: '自身',
    components: ['V', 'S'],
    duration: '1轮',
    description_short: '获得+5AC加值并免疫魔法飞弹。',
    description_long: '一道无形的魔法力场出现并保护你。直到你下个回合开始，你的AC获得+5加值，包括触发此法术的攻击，且你不受魔法飞弹法术影响。'
  },
  {
    name_zh: '魅惑人类',
    name_en: 'Charm Person',
    level: 1,
    school: '惑控',
    casting_time: '1 动作',
    range: '30尺',
    components: ['V', 'S'],
    duration: '1小时',
    description_short: '魅惑一个类人生物。',
    description_long: '你试图魅惑射程内一个你能看见的类人生物。目标必须进行感知豁免。失败时，目标被你魅惑直到法术结束或你伤害它。',
    save: { attribute: '感知', effect_on_success: '无效果' },
    higher_level_cast: {
      per_slot_above_base: '每高于1环的法术位',
      effect: '额外影响一个目标'
    }
  },
  {
    name_zh: '侦测魔法',
    name_en: 'Detect Magic',
    level: 1,
    school: '预言',
    casting_time: '1 动作',
    range: '自身',
    components: ['V', 'S'],
    duration: '专注，至多10分钟',
    description_short: '感知附近的魔法灵光。',
    description_long: '在法术持续时间内，你感知到30尺内的魔法存在。如果你感知到魔法，你可以用动作看到微弱的灵光围绕着任何可见的魔法生物或物体。'
  },
  {
    name_zh: '燃烧之手',
    name_en: 'Burning Hands',
    level: 1,
    school: '塑能',
    casting_time: '1 动作',
    range: '自身（15尺锥形）',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '锥形火焰攻击多个目标。',
    description_long: '你的手指张开，火焰从中喷出。15尺锥形范围内的每个生物必须进行敏捷豁免。失败时受到3d6火焰伤害，成功时伤害减半。',
    save: { attribute: '敏捷', effect_on_success: '伤害减半' },
    damage: '3d6',
    damage_type: '火焰',
    area_of_effect: { type: '锥形', size: '15尺' },
    higher_level_cast: {
      per_slot_above_base: '每高于1环的法术位',
      effect: '增加1d6伤害'
    }
  },
  {
    name_zh: '睡眠术',
    name_en: 'Sleep',
    level: 1,
    school: '惑控',
    casting_time: '1 动作',
    range: '90尺',
    components: ['V', 'S', 'M (一撮沙子、玫瑰花瓣或蟋蟀)'],
    duration: '1分钟',
    description_short: '使生物陷入魔法睡眠。',
    description_long: '这个法术让生物陷入魔法睡眠。掷5d8；总和就是这个法术能影响的生命值总数。从射程内20尺半径内生命值最少的生物开始。',
    area_of_effect: { type: '球形', size: '20尺半径' },
    higher_level_cast: {
      per_slot_above_base: '每高于1环的法术位',
      effect: '增加2d8生命值'
    }
  },
  {
    name_zh: '雷鸣波',
    name_en: 'Thunderwave',
    level: 1,
    school: '塑能',
    casting_time: '1 动作',
    range: '自身（15尺立方）',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '雷鸣冲击波推开敌人。',
    description_long: '一阵雷鸣从你身上爆发。15尺立方范围内的每个生物必须进行体质豁免。失败时受到2d8雷鸣伤害并被推开10尺，成功时伤害减半且不被推开。',
    save: { attribute: '体质', effect_on_success: '伤害减半，不被推开' },
    damage: '2d8',
    damage_type: '雷鸣',
    area_of_effect: { type: '立方', size: '15尺' },
    higher_level_cast: {
      per_slot_above_base: '每高于1环的法术位',
      effect: '增加1d8伤害'
    }
  }
];

/**
 * 生成世界书格式的法术数据
 */
function generateSpellWorldBooks() {
  console.log('开始生成AI法术世界书...\n');
  
  // 按等级分组法术
  const spellsByLevel = {};
  AI_SPELL_DATABASE.forEach(spell => {
    if (!spellsByLevel[spell.level]) {
      spellsByLevel[spell.level] = [];
    }
    spellsByLevel[spell.level].push(spell);
  });
  
  // 环数名称映射
  const levelNames = {
    0: '戏法',
    1: '一环法术',
    2: '二环法术',
    3: '三环法术',
    4: '四环法术',
    5: '五环法术',
    6: '六环法术',
    7: '七环法术',
    8: '八环法术',
    9: '九环法术'
  };
  
  // 为每个等级生成世界书
  for (const [level, spells] of Object.entries(spellsByLevel)) {
    const levelNum = parseInt(level);
    const levelName = levelNames[levelNum];
    
    const worldBook = {
      entries: {
        [3000 + levelNum]: {
          uid: 3000 + levelNum,
          key: [`AI_SPELLS_LEVEL_${levelNum}`, `AI_${levelName}`, `DND5E_AI_${levelName}`],
          keysecondary: [],
          comment: `AI生成的DND5e ${levelName}完整数据 (${spells.length}个法术)`,
          content: JSON.stringify(spells, null, 2),
          constant: true,
          vectorized: false,
          selective: true,
          selectiveLogic: 0,
          addMemo: true,
          order: 100,
          position: 0,
          disable: false,
          excludeRecursion: false,
          preventRecursion: false,
          delayUntilRecursion: false,
          probability: 100,
          useProbability: true,
          depth: 4,
          group: "",
          groupOverride: false,
          groupWeight: 100,
          scanDepth: null,
          caseSensitive: null,
          matchWholeWords: null,
          useGroupScoring: null,
          automationId: "",
          role: null,
          sticky: 0,
          cooldown: 0,
          delay: 0,
          displayIndex: 3000 + levelNum
        }
      }
    };
    
    const outputPath = `AI_DND5e_${levelName}_Complete_WorldBook.json`;
    fs.writeFileSync(outputPath, JSON.stringify(worldBook, null, 2), 'utf-8');
    console.log(`生成: ${outputPath} (${spells.length}个完整法术)`);
  }
  
  // 生成完整法术库
  const completeWorldBook = {
    entries: {
      4000: {
        uid: 4000,
        key: ["AI_SPELLS_ALL", "AI所有法术", "DND5E_AI_ALL_SPELLS", "AI法术库"],
        keysecondary: [],
        comment: `AI生成的DND5e完整法术库 (${AI_SPELL_DATABASE.length}个法术)`,
        content: JSON.stringify(AI_SPELL_DATABASE, null, 2),
        constant: true,
        vectorized: false,
        selective: true,
        selectiveLogic: 0,
        addMemo: true,
        order: 100,
        position: 0,
        disable: false,
        excludeRecursion: false,
        preventRecursion: false,
        delayUntilRecursion: false,
        probability: 100,
        useProbability: true,
        depth: 4,
        group: "",
        groupOverride: false,
        groupWeight: 100,
        scanDepth: null,
        caseSensitive: null,
        matchWholeWords: null,
        useGroupScoring: null,
        automationId: "",
        role: null,
        sticky: 0,
        cooldown: 0,
        delay: 0,
        displayIndex: 4000
      }
    }
  };
  
  const completeOutputPath = 'AI_DND5e_Complete_Spells_WorldBook.json';
  fs.writeFileSync(completeOutputPath, JSON.stringify(completeWorldBook, null, 2), 'utf-8');
  console.log(`生成: ${completeOutputPath} (${AI_SPELL_DATABASE.length}个完整法术)`);
}

// 执行生成
if (require.main === module) {
  console.log('=== AI法术生成器 ===');
  console.log('基于AI知识库生成完整的DND5e法术数据\n');
  
  generateSpellWorldBooks();
  
  console.log('\n=== 生成完成 ===');
  console.log('所有AI法术世界书文件已生成！');
  console.log('这些文件包含完整的法术属性，可直接用于游戏系统。');
}
