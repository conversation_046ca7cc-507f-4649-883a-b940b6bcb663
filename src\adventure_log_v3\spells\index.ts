import {
  SpellTemplate,
  SpellDamageResult,
  PlayerState,
  CheckResult,
} from '../types';
import {
  parseDamageString,
  rollDice,
  getProficiencyBonus,
  safeToastr,
} from '../utils';

// 全局法术模板存储
let spellTemplates: SpellTemplate[] = [];

/**
 * 导出法术模板数组（只读）
 */
export function getSpellTemplates(): SpellTemplate[] {
  return spellTemplates;
}

/**
 * 加载法术模板数据
 */
export async function loadSpellTemplates(): Promise<void> {
  try {
    // 这里可以从文件或API加载法术模板
    // 暂时使用内置的基础法术模板
    spellTemplates = getDefaultSpellTemplates();
    console.log(`[SpellSystem] 已加载 ${spellTemplates.length} 个法术模板`);
  } catch (error) {
    console.error('[SpellSystem] 加载法术模板失败:', error);
    spellTemplates = getDefaultSpellTemplates();
  }
}

/**
 * 获取法术模板
 */
export function getSpellTemplate(spellName: string): SpellTemplate | null {
  if (!spellTemplates || spellTemplates.length === 0) {
    return null;
  }

  const template = spellTemplates.find(
    template => template.name_zh === spellName || template.name_en === spellName
  ) || null;

  return template;
}

/**
 * 获取所有法术模板
 */
export function getAllSpellTemplates(): SpellTemplate[] {
  return [...spellTemplates];
}

/**
 * 计算法术攻击检定
 */
export function performSpellAttack(
  spellName: string,
  targetAC: number,
  playerState: PlayerState,
  advantageState: 'advantage' | 'disadvantage' | 'normal' = 'normal'
): CheckResult {
  const spellTemplate = getSpellTemplate(spellName);
  if (!spellTemplate) {
    throw new Error(`未找到法术模板: ${spellName}`);
  }

  // 确定施法属性
  const castingAbility = (playerState.spellcastingAbility?.toLowerCase() || 'intelligence') as keyof PlayerState['attributes'];
  const attributeMod = playerState.attributes[castingAbility]?.mod || 0;
  const proficiencyBonus = getProficiencyBonus(playerState.level);

  // 执行攻击检定
  let roll1 = Math.floor(Math.random() * 20) + 1;
  let roll = roll1;

  if (advantageState === 'advantage') {
    const roll2 = Math.floor(Math.random() * 20) + 1;
    roll = Math.max(roll1, roll2);
  } else if (advantageState === 'disadvantage') {
    const roll2 = Math.floor(Math.random() * 20) + 1;
    roll = Math.min(roll1, roll2);
  }

  const total = roll + attributeMod + proficiencyBonus;
  const success = total >= targetAC;
  const isCritical = roll === 20;
  const isFumble = roll === 1;

  return {
    success: isCritical || (!isFumble && success),
    roll,
    attributeMod,
    proficiencyBonusApplied: proficiencyBonus,
    total,
    dc: targetAC,
    attributeName: castingAbility,
    skillName: spellName,
    isCritical,
    isFumble,
    advantageState,
  };
}

/**
 * 计算法术伤害
 */
export function calculateSpellDamage(
  spellName: string,
  playerState: PlayerState,
  isCritical: boolean = false,
  spellSlotLevel?: number
): SpellDamageResult {
  const spellTemplate = getSpellTemplate(spellName);
  if (!spellTemplate) {
    throw new Error(`未找到法术模板: ${spellName}`);
  }

  if (!spellTemplate.damage) {
    return {
      totalDamage: 0,
      damageType: '无伤害',
      details: '此法术不造成伤害',
      breakdown: [],
    };
  }

  let totalDamage = 0;
  let damageBreakdown: { type: string; amount: number; source: string }[] = [];
  let detailsParts: string[] = [];
  let projectileCount = spellTemplate.num_projectiles || 1;

  // 获取施法属性调整值
  const castingAbility = (playerState.spellcastingAbility?.toLowerCase() || 'intelligence') as keyof PlayerState['attributes'];
  const castingMod = playerState.attributes[castingAbility]?.mod || 0;

  // 处理戏法等级提升
  let baseDamage = spellTemplate.damage;
  if (spellTemplate.level === 0 && spellTemplate.scaling) {
    const playerLevel = playerState.level;
    for (const [levelStr, scaledDamage] of Object.entries(spellTemplate.scaling)) {
      const level = parseInt(levelStr);
      if (playerLevel >= level) {
        baseDamage = scaledDamage;
      }
    }
  }

  // 处理升环施法
  if (spellSlotLevel && spellSlotLevel > spellTemplate.level && spellTemplate.higher_level_cast) {
    const levelsAboveBase = spellSlotLevel - spellTemplate.level;
    const effect = spellTemplate.higher_level_cast.effect;
    
    if (effect.includes('增加') && effect.includes('d')) {
      // 处理伤害增加，如 "增加1d6伤害"
      const damageMatch = effect.match(/增加(\d+d\d+)/);
      if (damageMatch) {
        const additionalDamage = damageMatch[1];
        const parsedAdditional = parseDamageString(additionalDamage);
        if (parsedAdditional) {
          for (let i = 0; i < levelsAboveBase; i++) {
            let additionalRoll = rollDice(parsedAdditional.count, parsedAdditional.die);
            if (isCritical) {
              additionalRoll += rollDice(parsedAdditional.count, parsedAdditional.die);
            }
            totalDamage += additionalRoll;
            detailsParts.push(`升环+${i + 1}(${additionalDamage}): ${additionalRoll}`);
          }
        }
      }
    } else if (effect.includes('飞弹') || effect.includes('投射物')) {
      // 处理投射物增加，如魔法飞弹
      projectileCount += levelsAboveBase;
    }
  }

  // 计算基础伤害
  const parsedDamage = parseDamageString(baseDamage);
  if (parsedDamage) {
    for (let i = 0; i < projectileCount; i++) {
      let damageRoll = rollDice(parsedDamage.count, parsedDamage.die);
      
      if (isCritical) {
        damageRoll += rollDice(parsedDamage.count, parsedDamage.die);
      }
      
      damageRoll += parsedDamage.modifier;
      
      // 添加施法调整值（如果适用）
      if (spellTemplate.add_casting_modifier_to_damage) {
        damageRoll += castingMod;
      }
      
      totalDamage += damageRoll;
      
      if (projectileCount > 1) {
        detailsParts.push(`投射物${i + 1}: ${damageRoll}`);
      } else {
        detailsParts.push(`基础伤害: ${damageRoll}`);
      }
    }
    
    damageBreakdown.push({
      type: spellTemplate.damage_type || '魔法',
      amount: totalDamage,
      source: spellName,
    });
  }

  const detailsString = detailsParts.join(' + ') + ` = ${totalDamage}`;
  
  // 计算豁免DC（如果需要）
  let saveRequired: SpellDamageResult['saveRequired'];
  if (spellTemplate.save) {
    const saveDC = 8 + castingMod + getProficiencyBonus(playerState.level);
    saveRequired = {
      attribute: spellTemplate.save.attribute,
      dc: saveDC,
      effectOnSuccess: spellTemplate.save.effect_on_success,
    };
  }

  return {
    totalDamage,
    damageType: spellTemplate.damage_type || '魔法',
    details: detailsString,
    breakdown: damageBreakdown,
    projectileCount: projectileCount > 1 ? projectileCount : undefined,
    saveRequired,
  };
}

/**
 * 检查法术槽是否足够
 */
export function canCastSpell(spellName: string, playerState: PlayerState, spellSlotLevel?: number): boolean {
  safeToastr('info', `检查法术施放能力: ${spellName}`, 'Debug - Can Cast');

  const spellTemplate = getSpellTemplate(spellName);
  if (!spellTemplate) {
    safeToastr('error', `找不到法术模板: ${spellName}`, 'Debug - Can Cast');
    return false;
  }

  const requiredLevel = spellSlotLevel || spellTemplate.level;
  safeToastr('info', `法术等级: ${requiredLevel}`, 'Debug - Can Cast');

  // 戏法不需要法术槽
  if (requiredLevel === 0) {
    safeToastr('success', `${spellName} 是戏法，可以施放`, 'Debug - Can Cast');
    return true;
  }

  if (!playerState.spellSlots) {
    safeToastr('error', '玩家没有法术槽数据', 'Debug - Can Cast');
    return false;
  }

  const slotInfo = playerState.spellSlots[requiredLevel.toString()];
  const canCast = slotInfo && slotInfo.current > 0;

  if (slotInfo) {
    safeToastr('info', `${requiredLevel}环法术槽: ${slotInfo.current}/${slotInfo.max}`, 'Debug - Can Cast');
  } else {
    safeToastr('error', `没有${requiredLevel}环法术槽`, 'Debug - Can Cast');
  }

  safeToastr(canCast ? 'success' : 'error', `${spellName} ${canCast ? '可以' : '无法'}施放`, 'Debug - Can Cast');
  return canCast;
}

/**
 * 消耗法术槽
 */
export function consumeSpellSlot(playerState: PlayerState, spellSlotLevel: number): boolean {
  if (spellSlotLevel === 0) return true; // 戏法不消耗法术槽

  const slotInfo = playerState.spellSlots[spellSlotLevel.toString()];
  if (slotInfo && slotInfo.current > 0) {
    slotInfo.current--;
    return true;
  }
  return false;
}

/**
 * 获取默认法术模板
 */
function getDefaultSpellTemplates(): SpellTemplate[] {
  return [
    // === 戏法 (0环) ===
    {
      name_zh: '火焰箭',
      name_en: 'Fire Bolt',
      level: 0,
      school: '塑能',
      casting_time: '1 动作',
      range: '120尺',
      components: ['V', 'S'],
      duration: '立即',
      description_short: '发出一道火焰射线攻击目标。',
      description_long: '你向一个你可见的目标发出一道火焰射线。进行一次远程法术攻击检定。若命中，目标受到1d10火焰伤害。',
      attack_type: '法术攻击 (远程)',
      damage: '1d10',
      damage_type: '火焰',
      scaling: { '5': '2d10', '11': '3d10', '17': '4d10' },
    },
    {
      name_zh: '寒冰射线',
      name_en: 'Ray of Frost',
      level: 0,
      school: '塑能',
      casting_time: '1 动作',
      range: '60尺',
      components: ['V', 'S'],
      duration: '立即',
      description_short: '发出寒冰射线，造成伤害并减缓目标。',
      description_long: '一道蓝白色的寒冰射线朝着射程内的一个生物射去。进行一次远程法术攻击检定。若命中，目标受到1d8寒冷伤害，且速度降低10尺直到你的下个回合开始。',
      attack_type: '法术攻击 (远程)',
      damage: '1d8',
      damage_type: '寒冷',
      scaling: { '5': '2d8', '11': '3d8', '17': '4d8' },
    },
    {
      name_zh: '光亮术',
      name_en: 'Light',
      level: 0,
      school: '塑能',
      casting_time: '1 动作',
      range: '触及',
      components: ['V', 'M (一只萤火虫或磷光苔藓)'],
      duration: '1小时',
      description_short: '使物体发出明亮光芒。',
      description_long: '你触碰一个不大于10尺立方的物体。直到法术结束，该物体散发出20尺半径的明亮光芒，以及额外20尺半径的微光。',
    },
    {
      name_zh: '法师之手',
      name_en: 'Mage Hand',
      level: 0,
      school: '咒法',
      casting_time: '1 动作',
      range: '30尺',
      components: ['V', 'S'],
      duration: '1分钟',
      description_short: '创造一只幽灵之手来操作物体。',
      description_long: '一只幽灵般的手出现在射程内的一个点上。这只手持续到法术结束或你用一个动作解除它。你可以用你的动作来控制这只手。',
    },
    {
      name_zh: '修复术',
      name_en: 'Mending',
      level: 0,
      school: '变化',
      casting_time: '1 分钟',
      range: '触及',
      components: ['V', 'S', 'M (两块磁石)'],
      duration: '立即',
      description_short: '修复物体上的小破损。',
      description_long: '这个法术修复物体上的单一破损或裂缝，比如断裂的链环、两半的钥匙、撕裂的斗篷或漏水的酒袋。',
    },
    {
      name_zh: '圣火术',
      name_en: 'Sacred Flame',
      level: 0,
      school: '塑能',
      casting_time: '1 动作',
      range: '60尺',
      components: ['V', 'S'],
      duration: '立即',
      description_short: '神圣火焰从天而降攻击目标。',
      description_long: '类似火焰的神圣能量从天而降，攻击射程内一个你能看见的生物。目标必须成功通过一次敏捷豁免，否则受到1d8光耀伤害。',
      save: { attribute: '敏捷', effect_on_success: '无伤害' },
      damage: '1d8',
      damage_type: '光耀',
      scaling: { '5': '2d8', '11': '3d8', '17': '4d8' },
    },
    {
      name_zh: '神导术',
      name_en: 'Guidance',
      level: 0,
      school: '预言',
      casting_time: '1 动作',
      range: '触及',
      components: ['V', 'S'],
      duration: '专注，至多1分钟',
      description_short: '为目标的下一次属性检定提供指导。',
      description_long: '你触碰一个自愿的生物。一次在法术结束前，该目标可以在进行一次属性检定时掷一个d4并将结果加到检定中。',
    },
    {
      name_zh: '抗力术',
      name_en: 'Resistance',
      level: 0,
      school: '防护',
      casting_time: '1 动作',
      range: '触及',
      components: ['V', 'S', 'M (一件小斗篷)'],
      duration: '专注，至多1分钟',
      description_short: '为目标的下一次豁免检定提供抗力。',
      description_long: '你触碰一个自愿的生物。一次在法术结束前，该目标可以在进行一次豁免检定时掷一个d4并将结果加到检定中。',
    },

    // === 1环法术 ===
    {
      name_zh: '魔法飞弹',
      name_en: 'Magic Missile',
      level: 1,
      school: '塑能',
      casting_time: '1 动作',
      range: '120尺',
      components: ['V', 'S'],
      duration: '立即',
      description_short: '自动命中目标的多枚魔法飞弹。',
      description_long: '你创造出三支闪光的魔法飞弹。每支飞弹自动命中你指定的一个位于射程内的可见生物。',
      attack_type: '自动命中',
      damage: '1d4+1',
      damage_type: '力场',
      num_projectiles: 3,
      add_casting_modifier_to_damage: false,
      higher_level_cast: {
        per_slot_above_base: '每高于1环的法术位',
        effect: '增加一支飞弹',
      },
    },
    {
      name_zh: '治疗真言',
      name_en: 'Cure Wounds',
      level: 1,
      school: '塑能',
      casting_time: '1 动作',
      range: '触及',
      components: ['V', 'S'],
      duration: '立即',
      description_short: '恢复目标的生命值。',
      description_long: '你触碰的生物恢复1d8+你的施法属性调整值点生命值。这个法术对不死生物或构装体无效。',
      damage: '1d8',
      damage_type: '治疗',
      add_casting_modifier_to_damage: true,
      higher_level_cast: {
        per_slot_above_base: '每高于1环的法术位',
        effect: '增加1d8治疗',
      },
    },
    {
      name_zh: '祝福术',
      name_en: 'Bless',
      level: 1,
      school: '惑控',
      casting_time: '1 动作',
      range: '30尺',
      components: ['V', 'S', 'M (一滴圣水)'],
      duration: '专注，至多1分钟',
      description_short: '为最多三个生物的攻击检定和豁免检定提供加值。',
      description_long: '你祝福最多三个射程内的生物。每当目标进行攻击检定或豁免检定时，可以掷一个d4并将结果加到检定中。',
      higher_level_cast: {
        per_slot_above_base: '每高于1环的法术位',
        effect: '额外影响一个目标',
      },
    },
    {
      name_zh: '命令术',
      name_en: 'Command',
      level: 1,
      school: '惑控',
      casting_time: '1 动作',
      range: '60尺',
      components: ['V'],
      duration: '1轮',
      description_short: '命令一个生物执行简单的动作。',
      description_long: '你对射程内一个你能看见的生物说出一个词的命令。目标必须成功通过感知豁免，否则必须在其下个回合执行该命令。',
      save: { attribute: '感知', effect_on_success: '无效果' },
      higher_level_cast: {
        per_slot_above_base: '每高于1环的法术位',
        effect: '额外影响一个目标',
      },
    },
    {
      name_zh: '防护善恶',
      name_en: 'Protection from Evil and Good',
      level: 1,
      school: '防护',
      casting_time: '1 动作',
      range: '触及',
      components: ['V', 'S', 'M (圣水或银粉和铁粉)'],
      duration: '专注，至多10分钟',
      description_short: '保护目标免受特定生物类型的伤害。',
      description_long: '直到法术结束，一个自愿的生物受到保护，免受特定类型生物的影响：异界生物、元素、精类、不死生物和邪魔。',
    },
    {
      name_zh: '魅惑人类',
      name_en: 'Charm Person',
      level: 1,
      school: '惑控',
      casting_time: '1 动作',
      range: '30尺',
      components: ['V', 'S'],
      duration: '1小时',
      description_short: '魅惑一个类人生物。',
      description_long: '你试图魅惑一个你能看见且在射程内的类人生物。目标必须进行一次感知豁免，否则被你魅惑直到法术结束或你或你的同伴对它造成伤害。',
      save: { attribute: '感知', effect_on_success: '无效果' },
      higher_level_cast: {
        per_slot_above_base: '每高于1环的法术位',
        effect: '额外影响一个目标',
      },
    },
    {
      name_zh: '护盾术',
      name_en: 'Shield',
      level: 1,
      school: '防护',
      casting_time: '1 反应',
      range: '自身',
      components: ['V', 'S'],
      duration: '直到你的下个回合开始',
      description_short: '获得+5 AC加值和魔法飞弹免疫。',
      description_long: '一道无形的魔法力场出现并保护你。直到你的下个回合开始，你的AC获得+5加值，包括触发这个法术的攻击，并且你对魔法飞弹法术免疫。',
    },
    {
      name_zh: '睡眠术',
      name_en: 'Sleep',
      level: 1,
      school: '惑控',
      casting_time: '1 动作',
      range: '90尺',
      components: ['V', 'S', 'M (一撮细沙、玫瑰花瓣或蟋蟀)'],
      duration: '1分钟',
      description_short: '使区域内的生物陷入魔法睡眠。',
      description_long: '这个法术使生物陷入魔法睡眠。在射程内一个你选择的20尺半径范围内掷5d8。从最低当前生命值的生物开始，受影响的生物陷入昏迷。',
      area_of_effect: { type: '球形', size: '20尺半径' },
      higher_level_cast: {
        per_slot_above_base: '每高于1环的法术位',
        effect: '增加2d8',
      },
    },
    {
      name_zh: '燃烧之手',
      name_en: 'Burning Hands',
      level: 1,
      school: '塑能',
      casting_time: '1 动作',
      range: '自身 (15尺锥形)',
      components: ['V', 'S'],
      duration: '立即',
      description_short: '从你手中喷出火焰。',
      description_long: '当你伸出双手时，一道火焰薄片从你指尖喷薄而出。区域内的每个生物都必须进行一次敏捷豁免。',
      damage: '3d6',
      damage_type: '火焰',
      save: { attribute: '敏捷', effect_on_success: '伤害减半' },
      area_of_effect: { type: '锥形', size: '15尺' },
      higher_level_cast: {
        per_slot_above_base: '每高于1环的法术位',
        effect: '增加1d6伤害',
      },
    },

    // === 2环法术 ===
    {
      name_zh: '蛛网术',
      name_en: 'Web',
      level: 2,
      school: '咒法',
      casting_time: '1 动作',
      range: '60尺',
      components: ['V', 'S', 'M (一点蛛网)'],
      duration: '专注，至多1小时',
      description_short: '创造粘性蛛网束缚敌人。',
      description_long: '你在射程内的一个点上咒法出厚厚的蛛网，填满一个20尺立方的区域。区域内的生物必须进行敏捷豁免，失败则被束缚。',
      save: { attribute: '敏捷', effect_on_success: '无效果' },
      area_of_effect: { type: '立方', size: '20尺' },
    },
    {
      name_zh: '隐身术',
      name_en: 'Invisibility',
      level: 2,
      school: '幻术',
      casting_time: '1 动作',
      range: '触及',
      components: ['V', 'S', 'M (一根睫毛包在阿拉伯胶中)'],
      duration: '专注，至多1小时',
      description_short: '使目标隐身。',
      description_long: '你触碰的生物变为隐身，直到法术结束。任何目标穿戴或携带的物品只要在目标身上就同样隐身。',
      higher_level_cast: {
        per_slot_above_base: '每高于2环的法术位',
        effect: '额外影响一个目标',
      },
    },
    {
      name_zh: '朦胧步',
      name_en: 'Misty Step',
      level: 2,
      school: '咒法',
      casting_time: '1 附赠动作',
      range: '自身',
      components: ['V'],
      duration: '立即',
      description_short: '瞬间传送30尺。',
      description_long: '你被银雾包围，然后传送至你能看见的30尺内的一个未被占据的空间。',
    },
    {
      name_zh: '灼热射线',
      name_en: 'Scorching Ray',
      level: 2,
      school: '塑能',
      casting_time: '1 动作',
      range: '120尺',
      components: ['V', 'S'],
      duration: '立即',
      description_short: '发射多道火焰射线。',
      description_long: '你创造三道火焰射线，并向射程内的目标发射。你可以将射线指向同一目标或不同目标。每道射线进行单独的攻击检定。',
      attack_type: '法术攻击 (远程)',
      damage: '2d6',
      damage_type: '火焰',
      num_projectiles: 3,
      higher_level_cast: {
        per_slot_above_base: '每高于2环的法术位',
        effect: '增加一道射线',
      },
    },

    // === 3环法术 ===
    {
      name_zh: '火球术',
      name_en: 'Fireball',
      level: 3,
      school: '塑能',
      casting_time: '1 动作',
      range: '150尺',
      components: ['V', 'S', 'M (一小撮蝙蝠粪和硫磺)'],
      duration: '立即',
      description_short: '爆炸性火球造成大范围伤害。',
      description_long: '一道明亮的火焰从你指尖射出，在射程内的一个点爆炸成火球。区域内的每个生物必须进行敏捷豁免。',
      damage: '8d6',
      damage_type: '火焰',
      save: { attribute: '敏捷', effect_on_success: '伤害减半' },
      area_of_effect: { type: '球形', size: '20尺半径' },
      higher_level_cast: {
        per_slot_above_base: '每高于3环的法术位',
        effect: '增加1d6伤害',
      },
    },
    {
      name_zh: '闪电束',
      name_en: 'Lightning Bolt',
      level: 3,
      school: '塑能',
      casting_time: '1 动作',
      range: '自身 (100尺线形)',
      components: ['V', 'S', 'M (一点毛皮和一根琥珀、玻璃或水晶棒)'],
      duration: '立即',
      description_short: '发射闪电线形攻击。',
      description_long: '一道闪电从你身上射出，形成100尺长、5尺宽的线形。线形路径上的每个生物必须进行敏捷豁免。',
      damage: '8d6',
      damage_type: '闪电',
      save: { attribute: '敏捷', effect_on_success: '伤害减半' },
      area_of_effect: { type: '线形', size: '100尺长5尺宽' },
      higher_level_cast: {
        per_slot_above_base: '每高于3环的法术位',
        effect: '增加1d6伤害',
      },
    },
    {
      name_zh: '反制法术',
      name_en: 'Counterspell',
      level: 3,
      school: '防护',
      casting_time: '1 反应',
      range: '60尺',
      components: ['S'],
      duration: '立即',
      description_short: '阻止敌人施法。',
      description_long: '你试图打断一个正在施法的生物。如果该生物施放的是3环或更低的法术，其法术失败且没有效果。',
      higher_level_cast: {
        per_slot_above_base: '每高于3环的法术位',
        effect: '自动反制更高环的法术',
      },
    },
  ];
}
