import './index.scss';

// --- 类型声明 ---
declare const $: any; // jQ<PERSON>y
declare const toastr: {
  success: (message: string, title?: string) => void;
  info: (message: string, title?: string) => void;
  error: (message: string, title?: string) => void;
  warning: (message: string, title?: string) => void;
};
// SillyTavern / TavernHelper injected functions
declare function triggerSlash(command: string): Promise<string | undefined>;
declare function getLastMessageId(): number | undefined;
declare function setChatMessages(
  messages: { message_id: number; message: string }[],
  options?: { refresh?: string },
): Promise<void>;

// --- 核心数据接口 (基于 adventure_log_data_format_guide.md) ---
interface Attribute {
  base: number;
  race_bonus: number;
  modifier_bonus: number;
  final: number;
  mod: number;
}

interface Hp {
  current: number;
  max: number;
}

interface Currency {
  gold: number;
  silver: number;
  copper: number;
}

interface Skill {
  name: string;
  proficient: boolean;
  value: number;
}

interface SpellSlotInfo {
  current: number;
  max: number;
}

interface Spell {
  name: string;
  level: number;
  source?: string;
  details?: string;
}

interface EquipmentItem {
  name: string;
  type: string;
  equipped?: boolean;
  details?: string;
}

interface InventoryItem {
  name: string;
  quantity: number;
  description?: string;
}

interface PlayerState {
  name: string;
  race: string;
  class: string;
  level: number;
  exp: number;
  hp: Hp;
  ac: number;
  currency: Currency;
  attributes: {
    strength: Attribute;
    dexterity: Attribute;
    constitution: Attribute;
    intelligence: Attribute;
    wisdom: Attribute;
    charisma: Attribute;
  };
  proficiencies: string[];
  skills: Skill[];
  spellSlots: Record<string, SpellSlotInfo>;
  equippedSpells: Spell[];
  equipment: EquipmentItem[];
  inventory: InventoryItem[];
  activeQuests: string[];
  exhaustion: number;
  time: string;
  currentLocation: string;
  player?: string;
  age?: string;
  gender?: string;
  alignment?: string;
  faith?: string;
  height?: string;
  weight?: string;
  appearance?: string;
  story?: string;
  background?: string;
  personalityTraits?: string;
  ideals?: string;
  bonds?: string;
  flaws?: string;
  subclass?: string;
  spellcastingAbility?: 'INT' | 'WIS' | 'CHA' | string;
}

interface ActionChoiceDefinition {
  id: string;
  text: string;
  actionCommand: string;
}

interface AdventureScene {
  sceneType: 'location' | 'dialogue' | 'system_message' | 'combat' | 'puzzle';
  title?: string;
  description: string;
  npcName?: string;
  playerChoices?: ActionChoiceDefinition[];
  healthUpdate?: string;
  goldUpdate?: number;
  locationUpdate?: string;
  timeUpdate?: string;
  exhaustionUpdate?: number;
  systemMessage?: string;
  enemyInfo?: string;
  rawInputBlock?: string;
  variableUpdates?: VariableUpdateInstruction[];
}

interface VariableUpdateInstruction {
  target: string; // e.g., "玩家"
  path: string; // e.g., "exp", "currency.gold", "inventory"
  operation: string; // e.g., "增加", "设置", "物品获得"
  value: any; // Can be number, string, or a parsed JSON object for items
}

// --- 模块级状态变量 ---
function getDefaultPlayerState(): PlayerState {
  return {
    name: '新冒险者',
    race: '人类',
    class: '平民',
    level: 1,
    exp: 0,
    hp: { current: 10, max: 10 },
    ac: 10,
    currency: { gold: 10, silver: 0, copper: 0 },
    attributes: {
      strength: { base: 10, race_bonus: 0, modifier_bonus: 0, final: 10, mod: 0 },
      dexterity: { base: 10, race_bonus: 0, modifier_bonus: 0, final: 10, mod: 0 },
      constitution: { base: 10, race_bonus: 0, modifier_bonus: 0, final: 10, mod: 0 },
      intelligence: { base: 10, race_bonus: 0, modifier_bonus: 0, final: 10, mod: 0 },
      wisdom: { base: 10, race_bonus: 0, modifier_bonus: 0, final: 10, mod: 0 },
      charisma: { base: 10, race_bonus: 0, modifier_bonus: 0, final: 10, mod: 0 },
    },
    proficiencies: ['匕首'],
    skills: [],
    spellSlots: {},
    equippedSpells: [],
    equipment: [
      { name: '平民服装', type: '衣物', equipped: true },
      { name: '匕首', type: '武器', equipped: true },
    ],
    inventory: [
      { name: '背包', quantity: 1 },
      { name: '火绒盒', quantity: 1 },
      { name: '口粮', quantity: 2, description: '一天的食物' },
    ],
    activeQuests: [],
    exhaustion: 0,
    time: '第一天 清晨',
    currentLocation: '旅途的起点',
  };
}

let playerState: PlayerState = getDefaultPlayerState();

interface AdventureLogEntry {
  rawSceneBlock: string;
  playerChoiceText?: string;
  timestamp: number;
}
let fullHistoryLog: AdventureLogEntry[] = [];

let currentSceneDataFromMessage: AdventureScene | null = null;
let currentHostMessageId: number | null = null;

// --- DOM元素引用 ---
let startScreenContainer: HTMLElement | null = null;
let startNewGameButton: HTMLButtonElement | null = null;
let adventureLogContainer: HTMLElement | null = null;
let playerStatusArea: HTMLElement | null = null;
let mainNarrativeArea: HTMLElement | null = null;
let actionChoicesArea: HTMLElement | null = null;
let healthDisplay: HTMLElement | null = null;
let locationDisplay: HTMLElement | null = null;
let timeDisplay: HTMLElement | null = null;
let charNameDisplay: HTMLElement | null = null;
let charRaceClassDisplay: HTMLElement | null = null;
let charLevelDisplay: HTMLElement | null = null;
let acDisplay: HTMLElement | null = null;
let attrStrDisplay: HTMLElement | null = null;
let attrDexDisplay: HTMLElement | null = null;
let attrConDisplay: HTMLElement | null = null;
let attrIntDisplay: HTMLElement | null = null;
let attrWisDisplay: HTMLElement | null = null;
let attrChaDisplay: HTMLElement | null = null;
let currencyGoldDisplay: HTMLElement | null = null;
let expDisplay: HTMLElement | null = null;
let exhaustionDisplay: HTMLElement | null = null;
let toggleCharSheetButton: HTMLButtonElement | null = null;
let detailedCharacterSheet: HTMLElement | null = null;
let proficienciesDisplay: HTMLElement | null = null;
let skillsDisplay: HTMLElement | null = null;
let spellSlotsDisplay: HTMLElement | null = null;
let equippedSpellsDisplay: HTMLElement | null = null;
let equipmentDisplay: HTMLElement | null = null;
let inventoryDisplay: HTMLElement | null = null;
let activeQuestsDisplay: HTMLElement | null = null;

// --- 工具函数 ---
function safeToastr(type: 'info' | 'success' | 'warning' | 'error', message: string, title?: string) {
  try {
     if (
       typeof (window as any).toastr !== 'object' &&
       typeof parent !== 'undefined' &&
       typeof (parent as any).toastr === 'object'
     ) {
       (window as any).toastr = (parent as any).toastr;
     }
     if (typeof toastr === 'object' && toastr !== null && typeof toastr[type] === 'function') {
       toastr[type](message, title);
     } else {
       const consoleFn = type === 'error' ? console.error : type === 'warning' ? console.warn : console.log;
       consoleFn(`[AdvLog Toastr Fallback - ${type}] ${title ? title + ': ' : ''}${message}`);
     }
  } catch (e) {
     console.error(`[AdvLog] safeToastr Error: ${(e as Error).message}`);
  }
}

function ensureGlobals() {
  try {
    if (typeof window !== 'undefined' && typeof parent !== 'undefined' && parent !== window) {
      const apiKeysToCopy = ['$', 'toastr', 'triggerSlash', 'getLastMessageId', 'setChatMessages'];
      apiKeysToCopy.forEach(key => {
        if (typeof (window as any)[key] === 'undefined') {
          if (typeof (parent as any)[key] !== 'undefined') {
            (window as any)[key] = (parent as any)[key];
          } else {
            // safeToastr('warning', `Global API "${key}" is UNDEFINED in parent.`, 'Globals Init Error');
          }
        }
      });
    }
  } catch (e) {
    // safeToastr('error', `ensureGlobals Error: ${(e as Error).message}`, 'Globals Init Error');
  }
}

// --- UI更新函数 ---
function updatePlayerStatusDisplay() {
  if (!playerState) {
    // safeToastr('warning', 'updatePlayerStatusDisplay called with null playerState.', 'UI Update Warning');
    return;
  }
  if (charNameDisplay) charNameDisplay.textContent = `角色名: ${playerState.name || 'N/A'}`;
  if (charRaceClassDisplay)
    charRaceClassDisplay.textContent = `种族/职业: ${playerState.race || 'N/A'} / ${playerState.class || 'N/A'}`;
  if (charLevelDisplay) charLevelDisplay.textContent = `等级: ${playerState.level || 0}`;

  if (healthDisplay)
    healthDisplay.textContent = `生命值: ${playerState.hp?.current || '?'}/${playerState.hp?.max || '?'}`;
  if (acDisplay) acDisplay.textContent = `AC: ${playerState.ac || 0}`;
  if (timeDisplay) timeDisplay.textContent = `时间: ${playerState.time || 'N/A'}`;
  if (locationDisplay) locationDisplay.textContent = `地点: ${playerState.currentLocation || 'N/A'}`;

  const formatAttr = (attr: Attribute | undefined) =>
    attr ? `${attr.final}(${attr.mod >= 0 ? '+' : ''}${attr.mod})` : 'N/A';
  if (attrStrDisplay) attrStrDisplay.textContent = `力量: ${formatAttr(playerState.attributes?.strength)}`;
  if (attrDexDisplay) attrDexDisplay.textContent = `敏捷: ${formatAttr(playerState.attributes?.dexterity)}`;
  if (attrConDisplay) attrConDisplay.textContent = `体质: ${formatAttr(playerState.attributes?.constitution)}`;
  if (attrIntDisplay) attrIntDisplay.textContent = `智力: ${formatAttr(playerState.attributes?.intelligence)}`;
  if (attrWisDisplay) attrWisDisplay.textContent = `感知: ${formatAttr(playerState.attributes?.wisdom)}`;
  if (attrChaDisplay) attrChaDisplay.textContent = `魅力: ${formatAttr(playerState.attributes?.charisma)}`;

  if (currencyGoldDisplay) currencyGoldDisplay.textContent = `${playerState.currency?.gold || 0}`;
  if (expDisplay) expDisplay.textContent = `${playerState.exp || 0}`;
  if (exhaustionDisplay) exhaustionDisplay.textContent = `${playerState.exhaustion || 0}`;

  if (proficienciesDisplay)
    proficienciesDisplay.innerHTML = playerState.proficiencies?.map(p => `<li>${p}</li>`).join('') || '<li>无</li>';
  if (skillsDisplay)
    skillsDisplay.innerHTML =
      playerState.skills
        ?.filter(s => s.proficient)
        .map(s => `<li>${s.name} (${s.value >= 0 ? '+' : ''}${s.value})</li>`)
        .join('') || '<li>无熟练技能</li>';

  if (spellSlotsDisplay) {
    let spellSlotsHtml = '';
    if (playerState.spellSlots && Object.keys(playerState.spellSlots).length > 0) {
      for (const level in playerState.spellSlots) {
        if (playerState.spellSlots.hasOwnProperty(level)) {
          const slots = playerState.spellSlots[level];
          spellSlotsHtml += `<div>${level}环: ${slots.current}/${slots.max}</div>`;
        }
      }
    }
    spellSlotsDisplay.innerHTML = spellSlotsHtml || '无';
  }

  if (equippedSpellsDisplay)
    equippedSpellsDisplay.innerHTML =
      playerState.equippedSpells?.map(s => `<li>${s.name} (${s.level}环)</li>`).join('') || '<li>无</li>';
  if (equipmentDisplay)
    equipmentDisplay.innerHTML =
      playerState.equipment?.map(e => `<li>${e.name} (${e.type})${e.equipped ? ' [已装备]' : ''}</li>`).join('') ||
      '<li>无</li>';
  if (inventoryDisplay)
    inventoryDisplay.innerHTML =
      playerState.inventory
        ?.map(i => `<li>${i.name} x${i.quantity} ${i.description ? '(' + i.description + ')' : ''}</li>`)
        .join('') || '<li>空</li>';
  if (activeQuestsDisplay)
    activeQuestsDisplay.innerHTML = playerState.activeQuests?.map(q => `<li>${q}</li>`).join('') || '<li>无</li>';
}

function renderNarrative(scene: AdventureScene) {
  if (mainNarrativeArea) {
    const descriptionText = typeof scene.description === 'string' ? scene.description : '';
    mainNarrativeArea.innerHTML = `<p>${descriptionText.replace(/\n/g, '<br>')}</p>`;
    if (scene.systemMessage) {
      mainNarrativeArea.innerHTML += `<p class="system-message"><em>${scene.systemMessage.replace(
        /\n/g,
        '<br>',
      )}</em></p>`;
    }
    if (scene.enemyInfo) {
      mainNarrativeArea.innerHTML += `<p class="enemy-info"><strong>遭遇敌人:</strong> ${scene.enemyInfo}</p>`;
    }
  }
}

function renderActionChoices(choices: ActionChoiceDefinition[] | undefined) {
  if (!actionChoicesArea) return;
  actionChoicesArea.innerHTML = '';
  if (choices && choices.length > 0) {
    choices.forEach(choice => {
      const button = document.createElement('button');
      button.id = choice.id;
      button.textContent = choice.text;
      button.dataset.action = choice.actionCommand;
      button.addEventListener('click', () => handleActionChoice(choice));
      actionChoicesArea!.appendChild(button);
    });
  } else {
    actionChoicesArea.innerHTML = '<p>暂无行动选项。</p>';
  }
}

// --- 核心逻辑函数 ---

// Helper function to get proficiency bonus based on level
function getProficiencyBonus(level: number): number {
  if (level >= 17) return 6;
  if (level >= 13) return 5;
  if (level >= 9) return 4;
  if (level >= 5) return 3;
  return 2; // Level 1-4
}

interface CheckResult {
  success: boolean;
  roll: number;
  attributeMod: number;
  proficiencyBonusApplied: number;
  total: number;
  dc: number;
  attributeName: string;
  skillName?: string;
}

// Helper function to perform a d20 check
function performCheck(
  dc: number,
  attributeName: string,
  skillName: string | undefined,
  pState: PlayerState,
): CheckResult {
  const roll = Math.floor(Math.random() * 20) + 1;
  let attributeMod = 0;
  let proficiencyBonusApplied = 0;

  const attributeKey = attributeName.toLowerCase() as keyof PlayerState['attributes'];
  if (pState.attributes[attributeKey]) {
    attributeMod = pState.attributes[attributeKey].mod;
  } else {
    // safeToastr('warning', `Unknown attribute "${attributeName}" for check. Using 0 mod.`, 'Check Error');
  }

  if (skillName) {
    const skill = pState.skills.find(s => s.name.toLowerCase() === skillName.toLowerCase());
    if (skill?.proficient) {
      proficiencyBonusApplied = getProficiencyBonus(pState.level);
    } else {
      // Check if it's a generic proficiency like "感知(察觉)" where "察觉" might be in proficiencies array directly
      // This part might need refinement based on how skills vs generic proficiencies are stored
      const genericProficiency = pState.proficiencies.find(
        p => p.toLowerCase().includes(skillName.toLowerCase()) && p.toLowerCase().includes(attributeName.toLowerCase()),
      );
      if (genericProficiency) {
        proficiencyBonusApplied = getProficiencyBonus(pState.level);
      }
    }
  }

  const total = roll + attributeMod + proficiencyBonusApplied;
  const success = total >= dc;

  return {
    success,
    roll,
    attributeMod,
    proficiencyBonusApplied,
    total,
    dc,
    attributeName,
    skillName,
  };
}

async function handleActionChoice(choice: ActionChoiceDefinition) {
  // safeToastr('info', `Player chose: "${choice.text}" (Action: ${choice.actionCommand})`, 'Player Action');

  if (actionChoicesArea) {
    actionChoicesArea.querySelectorAll('button').forEach(btn => ((btn as HTMLButtonElement).disabled = true));
  }
  if (mainNarrativeArea) {
    mainNarrativeArea.innerHTML += '<p><i>正在等待AI响应...</i></p>';
  }

  let promptContext = '';
  if (currentSceneDataFromMessage?.rawInputBlock) {
    promptContext += `之前的场景核心内容是:\n${currentSceneDataFromMessage.rawInputBlock}\n`;
  } else if (currentSceneDataFromMessage) {
    promptContext += `之前的场景是关于 "${
      currentSceneDataFromMessage.title || currentSceneDataFromMessage.sceneType
    }"。\n`;
    promptContext += `描述为: "${currentSceneDataFromMessage.description}"\n`;
    if (currentSceneDataFromMessage.systemMessage) {
      promptContext += `系统信息: "${currentSceneDataFromMessage.systemMessage}"\n`;
    }
  }

  const historyLookBack = 2;
  if (fullHistoryLog.length > 0) {
    promptContext += '\n最近的互动历史摘要:\n';
    const startIndex = Math.max(0, fullHistoryLog.length - historyLookBack);
    for (let i = startIndex; i < fullHistoryLog.length; i++) {
      const entry = fullHistoryLog[i];
      const tempParsedScene = parseAIResponse(entry.rawSceneBlock);
      if (tempParsedScene?.description) {
        promptContext += `- 场景: ${tempParsedScene.title || '...'} "${tempParsedScene.description.substring(
          0,
          70,
        )}..."\n`;
      } else {
        promptContext += `- 场景: (摘要不可用) "${entry.rawSceneBlock.substring(0, 70)}..."\n`;
      }
      if (entry.playerChoiceText) {
        promptContext += `  玩家选择: "${entry.playerChoiceText}"\n`;
      }
    }
  }

  let prompt = `你是一名D&D 5e的地下城主(DM)，正在主持一个文字冒险游戏“冒险日志”。\n`;
  prompt += `当前玩家状态：\n角色名: ${playerState.name}\n种族: ${playerState.race}\n职业: ${playerState.class} ${playerState.level}级\n`;
  prompt += `地点: ${playerState.currentLocation}\n生命值: ${playerState.hp.current}/${playerState.hp.max}\n时间: ${playerState.time}\n`;
  if (playerState.inventory && playerState.inventory.length > 0) {
    prompt += `物品: ${playerState.inventory.map(item => `${item.name} x${item.quantity}`).join(', ')}\n`;
  }
  if (playerState.exhaustion) prompt += `力竭等级: ${playerState.exhaustion}\n`;

  prompt += `\n${promptContext}`; // Existing context

  // --- Local Check/Roll Logic ---
  let checkFeedbackToAI = '';
  // Regex to match:
  // 1. [DC<num> <Attribute>(<Skill>)] e.g., [DC15 力量(运动)]
  // 2. [DC<num> <Attribute>] e.g., [DC14 敏捷]
  // 3. [<CheckType>检定] e.g., [先攻检定]
  const checkRegex = /\[(?:DC(\d+)\s*)?([^\(\]\s]+)(?:\s*\(([^\)]+)\))?\s*(检定)?\]/i;
  const checkMatch = choice.text.match(checkRegex);

  if (checkMatch) {
    const dcString = checkMatch[1];
    const primaryName = checkMatch[2].trim(); // Could be an Attribute or a check type like "先攻"
    const skillNameIfPresent = checkMatch[3] ? checkMatch[3].trim() : undefined;
    const isGenericCheck = checkMatch[4] !== undefined; // True if "检定" keyword is present

    if (primaryName.toLowerCase().includes('先攻')) {
      // Handle Initiative Roll (Agility check, no DC)
      const result = performCheck(0, '敏捷', undefined, playerState); // DC 0 for initiative as it's just a value
      let rollDetails = `投骰1d20[${result.roll}]`;
      if (result.attributeMod !== 0)
        rollDetails += `${result.attributeMod >= 0 ? '+' : ''}${result.attributeMod}(敏捷)`;
      // Initiative doesn't typically add proficiency bonus unless a specific feature allows it.
      rollDetails += `=${result.total}`;
      checkFeedbackToAI = ` [先攻检定 ${rollDetails}]`;
      const toastMessage = `先攻检定: ${result.total} (${rollDetails})`;
      safeToastr('info', toastMessage, '先攻结果');
      if (mainNarrativeArea) {
        mainNarrativeArea.innerHTML += `<p class="check-result"><em>${toastMessage}</em></p>`;
      }
    } else if (dcString) {
      // Handle DC-based attribute/skill check
      const dc = parseInt(dcString, 10);
      const attributeName = primaryName; // In this case, primaryName is the attribute
      const skillName = skillNameIfPresent;
      const result = performCheck(dc, attributeName, skillName, playerState);

      let rollDetails = `投骰1d20[${result.roll}]`;
      if (result.attributeMod !== 0)
        rollDetails += `${result.attributeMod >= 0 ? '+' : ''}${result.attributeMod}(${result.attributeName})`;
      if (result.proficiencyBonusApplied !== 0)
        rollDetails += `${result.proficiencyBonusApplied >= 0 ? '+' : ''}${result.proficiencyBonusApplied}(熟练)`;
      rollDetails += `=${result.total}`;

      checkFeedbackToAI = ` [${result.success ? '检定成功' : '检定失败'} DC${result.dc} ${result.attributeName}${
        result.skillName ? '(' + result.skillName + ')' : ''
      } ${rollDetails}]`;

      const toastMessage = `${result.attributeName}${result.skillName ? '(' + result.skillName + ')' : ''} 检定: ${
        result.total
      } vs DC ${result.dc} -> ${result.success ? '成功！' : '失败。'} (${rollDetails})`;
      safeToastr(result.success ? 'success' : 'error', toastMessage, '检定结果');
      if (mainNarrativeArea) {
        mainNarrativeArea.innerHTML += `<p class="check-result"><em>${toastMessage}</em></p>`;
      }
    } else if (isGenericCheck) {
      // Handle generic checks like "[力量检定]" or "[敏捷(体操)检定]" where DC is implied or to be set by DM (AI)
      // For now, we can't process this without a DC. AI needs to provide DC.
      // Or, we assume a default DC, e.g., 10 or 15, if AI doesn't specify.
      // For now, let's log a warning and not perform a check.
      safeToastr(
         'warning',
         `选项 "${choice.text}" 包含通用检定但未指定DC，无法本地处理。请AI提供DC。`,
         '检定信息不完整',
      );
      // checkFeedbackToAI will remain empty, AI will proceed based on choice text only.
    }
  }
  // --- End Local Check/Roll Logic ---

  prompt += `\n玩家刚刚选择了行动： "${choice.text}"${checkFeedbackToAI}\n`; // Append check result to AI prompt
  prompt += `\n请根据玩家的选择${
    checkFeedbackToAI ? '和检定结果' : ''
  }，结合D&D 5e的规则和风格，继续发展剧情，并生成下一个场景的内容。`;
  prompt += `\n你需要提供生动的场景描述、合理的事件发展，以及2-3个供玩家选择的新的行动选项。`;
  // Removed the part about AI giving check requests/results as client now handles it.
  // prompt += `\n如果玩家的行动需要进行属性检定（例如：尝试撬锁、说服NPC、躲避陷阱），请在场景描述中说明，并在必要时（如果检定结果会立即产生影响）通过 <系统:检定请求> 或 <系统:检定结果> 标签给出检定要求或结果。`;
  prompt += `\n如果发生战斗，请使用 <战斗:...> 标签，并描述战斗过程和结果，更新相关生命值。`;
  prompt += `\n\n【非常重要：输出格式指令】\n`;
  prompt += `你的完整回复必须严格遵循以下格式，以便游戏界面能够正确解析和显示：\n`;
  prompt += `1. 整个回复必须由 "查看系统\\nmsg_start" 开始，并以 "msg_end\\n关闭系统" 结束。\n`;
  prompt += `2. 在 "msg_start" 和 "msg_end" 之间，是核心的游戏数据块。\n`;
  prompt += `3. 核心数据块必须以一个主要标签开始，例如 <场景:场景标题> 或 <NPC:NPC名称> 或 <系统:消息类型>。\n`;
  prompt += `4. 在主要标签之后，是多行结构化数据。每一行结构化数据都必须采用 '键--"值"--HH:MM' 的格式。例如：'场景描述--"你看到一个古老的祭坛。"HH:MM'。\n`;
  prompt += `5. 确保所有的引号都正确配对，并且 "--HH:MM" 标记在值的引号之外，作为行尾。\n`;
  prompt += `6. 常见的键包括：场景描述, 当前地点, 生命值, 时间, 提示信息, 内容, 敌人信息, 对方的话, 行动选项A, 行动选项B, 回复选项A 等。\n`;
  prompt += `7. 必须提供至少两个行动选项（例如 行动选项A, 行动选项B）。\n`;
  prompt += `请务必严格遵守！\n`;

  if (typeof triggerSlash !== 'function') {
    // safeToastr('error', 'triggerSlash API is not available!', 'API Error');
    if (actionChoicesArea) {
      actionChoicesArea.querySelectorAll('button').forEach(btn => ((btn as HTMLButtonElement).disabled = false));
    }
    const waitingMsg = mainNarrativeArea?.querySelector('p > i');
    if (waitingMsg && waitingMsg.parentElement) mainNarrativeArea?.removeChild(waitingMsg.parentElement);
    return;
  }

  try {
    // safeToastr('info', 'Sending request to AI...', 'AI Interaction');
    const aiRawResponseWithWrappers = await triggerSlash(`/gen ${prompt}`);

    const waitingMsg = mainNarrativeArea?.querySelector('p > i');
    if (waitingMsg && waitingMsg.parentElement && mainNarrativeArea) {
      mainNarrativeArea.removeChild(waitingMsg.parentElement);
    }

    if (aiRawResponseWithWrappers && aiRawResponseWithWrappers.trim() !== '') {
      // safeToastr('info', `Received AI response. Length: ${aiRawResponseWithWrappers.length}`, 'AI Interaction');

      // --- DEBUGGING MODE: Display raw AI response and skip parsing ---
      const DEBUG_RAW_AI_RESPONSE = false; // Set to false to resume normal parsing

      if (DEBUG_RAW_AI_RESPONSE) {
        if (mainNarrativeArea) {
          mainNarrativeArea.innerHTML = `<p style="color: lightcoral; font-family: monospace; white-space: pre-wrap; border: 1px solid orange; padding: 10px; background-color: #333;"><strong>[调试] AI 原始回复 (长度: ${
            aiRawResponseWithWrappers.length
          }):</strong>\n${aiRawResponseWithWrappers.replace(/</g, '<').replace(/>/g, '>')}</p>`;
        }
        // safeToastr('warning', '调试模式：已显示AI原始回复。提取、解析和持久化已跳过。', '调试模式激活');
        // Re-enable buttons for next action in debug mode
        if (actionChoicesArea) {
          actionChoicesArea.querySelectorAll('button').forEach((btn: HTMLButtonElement) => {
            btn.disabled = false;
          });
        }
        return; // Exit early in debug mode
      }
      // --- END OF DEBUGGING MODE BLOCK ---

      // Normal processing starts here if DEBUG_RAW_AI_RESPONSE is false
      let coreContentFromAI: string | null = null;
      const fullPatternMatch = aiRawResponseWithWrappers.match(/查看系统\s*msg_start([\s\S]*?)msg_end\s*关闭系统/);

      if (fullPatternMatch && fullPatternMatch[1]) {
        coreContentFromAI = fullPatternMatch[1].trim();
        // safeToastr('info', 'Successfully extracted core content using full pattern.', 'AI Content Extraction');
      } else {
        // safeToastr(
        //   'warning',
        //   'Full "查看系统...msg_start...msg_end...关闭系统" pattern not found. Attempting fallback extraction.',
        //   'AI Content Extraction',
        // );
        const fallbackMatch = aiRawResponseWithWrappers.match(/msg_start([\s\S]*?)msg_end/);
        if (fallbackMatch && fallbackMatch[1]) {
          coreContentFromAI = fallbackMatch[1].trim();
          // safeToastr(
          //   'info',
          //   'Successfully extracted core content using fallback msg_start/msg_end pattern.',
          //   'AI Content Extraction',
          // );
        } else {
          // safeToastr(
          //   'error',
          //   'Could not extract core content (between msg_start/msg_end) from AI response even with fallback.',
          //   'AI Content Extraction Error',
          // );
        }
      }

      if (!coreContentFromAI) {
        // safeToastr(
        //   'error',
        //   'Fatal: Could not extract core content. Raw response was logged to console and displayed if possible.',
        //   'AI Interaction Error',
        // );
        console.error('Raw AI Response that failed extraction:', aiRawResponseWithWrappers);
        if (mainNarrativeArea) {
          mainNarrativeArea.innerHTML += `<p style='color:red;'>AI响应内容提取失败。原始回复已打印到控制台。</p><div style="font-family:monospace; white-space:pre-wrap; border:1px solid red; padding:5px; max-height:200px; overflow-y:auto;">${aiRawResponseWithWrappers
            .replace(/</g, '<')
            .replace(/>/g, '>')}</div>`;
        }
        if (actionChoicesArea) {
          actionChoicesArea.querySelectorAll('button').forEach((btn: HTMLButtonElement) => {
            btn.disabled = false;
          });
        }
        return;
      }

      const parsedScene = parseAIResponse(coreContentFromAI);

      if (parsedScene && parsedScene.rawInputBlock) {
        applySceneData(parsedScene);

        const historyEntry: AdventureLogEntry = {
          rawSceneBlock: parsedScene.rawInputBlock,
          playerChoiceText: choice.text,
          timestamp: Date.now(),
        };
        fullHistoryLog.push(historyEntry);
        await persistGameState();
      } else {
        // safeToastr(
        //   'error',
        //   'Failed to parse extracted AI content. The content might be malformed or incomplete, or no valid scene tag found.',
        //   'AI Interaction Error',
        // );
        if (mainNarrativeArea) {
          mainNarrativeArea.innerHTML += `<p style='color:red;'>AI响应解析失败。提取到的内容: </p><div style="font-family:monospace; white-space:pre-wrap; border:1px solid red; padding:5px; max-height:200px; overflow-y:auto;">${coreContentFromAI
            .replace(/</g, '<')
            .replace(/>/g, '>')}</div>`;
        }
      }
    } else {
      // safeToastr('warning', 'AI returned an empty or invalid response.', 'AI Interaction Error');
      if (mainNarrativeArea) {
        mainNarrativeArea.innerHTML += "<p style='color:orange;'>AI未返回有效数据。请尝试其他选项或检查连接。</p>";
      }
    }
  } catch (e) {
    // safeToastr('error', `Error during AI interaction: ${(e as Error).message}`, 'AI Interaction Exception');
    const waitingMsgOnError = mainNarrativeArea?.querySelector('p > i');
    if (waitingMsgOnError && waitingMsgOnError.parentElement && mainNarrativeArea) {
      mainNarrativeArea.removeChild(waitingMsgOnError.parentElement);
    }
    if (mainNarrativeArea) {
      mainNarrativeArea.innerHTML += `<p style='color:red;'>与AI交互时发生错误: ${(e as Error).message}</p>`;
    }
  } finally {
    if (actionChoicesArea) {
      actionChoicesArea.querySelectorAll('button').forEach((btn: HTMLButtonElement) => {
        btn.disabled = false;
      });
    }
  }
}

function parseAIResponse(contentPotentiallyWithJunk: string): AdventureScene | null {
  if (!contentPotentiallyWithJunk || typeof contentPotentiallyWithJunk !== 'string') {
    // safeToastr('error', 'ParseAI: Invalid content string: null or not a string.', 'Parse Error');
    return null;
  }

  const allLinesOriginal = contentPotentiallyWithJunk.split('\n');
  
  const processedLines: string[] = [];
  let i = 0;
  while (i < allLinesOriginal.length) {
    let currentLineOriginal = allLinesOriginal[i];
    let currentLineTrimmed = currentLineOriginal.trim();

    // Regex to check if a line starts a key-value pair that might span multiple lines
    // It looks for "KEY--\"" but not ending with "\"--HH:MM" on the same trimmed line
    const keyStartRegex = /^(.+?)--"(.*)$/; // Looser match for start
    const keyValueStartMatch = currentLineTrimmed.match(keyStartRegex);

    if (keyValueStartMatch && !currentLineTrimmed.endsWith('"--HH:MM')) {
      let combinedLine = currentLineOriginal; // Start with the original line to preserve initial spacing if any
      let j = i + 1;
      let foundEndMarker = false;
      while (j < allLinesOriginal.length) {
        combinedLine += '\n' + allLinesOriginal[j]; // Append next original line
        if (allLinesOriginal[j].trim().endsWith('"--HH:MM')) {
          processedLines.push(combinedLine);
          i = j; // Move main loop counter past the consumed lines
          foundEndMarker = true;
          break;
        }
        j++;
      }
      if (!foundEndMarker) { // If loop finished but no end marker, push what we have
        processedLines.push(combinedLine);
      }
    } else {
      processedLines.push(currentLineOriginal); // Push original line if it's not a multi-line start or is complete
    }
    i++;
  }

  const allLinesTrimmed = processedLines.map(line => line.trim()).filter(line => line); // Trim and filter out empty lines from processed

  let actualDataBlockFirstLineIndex = -1;
  let mainTagFullString: string | null = null;
  let sceneTagMatch, npcTagMatch, systemTagMatch, combatTagMatch; // Added combatTagMatch

  for (let i = 0; i < allLinesTrimmed.length; i++) {
    const currentLineTrimmed = allLinesTrimmed[i];
    if (!mainTagFullString) {
      sceneTagMatch = currentLineTrimmed.match(/^<场景:([^>]+)>/i);
      if (sceneTagMatch && sceneTagMatch[1]) {
        actualDataBlockFirstLineIndex = i;
        mainTagFullString = sceneTagMatch[0];
        break;
      }
      npcTagMatch = currentLineTrimmed.match(/^<NPC:([^>]+)>/i);
      if (npcTagMatch && npcTagMatch[1]) {
        actualDataBlockFirstLineIndex = i;
        mainTagFullString = npcTagMatch[0];
        break;
      }
      systemTagMatch = currentLineTrimmed.match(/^<系统:([^>]+)>/i);
      if (systemTagMatch && systemTagMatch[1]) {
        actualDataBlockFirstLineIndex = i;
        mainTagFullString = systemTagMatch[0];
        break;
      }
      combatTagMatch = currentLineTrimmed.match(/^<战斗:([^>]+)>/i); // Added check for combat tag
      if (combatTagMatch && combatTagMatch[1]) {
        actualDataBlockFirstLineIndex = i;
        mainTagFullString = combatTagMatch[0];
        break;
      }
    }
  }

  if (actualDataBlockFirstLineIndex === -1 || !mainTagFullString) {
    // safeToastr(
    //   'error',
    //   `ParseAI: First line of core content is not a recognized main tag: "${
    //     allLinesTrimmed.length > 0 ? allLinesTrimmed[0] : 'EMPTY'
    //   }"`,
    //   'Parse Error Critical',
    // );
    return null;
  }

  const parsedScene: Partial<AdventureScene> = { playerChoices: [] };

  if (sceneTagMatch && sceneTagMatch[1]) {
    parsedScene.sceneType = 'location';
    parsedScene.title = sceneTagMatch[1];
  } else if (npcTagMatch && npcTagMatch[1]) {
    parsedScene.sceneType = 'dialogue';
    parsedScene.npcName = npcTagMatch[1];
    parsedScene.title = `与 ${npcTagMatch[1]} 对话`;
  } else if (combatTagMatch && combatTagMatch[1]) {
    // Added handling for combatTagMatch
    parsedScene.sceneType = 'combat';
    parsedScene.title = combatTagMatch[1];
  } else if (systemTagMatch && systemTagMatch[1]) {
    const systemType = systemTagMatch[1].toLowerCase();
    // Ensure combat is handled by combatTagMatch first
    if ((systemType.includes('战斗') || systemType.includes('combat')) && !combatTagMatch) {
      parsedScene.sceneType = 'combat'; // Fallback if combat tag was missed but system tag indicates combat
    } else if (systemType.includes('谜题') || systemType.includes('puzzle')) {
      parsedScene.sceneType = 'puzzle';
    } else {
      parsedScene.sceneType = 'system_message';
    }
    parsedScene.title = systemTagMatch[1];
  } else {
    // This case should ideally not be reached if mainTagFullString is set
    // safeToastr(
    //   'error',
    //   `ParseAI: Main tag logic error after loop. Tag was: "${mainTagFullString}"`,
    //   'Parse Error Critical',
    // );
    return null;
  }

  let expectedClosingTag = '';
  if (sceneTagMatch) expectedClosingTag = `</场景:${sceneTagMatch[1]}>`;
  else if (npcTagMatch) expectedClosingTag = `</NPC:${npcTagMatch[1]}>`;
  else if (combatTagMatch) expectedClosingTag = `</战斗:${combatTagMatch[1]}>`; // Added combat closing tag
  else if (systemTagMatch) expectedClosingTag = `</系统:${systemTagMatch[1]}>`;

  let actualDataBlockLastLineIndex = -1;
  for (let i = actualDataBlockFirstLineIndex + 1; i < allLinesTrimmed.length; i++) {
    if (allLinesTrimmed[i] === expectedClosingTag) {
      actualDataBlockLastLineIndex = i;
      break;
    }
  }

  if (actualDataBlockLastLineIndex === -1) {
    // safeToastr(
    //   'warning',
    //   `ParseAI: Closing tag "${expectedClosingTag}" not found. Assuming data till end of content.`,
    //   'Parse Warning',
    // );
    actualDataBlockLastLineIndex = allLinesTrimmed.length - 1;
    parsedScene.rawInputBlock = allLinesOriginal.slice(actualDataBlockFirstLineIndex).join('\n');
  } else {
    parsedScene.rawInputBlock = allLinesOriginal
      .slice(actualDataBlockFirstLineIndex, actualDataBlockLastLineIndex + 1)
      .join('\n');
  }

  let dataLinesToParseAfterBlockProcessing: string[];
  if (allLinesTrimmed[actualDataBlockLastLineIndex] === expectedClosingTag) {
    dataLinesToParseAfterBlockProcessing = allLinesTrimmed.slice(actualDataBlockFirstLineIndex + 1, actualDataBlockLastLineIndex);
  } else {
    dataLinesToParseAfterBlockProcessing = allLinesTrimmed.slice(actualDataBlockFirstLineIndex + 1);
  }

  parsedScene.variableUpdates = []; // Initialize variableUpdates array

  dataLinesToParseAfterBlockProcessing.forEach(lineTrimmed => {
    // Try to match variable update format first
    const varUpdateMatch = lineTrimmed.match(/^变量更新--"([^:]+):([^:]+):([^:]+):(.+)"--HH:MM$/i);
    if (varUpdateMatch) {
      const [_full, target, path, operation, valueString] = varUpdateMatch;
      let value: any = valueString.trim(); 

      if ((operation.trim() === '物品获得' || operation.trim() === '物品失去') && value) {
        try {
          value = JSON.parse(valueString);
        } catch (e) {
          // safeToastr('error', `ParseAI: Failed to parse JSON value for ${operation}: "${valueString}" - ${(e as Error).message}`, 'Variable Update Parse Error');
          return; 
        }
      } else if (operation.trim() === '增加' || operation.trim() === '减少' || operation.trim() === '设置') {
        const numValue = parseFloat(valueString);
        if (!isNaN(numValue) && numValue.toString() === valueString.trim()) {
          value = numValue;
        } else {
          value = valueString.trim(); 
        }
      }
      parsedScene.variableUpdates?.push({
        target: target.trim(),
        path: path.trim(),
        operation: operation.trim(),
        value: value,
      });
    } else {
      // Standard key-value parsing, now allowing for multi-line values due to pre-processing
      const match = lineTrimmed.match(/^(.+?)--"([\s\S]*?)"--HH:MM\s*$/i);
      if (match) {
        const key = match[1].trim();
        let value = match[2]; // No need for (match[3] || '') as ([\s\S]*?) captures everything between quotes

        // Truncation logic for specific keys if they seem to contain other key patterns
        if (key === '场景描述' || key === '对方的话' || key === '提示信息') {
            const optionKeywords = [
                '行动选项A--', '行动选项B--', '行动选项C--', '行动选项D--', '行动选项E--',
                '回复选项A--', '回复选项B--', '回复选项C--', '回复选项D--', '回复选项E--',
                // Also check for other primary keys that should not be inside a description value
                '当前地点--', '生命值--', '时间--', 
                '提示信息--', // A prompt can't contain another prompt key
                '内容--', 
                '敌人信息--', 
                '对方的话--', // A dialog can't contain another dialog key
                '变量更新--'
            ];
            let earliestProblemIndex = -1;

            for (const keyword of optionKeywords) {
                // Search for the keyword if it's NOT the current key itself (e.g. a prompt can have multiple lines, but not another prompt key)
                if (key === '提示信息' && keyword === '提示信息--') continue;
                if (key === '对方的话' && keyword === '对方的话--') continue;

                const index = value.indexOf(keyword);
                if (index !== -1) {
                    if (earliestProblemIndex === -1 || index < earliestProblemIndex) {
                        earliestProblemIndex = index;
                    }
                }
            }

            if (earliestProblemIndex !== -1) {
                // safeToastr('warning', `AI格式警告：键 "${key}" 的值似乎过早地包含了其他键的内容。已尝试截断。原始内容预览：${value.substring(0,100)}...`, '解析警告');
                value = value.substring(0, earliestProblemIndex).trim();
            }
        }

        switch (key) {
          case '场景描述':
            parsedScene.description = value;
            break;
          case '当前地点':
            parsedScene.locationUpdate = value;
            break;
          case '生命值':
            parsedScene.healthUpdate = value;
            break;
          case '金币':
            parsedScene.goldUpdate = parseInt(value, 10);
            if (isNaN(parsedScene.goldUpdate)) {
              // safeToastr('warning', `ParseAI: Failed to parse Gold value: "${value}"`, 'Parse Warning');
              delete parsedScene.goldUpdate;
            }
            break;
          case '时间':
            parsedScene.timeUpdate = value;
            break;
          case '力竭等级':
            parsedScene.exhaustionUpdate = parseInt(value, 10);
            if (isNaN(parsedScene.exhaustionUpdate)) {
              // safeToastr('warning', `ParseAI: Failed to parse Exhaustion value: "${value}"`, 'Parse Warning');
              delete parsedScene.exhaustionUpdate;
            }
            break;
          case '提示信息':
          case '内容': // Often used in <系统:...> blocks
          case '伤害提示':
            parsedScene.systemMessage = (parsedScene.systemMessage ? parsedScene.systemMessage + '\n' : '') + value;
            break;
          case '敌人信息':
            parsedScene.enemyInfo = value;
            break;
          case '对方的话':
            if (parsedScene.sceneType === 'dialogue' && !parsedScene.description) {
              parsedScene.description = value;
            } else {
              parsedScene.systemMessage =
                (parsedScene.systemMessage ? parsedScene.systemMessage + '\n' : '') +
                `${parsedScene.npcName || '对方'}说: ${value}`;
            }
            break;
          case '玩家状态': // This might be a generic status update text
            parsedScene.systemMessage =
              (parsedScene.systemMessage ? parsedScene.systemMessage + '\n' : '') + `状态更新: ${value}`;
            break;
          default:
            const actionOptionMatch = key.match(/^(行动选项|回复选项)([A-Z])$/i);
            if (actionOptionMatch) {
              if (!parsedScene.playerChoices) {
                parsedScene.playerChoices = [];
              }
              parsedScene.playerChoices.push({
                id: `choice_${actionOptionMatch[2].toUpperCase()}_${Date.now()}`,
                text: value,
                actionCommand: `${actionOptionMatch[1].toLowerCase()}_${actionOptionMatch[2].toUpperCase()}`,
              });
            } else if (key !== '变量更新') {
              // Avoid warning for already processed variable updates
              // safeToastr('warning', `ParseAI: Unknown data key: "${key}" with value "${value}"`, 'Parse Warning');
            }
            break;
        }
      } else {
        if (lineTrimmed && lineTrimmed !== expectedClosingTag && !lineTrimmed.startsWith('变量更新--')) {
          // safeToastr(
          //   'warning',
          //   `ParseAI: Line does not match key--"value"--HH:MM format and is not a variable update: "${lineTrimmed}"`,
          //   'Parse Warning',
          // );
        }
      }
    }
  });

  if (!parsedScene.sceneType) {
    // This should not happen if mainTagFullString was matched
    // safeToastr(
    //   'error',
    //   'ParseAI: Parsed scene is missing sceneType despite matching a main tag.',
    //   'Parse Error Critical',
    // );
    return null;
  }

  // Relaxed validation: As long as we have a sceneType and a title (from the main tag),
  // and rawInputBlock is populated, consider it a partially valid scene for display/debugging,
  // even if description or choices are missing due to parsing issues or missing closing tag.
  if (!parsedScene.rawInputBlock || parsedScene.rawInputBlock.trim() === '') {
    // safeToastr('error', 'ParseAI: rawInputBlock is empty after processing.', 'Parse Error Critical');
    return null;
  }

  // Add a check for essential content: if it's not a system message, it should have a description or choices.
  // For combat, description or enemyInfo or playerChoices should exist.
  let hasEssentialContent = false;
  if (parsedScene.description && parsedScene.description.trim() !== '') hasEssentialContent = true;
  if (parsedScene.playerChoices && parsedScene.playerChoices.length > 0) hasEssentialContent = true;
  if (parsedScene.sceneType === 'combat' && parsedScene.enemyInfo && parsedScene.enemyInfo.trim() !== '')
    hasEssentialContent = true;
  if (parsedScene.systemMessage && parsedScene.systemMessage.trim() !== '') hasEssentialContent = true; // System messages can be standalone

  if (!hasEssentialContent && parsedScene.sceneType !== 'system_message') {
    // safeToastr(
    //   'warning',
    //   `ParseAI: Parsed ${parsedScene.sceneType} scene (Title: ${parsedScene.title}) seems to lack essential content (description, choices, or enemy info for combat). It might be incomplete.`,
    //   'Parse Warning',
    // );
    // We might still return it for debugging if rawInputBlock is present.
  }

  // safeToastr(
  //   'success',
  //   `ParseAI: Response processing finished. Type: ${parsedScene.sceneType}, Title: ${
  //     parsedScene.title || 'N/A'
  //   }. Essential content present: ${hasEssentialContent}`,
  //   'Parse Attempt Complete',
  // );
  return parsedScene as AdventureScene; // Return even if content is minimal, as long as type/title/raw is there
}

function applySceneData(scene: AdventureScene | null) {
  if (!scene) {
    // safeToastr('error', 'Cannot apply null scene data.', 'Apply Data Error');
    if (mainNarrativeArea) mainNarrativeArea.innerHTML = '<p>错误：无法加载场景数据。请检查AI响应或联系开发者。</p>';
    if (actionChoicesArea) actionChoicesArea.innerHTML = '';
    return;
  }

  currentSceneDataFromMessage = scene;
  if (scene.healthUpdate) {
    const parts = scene.healthUpdate.split('/');
    if (parts.length === 2) {
      playerState.hp.current = parseInt(parts[0], 10) || playerState.hp.current;
      playerState.hp.max = parseInt(parts[1], 10) || playerState.hp.max;
    }
  }
  // Gold, location, time, exhaustion updates are now primarily handled by variableUpdates
  // but we can keep these direct updates as a fallback or for simple cases if AI still sends them.
  if (scene.goldUpdate !== undefined && !scene.variableUpdates?.find(upd => upd.path === 'currency.gold')) {
    playerState.currency.gold = scene.goldUpdate;
  }
  if (scene.locationUpdate && !scene.variableUpdates?.find(upd => upd.path === 'currentLocation')) {
    playerState.currentLocation = scene.locationUpdate;
  }
  if (scene.timeUpdate && !scene.variableUpdates?.find(upd => upd.path === 'time')) {
    playerState.time = scene.timeUpdate;
  }
  if (scene.exhaustionUpdate !== undefined && !scene.variableUpdates?.find(upd => upd.path === 'exhaustion')) {
    playerState.exhaustion = scene.exhaustionUpdate;
  }

  // Apply variable updates
  if (scene.variableUpdates && scene.variableUpdates.length > 0) {
    scene.variableUpdates.forEach(update => {
      applyVariableUpdate(playerState, update.path, update.operation, update.value);
    });
  }

  renderNarrative(scene);
  updatePlayerStatusDisplay(); // Call this *after* all state changes, including variable updates
  renderActionChoices(scene.playerChoices);
}

// --- 变量更新核心函数 ---
function applyVariableUpdate(targetObject: any, path: string, operation: string, value: any): void {
  const pathParts = path.split('.');
  let currentTarget = targetObject;

  // Navigate to the parent of the target property
  for (let i = 0; i < pathParts.length - 1; i++) {
    if (currentTarget[pathParts[i]] === undefined || typeof currentTarget[pathParts[i]] !== 'object') {
      // safeToastr('error', `applyVariableUpdate: Invalid path "${path}". Part "${pathParts[i]}" not found or not an object.`, 'Variable Update Error');
      return;
    }
    currentTarget = currentTarget[pathParts[i]];
  }
  const finalPropertyKey = pathParts[pathParts.length - 1];

  switch (operation) {
    case '设置':
      if (typeof currentTarget === 'object' && currentTarget !== null) {
        currentTarget[finalPropertyKey] = value;
        // safeToastr('info', `Variable "${path}" set to "${value}".`, 'Variable Update');
      } else {
        // safeToastr('error', `applyVariableUpdate: Cannot set property on non-object for path "${path}".`, 'Variable Update Error');
      }
      break;
    case '增加':
      if (
        typeof currentTarget === 'object' &&
        currentTarget !== null &&
        typeof currentTarget[finalPropertyKey] === 'number' &&
        typeof value === 'number'
      ) {
        currentTarget[finalPropertyKey] += value;
        // safeToastr('info', `Variable "${path}" increased by ${value} to ${currentTarget[finalPropertyKey]}.`, 'Variable Update');
      } else {
        // safeToastr('error', `applyVariableUpdate: Invalid types for "增加" on path "${path}". Current: ${typeof currentTarget[finalPropertyKey]}, Value: ${typeof value}.`, 'Variable Update Error');
      }
      break;
    case '减少':
      if (
        typeof currentTarget === 'object' &&
        currentTarget !== null &&
        typeof currentTarget[finalPropertyKey] === 'number' &&
        typeof value === 'number'
      ) {
        currentTarget[finalPropertyKey] -= value;
        // safeToastr('info', `Variable "${path}" decreased by ${value} to ${currentTarget[finalPropertyKey]}.`, 'Variable Update');
      } else {
        // safeToastr('error', `applyVariableUpdate: Invalid types for "减少" on path "${path}". Current: ${typeof currentTarget[finalPropertyKey]}, Value: ${typeof value}.`, 'Variable Update Error');
      }
      break;
    case '添加元素':
      if (
        typeof currentTarget === 'object' &&
        currentTarget !== null &&
        Array.isArray(currentTarget[finalPropertyKey])
      ) {
        currentTarget[finalPropertyKey].push(value);
        // safeToastr('info', `Element "${value}" added to array "${path}".`, 'Variable Update');
      } else {
        // safeToastr('error', `applyVariableUpdate: Target for "添加元素" on path "${path}" is not an array.`, 'Variable Update Error');
      }
      break;
    case '移除元素':
      if (
        typeof currentTarget === 'object' &&
        currentTarget !== null &&
        Array.isArray(currentTarget[finalPropertyKey])
      ) {
        const index = currentTarget[finalPropertyKey].indexOf(value);
        if (index > -1) {
          currentTarget[finalPropertyKey].splice(index, 1);
          // safeToastr('info', `Element "${value}" removed from array "${path}".`, 'Variable Update');
        } else {
          // safeToastr('warning', `applyVariableUpdate: Element "${value}" not found in array "${path}" for "移除元素".`, 'Variable Update Warning');
        }
      } else {
        // safeToastr('error', `applyVariableUpdate: Target for "移除元素" on path "${path}" is not an array.`, 'Variable Update Error');
      }
      break;
    case '物品获得':
      if (
        path === 'inventory' &&
        Array.isArray(currentTarget.inventory) &&
        typeof value === 'object' &&
        value.name &&
        typeof value.quantity === 'number'
      ) {
        const existingItem = currentTarget.inventory.find((item: InventoryItem) => item.name === value.name);
        if (existingItem) {
          existingItem.quantity += value.quantity;
          if (value.description && !existingItem.description) existingItem.description = value.description; // Update description if new one provided and old one was empty
          // safeToastr('info', `Increased quantity of "${value.name}" by ${value.quantity}. New total: ${existingItem.quantity}.`, 'Inventory Update');
        } else {
          currentTarget.inventory.push({
            name: value.name,
            quantity: value.quantity,
            description: value.description || '',
          });
          // safeToastr('info', `Added new item "${value.name}" (x${value.quantity}) to inventory.`, 'Inventory Update');
        }
      } else {
        // safeToastr('error', `applyVariableUpdate: Invalid "物品获得" operation. Path: "${path}", Value: ${JSON.stringify(value)}`, 'Inventory Update Error');
      }
      break;
    case '物品失去':
      if (
        path === 'inventory' &&
        Array.isArray(currentTarget.inventory) &&
        typeof value === 'object' &&
        value.name &&
        typeof value.quantity === 'number'
      ) {
        const itemIndex = currentTarget.inventory.findIndex((item: InventoryItem) => item.name === value.name);
        if (itemIndex > -1) {
          currentTarget.inventory[itemIndex].quantity -= value.quantity;
          // safeToastr('info', `Decreased quantity of "${value.name}" by ${value.quantity}. New total: ${currentTarget.inventory[itemIndex].quantity}.`, 'Inventory Update');
          if (currentTarget.inventory[itemIndex].quantity <= 0) {
            currentTarget.inventory.splice(itemIndex, 1);
            // safeToastr('info', `Item "${value.name}" removed from inventory as quantity reached zero.`, 'Inventory Update');
          }
        } else {
          // safeToastr('warning', `applyVariableUpdate: Item "${value.name}" not found in inventory for "物品失去".`, 'Inventory Update Warning');
        }
      } else {
        // safeToastr('error', `applyVariableUpdate: Invalid "物品失去" operation. Path: "${path}", Value: ${JSON.stringify(value)}`, 'Inventory Update Error');
      }
      break;
    default:
    // safeToastr('warning', `applyVariableUpdate: Unknown operation type "${operation}" for path "${path}".`, 'Variable Update Warning');
  }
}

// --- 持久化函数 ---
const HISTORY_START_TAG = '<!-- ADVENTURE_LOG_HISTORY_START -->'; // Commented out as it's defined below
const HISTORY_END_TAG = '<!-- ADVENTURE_LOG_HISTORY_END -->'; // Commented out as it's defined below
const HISTORY_ENTRY_SEPARATOR = '\n<!-- ENTRY_SEP -->\n';
const PLAYER_STATE_START_TAG = '<!-- PLAYER_STATE_START -->';
const PLAYER_STATE_END_TAG = '<!-- PLAYER_STATE_END -->';

async function persistGameState() {
  // safeToastr('info', 'Persisting game state...', 'Persistence');
  if (currentHostMessageId === null || typeof setChatMessages !== 'function') {
    // safeToastr('warning', 'Cannot persist: No host message ID or setChatMessages not available.', 'Persistence');
    return;
  }

  let persistedString = '';
  persistedString += `${PLAYER_STATE_START_TAG}\n`;
  persistedString += `${JSON.stringify(playerState, null, 2)}\n`;
  persistedString += `${PLAYER_STATE_END_TAG}\n\n`;

  let historyToPersist = [...fullHistoryLog];
  let currentSceneBlockForDisplay: string | undefined = undefined;

  if (currentSceneDataFromMessage && currentSceneDataFromMessage.rawInputBlock) {
    currentSceneBlockForDisplay = currentSceneDataFromMessage.rawInputBlock;
  } else if (fullHistoryLog.length > 0) {
    currentSceneBlockForDisplay = historyToPersist.pop()?.rawSceneBlock;
  }

  if (historyToPersist.length > 0) {
    persistedString += `${HISTORY_START_TAG}\n`;
    persistedString += historyToPersist
      .map(entry => {
        let entryStr = entry.rawSceneBlock;
        if (entry.playerChoiceText) {
          entryStr += `\n[玩家选择文本]: "${entry.playerChoiceText}"`;
        }
        return entryStr;
      })
      .join(HISTORY_ENTRY_SEPARATOR);
    persistedString += `\n${HISTORY_END_TAG}\n\n`;
  }

  if (currentSceneBlockForDisplay) {
    persistedString += `查看系统\nmsg_start\n${currentSceneBlockForDisplay}\nmsg_end\n关闭系统`;
  } else {
    // safeToastr(
    //   'warning',
    //   'No current scene data to persist for the "查看系统" block. Persisted message might be incomplete.',
    //   'Persistence',
    // );
  }

  try {
    await setChatMessages([{ message_id: currentHostMessageId, message: persistedString }], { refresh: 'affected' });
    // safeToastr(
    //   'success',
    //   `Game state persisted to message ID ${currentHostMessageId}. Length: ${persistedString.length}`,
    //   'Persistence',
    // );
  } catch (e) {
    // safeToastr('error', `Failed to persist game state: ${(e as Error).message}`, 'Persistence Error');
  }
}

interface LoadGameResult {
  playerStateLoadedFromMsg: boolean;
  sceneDataLoadedFromMsg: boolean;
}

async function loadGameState(): Promise<LoadGameResult> {
  // safeToastr('info', 'Loading game state from message ONLY...', 'LoadGameState V3');
  if (typeof getLastMessageId !== 'function' || typeof triggerSlash !== 'function') {
    // safeToastr('error', 'Core APIs (getLastMessageId/triggerSlash) not available.', 'Load Error');
    return { playerStateLoadedFromMsg: false, sceneDataLoadedFromMsg: false };
  }

  const hostIdResult = getLastMessageId();
  currentHostMessageId = hostIdResult === undefined ? null : hostIdResult;

  if (currentHostMessageId === null) {
    // safeToastr('info', 'No host message ID. Cannot load from message.', 'Load Info');
    return { playerStateLoadedFromMsg: false, sceneDataLoadedFromMsg: false };
  }

  let rawPersistedState: string | undefined;
  try {
    rawPersistedState = await triggerSlash(`/messages ${currentHostMessageId}`);
  } catch (e) {
    // safeToastr('error', `Error fetching message content: ${(e as Error).message}`, 'Load Error');
    return { playerStateLoadedFromMsg: false, sceneDataLoadedFromMsg: false };
  }

  if (!rawPersistedState || rawPersistedState.trim() === '') {
    // safeToastr('info', 'Host message is empty. No state to load from message.', 'Load Info');
    return { playerStateLoadedFromMsg: false, sceneDataLoadedFromMsg: false };
  }
  // safeToastr('info', `Message content loaded (length: ${rawPersistedState.length}). Parsing...`, 'Load Info');

  let playerStateSuccessfullyParsed = false;
  let sceneDataSuccessfullyParsed = false;
  currentSceneDataFromMessage = null;
  fullHistoryLog = [];

  const playerStateMatch = rawPersistedState.match(
    new RegExp(`${PLAYER_STATE_START_TAG}\\n([\\s\\S]*?)\\n${PLAYER_STATE_END_TAG}`),
  );
  if (playerStateMatch && playerStateMatch[1]) {
    try {
      const tempState = JSON.parse(playerStateMatch[1]);
      if (tempState && typeof tempState.name === 'string' && tempState.attributes) {
        playerState = tempState;
        playerStateSuccessfullyParsed = true;
        // safeToastr('success', 'PlayerState loaded and parsed from message.', 'Load Success');
      } else {
        // safeToastr('error', 'Parsed PlayerState JSON from message is invalid.', 'Load Error');
      }
    } catch (e) {
      // safeToastr('error', `Error parsing PlayerState JSON from message: ${(e as Error).message}.`, 'Load Error');
    }
  } else {
    // safeToastr('warning', 'PlayerState block not found in message.', 'Load Warning');
  }

  const historyBlockMatch = rawPersistedState.match(
    new RegExp(`${HISTORY_START_TAG}\\n([\\s\\S]*?)\\n${HISTORY_END_TAG}`),
  );
  if (historyBlockMatch && historyBlockMatch[1]) {
    const historyContent = historyBlockMatch[1].trim();
    if (historyContent) {
      const historyEntriesText = historyContent.split(
        new RegExp(HISTORY_ENTRY_SEPARATOR.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'),
      );
      historyEntriesText.forEach(entryText => {
        let playerChoice: string | undefined = undefined;
        const choiceMatch = entryText.match(/\n\[玩家选择文本\]: "([^"]+)"$/);
        let sceneBlockText = entryText.trim();
        if (choiceMatch && choiceMatch[1]) {
          playerChoice = choiceMatch[1];
          sceneBlockText = entryText.substring(0, entryText.lastIndexOf('\n[玩家选择文本]:')).trim();
        }
        if (sceneBlockText) {
          fullHistoryLog.push({ rawSceneBlock: sceneBlockText, playerChoiceText: playerChoice, timestamp: 0 });
        }
      });
    }
    // safeToastr('info', `Loaded ${fullHistoryLog.length} entries from history in message.`, 'Load Info');
  } else {
    // safeToastr('info', 'No history block found in message.', 'Load Info');
  }

  // Attempt to find the last "查看系统...msg_end...关闭系统" block
  const sceneBlockStartMarker = '查看系统\nmsg_start';
  const sceneBlockEndMarker = 'msg_end\n关闭系统';
  const lastSceneBlockStartIndex = rawPersistedState.lastIndexOf(sceneBlockStartMarker);

  if (lastSceneBlockStartIndex !== -1) {
    // Extract the substring from the last start marker to the end of the string
    const candidateString = rawPersistedState.substring(lastSceneBlockStartIndex);
    // Find the end marker within this candidate string
    const endMarkerIndexInCandidate = candidateString.indexOf(sceneBlockEndMarker);

    if (endMarkerIndexInCandidate !== -1) {
      // Calculate the actual start of the core content (after "msg_start\n")
      // sceneBlockStartMarker is "查看系统\nmsg_start"
      // We need content after "msg_start\n"
      const coreContentRelativeStart = 'msg_start\n'.length;
      // The core content is within the candidateString, starting after "查看系统\nmsg_start\n"
      // and ending before "msg_end\n关闭系统"
      // candidateString starts with "查看系统\nmsg_start"
      // So, the actual start of core content in candidateString is sceneBlockStartMarker.length - "查看系统\n".length
      // No, simpler: the match for msg_start([\s\S]*?)msg_end was correct for the content part.
      // Let's re-use that idea on the candidateString.

      const sceneBlockInnerMatch = candidateString.match(/msg_start([\s\S]*?)msg_end\s*关闭系统/);
      if (sceneBlockInnerMatch && sceneBlockInnerMatch[1]) {
        const latestRawSceneBlockContent = sceneBlockInnerMatch[1].trim();
        if (latestRawSceneBlockContent) {
          const parsedLatestScene = parseAIResponse(latestRawSceneBlockContent);
          if (parsedLatestScene && parsedLatestScene.rawInputBlock) {
            currentSceneDataFromMessage = parsedLatestScene;
            sceneDataSuccessfullyParsed = true;
            // safeToastr('success', 'Latest scene data parsed from message for display.', 'Load Success');
          } else {
            // safeToastr('error', 'Failed to parse latest scene block (found via lastIndexOf) from message for display.', 'Load Error');
          }
        } else {
          // safeToastr('warning', 'Extracted latest scene block content (from last block) is empty.', 'Load Warning');
        }
      } else {
        // safeToastr('warning', 'Found last scene block start, but failed to match full inner pattern (msg_start...msg_end).', 'Load Warning');
      }
    } else {
      // safeToastr('warning', 'Found last scene block start, but failed to find its corresponding end pattern (msg_end 关闭系统).', 'Load Warning');
    }
  }

  if (!sceneDataSuccessfullyParsed) {
    // safeToastr('warning', 'No current scene block (查看系统...) found or parsed correctly in message for display.', 'Load Warning');
    if (fullHistoryLog.length > 0) {
      const lastHistoryEntry = fullHistoryLog[fullHistoryLog.length - 1];
      const parsedLastHistoryScene = parseAIResponse(lastHistoryEntry.rawSceneBlock);
      if (parsedLastHistoryScene && parsedLastHistoryScene.rawInputBlock) {
        currentSceneDataFromMessage = parsedLastHistoryScene;
        sceneDataSuccessfullyParsed = true; // Considered successful if history is used
        // safeToastr('info', 'Used last history entry as current scene (from message).', 'Load Info');
      }
    }
  }

  return {
    playerStateLoadedFromMsg: playerStateSuccessfullyParsed,
    sceneDataLoadedFromMsg: sceneDataSuccessfullyParsed,
  };
}

function generateInitialSceneBlock(ps: PlayerState): string {
  const sceneTitle = "冒险的序章";
  const initialDescription = `你已准备好踏上征程！这个世界的故事将根据你所激活的冒险模组（通常位于 RPG_Modules_Test.json 世界书中）展开。请选择你的第一个行动，让传奇开始！`;
  
  const optionA_Text = "根据我的模组设定，正式开始冒险！"; 
  const optionB_Text = "我应该先了解一下我所处的环境（基于模组）。"; 
  const optionC_Text = "查看我的角色状态。";

  return `<场景:${sceneTitle}>
场景描述--"${initialDescription}"--HH:MM
当前地点--"${ps.currentLocation || '未知起点'}"--HH:MM
生命值--"${ps.hp.current}/${ps.hp.max}"--HH:MM
时间--"${ps.time || '某个时刻'}"--HH:MM
行动选项A--"${optionA_Text}"--HH:MM
行动选项B--"${optionB_Text}"--HH:MM
行动选项C--"${optionC_Text}"--HH:MM
</场景:${sceneTitle}>`;
}

async function handleStartNewGameClick() {
  // safeToastr('info', 'Starting new game from Worldbook...', 'New Game');
  if (!startNewGameButton || !adventureLogContainer || !startScreenContainer) return;

  startNewGameButton.disabled = true;
  startNewGameButton.textContent = '正在加载角色...';

  const characterDataJson = await loadCharacterDataFromLorebook('PLAYER');
  if (characterDataJson) {
    try {
      const loadedPlayerState: PlayerState = JSON.parse(characterDataJson);
      if (loadedPlayerState && typeof loadedPlayerState.name === 'string' && loadedPlayerState.attributes) {
        playerState = loadedPlayerState;
        // safeToastr('success', `角色 '${playerState.name}' 已从世界书加载！`, 'New Game');

        currentSceneDataFromMessage = null;
        fullHistoryLog = [];

        const initialSceneRawBlock = generateInitialSceneBlock(playerState);
        const parsedInitialScene = parseAIResponse(initialSceneRawBlock);

        if (parsedInitialScene && parsedInitialScene.rawInputBlock) {
          applySceneData(parsedInitialScene);
          fullHistoryLog.push({ rawSceneBlock: parsedInitialScene.rawInputBlock, timestamp: Date.now() });
          updatePlayerStatusDisplay();

          startScreenContainer.style.display = 'none';
          adventureLogContainer.style.display = 'flex';

          if (currentHostMessageId !== null) {
            await persistGameState();
          } else {
            // safeToastr('warning', '无法持久化新游戏：currentHostMessageId 为空。', 'New Game');
          }
        } else {
          // safeToastr('error', '无法为新角色解析初始场景。', 'New Game Error');
          startNewGameButton.disabled = false;
          startNewGameButton.textContent = '开始新冒险 (从世界书加载角色)';
        }
      } else {
        // safeToastr('error', '从世界书加载的角色数据格式无效。', 'New Game Error');
        playerState = getDefaultPlayerState();
        startNewGameButton.disabled = false;
        startNewGameButton.textContent = '开始新冒险 (从世界书加载角色)';
      }
    } catch (e) {
      // safeToastr('error', `解析世界书角色数据失败: ${(e as Error).message}`, 'New Game Error');
      playerState = getDefaultPlayerState();
      startNewGameButton.disabled = false;
      startNewGameButton.textContent = '开始新冒险 (从世界书加载角色)';
    }
  } else {
    // safeToastr('error', '无法从世界书加载角色 "PLAYER"。请检查世界书配置。将使用默认角色。', 'New Game Error');
    playerState = getDefaultPlayerState();
    startNewGameButton.disabled = false;
    startNewGameButton.textContent = '开始新冒险 (从世界书加载角色)';
  }
}

// --- 初始化函数 ---
async function onMounted() {
  setTimeout(async () => {
    ensureGlobals();
    // safeToastr('info', 'Adventure Log: Initializing (V3 - Start Screen Logic)...', 'Init');

    startScreenContainer = document.getElementById('start-screen-container');
    startNewGameButton = document.getElementById('start-new-game-button') as HTMLButtonElement;
    adventureLogContainer = document.getElementById('adventure-log-container');

    playerStatusArea = document.getElementById('player-status-area');
    mainNarrativeArea = document.getElementById('main-narrative-area');
    actionChoicesArea = document.getElementById('action-choices-area');
    healthDisplay = document.getElementById('health');
    locationDisplay = document.getElementById('location');
    timeDisplay = document.getElementById('time');
    charNameDisplay = document.getElementById('char-name-display');
    charRaceClassDisplay = document.getElementById('char-race-class-display');
    charLevelDisplay = document.getElementById('char-level-display');
    acDisplay = document.getElementById('ac-display');
    attrStrDisplay = document.getElementById('attr-str-display');
    attrDexDisplay = document.getElementById('attr-dex-display');
    attrConDisplay = document.getElementById('attr-con-display');
    attrIntDisplay = document.getElementById('attr-int-display');
    attrWisDisplay = document.getElementById('attr-wis-display');
    attrChaDisplay = document.getElementById('attr-cha-display');
    currencyGoldDisplay = document.getElementById('currency-gold-display');
    expDisplay = document.getElementById('exp-display');
    exhaustionDisplay = document.getElementById('exhaustion-display');
    toggleCharSheetButton = document.getElementById('toggle-char-sheet-button') as HTMLButtonElement;
    detailedCharacterSheet = document.getElementById('detailed-character-sheet');
    proficienciesDisplay = document.getElementById('proficiencies-display');
    skillsDisplay = document.getElementById('skills-display');
    spellSlotsDisplay = document.getElementById('spell-slots-display');
    equippedSpellsDisplay = document.getElementById('equipped-spells-display');
    equipmentDisplay = document.getElementById('equipment-display');
    inventoryDisplay = document.getElementById('inventory-display');
    activeQuestsDisplay = document.getElementById('active-quests-display');

    if (
      !startScreenContainer ||
      !startNewGameButton ||
      !adventureLogContainer ||
      !playerStatusArea ||
      !mainNarrativeArea ||
      !actionChoicesArea ||
      !toggleCharSheetButton ||
      !detailedCharacterSheet
    ) {
      // safeToastr('error', 'Core UI elements not found!', 'Init Error');
      return;
    }

    toggleCharSheetButton.addEventListener('click', () => {
      if (detailedCharacterSheet && toggleCharSheetButton) {
        const isHidden = detailedCharacterSheet.style.display === 'none';
        detailedCharacterSheet.style.display = isHidden ? 'block' : 'none';
        toggleCharSheetButton.textContent = isHidden ? '隐藏详细角色卡' : '显示详细角色卡';
      }
    });
    startNewGameButton.addEventListener('click', handleStartNewGameClick);

    // Try to load an existing game from the message
    const loadResult = await loadGameState();

    if (loadResult.playerStateLoadedFromMsg && loadResult.sceneDataLoadedFromMsg && currentSceneDataFromMessage) {
      // safeToastr('info', 'Existing game state loaded from message. Starting game.', 'Init');
      applySceneData(currentSceneDataFromMessage);
      updatePlayerStatusDisplay();
      startScreenContainer.style.display = 'none';
      adventureLogContainer.style.display = 'flex';
    } else {
      // safeToastr(
      //   'info',
      //   'No valid existing game state in message, or message is empty. Displaying start screen.',
      //   'Init',
      // );
      startScreenContainer.style.display = 'flex';
      adventureLogContainer.style.display = 'none';
      // playerState is already default from module scope or will be set by new game button
      updatePlayerStatusDisplay(); // Display default/empty state initially on status bar if needed
    }
    // safeToastr('success', 'Adventure Log UI setup complete.', 'Init Complete');
  }, 200);
}

// --- 脚本入口 ---
function calculateAttributeModifier(score: number): number {
  return Math.floor((score - 10) / 2);
}

async function loadCharacterDataFromLorebook(characterName: string): Promise<string | null> {
  if (typeof triggerSlash !== 'function') {
    // safeToastr('error', 'triggerSlash API is not available for loading character data.', 'API Error');
    return null;
  }
  const lorebookFileName = 'RPG_Modules_Test.json';
  // safeToastr(
  //   'info',
  //   `Attempting to load character '${characterName}' from lorebook '${lorebookFileName}'.`,
  //   'Lorebook Load',
  // );
  try {
    const findEntryCommand = `/findentry file="${lorebookFileName}" "${characterName}"`;
    const uidResult = await triggerSlash(findEntryCommand);
    if (!uidResult || uidResult.trim() === '' || uidResult.trim() === '[]') {
      // safeToastr('warning', `No UID found for key '${characterName}'.`, 'Lorebook Load');
      return null;
    }
    let entryUid: string | null = null;
    try {
      const parsedUidResult = JSON.parse(uidResult);
      if (Array.isArray(parsedUidResult) && parsedUidResult.length > 0) entryUid = parsedUidResult[0].toString();
      else if (typeof parsedUidResult === 'string' && parsedUidResult.trim() !== '') entryUid = parsedUidResult.trim();
      else if (typeof parsedUidResult === 'number') entryUid = parsedUidResult.toString();
    } catch (e) {
      if (typeof uidResult === 'string' && uidResult.trim() !== '') entryUid = uidResult.trim();
    }
    if (!entryUid) {
      // safeToastr('warning', `Could not extract UID from /findentry result: ${uidResult}`, 'Lorebook Load');
      return null;
    }
    const getContentCommand = `/getentryfield file="${lorebookFileName}" field=content "${entryUid}"`;
    const content = await triggerSlash(getContentCommand);
    if (content && content.trim() !== '') {
      // safeToastr('success', `Content retrieved for UID '${entryUid}' (key '${characterName}').`, 'Lorebook Load');
      return content;
    } else {
      // safeToastr('warning', `No content for UID '${entryUid}' (key '${characterName}').`, 'Lorebook Load');
      return null;
    }
  } catch (error) {
    // safeToastr(
    //   'error',
    //   `Error loading from lorebook for '${characterName}': ${(error as Error).message}`,
    //   'Lorebook Load Error',
    // );
    return null;
  }
}

if (document.readyState === 'complete' || document.readyState === 'interactive') {
  onMounted();
} else {
  window.addEventListener('DOMContentLoaded', onMounted);
}
