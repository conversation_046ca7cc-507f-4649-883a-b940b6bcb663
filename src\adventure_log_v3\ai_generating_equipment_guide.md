# AI动态生成武器与装备指南 (冒险日志 v3)

## 1. 文档目的

本文档旨在为AI提供清晰的指导，说明如何在“冒险日志 v3”游戏中动态生成符合客户端解析格式的武器和装备数据。这些数据将用于玩家可能通过各种方式（如战斗掉落、商店购买、任务奖励、宝箱发现、NPC赠与等）获取的物品。

客户端将依赖这些结构化的物品数据来更新玩家的状态、物品栏和装备，并正确应用其效果（如攻击加值、伤害加值、AC加值等）。

**核心原则**：AI负责创造性地设计物品（尤其是魔法物品的名称、描述和效果组合），并以严格的JSON格式返回这些物品的数据。客户端负责解析这些数据并应用其机制效果。

## 2. 核心数据结构回顾

AI生成的武器和装备数据主要遵循以下TypeScript接口（定义于 `src/adventure_log_v3/types/index.ts`）：

```typescript
export interface EquipmentItem {
  name: string;                 // 物品的显示名称，例如 "焰舌长剑+1"
  type: string;                 // "武器", "盔甲", "盾牌", "戒指", "护符", "奇物" 等
  id?: string;                   // (推荐) 物品的唯一ID，AI应尽量生成 (例如 "uuid_longsword_flame_tongue_01")
  baseItemName?: string;         // (可选) 指向标准物品模板的名称 (例如 "长剑", "皮甲")
  description?: string;          // 物品描述，包括魔法效果的文字说明
  equipped?: boolean;            // (当给予玩家时) 是否立即装备
  properties?: string[];         // (主要用于武器) 武器属性，如 ["灵巧", "多用 (1d10)"]
  damage?: string;               // (武器可选) 基础伤害骰，可覆盖模板
  damageType?: string;           // (武器可选) 基础伤害类型，可覆盖模板
  magicEffects?: MagicEffect[];  // 魔法效果列表 (核心！)
  // 其他如 cost, weight 可按需添加，但主要通过 baseItemName 从模板获取
}

export interface InventoryItem { // 用于玩家物品栏中的物品
  id?: string;
  name: string;
  quantity: number;
  description?: string;
  baseItemName?: string;
  properties?: string[];
  magicEffects?: MagicEffect[];
  type?: string; // 建议也包含类型，便于分类
}

export interface MagicEffect {
  type: EffectType;
  value: any;
  condition?: string; // 例如 "仅对不死生物生效"
  notes?: string;     // 效果的简短说明或名称
  // sourceId, duration 等字段按需添加
}

// EffectType 示例 (完整列表见 types/index.ts)
// "ATTACK_BONUS", "DAMAGE_BONUS_STATIC", "DAMAGE_BONUS_DICE", 
// "ON_HIT_EFFECT_SAVE", "ON_HIT_EFFECT_CONDITION", "AC_BONUS", 
// "SAVE_BONUS", "SKILL_BONUS", "WEAPON_PROPERTY_GRANT", "WEAPON_PROPERTY_MODIFY"
```

## 3. AI生成武器/装备数据的场景

AI可能需要在以下场景中生成物品数据：
*   **敌人掉落**: 击败敌人后，AI决定敌人掉落了什么物品。
*   **商店购买/出售**: 玩家在商店中查看或购买物品。
*   **任务奖励**: 完成任务后，AI给予玩家物品奖励。
*   **宝箱/探索发现**: 玩家打开宝箱或在特定地点探索时发现物品。
*   **NPC赠与/交易**: NPC给予玩家物品。

## 4. AI返回物品数据的机制

AI主要通过当前场景 `AdventureSceneJSON` 中的 `variableUpdates` 数组来将物品数据传递给客户端。
*   **操作类型**: 使用 `operation: "物品获得"`。
*   **目标**: `target: "玩家"`。
*   **路径**: `path: "inventory"` (通常先加入物品栏) 或 `path: "equipment"` (如果AI希望玩家直接装备)。
*   **值 (`value`)**: **此字段必须是一个符合 `InventoryItem` 或 `EquipmentItem` 接口的完整JSON对象。**

**示例 (`variableUpdates` 中给予玩家一把魔法匕首):**
```json
"variableUpdates": [
  {
    "target": "玩家",
    "path": "inventory", // 或 "equipment"
    "operation": "物品获得",
    "value": {
      "id": "dagger_venom_001",
      "name": "毒蛇之牙匕首",
      "type": "武器",
      "baseItemName": "匕首",
      "description": "一把淬毒的匕首，刀刃上闪烁着不祥的绿光。",
      "equipped": false, // 初始放入物品栏
      "properties": ["灵巧", "轻型", "投掷 (射程 20/60)"], // 从匕首模板继承
      "damage": "1d4", // 从匕首模板继承
      "damageType": "穿刺", // 从匕首模板继承
      "magicEffects": [
        { 
          "type": "ON_HIT_EFFECT_SAVE", 
          "value": { 
            "saveAttribute": "体质", 
            "dc": 13, 
            "effectOnFail": "目标中毒1分钟",
            "notes": "毒素攻击"
          }
        },
        {
          "type": "DAMAGE_BONUS_DICE",
          "value": { "dice": "1d6", "type": "毒素" },
          "condition": "仅在目标豁免失败时生效" // 示例条件
        }
      ]
    }
  }
]
```

## 5. 生成普通武器/装备的指南

*   **`id`**: (推荐) 生成一个唯一的ID，例如 `dagger_standard_001`。
*   **`name`**: 物品的显示名称。可以是标准名称（如“长剑”），也可以是描述性名称（如“一把生锈的旧匕首”）。
*   **`type`**: 明确物品类型 ("武器", "盔甲", "盾牌", "戒指", "药水", "卷轴", "奇物"等)。
*   **`baseItemName`**: (推荐) 尽可能引用 `weapon_templates.md` (或其他未来可能存在的标准物品模板文件) 中的物品名称。这有助于客户端获取基础属性。
*   **`description`**: 对物品的文字描述。
*   **物理属性 (`damage`, `damageType`, `properties` for weapons; AC for armor)**:
    *   如果指定了有效的 `baseItemName`，AI**可以不提供**这些字段，客户端会尝试从模板加载。
    *   如果AI希望生成的物品与标准模板有差异（例如，一把损坏的武器伤害更低，或一把特制的武器有额外属性），则AI**必须**在物品实例中明确提供这些覆盖模板的属性。
*   **`magicEffects`**: 对于普通物品，此数组通常为空或不存在。

## 6. 生成魔法武器/装备的指南

这是AI创造力的核心体现。
*   **`id`, `name`, `type`, `baseItemName`, `description`**: 同上。`name` 应能反映其魔法特性 (例如 "焰舌长剑+1", "防护戒指+1")。`description` 应包含对魔法效果的文字描述。
*   **`magicEffects`: `MagicEffect[]` (核心！)**:
    *   这是一个对象数组，每个对象描述一个魔法效果。
    *   **`type`**: 必须是 `EffectType` 中定义的值之一。
    *   **`value`**: 根据 `type` 的不同，`value` 的结构也不同。请严格遵循以下示例：
        *   `ATTACK_BONUS`: `value: 1` (代表攻击检定+1)
        *   `DAMAGE_BONUS_STATIC`: `value: { "amount": 1 }` (代表伤害+1) 或 `value: { "amount": 2, "type": "火焰" }` (代表火焰伤害+2)
        *   `DAMAGE_BONUS_DICE`: `value: { "dice": "1d6", "type": "火焰" }` (代表附加1d6火焰伤害)
        *   `AC_BONUS`: `value: 1` (代表AC+1)
        *   `SAVE_BONUS`: `value: { "attribute": "all", "bonus": 1 }` (所有豁免+1) 或 `value: { "attribute": "感知", "bonus": 1 }` (感知豁免+1)
        *   `SKILL_BONUS`: `value: { "skillName": "隐匿", "bonus": 3 }` (隐匿技能+3)
        *   `WEAPON_PROPERTY_GRANT`: `value: "灵巧"` (赋予武器“灵巧”属性)
        *   `WEAPON_PROPERTY_MODIFY`: `value: { "action": "remove", "propertyToRemove": "重型" }` (移除“重型”属性)
        *   `ON_HIT_EFFECT_SAVE`: `value: { "saveAttribute": "体质", "dc": 14, "effectOnFail": "目标陷入中毒状态1分钟", "notes": "毒性爆发" }`
        *   `ON_HIT_EFFECT_CONDITION`: `value: { "condition": "目眩", "duration": "1轮", "saveDC": 12, "saveAttribute": "感知" }` (命中时目标需进行DC12感知豁免，失败则目眩1轮)
    *   **`condition`**: (可选) 描述效果生效的条件，例如 "仅对不死生物生效", "每日一次"。客户端目前可能不会完全解析和执行复杂条件，但AI应提供以供未来扩展和叙事。
    *   **`notes`**: (可选) 对魔法效果的简短命名或注释，例如 "吸血鬼之触", "斩龙"。

## 7. 敌人装备的表示与掉落

*   **我们不处理敌人的详细战斗计算逻辑，这完全交给AI。** AI在其叙述和攻击宣告中体现敌人的能力。
*   **目的**: 为了让AI在敌人被击败后，能够方便地决定掉落什么，并且掉落的物品数据是客户端可解析的。
*   **机制**:
    1.  当AI在 `AdventureSceneJSON` 的 `enemies` 数组中描述一个敌人时，可以为其增加一个**可选的**字段 `notableEquipment: EquipmentItem[]`。
    2.  此字段用于存放该敌人身上**值得注意的、且玩家有可能获取**的装备的详细数据（遵循 `EquipmentItem` 格式，包括魔法效果）。
        ```json
        // EnemyStateJSON 示例扩展
        {
          "id": "goblin_boss_01",
          "name": "地精酋长格鲁克",
          "hp": { "current": 27, "max": 27 },
          "ac": 15,
          "intent": "指挥地精攻击！",
          "notableEquipment": [
            {
              "id": "grubnaks_choppa",
              "name": "格鲁克的劈砍小刀",
              "type": "武器",
              "baseItemName": "短剑",
              "description": "一把粗制但锋利的短剑，剑柄上缠着肮脏的布条。",
              "magicEffects": [
                { "type": "ATTACK_BONUS", "value": 1 },
                { "type": "DAMAGE_BONUS_DICE", "value": { "dice": "1d4", "type": "流血" }, "notes": "恶毒伤口" }
              ]
            }
          ]
        }
        ```
    3.  **客户端不会使用 `notableEquipment` 来计算敌人的攻防。**
    4.  当该敌人被击败后，AI在生成新的场景时，可以从其 `notableEquipment` 中选择一件或多件物品，并通过 `variableUpdates` (操作 "物品获得") 将其完整的 `EquipmentItem` 数据给予玩家。

## 8. 商店物品的表示与交易

*   **描述商品**: AI在 `narrative` 中描述商店出售的商品及其价格。
*   **提供购买选项**: AI在 `playerChoices` 中提供购买特定商品的选项。
*   **执行交易**:
    *   如果玩家选择购买，客户端会通知AI玩家的选择。
    *   AI在下一个场景的 `variableUpdates` 中：
        1.  使用 `operation: "物品获得"`，`target: "玩家"`，`path: "inventory"`，并将完整的 `EquipmentItem` 或 `InventoryItem` 数据作为 `value`，将物品给予玩家。
        2.  使用 `operation: "减少"`，`target: "玩家"`，`path: "currency.gold"` (或其他货币类型)，并将价格作为 `value`，扣除玩家金钱。
*   **(可选/未来扩展) 结构化商店库存**: 如果需要更复杂的商店界面，AI可以在 `AdventureSceneJSON` 中增加一个自定义字段，例如 `shopInventory: EquipmentItem[]`，其中包含所有商品的详细数据。客户端可以读取此字段来动态生成商店UI。目前，优先通过 `narrative` 描述和 `variableUpdates` 交易。

## 9. JSON格式最终强调

*   **严格的JSON**: 所有在 `##@@_ADVENTURE_BEGIN_@@##` 和 `##@@_ADVENTURE_END_@@##` 标记之间的内容必须是单一的、语法完全正确的JSON对象。
*   **禁止注释**: JSON对象内部**绝对不能**包含任何 `//` 或 `/* ... */` 形式的注释。
*   **引号**: 所有键名和字符串值必须使用双引号 `"`。
*   **逗号**: 对象和数组的最后一个元素后不能有逗号。
*   **换行符**: JSON字符串值内部的换行必须使用 `\\n`。

遵循这些指南，AI将能够生成客户端可以正确解析和使用的武器装备数据，从而丰富游戏体验。
