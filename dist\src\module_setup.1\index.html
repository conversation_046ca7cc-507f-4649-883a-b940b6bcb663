<!doctype html><html lang="zh-CN"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1"><title>模组设置</title><style>body{font-family:sans-serif;padding:20px;background-color:#f4f4f4;color:#333}#module-setup-container{background-color:#fff;padding:20px;border-radius:8px;box-shadow:0 0 10px rgba(0,0,0,.1);max-width:700px;margin:auto}h1{color:#333;text-align:center;margin-bottom:20px}.form-group{margin-bottom:15px}.form-group label{display:block;margin-bottom:5px;font-weight:bold}.form-group input[type=text],.form-group textarea{width:calc(100% - 22px);padding:10px;border:1px solid #ccc;border-radius:4px;font-size:14px;box-sizing:border-box}.form-group textarea{resize:vertical;min-height:100px}.button-group{display:flex;gap:10px;margin-top:20px}button{flex-grow:1;padding:12px 15px;background-color:#007bff;color:#fff;border:none;border-radius:4px;cursor:pointer;font-size:16px}#ai-generate-content-button{background-color:#28a745}button:hover{opacity:.9}#output-message{margin-top:20px;padding:10px;border:1px solid #ddd;background-color:#e9e9e9;min-height:30px;border-radius:4px;word-wrap:break-word}hr{border:0;height:1px;background-color:#ccc;margin:30px 0}.navigation-buttons button{background-color:#5a6268}.navigation-buttons button:hover{background-color:#4a4f54}#character-sheet-form .dynamic-list-section .weapon-selection-row{display:flex;flex-direction:column;align-items:stretch;gap:8px}#character-sheet-form .dynamic-list-section .weapon-selection-row .weapon-details-display{padding:8px;border:1px solid #e0e0e0;border-radius:4px;background-color:#f8f9fa;font-size:.9em;line-height:1.4;min-height:40px;word-break:break-word}#character-sheet-form .dynamic-list-section .weapon-selection-row .weapon-details-display strong{color:#333}#character-sheet-form .dynamic-list-section .weapon-selection-row .weapon-details-display br{display:block;content:"";margin-top:4px}#character-sheet-form .dynamic-list-section .weapon-selection-row .weapon-select-control-group{display:flex;align-items:center;gap:10px;flex-wrap:nowrap}#character-sheet-form .dynamic-list-section .weapon-selection-row .weapon-select-control-group label{font-weight:normal;margin-bottom:0;flex-shrink:0}#character-sheet-form .dynamic-list-section .weapon-selection-row .weapon-select-control-group select{flex-grow:1;min-width:150px}#character-sheet-form .dynamic-list-section .weapon-selection-row .weapon-select-control-group .remove-item-button{margin-left:0;flex-shrink:0}@media(max-width: 768px){#character-sheet-form .dynamic-list-section .weapon-selection-row .weapon-select-control-group{flex-wrap:wrap}#character-sheet-form .dynamic-list-section .weapon-selection-row .weapon-select-control-group select{min-width:calc(100% - 80px)}}#character-creation-container h1{color:#333;text-align:center;margin-bottom:20px}#character-sheet-form .form-section{margin-bottom:25px;padding-bottom:15px;border-bottom:1px dashed #eee}#character-sheet-form .form-section:last-child{border-bottom:none;margin-bottom:0;padding-bottom:0}#character-sheet-form .form-row{display:flex;flex-wrap:wrap;gap:15px;margin-bottom:10px}#character-sheet-form .form-group-inline{flex:1 1 calc(50% - 15px);min-width:180px;display:flex;flex-direction:column}@media(max-width: 600px){#character-sheet-form .form-group-inline{flex-basis:100%}}#character-sheet-form .form-group-inline label{margin-bottom:3px;font-size:.9em}#character-sheet-form .form-group-inline input[type=text],#character-sheet-form .form-group-inline input[type=number],#character-sheet-form .form-group-inline select{width:100%;padding:8px;border:1px solid #ccc;border-radius:4px;font-size:14px;box-sizing:border-box}#character-sheet-form textarea{min-height:60px}#character-sheet-form .clear-section-button{background-color:#dc3545;font-size:.8em;padding:6px 10px;margin-top:10px;flex-grow:0;max-width:120px}#character-sheet-form .attribute-grid{display:flex;flex-direction:column;gap:15px;margin-bottom:10px}#character-sheet-form .attribute-row{display:flex;flex-direction:column;gap:5px;padding:10px;border:1px solid #eee;border-radius:4px}#character-sheet-form .attribute-row>strong{font-size:1.1em;margin-bottom:8px;color:#333}#character-sheet-form .attribute-inputs{display:grid;grid-template-columns:repeat(auto-fit, minmax(70px, 1fr));gap:5px 8px;align-items:center}#character-sheet-form .attribute-inputs>div{display:flex;flex-direction:column}#character-sheet-form .attribute-inputs label{font-size:.75em;color:#555;margin-bottom:1px;text-align:center}#character-sheet-form .attribute-inputs input[type=number],#character-sheet-form .attribute-inputs input[type=text]{width:100%;padding:6px;font-size:13px;text-align:center;border:1px solid #ccc;border-radius:4px;box-sizing:border-box}#character-sheet-form .attribute-inputs input[readonly]{background-color:#e9ecef;font-weight:bold}#character-sheet-form .attribute-grid .grid-header,#character-sheet-form .attribute-grid span:not(.op),#character-sheet-form .attribute-grid span.op{display:none}#character-sheet-form h3,#character-sheet-form h4{margin-top:20px;margin-bottom:10px;color:#495057}#character-sheet-form h4{font-size:1.1em}#character-sheet-form .spell-list{display:flex;flex-direction:column;gap:5px;margin-bottom:10px}#character-sheet-form .spell-list .spell-item{display:flex;align-items:center;gap:5px;margin-bottom:5px}#character-sheet-form .spell-list input[type=text]{flex-grow:1;padding:8px;border:1px solid #ccc;border-radius:4px}#character-sheet-form .spell-list .remove-spell-button{background-color:#6c757d;color:#fff;border:none;padding:2px 6px;font-size:.75em;line-height:1;cursor:pointer;border-radius:3px;flex-shrink:0;min-width:auto;flex-grow:0;margin-left:5px}#character-sheet-form .spell-list .remove-spell-button:hover{background-color:#5a6268;opacity:1}#character-sheet-form #add-cantrip-button,#character-sheet-form #add-level1-spell-button{background-color:#6c757d;font-size:.9em;padding:10px 15px;margin-top:8px;margin-bottom:15px;flex-grow:0;max-width:180px}#character-sheet-form .form-row .clear-section-button{margin-left:auto;align-self:center}#character-sheet-form .skills-section h4{margin-bottom:10px}#character-sheet-form .skills-section .skills-grid{display:grid;grid-template-columns:repeat(2, 1fr);gap:10px 20px;margin-bottom:10px}@media(max-width: 600px){#character-sheet-form .skills-section .skills-grid{grid-template-columns:repeat(1, 1fr)}}#character-sheet-form .skills-section .skill-item{display:flex;flex-wrap:wrap;align-items:center;gap:5px;padding:5px;border:1px solid #efefef;border-radius:4px}#character-sheet-form .skills-section .skill-item label{font-weight:normal;margin-left:5px;margin-right:auto;font-size:.9em;flex-basis:calc(100% - 100px)}#character-sheet-form .skills-section .skill-item input[type=checkbox]{margin-right:5px}#character-sheet-form .skills-section .skill-item .skill-modifier-input,#character-sheet-form .skills-section .skill-item .skill-final-value{width:50px;padding:5px;font-size:.9em;text-align:center;border:1px solid #ccc;border-radius:3px;box-sizing:border-box}#character-sheet-form .skills-section .skill-item .skill-final-value{background-color:#e9ecef;font-weight:bold}#character-sheet-form .skills-section .clear-section-button{margin-top:10px}#character-sheet-form .dynamic-list-section{margin-bottom:20px}#character-sheet-form .dynamic-list-section h4{margin-bottom:8px}#character-sheet-form .dynamic-list-section .dynamic-item-row{display:flex;gap:10px;margin-bottom:8px;align-items:center}#character-sheet-form .dynamic-list-section .dynamic-item-row input[type=text],#character-sheet-form .dynamic-list-section .dynamic-item-row input[type=number]{padding:8px;border:1px solid #ccc;border-radius:4px;font-size:14px;box-sizing:border-box}#character-sheet-form .dynamic-list-section .dynamic-item-row input[name\$="Name[]"]{flex-grow:2}#character-sheet-form .dynamic-list-section .dynamic-item-row input[name\$="Details[]"],#character-sheet-form .dynamic-list-section .dynamic-item-row input[name\$="Description[]"]{flex-grow:3}#character-sheet-form .dynamic-list-section .dynamic-item-row input[name\$="Quantity[]"]{width:70px;flex-grow:0;text-align:center}#character-sheet-form .dynamic-list-section .dynamic-item-row .remove-item-button{padding:6px 10px;font-size:.8em;background-color:#dc3545;color:#fff;border:none;border-radius:3px;cursor:pointer;flex-shrink:0}#character-sheet-form .dynamic-list-section .add-item-button{background-color:#5cb85c;font-size:.9em;padding:10px 15px;margin-top:5px;flex-grow:0;max-width:150px}#character-sheet-form .dynamic-list-section .full-equipment-row{display:flex;flex-direction:column;align-items:stretch;gap:12px;padding:12px;border:1px solid #b0b0b0;border-radius:5px;margin-bottom:15px;background-color:#fdfdfd}#character-sheet-form .dynamic-list-section .full-equipment-row .equipment-controls-container{display:flex;flex-wrap:wrap;gap:8px;align-items:center;margin-bottom:8px}#character-sheet-form .dynamic-list-section .full-equipment-row .equipment-controls-container .equipment-type-select{min-width:120px;flex-basis:120px;flex-grow:1}#character-sheet-form .dynamic-list-section .full-equipment-row .equipment-controls-container .equipment-custom-name{min-width:150px;flex-basis:200px;flex-grow:2}#character-sheet-form .dynamic-list-section .full-equipment-row .equipment-controls-container .remove-item-button{padding:7px 12px;font-size:.85em;background-color:#dc3545;flex-shrink:0;margin-left:auto}@media(max-width: 500px){#character-sheet-form .dynamic-list-section .full-equipment-row .equipment-controls-container{flex-direction:column;align-items:stretch}#character-sheet-form .dynamic-list-section .full-equipment-row .equipment-controls-container .equipment-type-select,#character-sheet-form .dynamic-list-section .full-equipment-row .equipment-controls-container .equipment-custom-name{flex-basis:auto;width:100%}#character-sheet-form .dynamic-list-section .full-equipment-row .equipment-controls-container .remove-item-button{margin-left:0;margin-top:8px;width:100%}}#character-sheet-form .dynamic-list-section .full-equipment-row .template-select-container{display:flex;gap:8px;align-items:center}#character-sheet-form .dynamic-list-section .full-equipment-row .template-select-container label{margin-bottom:0;flex-shrink:0;font-weight:bold}#character-sheet-form .dynamic-list-section .full-equipment-row .template-select-container .equipment-template-select{flex-grow:1}#character-sheet-form .dynamic-list-section .full-equipment-row .base-properties-display{padding:10px;border:1px solid #dcdcdc;border-radius:4px;background-color:#f7f7f7;font-size:.9em;line-height:1.5;min-height:35px;word-break:break-word}#character-sheet-form .dynamic-list-section .full-equipment-row .magic-effects-container{display:flex;flex-direction:column;gap:10px;margin-top:8px;padding-top:8px;border-top:1px dashed #ccc}#character-sheet-form .dynamic-list-section .full-equipment-row .magic-effect-edit-row{display:flex;flex-wrap:wrap;gap:10px;padding:10px;border:1px solid #c9c9c9;border-radius:4px;background-color:#f0f0f0}#character-sheet-form .dynamic-list-section .full-equipment-row .magic-effect-edit-row .magic-effect-type-select{min-width:180px;flex-basis:220px;flex-grow:1}#character-sheet-form .dynamic-list-section .full-equipment-row .magic-effect-edit-row .magic-effect-value-inputs{display:flex;flex-wrap:wrap;gap:8px 12px;align-items:center;flex-basis:320px;flex-grow:2}#character-sheet-form .dynamic-list-section .full-equipment-row .magic-effect-edit-row .magic-effect-value-inputs label{margin-bottom:0;margin-right:5px;font-size:.85em;flex-shrink:0}#character-sheet-form .dynamic-list-section .full-equipment-row .magic-effect-edit-row .magic-effect-value-inputs input,#character-sheet-form .dynamic-list-section .full-equipment-row .magic-effect-edit-row .magic-effect-value-inputs textarea,#character-sheet-form .dynamic-list-section .full-equipment-row .magic-effect-edit-row .magic-effect-value-inputs select{padding:7px;font-size:.9em;border:1px solid #a0a0a0;border-radius:3px;flex-grow:1;min-width:100px}#character-sheet-form .dynamic-list-section .full-equipment-row .magic-effect-edit-row .magic-effect-value-inputs .full-width-field{flex-basis:100%;display:flex;flex-direction:column}#character-sheet-form .dynamic-list-section .full-equipment-row .magic-effect-edit-row .magic-effect-value-inputs .full-width-field label{margin-bottom:3px}#character-sheet-form .dynamic-list-section .full-equipment-row .magic-effect-edit-row .magic-effect-value-inputs textarea.full-width-field{min-height:45px}#character-sheet-form .dynamic-list-section .full-equipment-row .magic-effect-edit-row .remove-magic-effect-button{padding:5px 9px;font-size:.8em;background-color:#c82333;align-self:center;margin-left:auto}#character-sheet-form .dynamic-list-section .full-equipment-row .add-magic-effect-button{align-self:flex-start;max-width:200px;background-color:#17a2b8;font-size:.9em}#character-sheet-form .dynamic-list-section .full-equipment-row .equipment-custom-description{width:100%;min-height:60px;padding:8px;border:1px solid #ccc;border-radius:4px;font-size:14px;box-sizing:border-box}
</style></head><body><div id="module-setup-container"><h1>游戏模组创作</h1><div class="form-group"><label for="module-title-input">模组标题 (将作为世界书条目Key):</label> <input id="module-title-input" placeholder="例如：失落的遗迹探险 或 附录章节A"></div><div class="form-group"><label for="module-content-textarea">模组内容 (或 AI生成提示):</label> <textarea id="module-content-textarea" rows="10" placeholder="在此输入模组的详细描述，或输入给AI的创作指令（如：'详细描述一个被遗忘的森林神殿，包括它的历史和守护者。'）"></textarea></div><div class="button-group"><button id="save-custom-module-button">保存当前内容到世界书</button> <button id="ai-generate-content-button">AI辅助创作并保存</button></div><div id="output-message" style="margin-bottom:20px"></div><hr style="margin:20px 0"><div class="button-group navigation-buttons"><button id="go-to-character-creation-button">创建新人物</button></div></div><div id="character-creation-container" style="display:none;background-color:#fff;padding:20px;border-radius:8px;box-shadow:0 0 10px rgba(0,0,0,.1);max-width:700px;margin:20px auto"><h1>创建人物</h1><form id="character-sheet-form"><div class="form-section"><div class="form-row"><div class="form-group form-group-inline"><label for="char-name">角色名</label> <input id="char-name" name="charName"></div><div class="form-group form-group-inline"><label for="char-player">玩家</label> <input id="char-player" name="charPlayer"></div></div><div class="form-row"><div class="form-group form-group-inline"><label for="char-age">年龄</label> <input id="char-age" name="charAge"></div><div class="form-group form-group-inline"><label for="char-gender">性别</label> <select id="char-gender" name="charGender"><option value="male">男性</option><option value="female">女性</option><option value="other">其他</option></select></div><div class="form-group form-group-inline"><label for="char-alignment">阵营</label> <input id="char-alignment" name="charAlignment"></div></div><div class="form-row"><div class="form-group form-group-inline"><label for="char-faith">信仰</label> <input id="char-faith" name="charFaith" style="width:100%"></div></div><div class="form-row"><div class="form-group form-group-inline"><label for="char-height">身高</label> <input id="char-height" name="charHeight"></div><div class="form-group form-group-inline"><label for="char-weight">体重</label> <input id="char-weight" name="charWeight"></div><div class="form-group form-group-inline"><label for="char-xp">经验值</label> <input type="number" id="char-xp" name="charXp" value="0"></div></div><div class="form-row"><div class="form-group form-group-inline"><label for="char-currency-gold">金币 (GP)</label> <input type="number" id="char-currency-gold" name="charCurrencyGold" value="0" min="0"></div><div class="form-group form-group-inline"><label for="char-currency-silver">银币 (SP)</label> <input type="number" id="char-currency-silver" name="charCurrencySilver" value="0" min="0"></div><div class="form-group form-group-inline"><label for="char-currency-copper">铜币 (CP)</label> <input type="number" id="char-currency-copper" name="charCurrencyCopper" value="0" min="0"></div></div></div><div class="form-section"><div class="form-group"><label for="char-appearance">外貌描写</label> <textarea id="char-appearance" name="charAppearance" rows="3"></textarea></div><div class="form-group"><label for="char-story">角色故事</label> <textarea id="char-story" name="charStory" rows="5"></textarea></div></div><div class="form-section"><div class="form-row"><div class="form-group form-group-inline"><label for="char-race">种族</label> <select id="char-race" name="charRace"><option value="人类">人类</option><option value="精灵">精灵</option><option value="矮人">矮人</option><option value="半身人">半身人</option><option value="龙裔">龙裔</option><option value="侏儒">侏儒</option><option value="提夫林">提夫林</option><option value="兽人">兽人</option><option value="阿斯莫">阿斯莫</option><option value="歌利亚">歌利亚</option></select></div><div class="form-group form-group-inline"><label for="char-background">背景</label> <input id="char-background" name="charBackground"></div></div><div class="form-section skills-section"><h4>技能熟练</h4><div class="skills-grid"><div class="skill-item"><input type="checkbox" id="skill-athletics" name="skillAthleticsProf" data-skill-name="运动" data-attribute="strength"><label for="skill-athletics">运动 (力量)</label><input type="number" name="skillAthleticsMod" value="0" class="skill-modifier-input"><input name="skillAthleticsFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-acrobatics" name="skillAcrobaticsProf" data-skill-name="体操" data-attribute="dexterity"><label for="skill-acrobatics">体操 (敏捷)</label><input type="number" name="skillAcrobaticsMod" value="0" class="skill-modifier-input"><input name="skillAcrobaticsFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-sleight-of-hand" name="skillSleightOfHandProf" data-skill-name="巧手" data-attribute="dexterity"><label for="skill-sleight-of-hand">巧手 (敏捷)</label><input type="number" name="skillSleightOfHandMod" value="0" class="skill-modifier-input"><input name="skillSleightOfHandFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-stealth" name="skillStealthProf" data-skill-name="隐匿" data-attribute="dexterity"><label for="skill-stealth">隐匿 (敏捷)</label><input type="number" name="skillStealthMod" value="0" class="skill-modifier-input"><input name="skillStealthFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-arcana" name="skillArcanaProf" data-skill-name="奥秘" data-attribute="intelligence"><label for="skill-arcana">奥秘 (智力)</label><input type="number" name="skillArcanaMod" value="0" class="skill-modifier-input"><input name="skillArcanaFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-history" name="skillHistoryProf" data-skill-name="历史" data-attribute="intelligence"><label for="skill-history">历史 (智力)</label><input type="number" name="skillHistoryMod" value="0" class="skill-modifier-input"><input name="skillHistoryFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-investigation" name="skillInvestigationProf" data-skill-name="调查" data-attribute="intelligence"><label for="skill-investigation">调查 (智力)</label><input type="number" name="skillInvestigationMod" value="0" class="skill-modifier-input"><input name="skillInvestigationFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-nature" name="skillNatureProf" data-skill-name="自然" data-attribute="intelligence"><label for="skill-nature">自然 (智力)</label><input type="number" name="skillNatureMod" value="0" class="skill-modifier-input"><input name="skillNatureFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-religion" name="skillReligionProf" data-skill-name="宗教" data-attribute="intelligence"><label for="skill-religion">宗教 (智力)</label><input type="number" name="skillReligionMod" value="0" class="skill-modifier-input"><input name="skillReligionFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-animal-handling" name="skillAnimalHandlingProf" data-skill-name="驯兽" data-attribute="wisdom"><label for="skill-animal-handling">驯兽 (感知)</label><input type="number" name="skillAnimalHandlingMod" value="0" class="skill-modifier-input"><input name="skillAnimalHandlingFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-insight" name="skillInsightProf" data-skill-name="洞悉" data-attribute="wisdom"><label for="skill-insight">洞悉 (感知)</label><input type="number" name="skillInsightMod" value="0" class="skill-modifier-input"><input name="skillInsightFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-medicine" name="skillMedicineProf" data-skill-name="医药" data-attribute="wisdom"><label for="skill-medicine">医药 (感知)</label><input type="number" name="skillMedicineMod" value="0" class="skill-modifier-input"><input name="skillMedicineFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-perception" name="skillPerceptionProf" data-skill-name="察觉" data-attribute="wisdom"><label for="skill-perception">察觉 (感知)</label><input type="number" name="skillPerceptionMod" value="0" class="skill-modifier-input"><input name="skillPerceptionFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-survival" name="skillSurvivalProf" data-skill-name="求生" data-attribute="wisdom"><label for="skill-survival">求生 (感知)</label><input type="number" name="skillSurvivalMod" value="0" class="skill-modifier-input"><input name="skillSurvivalFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-deception" name="skillDeceptionProf" data-skill-name="欺瞒" data-attribute="charisma"><label for="skill-deception">欺瞒 (魅力)</label><input type="number" name="skillDeceptionMod" value="0" class="skill-modifier-input"><input name="skillDeceptionFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-intimidation" name="skillIntimidationProf" data-skill-name="威吓" data-attribute="charisma"><label for="skill-intimidation">威吓 (魅力)</label><input type="number" name="skillIntimidationMod" value="0" class="skill-modifier-input"><input name="skillIntimidationFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-performance" name="skillPerformanceProf" data-skill-name="表演" data-attribute="charisma"><label for="skill-performance">表演 (魅力)</label><input type="number" name="skillPerformanceMod" value="0" class="skill-modifier-input"><input name="skillPerformanceFinal" readonly="readonly" class="skill-final-value"></div><div class="skill-item"><input type="checkbox" id="skill-persuasion" name="skillPersuasionProf" data-skill-name="游说" data-attribute="charisma"><label for="skill-persuasion">游说 (魅力)</label><input type="number" name="skillPersuasionMod" value="0" class="skill-modifier-input"><input name="skillPersuasionFinal" readonly="readonly" class="skill-final-value"></div></div><button type="button" id="clear-skills-button" class="clear-section-button">清空技能修正与熟练</button></div><div class="form-group"><label for="char-tool-proficiencies-text">工具与其它熟练项 (逗号分隔)</label> <input id="char-tool-proficiencies-text" name="charToolProficienciesText" placeholder="例如：盗贼工具, 草药学工具套件, 轻甲, 简单武器"></div><div class="form-section dynamic-list-section"><h4>已装备物品</h4><div id="equipment-list-container"></div><button type="button" id="add-equipment-button" class="add-item-button">添加装备</button></div><div class="form-section dynamic-list-section"><h4>初始物品 (背包)</h4><div id="inventory-list-container"></div><button type="button" id="add-inventory-item-button" class="add-item-button">添加物品</button></div><div class="form-group"><label for="char-personality-traits">特点</label> <textarea id="char-personality-traits" name="charPersonalityTraits" rows="2"></textarea></div><div class="form-group"><label for="char-ideals">理想</label> <textarea id="char-ideals" name="charIdeals" rows="2"></textarea></div><div class="form-group"><label for="char-bonds">牵绊</label> <textarea id="char-bonds" name="charBonds" rows="2"></textarea></div><div class="form-group"><label for="char-flaws">缺点</label> <textarea id="char-flaws" name="charFlaws" rows="2"></textarea></div></div><div class="form-section"><div class="attribute-grid"><div class="attribute-row"><strong>力量 (STR)</strong><div class="attribute-inputs"><div><label for="attr-str-base">初始值</label><input type="number" id="attr-str-base" name="attrStrBase" value="10" min="1" max="20"></div><div><label for="attr-str-race">种族</label><input type="number" id="attr-str-race" name="attrStrRace" value="0"></div><div><label for="attr-str-mod">修正</label><input type="number" id="attr-str-mod" name="attrStrMod" value="0"></div><div><label for="attr-str-final">最终值</label><input id="attr-str-final" name="attrStrFinal" readonly="readonly"></div></div></div><div class="attribute-row"><strong>敏捷 (DEX)</strong><div class="attribute-inputs"><div><label for="attr-dex-base">初始值</label><input type="number" id="attr-dex-base" name="attrDexBase" value="10" min="1" max="20"></div><div><label for="attr-dex-race">种族</label><input type="number" id="attr-dex-race" name="attrDexRace" value="0"></div><div><label for="attr-dex-mod">修正</label><input type="number" id="attr-dex-mod" name="attrDexMod" value="0"></div><div><label for="attr-dex-final">最终值</label><input id="attr-dex-final" name="attrDexFinal" readonly="readonly"></div></div></div><div class="attribute-row"><strong>体质 (CON)</strong><div class="attribute-inputs"><div><label for="attr-con-base">初始值</label><input type="number" id="attr-con-base" name="attrConBase" value="10" min="1" max="20"></div><div><label for="attr-con-race">种族</label><input type="number" id="attr-con-race" name="attrConRace" value="0"></div><div><label for="attr-con-mod">修正</label><input type="number" id="attr-con-mod" name="attrConMod" value="0"></div><div><label for="attr-con-final">最终值</label><input id="attr-con-final" name="attrConFinal" readonly="readonly"></div></div></div><div class="attribute-row"><strong>智力 (INT)</strong><div class="attribute-inputs"><div><label for="attr-int-base">初始值</label><input type="number" id="attr-int-base" name="attrIntBase" value="10" min="1" max="20"></div><div><label for="attr-int-race">种族</label><input type="number" id="attr-int-race" name="attrIntRace" value="0"></div><div><label for="attr-int-mod">修正</label><input type="number" id="attr-int-mod" name="attrIntMod" value="0"></div><div><label for="attr-int-final">最终值</label><input id="attr-int-final" name="attrIntFinal" readonly="readonly"></div></div></div><div class="attribute-row"><strong>感知 (WIS)</strong><div class="attribute-inputs"><div><label for="attr-wis-base">初始值</label><input type="number" id="attr-wis-base" name="attrWisBase" value="10" min="1" max="20"></div><div><label for="attr-wis-race">种族</label><input type="number" id="attr-wis-race" name="attrWisRace" value="0"></div><div><label for="attr-wis-mod">修正</label><input type="number" id="attr-wis-mod" name="attrWisMod" value="0"></div><div><label for="attr-wis-final">最终值</label><input id="attr-wis-final" name="attrWisFinal" readonly="readonly"></div></div></div><div class="attribute-row"><strong>魅力 (CHA)</strong><div class="attribute-inputs"><div><label for="attr-cha-base">初始值</label><input type="number" id="attr-cha-base" name="attrChaBase" value="10" min="1" max="20"></div><div><label for="attr-cha-race">种族</label><input type="number" id="attr-cha-race" name="attrChaRace" value="0"></div><div><label for="attr-cha-mod">修正</label><input type="number" id="attr-cha-mod" name="attrChaMod" value="0"></div><div><label for="attr-cha-final">最终值</label><input id="attr-cha-final" name="attrChaFinal" readonly="readonly"></div></div></div></div></div><div class="form-section"><div class="form-row"><div class="form-group form-group-inline"><label for="char-class-1">职业1</label> <select id="char-class-1" name="charClass1"><option value="战士">战士</option><option value="法师">法师</option><option value="牧师">牧师</option><option value="游荡者">游荡者</option><option value="野蛮人">野蛮人</option><option value="吟游诗人">吟游诗人</option><option value="圣武士">圣武士</option><option value="游侠">游侠</option><option value="术士">术士</option><option value="魔契师">魔契师</option><option value="武僧">武僧</option><option value="德鲁伊">德鲁伊</option></select></div><div class="form-group form-group-inline"><label for="char-level-1">等级</label> <input type="number" id="char-level-1" name="charLevel1" value="1" min="1"></div><div class="form-group form-group-inline"><label for="char-subclass-1">子职</label> <input id="char-subclass-1" name="charSubclass1"></div></div></div><div class="form-section"><h3>法术</h3><div class="form-row"><div class="form-group form-group-inline"><label for="spell-ability">施法关键属性</label> <select id="spell-ability" name="spellAbility"><option value="STR">力量</option><option value="DEX">敏捷</option><option value="CON">体质</option><option value="INT">智力</option><option value="WIS">感知</option><option value="CHA">魅力</option></select></div><div class="form-group form-group-inline"><label for="spell-attack-bonus">法术攻击加值</label> <input id="spell-attack-bonus" name="spellAttackBonus" readonly="readonly"></div><div class="form-group form-group-inline"><label for="spell-save-dc">法术豁免DC</label> <input id="spell-save-dc" name="spellSaveDc" readonly="readonly"></div></div><h4>戏法</h4><div class="spell-list" id="cantrips-list"></div><datalist id="cantrips-datalist"></datalist><button type="button" id="add-cantrip-button">添加戏法</button><h4>一环法术 (法术位: <input type="number" id="spell-slots-1" name="spellSlots1" value="0" style="width:50px">)</h4><div class="spell-list" id="level1-spells-list"></div><datalist id="level1-spells-datalist"></datalist><button type="button" id="add-level1-spell-button">添加一环法术</button></div></form><div class="form-section"><label for="ai-character-prompt-textarea">AI角色生成指导提示 (可选):</label> <textarea id="ai-character-prompt-textarea" rows="3" placeholder="例如：我想要一个擅长潜行和欺骗的半精灵游荡者，背景是街头顽童。"></textarea></div><div class="button-group" style="margin-top:20px"><button id="save-character-button" style="background-color:#17a2b8">保存人物卡</button> <button id="ai-generate-character-button" style="background-color:#28a745">AI生成角色</button> <button id="back-to-module-setup-button">返回模组设置</button></div></div><script>function e(e,t,n){try{if("object"!=typeof window.toastr&&"undefined"!=typeof parent&&"object"==typeof parent.toastr&&(window.toastr=parent.toastr),"object"==typeof toastr&&null!==toastr&&"function"==typeof toastr[e])toastr[e](t,n);else{("error"===e?console.error:"warning"===e?console.warn:console.log)(`[ModuleSetup Toastr Fallback - ${e}] ${n?n+": ":""}${t}`)}}catch(e){console.error(`[ModuleSetup] safeToastr Error: ${e.message}`)}}async function t(t,n,a){if(!a)return void console.error("Output message div not found for saveContentToLorebook");if(!t)return e("warning","模组标题 (Key) 或角色名不能为空!","输入错误"),void(a.textContent="错误: 模组标题 (Key) 或角色名不能为空。");a.textContent=`正在尝试将内容保存到世界书条目 (Key: ${t})...`;const i="RPG_Modules_Test.json",r=`/createentry file="${i}" key="${t}" ${n}`;e("info",`执行命令... (Key: ${t}, 内容长度: ${n.length})`,"世界书操作"),console.log(`[ModuleSetup] Executing command for key "${t}" (content preview: ${n.substring(0,50)}...)`);try{if("function"!=typeof triggerSlash)return e("error","triggerSlash API 不可用!","API 错误"),void(a.textContent="错误: triggerSlash API 不可用。");const o=await triggerSlash(r);if(o&&""!==o.trim()){const r=`成功创建/更新世界书条目！\n文件名: ${i}\nKey: ${t}\nUID: ${o}\n内容预览: ${n.substring(0,100)}...`;e("success",`条目 '${t}' 已保存。UID: ${o}`,"操作成功"),a.innerHTML=r.replace(/\n/g,"<br>")}else{const n=`创建/更新世界书条目可能失败或没有返回UID。\n文件名: ${i}\nKey: ${t}.\n请检查SillyTavern日志或世界书。可能原因：内容过长或包含无法处理的特殊字符。`;e("warning",`条目 '${t}' 保存结果未知。`,"操作结果未知"),a.innerHTML=n.replace(/\n/g,"<br>")}}catch(n){const i=`保存条目 '${t}' 时发生错误: ${n.message}`;e("error",i,"操作失败"),a.textContent=i,console.error(`[ModuleSetup] Error saving entry '${t}':`,n)}}async function n(){const n=document.getElementById("module-title-input"),a=document.getElementById("module-content-textarea"),i=document.getElementById("output-message");if(!n||!a||!i)return void e("error","界面元素未完全找到 (save custom)!","界面错误");const r=n.value.trim(),o=a.value.trim();if(!o)return e("warning","模组内容不能为空!","输入错误"),i.textContent="错误: 模组内容不能为空。",void a.focus();await t(r,o,i)}async function a(){const n=document.getElementById("module-title-input"),a=document.getElementById("module-content-textarea"),i=document.getElementById("output-message");if(!n||!a||!i)return void e("error","界面元素未完全找到 (ai generate module)!","界面错误");const r=n.value.trim(),o=a.value.trim();if(!r)return e("warning","请输入模组标题 (作为Key)!","输入错误"),i.textContent="错误: 模组标题 (Key) 不能为空。",void n.focus();if(!o)return e("warning","请输入给AI的创作提示!","输入错误"),i.textContent="错误: AI创作提示不能为空。",void a.focus();i.textContent="正在向AI发送请求以生成模组内容 (JSON格式)...",e("info","向AI发送模组创作请求 (JSON)...","AI交互");const l=`请根据以下用户提示为D&D 5e游戏创作一个模组设定。用户提示： "${o}". 请严格按照 "module_setup_ai_prompts_v2_json.md" 中针对 "moduleCreation" 更新后的 "NarrativeModuleData" JSON Schema返回结果，核心内容应在 "narrativeContent" 字段中以人类易读的叙述形式提供。确保整个JSON响应被 "开始制作\\n##@@_MODULE_CONTENT_BEGIN_@@##" 和 "##@@_MODULE_CONTENT_END_@@##\\n结束制作" 包裹。`;try{if("function"!=typeof triggerSlash)return e("error","triggerSlash API 不可用!","API 错误"),void(i.textContent="错误: triggerSlash API 不可用。");const n=await triggerSlash(`/gen ${l}`);if(n?.trim()){e("success","AI已返回内容!","AI交互完成");let o=null;const l=n.match(/##@@_MODULE_CONTENT_BEGIN_@@##([\s\S]*?)##@@_MODULE_CONTENT_END_@@##/);if(l&&l[1]?o=l[1].trim():console.warn("New markers ##@@_MODULE_CONTENT_BEGIN_@@##...##@@_MODULE_CONTENT_END_@@## not found in AI response for module creation."),o)try{const e=JSON.parse(o);if("moduleCreation"!==e.requestType||!e.data||!e.data.narrativeContent)throw new Error("AI返回的JSON格式不符合预期的叙述性模组创作类型，或缺少 narrativeContent。");{const n=e.data,o=n.narrativeContent;a.value=o,i.textContent=`AI模组叙述内容已生成并填充到文本框。标题: ${n.title||r}。请审阅后手动保存，或直接保存。`,await t(n.title||r,o,i)}}catch(n){e("error",`AI返回的JSON解析失败: ${n.message}. 将尝试保存原始提取内容。`,"JSON解析错误"),i.textContent=`AI返回的JSON解析失败。原始提取内容 (可能需要手动清理):\n${o.substring(0,300)}...`,await t(r,o,i)}else e("warning","未能从AI回复中提取有效的JSON内容 (新标记)。","内容提取失败"),i.textContent="未能从AI回复中提取有效的JSON内容。原始回复：\n"+n.substring(0,300)+"..."}else e("warning","AI未能生成有效内容或返回为空。","AI交互失败"),i.textContent="AI未能生成有效内容或返回为空。"}catch(t){const n=`AI模组内容生成或保存过程中发生错误: ${t.message}`;e("error",n,"AI或保存失败"),i.textContent=n}}let i=[],r=[],o=[];const l=[{label:"攻击加值",type:"ATTACK_BONUS",valueSchema:[{name:"bonus",type:"number",label:"加值 (例如: 1)"}],notes:"攻击检定加值"},{label:"伤害加值 (固定)",type:"DAMAGE_BONUS_STATIC",valueSchema:[{name:"amount",type:"number",label:"伤害量 (例如: 1)"},{name:"damageType",type:"text",label:"伤害类型 (可选, 如 火焰)"}],notes:"固定伤害加值"},{label:"伤害加值 (伤害骰)",type:"DAMAGE_BONUS_DICE",valueSchema:[{name:"dice",type:"text",label:"伤害骰 (例如: 1d6)"},{name:"type",type:"text",label:"伤害类型 (例如: 火焰)"}],notes:"附加伤害骰"},{label:"AC 加值",type:"AC_BONUS",valueSchema:[{name:"bonus",type:"number",label:"AC 加值 (例如: 1)"}],notes:"防御等级加值"},{label:"命中时触发豁免效果",type:"ON_HIT_EFFECT_SAVE",valueSchema:[{name:"saveAttribute",type:"select",label:"豁免属性",options:["力量","敏捷","体质","智力","感知","魅力"]},{name:"dc",type:"number",label:"豁免DC"},{name:"effectOnFail",type:"textarea",label:"豁免失败效果描述"},{name:"effectOnSuccess",type:"textarea",label:"豁免成功效果描述 (可选)"}],notes:"命中时触发豁免"}],c=[{name_zh:"皮甲",name_en:"Leather",category:"轻甲",ac_base:11,ac_dex_bonus:!0,ac_dex_max:null,strength_requirement:null,stealth_disadvantage:!1,weight:"10磅",cost:"10 GP"},{name_zh:"镶钉皮甲",name_en:"Studded Leather",category:"轻甲",ac_base:12,ac_dex_bonus:!0,ac_dex_max:null,strength_requirement:null,stealth_disadvantage:!1,weight:"13磅",cost:"45 GP"},{name_zh:"链甲衫",name_en:"Chain Shirt",category:"中甲",ac_base:13,ac_dex_bonus:!0,ac_dex_max:2,strength_requirement:null,stealth_disadvantage:!1,weight:"20磅",cost:"50 GP"},{name_zh:"板条甲",name_en:"Splint",category:"重甲",ac_base:17,ac_dex_bonus:!1,ac_dex_max:0,strength_requirement:15,stealth_disadvantage:!0,weight:"60磅",cost:"200 GP"},{name_zh:"盾牌",name_en:"Shield",category:"盾牌",ac_base:2,ac_dex_bonus:!1,ac_dex_max:0,strength_requirement:null,stealth_disadvantage:!1,weight:"6磅",cost:"10 GP"}],s=[{id:"dagger_venom_001",name:"毒蛇之牙匕首",type:"武器",baseItemName:"匕首",description:"一把淬毒的匕首，刀刃上闪烁着不祥的绿光。",magicEffects:[{type:"ON_HIT_EFFECT_SAVE",value:{saveAttribute:"体质",dc:13,effectOnFail:"目标中毒1分钟",notes:"毒素攻击"}},{type:"DAMAGE_BONUS_DICE",value:{dice:"1d6",type:"毒素"},condition:"仅在目标豁免失败时生效"}]},{id:"grubnaks_choppa",name:"格鲁克的劈砍小刀",type:"武器",baseItemName:"短剑",description:"一把粗制但锋利的短剑，剑柄上缠着肮脏的布条。",magicEffects:[{type:"ATTACK_BONUS",value:1},{type:"DAMAGE_BONUS_DICE",value:{dice:"1d4",type:"流血"},notes:"恶毒伤口"}]},{id:"longsword_flame_tongue_001",name:"焰舌长剑+1",type:"武器",baseItemName:"长剑",description:"剑刃燃烧着熊熊烈焰的魔法长剑。",magicEffects:[{type:"ATTACK_BONUS",value:1},{type:"DAMAGE_BONUS_STATIC",value:{amount:1}},{type:"DAMAGE_BONUS_DICE",value:{dice:"2d6",type:"火焰"},notes:"焰舌"}]},{name:"长剑+1",type:"武器",baseItemName:"长剑",description:"一把制作精良的长剑，附有微弱的魔法力量。",magicEffects:[{type:"ATTACK_BONUS",value:1},{type:"DAMAGE_BONUS_STATIC",value:{amount:1}}]}],d='\n[\n  {\n    "name_zh": "匕首",\n    "name_en": "Dagger",\n    "category": "简易近战",\n    "damage": "1d4",\n    "damageType": "穿刺",\n    "properties": ["灵巧", "轻型", "投掷 (射程 20/60)"],\n    "weight": "1磅",\n    "cost": "2 GP",\n    "mastery": "迅击"\n  },\n  {\n    "name_zh": "短棒",\n    "name_en": "Club",\n    "category": "简易近战",\n    "damage": "1d4",\n    "damageType": "钝击",\n    "properties": ["轻型"],\n    "weight": "2磅",\n    "cost": "1 SP",\n    "mastery": "缓速"\n  },\n  {\n    "name_zh": "手斧",\n    "name_en": "Handaxe",\n    "category": "简易近战",\n    "damage": "1d6",\n    "damageType": "挥砍",\n    "properties": ["轻型", "投掷 (射程 20/60)"],\n    "weight": "2磅",\n    "cost": "5 GP",\n    "mastery": "侵扰"\n  },\n  {\n    "name_zh": "标枪",\n    "name_en": "Javelin",\n    "category": "简易近战",\n    "damage": "1d6",\n    "damageType": "穿刺",\n    "properties": ["投掷 (射程 30/120)"],\n    "weight": "2磅",\n    "cost": "5 SP",\n    "mastery": "缓速"\n  },\n  {\n    "name_zh": "轻锤",\n    "name_en": "Light Hammer",\n    "category": "简易近战",\n    "damage": "1d4",\n    "damageType": "钝击",\n    "properties": ["轻型", "投掷 (射程 20/60)"],\n    "weight": "2磅",\n    "cost": "2 GP",\n    "mastery": "迅击"\n  },\n  {\n    "name_zh": "硬头锤",\n    "name_en": "Mace",\n    "category": "简易近战",\n    "damage": "1d6",\n    "damageType": "钝击",\n    "properties": [],\n    "weight": "4磅",\n    "cost": "5 GP",\n    "mastery": "削弱"\n  },\n  {\n    "name_zh": "长棍",\n    "name_en": "Quarterstaff",\n    "category": "简易近战",\n    "damage": "1d6",\n    "damageType": "钝击",\n    "properties": ["多用 (1d8)"],\n    "weight": "4磅",\n    "cost": "2 SP",\n    "mastery": "失衡"\n  },\n  {\n    "name_zh": "矛",\n    "name_en": "Spear",\n    "category": "简易近战",\n    "damage": "1d6",\n    "damageType": "穿刺",\n    "properties": ["投掷 (射程 20/60)", "多用 (1d8)"],\n    "weight": "3磅",\n    "cost": "1 GP",\n    "mastery": "削弱"\n  },\n  {\n    "name_zh": "轻弩",\n    "name_en": "Light Crossbow",\n    "category": "简易远程",\n    "damage": "1d8",\n    "damageType": "穿刺",\n    "properties": ["弹药 (射程 80/320；弩矢)", "装填", "双手"],\n    "weight": "5磅",\n    "cost": "25 GP",\n    "mastery": "缓速"\n  },\n  {\n    "name_zh": "短弓",\n    "name_en": "Shortbow",\n    "category": "简易远程",\n    "damage": "1d6",\n    "damageType": "穿刺",\n    "properties": ["弹药 (射程 80/320；箭矢)", "双手"],\n    "weight": "2磅",\n    "cost": "25 GP",\n    "mastery": "侵扰"\n  },\n  {\n    "name_zh": "投石索",\n    "name_en": "Sling",\n    "category": "简易远程",\n    "damage": "1d4",\n    "damageType": "钝击",\n    "properties": ["弹药 (射程 30/120；弹丸)"],\n    "weight": "—",\n    "cost": "1 SP",\n    "mastery": "缓速"\n  },\n  {\n    "name_zh": "战斧",\n    "name_en": "Battleaxe",\n    "category": "军用近战",\n    "damage": "1d8",\n    "damageType": "挥砍",\n    "properties": ["多用 (1d10)"],\n    "weight": "4磅",\n    "cost": "10 GP",\n    "mastery": "失衡"\n  },\n  {\n    "name_zh": "长剑",\n    "name_en": "Longsword",\n    "category": "军用近战",\n    "damage": "1d8",\n    "damageType": "挥砍",\n    "properties": ["多用 (1d10)"],\n    "weight": "3磅",\n    "cost": "15 GP",\n    "mastery": "削弱"\n  },\n  {\n    "name_zh": "巨剑",\n    "name_en": "Greatsword",\n    "category": "军用近战",\n    "damage": "2d6",\n    "damageType": "挥砍",\n    "properties": ["重型", "双手"],\n    "weight": "6磅",\n    "cost": "50 GP",\n    "mastery": "擦掠"\n  },\n  {\n    "name_zh": "刺剑",\n    "name_en": "Rapier",\n    "category": "军用近战",\n    "damage": "1d8",\n    "damageType": "穿刺",\n    "properties": ["灵巧"],\n    "weight": "2磅",\n    "cost": "25 GP",\n    "mastery": "侵扰"\n  },\n  {\n    "name_zh": "短剑",\n    "name_en": "Shortsword",\n    "category": "军用近战",\n    "damage": "1d6",\n    "damageType": "穿刺",\n    "properties": ["灵巧", "轻型"],\n    "weight": "2磅",\n    "cost": "10 GP",\n    "mastery": "侵扰"\n  },\n  {\n    "name_zh": "长弓",\n    "name_en": "Longbow",\n    "category": "军用远程",\n    "damage": "1d8",\n    "damageType": "穿刺",\n    "properties": ["弹药 (射程 150/600；箭矢)", "重型", "双手"],\n    "weight": "2磅",\n    "cost": "50 GP",\n    "mastery": "缓速"\n  }\n]\n';function m(){if(0===r.length&&(r=function(){try{const e=d.match(/(\[[\s\S]*\])/);if(e&&e[1])return JSON.parse(e[1])}catch(e){console.error("Error parsing weapon templates:",e)}return[]}()),0===o.length&&(o=[...c]),0===i.length){const e=r.map((e=>({name:e.name_zh,type:"武器",baseItemName:e.name_zh,description:`${e.category} - ${e.damage} ${e.damageType}. 属性: ${e.properties.join(", ")||"无"}.`,damage:e.damage,damageType:e.damageType,properties:e.properties})));i=[...e,...s]}}function u(e){m();const t=document.createElement("div");t.className="dynamic-item-row full-equipment-row";const n=document.createElement("div");n.className="equipment-controls-container";const a=document.createElement("select");a.className="equipment-type-select",["请选择类型","武器","防具","饰品"].forEach((e=>{const t=document.createElement("option");t.value="请选择类型"===e?"":e,t.textContent=e,a.appendChild(t)})),n.appendChild(a);const i=document.createElement("input");i.type="text",i.placeholder="自定义物品名称 (可选)",i.className="equipment-custom-name",n.appendChild(i);const l=document.createElement("button");l.type="button",l.className="remove-item-button",l.textContent="移除此物品",l.onclick=()=>t.remove(),n.appendChild(l),t.appendChild(n);const c=document.createElement("div");c.className="template-select-container",c.style.display="none",t.appendChild(c);const s=document.createElement("div");s.className="base-properties-display",t.appendChild(s);const d=document.createElement("div");d.className="magic-effects-container",t.appendChild(d);const u=document.createElement("button");u.type="button",u.textContent="添加魔法效果",u.className="add-magic-effect-button",u.style.display="none",t.appendChild(u);const h=document.createElement("textarea");h.placeholder="自定义物品描述...",h.className="equipment-custom-description",h.rows=2,t.appendChild(h);const g=(e,t)=>{if(c.innerHTML="",s.innerHTML="",c.style.display="none",!e)return void(u.style.display="none");u.style.display="block";const n=document.createElement("label"),a=document.createElement("select");a.className="equipment-template-select";const l=document.createElement("option");if(l.value="",c.appendChild(n),c.appendChild(a),"武器"===e?(n.textContent="选择武器模板:",l.textContent="选择基础武器",a.appendChild(l),r.forEach((e=>{const t=document.createElement("option");t.value=e.name_en,t.textContent=e.name_zh,a.appendChild(t)})),c.style.display="flex"):"防具"===e?(n.textContent="选择防具模板:",l.textContent="选择基础防具/盾牌",a.appendChild(l),o.forEach((e=>{const t=document.createElement("option");t.value=e.name_en,t.textContent=`${e.name_zh} (${e.category})`,a.appendChild(t)})),c.style.display="flex"):"饰品"===e&&(s.innerHTML="饰品通常没有基础模板属性，请直接添加魔法效果和描述。"),a.onchange=()=>{s.innerHTML="";const t=a.value;if(!t)return;let n="";if("武器"===e){const e=r.find((e=>e.name_en===t));e&&(n=`<strong>基础: ${e.name_zh} (${e.category})</strong><br>\n                                   伤害: ${e.damage} ${e.damageType}<br>\n                                   属性: ${e.properties.join(", ")||"无"}<br>\n                                   重量: ${e.weight}, 价格: ${e.cost}\n                                   ${e.mastery?`<br>精通: ${e.mastery}`:""}`)}else if("防具"===e){const e=o.find((e=>e.name_en===t));e&&(n=`<strong>基础: ${e.name_zh} (${e.category})</strong><br>\n                                   AC: ${e.ac_base} ${e.ac_dex_bonus?"+ 敏捷"+(e.ac_dex_max?` (最高 ${e.ac_dex_max})`:""):""}<br>\n                                   ${e.strength_requirement?`力量需求: ${e.strength_requirement}<br>`:""}\n                                   ${e.stealth_disadvantage?"隐匿劣势<br>":""}\n                                   重量: ${e.weight}, 价格: ${e.cost}`)}s.innerHTML=n;if(!i.value.trim()){let n;if("武器"===e){const e=r.find((e=>e.name_en===t));e&&(n=e.name_zh)}else if("防具"===e){const e=o.find((e=>e.name_en===t));e&&(n=e.name_zh)}n&&(i.value=n)}},t){if(Array.from(a.options).some((e=>e.value===t)))a.value=t;else{let n;"武器"===e?n=r.find((e=>e.name_zh===t)):"防具"===e&&(n=o.find((e=>e.name_zh===t))),n&&n.name_en&&(a.value=n.name_en)}}a.dispatchEvent(new Event("change"))};return a.onchange=()=>{g(a.value)},u.onclick=()=>{d.appendChild(p())},e&&(a.value=e.type||"",g(a.value,e.baseItemName),i.value=e.name||"",h.value=e.description||"",d.innerHTML="",e.magicEffects?.forEach((e=>{d.appendChild(p(e))}))),t}function p(e){const t=document.createElement("div");t.className="magic-effect-edit-row";const n=document.createElement("select");n.className="magic-effect-type-select";const a=document.createElement("option");a.value="",a.textContent="选择效果类型",n.appendChild(a),l.forEach((e=>{const t=document.createElement("option");t.value=e.type,t.textContent=e.label,n.appendChild(t)})),t.appendChild(n);const i=document.createElement("div");if(i.className="magic-effect-value-inputs",t.appendChild(i),n.onchange=()=>{i.innerHTML="";const e=n.value,t=l.find((t=>t.type===e));t&&t.valueSchema.forEach((e=>{const t=document.createElement("label");let n;t.textContent=e.label+":","textarea"===e.type?(n=document.createElement("textarea"),n.rows=2,n.classList.add("full-width-field")):"select"===e.type&&e.options?(n=document.createElement("select"),e.options.forEach((e=>{const t=document.createElement("option");t.value=e,t.textContent=e,n.appendChild(t)}))):(n=document.createElement("input"),n.type=e.type),n instanceof HTMLSelectElement||(n.placeholder=e.label),n.name=e.name,i.appendChild(t),i.appendChild(n)}))},e)if(n.value=e.type,n.dispatchEvent(new Event("change")),"object"==typeof e.value&&null!==e.value)Object.entries(e.value).forEach((([e,t])=>{const n=i.querySelector(`[name="${e}"]`);n&&(n.value=String(t))}));else{const t=i.querySelector('[name="bonus"], [name="value"]');t&&(t.value=String(e.value))}const r=document.createElement("button");return r.type="button",r.textContent="移除效果",r.className="remove-magic-effect-button",r.onclick=()=>t.remove(),t.appendChild(r),t}function h(e="",t="",n=1){const a=document.createElement("div");a.className="dynamic-item-row";const i=document.createElement("input");i.type="text",i.name="inventoryItemName[]",i.placeholder="物品名称",i.value=e;const r=document.createElement("input");r.type="text",r.name="inventoryItemDetails[]",r.placeholder="描述 (可选)",r.value=t;const o=document.createElement("input");o.type="number",o.name="inventoryItemQuantity[]",o.placeholder="数量",o.value=n.toString(),o.min="1";const l=document.createElement("button");return l.type="button",l.className="remove-item-button",l.textContent="移除",l.onclick=()=>a.remove(),a.appendChild(i),a.appendChild(r),a.appendChild(o),a.appendChild(l),a}function g(e,t,n){return(e||0)+(t||0)+(n||0)}function y(e){return Math.floor(((e||10)-10)/2)}function _(e){return e>=17?6:e>=13?5:e>=9?4:e>=5?3:e>=1?2:0}function f(e){const t=[],n=/<H4 id="[^"]*">([^｜]+)｜[^<]*<\/H4>/g;let a;for(;null!==(a=n.exec(e));)a[1]&&t.push(a[1].trim());return t}function v(e,t,n,a=""){const i=document.createElement("div");i.className="spell-item";const r=document.createElement("input");r.type="text",r.name=`${e}[]`,r.placeholder=""+("cantrip"===e?"戏法":"一环法术"),r.setAttribute("list",n),r.value=a;const o=document.createElement("button");return o.type="button",o.className="remove-spell-button",o.textContent="-",o.addEventListener("click",(()=>{i.remove()})),i.appendChild(r),i.appendChild(o),i}function H(){const e=document.getElementById("spell-ability"),t=document.getElementById("spell-attack-bonus"),n=document.getElementById("spell-save-dc"),a=document.getElementById("char-level-1");if(!(e&&t&&n&&a))return;const i=e.value.toLowerCase(),r=document.getElementById(`attr-${i}-final`);if(!r)return void console.warn(`Final ability score element for '${i}' not found.`);const o=y(parseInt(r.value,10)||10),l=_(parseInt(a.value,10)||1),c=o+l,s=8+o+l;t.value=(c>=0?"+":"")+c.toString(),n.value=s.toString()}function E(e){const t=e.querySelector('input[type="checkbox"]'),n=e.querySelector(".skill-modifier-input"),a=e.querySelector(".skill-final-value"),i=t.dataset.attribute,r=document.getElementById("char-level-1");if(!(t&&n&&a&&i&&r))return;const o=i.substring(0,3).toLowerCase(),l=document.getElementById(`attr-${o}-final`);if(!l)return;const c=y(parseInt(l.value,10)||10),s=t.checked,d=parseInt(n.value,10)||0,m=parseInt(r.value,10)||1,u=function(e,t,n,a,i){let r=t+a;return n&&(r+=_(i)),r}(t.dataset.skillName,c,s,d,m);a.value=(u>=0?"+":"")+u.toString()}function S(){document.querySelectorAll(".skill-item").forEach((e=>{E(e)}))}async function b(){const t=document.getElementById("output-message"),n=document.getElementById("char-name"),a=document.getElementById("ai-character-prompt-textarea");let i=a?.value.trim();if(i||(i=document.getElementById("module-content-textarea")?.value.trim()),i||(i="请为D&D 5e生成一个详细的1级角色。"),!t)return void e("error","输出消息区域未找到 (ai generate character)!","界面错误");t.textContent="正在向AI发送请求以生成角色数据 (JSON格式)...",e("info","向AI发送角色生成请求 (JSON)...","AI交互");const r=`请根据以下用户提示为D&D 5e游戏生成一个角色。用户提示： "${i}". 请严格按照 "module_setup_ai_prompts_v2_json.md" 中定义的 "characterGeneration" JSON Schema返回结果，确保整个JSON响应被 "开始制作\\n##@@_MODULE_CONTENT_BEGIN_@@##" 和 "##@@_MODULE_CONTENT_END_@@##\\n结束制作" 包裹。`;try{if("function"!=typeof triggerSlash)return e("error","triggerSlash API 不可用!","API 错误"),void(t.textContent="错误: triggerSlash API 不可用。");const a=await triggerSlash(`/gen ${r}`);if(a?.trim()){e("success","AI已返回角色数据!","AI交互完成");let i=null;const r=a.match(/##@@_MODULE_CONTENT_BEGIN_@@##([\s\S]*?)##@@_MODULE_CONTENT_END_@@##/);if(r&&r[1]?i=r[1].trim():console.warn("New markers ##@@_MODULE_CONTENT_BEGIN_@@##...##@@_MODULE_CONTENT_END_@@## not found in AI response for character generation."),i)try{const a=JSON.parse(i);if("characterGeneration"!==a.requestType||!a.data)throw new Error("AI返回的JSON格式不符合预期的角色生成类型。");!function(t){if(e("info",`正在使用AI生成的数据填充角色表单: ${t.name}`,"角色填充"),!document.getElementById("character-sheet-form"))return;const n=(e,t,n=!1)=>{const a=document.getElementById(e);if(a&&void 0!==t){if(n&&"options"in a){let e=!1;for(let n=0;n<a.options.length;n++)if(a.options[n].value===t.toString()||a.options[n].text===t.toString()){a.selectedIndex=n,e=!0;break}if(!e){const e=new Option(t.toString(),t.toString(),!0,!0);a.add(e)}}else a.value=t.toString();e.startsWith("attr-")&&(e.includes("-base")||e.includes("-race")||e.includes("-mod"))&&a.dispatchEvent(new Event("input"))}};n("char-name",t.name),n("char-race",t.race,!0),n("char-class-1",t.class,!0),n("char-level-1",t.level),n("char-alignment",t.alignment),n("char-background",t.background),n("char-personality-traits",t.personalityTraits),n("char-ideals",t.ideals),n("char-bonds",t.bonds),n("char-flaws",t.flaws),n("char-appearance",t.appearance),n("char-story",t.story),n("char-age",t.age),n("char-gender",t.gender,!0),n("char-faith",t.faith),n("char-height",t.height),n("char-weight",t.weight),n("char-xp",t.exp),n("char-subclass-1",t.subclass),n("spell-ability",t.spellcastingAbility,!0),n("char-tool-proficiencies-text",t.toolProficienciesText),t.currency&&(n("char-currency-gold",t.currency.gold),n("char-currency-silver",t.currency.silver),n("char-currency-copper",t.currency.copper));const a={str:"strength",dex:"dexterity",con:"constitution",int:"intelligence",wis:"wisdom",cha:"charisma"};["str","dex","con","int","wis","cha"].forEach((e=>{const i=a[e],r=t.attributes[i];r&&(n(`attr-${e}-base`,r.base),n(`attr-${e}-race`,r.race_bonus),n(`attr-${e}-mod`,r.modifier_bonus))})),t.skills?.forEach((e=>{const t=document.querySelector(`input[data-skill-name="${e.name}"]`),n=t?.closest(".skill-item")?.querySelector(".skill-modifier-input");t&&(t.checked=e.proficient),n&&(n.value=e.modifierValue.toString())})),S();const i=document.getElementById("equipment-list-container");i&&(i.innerHTML="",t.equipment?.forEach((e=>{const t=u(e);i.appendChild(t)})));const r=document.getElementById("inventory-list-container");r&&(r.innerHTML="",t.inventory?.forEach((e=>{r.appendChild(h(e.name,e.description,e.quantity))})));const o=document.getElementById("cantrips-list");o&&(o.innerHTML="",t.equippedSpells?.filter((e=>0===e.level)).forEach((e=>{o.appendChild(v("cantrip",0,"cantrips-datalist",e.name))})),0===o.children.length&&0===t.equippedSpells?.filter((e=>0===e.level)).length&&o.appendChild(v("cantrip",0,"cantrips-datalist")));const l=document.getElementById("level1-spells-list");l&&(l.innerHTML="",t.equippedSpells?.filter((e=>1===e.level)).forEach((e=>{l.appendChild(v("level1spell",0,"level1-spells-datalist",e.name))})),0===l.children.length&&0===t.equippedSpells?.filter((e=>1===e.level)).length&&l.appendChild(v("level1spell",0,"level1-spells-datalist"))),t.spellSlots?.[1]&&n("spell-slots-1",t.spellSlots[1].max),H(),e("success",`角色 "${t.name}" 的数据已填充到表单。`,"AI角色生成")}(a.data),t.textContent="AI角色数据已填充到表单。请检查并保存。",n&&a.data.name&&(n.value=a.data.name)}catch(n){e("error",`AI返回的角色JSON解析失败: ${n.message}.`,"JSON解析错误"),t.textContent=`AI返回的角色JSON解析失败。原始提取内容:\n${i.substring(0,300)}...`}else e("warning","未能从AI回复中提取有效的JSON角色数据。","内容提取失败"),t.textContent="未能从AI回复中提取有效的JSON角色数据。"}else e("warning","AI未能生成角色数据或返回为空。","AI交互失败"),t.textContent="AI未能生成角色数据或返回为空。"}catch(n){const a=`AI角色生成过程中发生错误: ${n.message}`;e("error",a,"AI错误"),t.textContent=a}}async function I(){const n=document.getElementById("output-message"),a=document.getElementById("character-sheet-form");if(!a||!n)return void e("error","人物创建表单或输出区域未找到!","界面错误");const i=new FormData(a),c=e=>i.get(e)?.toString().trim()||"",s=(e,t=0)=>{const n=i.get(e)?.toString().trim();if(""===n||null==n)return t;const a=parseInt(n,10);return isNaN(a)?t:a},d=c("charName");if(!d)return e("warning","角色名不能为空!","保存错误"),n.textContent="错误: 角色名不能为空。",void document.getElementById("char-name")?.focus();const m={name:d,race:c("charRace"),class:c("charClass1"),level:s("charLevel1",1),alignment:c("charAlignment"),background:c("charBackground"),personalityTraits:c("charPersonalityTraits"),ideals:c("charIdeals"),bonds:c("charBonds"),flaws:c("charFlaws"),appearance:c("charAppearance"),story:c("charStory"),age:c("charAge"),gender:c("charGender"),faith:c("charFaith"),height:c("charHeight"),weight:c("charWeight"),exp:s("charXp"),subclass:c("charSubclass1"),spellcastingAbility:c("spellAbility"),toolProficienciesText:c("charToolProficienciesText"),attributes:{strength:{base:0,race_bonus:0,modifier_bonus:0,final:0,mod:0},dexterity:{base:0,race_bonus:0,modifier_bonus:0,final:0,mod:0},constitution:{base:0,race_bonus:0,modifier_bonus:0,final:0,mod:0},intelligence:{base:0,race_bonus:0,modifier_bonus:0,final:0,mod:0},wisdom:{base:0,race_bonus:0,modifier_bonus:0,final:0,mod:0},charisma:{base:0,race_bonus:0,modifier_bonus:0,final:0,mod:0}},hp:{current:0,max:0},ac:10,currency:{gold:s("charCurrencyGold",0),silver:s("charCurrencySilver",0),copper:s("charCurrencyCopper",0)},proficiencies:[],skills:[],equippedSpells:[],spellSlots:{},equipment:[],inventory:[]},u={str:"strength",dex:"dexterity",con:"constitution",int:"intelligence",wis:"wisdom",cha:"charisma"};if(["str","dex","con","int","wis","cha"].forEach((e=>{const t=document.getElementById(`attr-${e}-base`),n=document.getElementById(`attr-${e}-race`),a=document.getElementById(`attr-${e}-mod`),i=t?parseInt(t.value,10):NaN,r=n?parseInt(n.value,10):NaN,o=a?parseInt(a.value,10):NaN,l=isNaN(i)?8:i,c=isNaN(r)?0:r,s=isNaN(o)?0:o,d=g(l,c,s),p=y(d);m.attributes[u[e]]={base:l,race_bonus:c,modifier_bonus:s,final:d,mod:p}})),m.attributes.constitution&&m.level){const e=m.attributes.constitution.mod,t="战士"===m.class?10:"法师"===m.class?6:8;let n=t+e;m.level>1&&(n+=(m.level-1)*(Math.floor(t/2)+1+e)),m.hp={current:n>0?n:1,max:n>0?n:1}}m.attributes.dexterity&&(m.ac=10+m.attributes.dexterity.mod),m.skills=[],document.querySelectorAll(".skill-item").forEach((e=>{const t=e.querySelector('input[type="checkbox"]'),n=e.querySelector(".skill-modifier-input"),a=e.querySelector(".skill-final-value");t&&n&&a&&m.skills.push({name:t.dataset.skillName||"未知技能",proficient:t.checked,attribute:t.dataset.attribute||"unknown",modifierValue:parseInt(n.value,10)||0,finalValue:parseInt(a.value,10)||0})}));const p=c("charToolProficienciesText").split(",").map((e=>e.trim())).filter((e=>e));m.proficiencies=[...new Set([...m.proficiencies||[],...p])],m.equippedSpells=[],document.querySelectorAll('#cantrips-list div.spell-item input[type="text"]').forEach((e=>{const t=e.value.trim();t&&m.equippedSpells.push({name:t,level:0,source:"习得"})})),document.querySelectorAll('#level1-spells-list div.spell-item input[type="text"]').forEach((e=>{const t=e.value.trim();t&&m.equippedSpells.push({name:t,level:1,source:"习得"})})),m.spellSlots||(m.spellSlots={});const h=s("spellSlots1",0);h>=0&&m.spellSlots&&(m.spellSlots[1]={current:h,max:h}),m.equipment=[],document.querySelectorAll("#equipment-list-container .full-equipment-row").forEach((e=>{const t=e.querySelector(".equipment-type-select"),n=e.querySelector(".equipment-custom-name"),a=e.querySelector(".equipment-template-select"),i=e.querySelector(".equipment-custom-description"),c=t.value;if(!c)return;const s=n.value.trim(),d=a?.value||void 0;let u=s;if(!u&&d)if("武器"===c){const e=r.find((e=>e.name_en===d));e&&(u=e.name_zh)}else if("防具"===c){const e=o.find((e=>e.name_en===d));e&&(u=e.name_zh)}if(u||"饰品"!==c||(u="未命名饰品"),!u)return;const p=[];e.querySelectorAll(".magic-effect-edit-row").forEach((e=>{const t=e.querySelector(".magic-effect-type-select").value;if(!t)return;const n=l.find((e=>e.type===t));if(!n)return;const a={};let i=!0;n.valueSchema.forEach((t=>{const n=e.querySelector(`[name="${t.name}"]`);if(n){if("number"===t.type){const e=parseFloat(n.value);a[t.name]=isNaN(e)?void 0:e}else a[t.name]=n.value;void 0===a[t.name]&&"textarea"!==t.type&&t.type}else i=!1}));let r=a;1===n.valueSchema.length&&"bonus"===n.valueSchema[0].name&&a.hasOwnProperty("bonus")?r=a.bonus:1===n.valueSchema.length&&"amount"===n.valueSchema[0].name&&a.hasOwnProperty("amount")&&!a.damageType&&(r={amount:a.amount}),i&&p.push({type:t,value:r,notes:n.notes||t})}));const h={name:u,type:c,baseItemName:d,description:i.value.trim(),equipped:!0,magicEffects:p.length>0?p:void 0};m.equipment.push(h)})),m.inventory=[],document.querySelectorAll("#inventory-list-container .dynamic-item-row").forEach((e=>{const t=e.querySelector('input[name="inventoryItemName[]"]'),n=e.querySelector('input[name="inventoryItemDetails[]"]'),a=e.querySelector('input[name="inventoryItemQuantity[]"]');t&&t.value.trim()&&m.inventory.push({name:t.value.trim(),quantity:a&&parseInt(a.value,10)||1,description:n?n.value.trim():""})}));const _=JSON.stringify(m,null,2),f=document.getElementById("module-setup-container"),v=document.getElementById("character-creation-container");f&&v&&(v.style.display="none",f.style.display="block"),await t(d,_,n)}function C(){const t=document.getElementById("module-setup-container"),i=document.getElementById("character-creation-container"),r=document.getElementById("save-custom-module-button"),o=document.getElementById("ai-generate-content-button"),l=document.getElementById("go-to-character-creation-button"),c=document.getElementById("back-to-module-setup-button"),s=document.getElementById("save-character-button"),d=document.getElementById("ai-generate-character-button");t&&i?(r&&r.addEventListener("click",n),o&&o.addEventListener("click",a),l&&l.addEventListener("click",(()=>{t.style.display="none",i.style.display="block",e("info","已切换到人物创建界面。","界面切换")})),c&&c.addEventListener("click",(()=>{i.style.display="none",t.style.display="block",e("info","已返回模组设置界面。","界面切换")})),s&&s.addEventListener("click",I),d&&d.addEventListener("click",b),["str","dex","con","int","wis","cha"].forEach((e=>{const t=document.getElementById(`attr-${e}-base`),n=document.getElementById(`attr-${e}-race`),a=document.getElementById(`attr-${e}-mod`),i=document.getElementById(`attr-${e}-final`),r=()=>{if(t&&n&&a&&i){const e=g(parseInt(t.value,10)||0,parseInt(n.value,10)||0,parseInt(a.value,10)||0);i.value=e.toString(),"function"==typeof updateSpellAttackAndDc&&updateSpellAttackAndDc(),"function"==typeof updateAllSkillFinalValues&&updateAllSkillFinalValues()}};[t,n,a].forEach((e=>{e&&e.addEventListener("input",r)})),"complete"===document.readyState||"interactive"===document.readyState?r():window.addEventListener("DOMContentLoaded",r)})),function(){document.querySelectorAll(".skill-item").forEach((e=>{const t=e.querySelector('input[type="checkbox"]'),n=e.querySelector(".skill-modifier-input");t&&t.addEventListener("change",(()=>E(e))),n&&n.addEventListener("input",(()=>E(e))),E(e)}));const e=document.getElementById("clear-skills-button");e&&e.addEventListener("click",(()=>{document.querySelectorAll(".skill-item").forEach((e=>{const t=e.querySelector('input[type="checkbox"]'),n=e.querySelector(".skill-modifier-input");t&&(t.checked=!1),n&&(n.value="0"),E(e)}))}))}(),function(){const e=document.getElementById("spell-ability"),t=document.getElementById("char-level-1");e&&e.addEventListener("change",H),t&&t.addEventListener("input",(()=>{H(),"function"==typeof updateAllSkillFinalValues&&updateAllSkillFinalValues()})),"complete"===document.readyState||"interactive"===document.readyState?H():window.addEventListener("DOMContentLoaded",H)}(),function(){const e=document.getElementById("cantrips-datalist"),t=document.getElementById("level1-spells-datalist");e&&f('\n<H4 id="Acid_Splash">酸液飞溅｜Acid Splash</H4>\n<H4 id="Blade_Ward">剑刃防护｜Blade Ward</H4>\n<H4 id="Chill_Touch">颤栗之触｜Chill Touch</H4>\n<H4 id="Dancing_Lights">舞光术｜Dancing Lights</H4>\n<H4 id="Druidcraft">德鲁伊伎俩｜Druidcraft</H4>\n<H4 id="Eldritch_Blast">魔能爆｜Eldritch Blast</H4>\n<H4 id="Elementalism">四象法门｜Elementalism</H4>\n<H4 id="Fire_Bolt">火焰箭｜Fire Bolt</H4>\n<H4 id="Friends">交友术｜Friends</H4>\n<H4 id="Guidance">神导术｜Guidance</H4>\n<H4 id="Light">光亮术｜Light</H4>\n<H4 id="Mage_Hand">法师之手｜Mage Hand</H4>\n<H4 id="Mending">修复术｜Mending</H4>\n<H4 id="Message">传讯术｜Message</H4>\n<H4 id="Mind_Sliver">心灵之楔｜Mind Sliver</H4>\n<H4 id="Minor_Illusion">次级幻象｜Minor Illusion</H4>\n<H4 id="Poison_Spray">毒气喷涌｜Poison Spray</H4>\n<H4 id="Prestidigitation">魔法伎俩｜Prestidigitation</H4>\n<H4 id="Produce_Flame">燃火术｜Produce Flame</H4>\n<H4 id="Ray_of_Frost">冷冻射线｜Ray of Frost</H4>\n<H4 id="Resistance">抵抗术｜Resistance</H4>\n<H4 id="Sacred_Flame">圣火术｜Sacred Flame</H4>\n<H4 id="Shillelagh">橡棍术｜Shillelagh</H4>\n<H4 id="Shocking_Grasp">电爪｜Shocking Grasp</H4>\n<H4 id="Sorcerous_Burst">术法爆发｜Sorcerous Burst</H4>\n<H4 id="Spare_the_Dying">维生术｜Spare the Dying</H4>\n<H4 id="Starry_Wisp">点点星芒｜Starry Wisp</H4>\n<H4 id="Thaumaturgy">奇术｜Thaumaturgy</H4>\n<H4 id="Thorn_Whip">荆棘之鞭｜Thorn Whip</H4>\n<H4 id="Thunderclap">鸣雷破｜Thunderclap</H4>\n<H4 id="Toll_the_Dead">亡者丧钟｜Toll the Dead</H4>\n<H4 id="True_Strike">克敌先击｜True Strike</H4>\n<H4 id="Vicious_Mockery">恶言相加｜Vicious Mockery</H4>\n<H4 id="Word_of_Radiance">光耀祷词｜Word of Radiance</H4>\n').forEach((t=>{const n=document.createElement("option");n.value=t,e.appendChild(n)}));t&&f('\n<H4 id="Alarm">警报术｜Alarm</H4>\n<H4 id="Animal_Friendship">化兽为友｜Animal Friendship</H4>\n<H4 id="Armor_of_Agathys">黯冰狱铠｜Armor of Agathys</H4>\n<H4 id="Arms_of_Hadar">哈达之臂｜Arms of Hadar</H4>\n<H4 id="Bane">灾祸术｜Bane</H4>\n<H4 id="Bless">祝福术｜Bless</H4>\n<H4 id="Burning_Hands">燃烧之手｜Burning Hands</H4>\n<H4 id="Charm_Person">魅惑类人｜Charm Person</H4>\n<H4 id="Chromatic_Orb">繁彩球｜Chromatic Orb</H4>\n<H4 id="Color_Spray">七彩喷射｜Color Spray</H4>\n<H4 id="Command">命令术｜Command</H4>\n<H4 id="Compelled_Duel">强令对决｜Compelled Duel</H4>\n<H4 id="Comprehend_Languages">通晓语言｜Comprehend Languages</H4>\n<H4 id="Create_or_Destroy_Water">造水术/枯水术｜Create or Destroy Water</H4>\n<H4 id="Cure_Wounds">疗伤术｜Cure Wounds</H4>\n<H4 id="Detect_Evil_and_Good">侦测善恶｜Detect Evil and Good</H4>\n<H4 id="Detect_Magic">侦测魔法｜Detect Magic</H4>\n<H4 id="Detect_Poison_and_Disease">侦测毒性和疾病｜Detect Poison and Disease</H4>\n<H4 id="Disguise_Self">易容术｜Disguise Self</H4>\n<H4 id="Dissonant_Whispers">不谐低语｜Dissonant Whispers</H4>\n<H4 id="Divine_Favor">神恩｜Divine Favor</H4>\n<H4 id="Divine_Smite">至圣斩｜Divine Smite</H4>\n<H4 id="Ensnaring_Strike">捕获打击｜Ensnaring Strike</H4>\n<H4 id="Entangle">纠缠术｜Entangle</H4>\n<H4 id="Expeditious_Retreat">脚底抹油｜Expeditious Retreat</H4>\n<H4 id="Faerie_Fire">妖火｜Faerie Fire</H4>\n<H4 id="False_Life">虚假生命｜False Life</H4>\n<H4 id="Feather_Fall">羽落术｜Feather Fall</H4>\n<H4 id="Find_Familiar">寻获魔宠｜Find Familiar</H4>\n<H4 id="Fog_Cloud">云雾术｜Fog Cloud</H4>\n<H4 id="Goodberry">神莓术｜Goodberry</H4>\n<H4 id="Grease">油腻术｜Grease</H4>\n<H4 id="Guiding_Bolt">光导箭｜Guiding Bolt</H4>\n<H4 id="Hail_of_Thorns">荆棘之雨｜Hail of Thorns</H4>\n<H4 id="Healing_Word">治愈真言｜Healing Word</H4>\n<H4 id="Hellish_Rebuke">炼狱叱喝｜Hellish Rebuke</H4>\n<H4 id="Heroism">英雄气概｜Heroism</H4>\n<H4 id="Hex">脆弱诅咒｜Hex</H4>\n<H4 id="Hunter\'s_Mark">猎人印记｜Hunter\'s Mark</H4>\n<H4 id="Ice_Knife">冰刃｜Ice Knife</H4>\n<H4 id="Identify">鉴定术｜Identify</H4>\n<H4 id="Illusory_Script">迷幻手稿｜Illusory Script</H4>\n<H4 id="Inflict_Wounds">致伤术｜Inflict Wounds</H4>\n<H4 id="Jump">跳跃术｜Jump</H4>\n<H4 id="Longstrider">大步奔行｜Longstrider</H4>\n<H4 id="Mage_Armor">法师护甲｜Mage Armor</H4>\n<H4 id="Magic_Missile">魔法飞弹｜Magic Missile</H4>\n<H4 id="Protection_from_Evil_and_Good">防护善恶｜Protection from Evil and Good</H4>\n<H4 id="Purify_Food_and_Drink">净化饮食｜Purify Food and Drink</H4>\n<H4 id="Ray_of_Sickness">致病射线｜Ray of Sickness</H4>\n<H4 id="Sanctuary">庇护术｜Sanctuary</H4>\n<H4 id="Searing_Smite">炽焰斩｜Searing Smite</H4>\n<H4 id="Shield">护盾术｜Shield</H4>\n<H4 id="Shield_of_Faith">虔诚护盾｜Shield of Faith</H4>\n<H4 id="Silent_Image">无声幻影｜Silent Image</H4>\n<H4 id="Sleep">睡眠术｜Sleep</H4>\n<H4 id="Speak_with_Animals">动物交谈｜Speak with Animals</H4>\n<H4 id="Tasha\'s_Hideous_Laughter">塔莎狂笑术｜Tasha\'s Hideous Laughter</H4>\n<H4 id="Tenser\'s_Floating_Disk">谭森浮碟术｜Tenser\'s Floating Disk</H4>\n<H4 id="Thunderous_Smite">雷鸣斩｜Thunderous Smite</H4>\n<H4 id="Thunderwave">雷鸣波｜Thunderwave</H4>\n<H4 id="Unseen_Servant">隐形仆役｜Unseen Servant</H4>\n<H4 id="Witch_Bolt">巫术箭｜Witch Bolt</H4>\n<H4 id="Wrathful_Smite">激愤斩｜Wrathful Smite</H4>\n').forEach((e=>{const n=document.createElement("option");n.value=e,t.appendChild(n)}))}(),function(){const e=document.getElementById("add-cantrip-button"),t=document.getElementById("cantrips-list");e&&t&&e.addEventListener("click",(()=>{const e=t.querySelector('input[name="cantrip1"]');e&&e.parentElement?.classList.contains("spell-item")&&1===t.querySelectorAll(".spell-item").length&&!e.value&&e.parentElement.remove(),t.appendChild(v("cantrip",0,"cantrips-datalist"))}));const n=document.getElementById("add-level1-spell-button"),a=document.getElementById("level1-spells-list");n&&a&&n.addEventListener("click",(()=>{const e=a.querySelector('input[name="level1spell1"]');e&&e.parentElement?.classList.contains("spell-item")&&1===a.querySelectorAll(".spell-item").length&&!e.value&&e.parentElement.remove(),a.appendChild(v("level1spell",0,"level1-spells-datalist"))})),document.getElementById("character-sheet-form")?.addEventListener("click",(function(e){const t=e.target;t&&t.classList.contains("remove-spell-button")&&t.parentElement?.remove()}))}(),function(){const e=document.getElementById("equipment-list-container"),t=document.getElementById("add-equipment-button");m(),e&&t&&t.addEventListener("click",(()=>{e.appendChild(u())}))}(),function(){const e=document.getElementById("inventory-list-container"),t=document.getElementById("add-inventory-item-button");e&&t&&t.addEventListener("click",(()=>{e.appendChild(h())}))}(),S(),H(),e("info","模组创作与人物创建导航界面已初始化。","初始化完成")):e("error","核心界面容器未找到!","界面初始化错误")}if(window.updateSpellAttackAndDc=H,window.updateAllSkillFinalValues=S,"undefined"!=typeof window&&"undefined"!=typeof parent&&parent!==window){["$","toastr","triggerSlash"].forEach((e=>{void 0===window[e]&&void 0!==parent[e]&&(window[e]=parent[e])}))}"complete"===document.readyState||"interactive"===document.readyState?C():document.addEventListener("DOMContentLoaded",C);</script></body></html>