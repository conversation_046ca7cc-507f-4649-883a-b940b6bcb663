# 法术模板数据

本文档定义了冒险日志 v3 中使用的法术模板数据。这些数据基于 D&D 5e 规则，用于客户端进行法术效果处理、伤害计算和豁免判定。

## 法术模板JSON结构

每个法术模板对象可能包含以下字段（根据法术具体特性增减）：

*   `name_zh`: 法术的中文名称 (例如："火球术")
*   `name_en`: 法术的英文名称 (例如："Fireball")
*   `level`: 法术环位 (0 代表戏法)
*   `school`: 法术学派 (例如："塑能")
*   `casting_time`: 施法时间 (例如："1 动作")
*   `range`: 射程 (例如："150尺"，"自身"，"触及")
*   `components`: 法术成分 (一个包含 "V", "S", "M" 的数组，M 可能附带材料描述)
    *   例如: `["V", "S", "M (一小撮蝙蝠粪和硫磺)"]`
*   `duration`: 持续时间 (例如："立即"，"1分钟 (专注)")
*   `description_short`: 法术效果的简短描述。
*   `description_long`: 法术效果的详细描述 (可以包含 `\n` 进行换行)。
*   `attack_type`: 攻击类型 (例如："法术攻击 (远程)", "法术攻击 (近战)", "自动命中", 或无攻击检定)
*   `damage`: 基础伤害骰 (例如："8d6", "1d10")，如果法术造成伤害。
*   `damage_type`: 伤害类型 (例如："火焰", "力场", "穿刺")。
*   `save`: 豁免检定信息对象，如果法术允许豁免。
    *   `attribute`: 需要进行的豁免属性 (例如："敏捷", "体质", "感知")
    *   `effect_on_success`: 豁免成功时的效果 (例如："伤害减半", "无效果", "状态效果减弱")
*   `add_casting_modifier_to_damage`: 布尔值，指示是否将施法属性调整值加入伤害 (例如，魔法飞弹)。
*   `num_projectiles`: 投射物数量 (例如，魔法飞弹的基础数量)。
*   `scaling`: 对象，描述戏法随角色总等级提升的效果。
    *   键为角色等级，值为该等级下的伤害骰或效果描述。
    *   例如: `{ "5": "2d10", "11": "3d10", "17": "4d10" }` (火焰箭)
*   `higher_level_cast`: 对象，描述使用更高环阶法术位施放该法术时的增强效果。
    *   `per_slot_above_base`: 描述每高于基础环阶的法术位。
    *   `effect`: 描述增强的效果 (例如："增加1d6伤害", "增加一个目标", "持续时间加倍")。
*   `area_of_effect`: 影响区域描述，如果法术是范围效果。
    *   `type`: 区域形状 (例如："球形", "立方体", "锥形", "直线")
    *   `size`: 区域大小 (例如："20尺半径", "15尺立方体")
*   `conditions_applied`: 数组，列出法术可能施加的状态效果 (例如：`["目眩", "束缚"]`)。

## 示例法术模板数据 (JSON格式)

```json
[
  {
    "name_zh": "火焰箭",
    "name_en": "Fire Bolt",
    "level": 0,
    "school": "塑能",
    "casting_time": "1 动作",
    "range": "120尺",
    "components": ["V", "S"],
    "duration": "立即",
    "description_short": "发出一道火焰射线攻击目标。",
    "description_long": "你向一个你可见的目标发出一道火焰射线。进行一次远程法术攻击检定。若命中，目标受到1d10火焰伤害。此法术的伤害在你达到更高等级时增强：在5级（2d10）、11级（3d10）和17级（4d10）时各增加1d10。",
    "attack_type": "法术攻击 (远程)",
    "damage": "1d10",
    "damage_type": "火焰",
    "scaling": { "5": "2d10", "11": "3d10", "17": "4d10" }
  },
  {
    "name_zh": "光亮术",
    "name_en": "Light",
    "level": 0,
    "school": "塑能",
    "casting_time": "1 动作",
    "range": "触及",
    "components": ["V", "M (一只萤火虫或一些磷光苔藓)"],
    "duration": "1小时",
    "description_short": "使一个物件发光。",
    "description_long": "你触碰一个不大于10尺见方的物件。直到法术终止，该物件发出20尺半径的明亮光照和额外20尺半径的微光光照。此光照可以被不透明物件所阻挡。\n如果你以一个由生物穿戴或持握的物件为目标，则该生物可以进行一次敏捷豁免来避免此法术。"
  },
  {
    "name_zh": "魔法飞弹",
    "name_en": "Magic Missile",
    "level": 1,
    "school": "塑能",
    "casting_time": "1 动作",
    "range": "120尺",
    "components": ["V", "S"],
    "duration": "立即",
    "description_short": "自动命中目标的多枚魔法飞弹。",
    "description_long": "你创造出三支闪光的魔法飞弹。每支飞弹自动命中你指定的一个位于射程内的可见生物。每支飞弹对目标造成1d4+1力场伤害。\n**升环施法效应**：当你使用2环或更高法术位施放此法术时，你使用的法术位每比1环高一环，你就可以多创造一支飞弹。",
    "attack_type": "自动命中",
    "damage": "1d4+1",
    "damage_type": "力场",
    "num_projectiles": 3,
    "add_casting_modifier_to_damage": false, // 1d4+1 中的 +1 是固定的，不是施法调整值
    "higher_level_cast": {
      "per_slot_above_base": "每高于1环的法术位",
      "effect": "增加一支飞弹"
    }
  },
  {
    "name_zh": "护盾术",
    "name_en": "Shield",
    "level": 1,
    "school": "防护",
    "casting_time": "1 反应，当你被一次攻击命中或成为魔法飞弹法术的目标时",
    "range": "自身",
    "components": ["V", "S"],
    "duration": "1轮的开始",
    "description_short": "创造一个魔法护盾保护自己。",
    "description_long": "一道不可见的魔法护盾出现在你周围并保护着你。直到你的下一回合开始前，你的AC获得+5加值，此加值也作用于触发此法术的攻击，并且你免疫魔法飞弹法术的伤害。"
  },
  {
    "name_zh": "燃烧之手",
    "name_en": "Burning Hands",
    "level": 1,
    "school": "塑能",
    "casting_time": "1 动作",
    "range": "自身 (15尺锥形)",
    "components": ["V", "S"],
    "duration": "立即",
    "description_short": "从你手中喷出火焰。",
    "description_long": "当你伸出双手，十指张开时，一道火焰薄片从你指尖喷薄而出。区域内的每个生物都必须进行一次敏捷豁免。豁免失败者受到3d6火焰伤害，豁免成功则只受一半伤害。\n火焰会点燃区域内任何没有被着装或携带的可燃物。\n**升环施法效应**：当你使用2环或更高法术位施放此法术时，你使用的法术位每比1环高一环，其伤害就增加1d6。",
    "damage": "3d6",
    "damage_type": "火焰",
    "save": { "attribute": "敏捷", "effect_on_success": "伤害减半" },
    "area_of_effect": { "type": "锥形", "size": "15尺" },
    "higher_level_cast": {
      "per_slot_above_base": "每高于1环的法术位",
      "effect": "增加1d6伤害"
    }
  },
  {
    "name_zh": "蛛网术",
    "name_en": "Web",
    "level": 2,
    "school": "咒法",
    "casting_time": "1 动作",
    "range": "60尺",
    "components": ["V", "S", "M (一小撮蜘蛛网)"],
    "duration": "专注，至多1小时",
    "description_short": "创造一片黏性的蛛网区域。",
    "description_long": "你创造出一片厚实的黏性蛛网，其充满一个半径20尺的立方体空间，该空间源自射程内你指定的一点。蛛网属于困难地形，并且会轻度遮蔽其区域。\n如果蛛网没有被固定在两个坚实的实体（例如墙壁或树木）之间，或者没有铺在地面、墙壁或天花板上，则塌陷的蛛网会在1轮后自行瓦解。暴露在火焰中的蛛网部分每轮会烧毁5尺见方的区域。\n任何以蛛网区域作为起始回合位置，或者在该回合进入蛛网区域的生物都必须进行一次敏捷豁免。豁免失败者将陷入束缚状态，只要其还停留在蛛网中或直到其挣脱束缚为止。\n陷入蛛网束缚的生物可以用其动作来进行一次力量检定对抗你的法术豁免DC。如果检定成功，则该生物不再受束缚。",
    "area_of_effect": { "type": "立方体", "size": "20尺" },
    "save": { "attribute": "敏捷", "effect_on_fail": "束缚" }, // 初始豁免
    "conditions_applied": ["束缚 (豁免失败)"]
  },
  {
    "name_zh": "迷踪步",
    "name_en": "Misty Step",
    "level": 2,
    "school": "咒法",
    "casting_time": "1 附赠动作",
    "range": "自身",
    "components": ["V"],
    "duration": "立即",
    "description_short": "短距离传送。",
    "description_long": "你被一阵银色的薄雾短暂环绕，然后传送到30尺内一个你能看见的未被占据空间。"
  }
]
```

**注意**:
*   以上列表仅为示例，实际游戏中需要更完整的法术列表，并可能需要根据具体模组或游戏风格进行调整。
*   法术的详细效果，特别是那些涉及复杂状态、多轮豁免或特殊交互的法术，需要在客户端逻辑中仔细处理。
*   `add_casting_modifier_to_damage` 字段用于明确指示那些伤害公式中不包含 "+MOD" 但实际上应该加上施法调整值的法术（例如，某些职业特性可能会修改此行为）。
*   `description_short` 和 `description_long` 可以用于在不同UI情境下显示法术信息。
