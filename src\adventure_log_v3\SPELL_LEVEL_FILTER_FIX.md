# 法术等级过滤修复

## 🐛 问题描述

用户报告在打开法术书界面时，会出现大量"没有2环法术槽"、"没有3环法术槽"等错误信息。这是因为：

1. **根本原因**：`renderAvailableSpellsList()` 函数显示所有法术模板（包括2环、3环等高等级法术）
2. **触发条件**：对每个法术调用 `canCastSpell()` 检查是否可以施放
3. **错误产生**：1级角色只有1环法术槽，检查高等级法术时会报错"没有X环法术槽"

## 🔧 解决方案

### 1. 新增 `getMaxAvailableSpellLevel()` 函数

**位置**：`src/adventure_log_v3/spells/index.ts`

```typescript
/**
 * 获取角色当前可以使用的最高法术等级
 */
export function getMaxAvailableSpellLevel(playerState: PlayerState): number {
  if (!playerState.spellSlots) {
    return 0; // 只能使用戏法
  }

  // 找到最高的有法术槽的等级
  let maxLevel = 0;
  for (const levelStr in playerState.spellSlots) {
    const level = parseInt(levelStr);
    const slotInfo = playerState.spellSlots[levelStr];
    if (slotInfo && slotInfo.max > 0) {
      maxLevel = Math.max(maxLevel, level);
    }
  }

  return maxLevel;
}
```

**功能**：
- 检查角色的 `spellSlots` 对象
- 找到最高等级的法术槽
- 返回角色可以使用的最高法术等级

### 2. 修改 `renderAvailableSpellsList()` 函数

**位置**：`src/adventure_log_v3/ui/render.ts`

**修改内容**：
```typescript
// 首先过滤掉角色当前无法使用的高等级法术
// 获取角色当前最高可用法术等级
const maxSpellLevel = getMaxAvailableSpellLevel(playerState);
filteredSpells = filteredSpells.filter(spell => spell.level <= maxSpellLevel);
```

**效果**：
- 在应用其他过滤器之前，先过滤掉超出角色等级的法术
- 只显示角色当前可以使用的法术等级
- 避免对高等级法术调用 `canCastSpell()` 检查

### 3. 更新导入语句

**位置**：`src/adventure_log_v3/ui/render.ts`

```typescript
import { getSpellTemplates, canCastSpell, getMaxAvailableSpellLevel } from '../spells';
```

## 📊 修复效果

### 修复前的问题
```
Debug - Can Cast: 没有2环法术槽
Debug - Can Cast: 没有2环法术槽  
Debug - Can Cast: 没有2环法术槽
Debug - Can Cast: 没有3环法术槽
Debug - Can Cast: 没有3环法术槽
Debug - Can Cast: 没有3环法术槽
... (大量重复错误)
```

### 修复后的效果
- ✅ **1级角色**：只显示戏法(0环)和1环法术
- ✅ **无法术槽角色**：只显示戏法(0环)
- ✅ **高等级角色**：显示对应等级的所有法术
- ✅ **无错误信息**：不再检查超出角色等级的法术

## 🎯 测试用例

### 测试场景1：1级牧师（当前用户情况）
```typescript
spellSlots: {
  "1": { current: 1, max: 2 }
}
```
**期望结果**：`getMaxAvailableSpellLevel()` 返回 `1`
**显示法术**：戏法 + 1环法术

### 测试场景2：无法术槽角色（如战士）
```typescript
spellSlots: {}
```
**期望结果**：`getMaxAvailableSpellLevel()` 返回 `0`
**显示法术**：仅戏法

### 测试场景3：5级法师
```typescript
spellSlots: {
  "1": { current: 4, max: 4 },
  "2": { current: 3, max: 3 },
  "3": { current: 2, max: 2 }
}
```
**期望结果**：`getMaxAvailableSpellLevel()` 返回 `3`
**显示法术**：戏法 + 1环 + 2环 + 3环法术

## 🔍 技术细节

### 过滤顺序
1. **等级过滤**：首先过滤掉超出角色等级的法术
2. **UI过滤器**：然后应用用户选择的等级和学派过滤器
3. **可施放检查**：最后对剩余法术检查是否可以施放

### 性能优化
- **减少检查次数**：避免对高等级法术进行不必要的 `canCastSpell()` 检查
- **减少错误信息**：大幅减少调试信息的数量
- **提升用户体验**：法术列表更加清晰，只显示相关法术

## 🚀 部署说明

### 文件修改列表
1. `src/adventure_log_v3/spells/index.ts` - 新增 `getMaxAvailableSpellLevel()` 函数
2. `src/adventure_log_v3/ui/render.ts` - 修改 `renderAvailableSpellsList()` 和导入语句

### 测试文件
- `src/adventure_log_v3/test_spell_level_filter.ts` - 测试函数功能

### 兼容性
- ✅ 向后兼容：不影响现有功能
- ✅ 类型安全：使用现有的 TypeScript 类型
- ✅ 无破坏性变更：只是添加过滤逻辑

## 📝 后续维护

### 如果需要调整过滤逻辑
1. 修改 `getMaxAvailableSpellLevel()` 函数的逻辑
2. 可以添加额外的过滤条件（如职业限制、学派限制等）

### 如果需要显示所有法术
1. 可以添加一个"显示所有法术"的选项
2. 在该模式下跳过等级过滤

这个修复彻底解决了用户遇到的错误信息问题，同时提升了法术书界面的用户体验。
