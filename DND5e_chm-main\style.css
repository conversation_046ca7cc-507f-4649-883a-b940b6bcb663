/*整体配置*/
html,body {
	font-family: Arial, Helvetica, sans-serif;
}
/*标题颜色*/
h1,h2,h3{
	color:#800000;
}
/*列表聚拢*/
UL {
	margin-top: -16px;
	margin-bottom: -16px;
}
OL {
	margin-top: -16px;
	margin-bottom: -16px;
}
/*下划线→术语格式*/
U{
	color: #008000;
	font-weight: bold;
	text-decoration: none;
}
/*下划线+斜体→法术格式*/
U EM,U I{
	color: #704CD9;
	font-style: italic;
	font-weight: bold;

}
/*斜体+下划线→魔法物品格式*/
EM U,I U{
	color: #0000ff;
	font-style: normal;
	font-weight: bold;

}
/*粗体+下划线→怪物格式*/
B U,U B,STRONG U,U STRONG{
	color: #ee3300;
	font-style: normal;
	font-weight: bold
}
/*法术页使用h4*/
h4 {
    color: #704cd9;
    margin-bottom: -20px;
	font-size: 22px;
}
/*怪物页使用h5*/
div h5{
	color: #800000;
	font-weight: bold;
	font-size: 22px;
	margin-top: 0px;
	margin-bottom: 5px;
}
/*道具页使用h6*/
h6 {
	color: #800000;
    margin-bottom: -20px;
	font-size: 18px;
}
/*居中框*/
div.center{
	BORDER: #656565 2px solid;
	WIDTH: 100%;
	PADDING-BOTTOM: 10px;
	PADDING-TOP: 10px;
	PADDING-LEFT: 50px;
	MARGIN-LEFT: 8%;
	PADDING-RIGHT: 50px;
	MARGIN-RIGHT: 8%;
	BACKGROUND-COLOR: #ebd6c1;
}
/*备用数据卡框*/
div.stat-block{
	margin-left: 22px;
	width: 537px;
	background-color: #F8F8F6;
	padding: 15px;
	border:3px double #808080;
	font-size: 16px;
	margin-bottom:20px;
}
/*实例文本框*/
div.example{
	font-family: "fangsong","仿宋",Arial, Helvetica, sans-serif;
	WIDTH: 100%; 
	BACKGROUND: #fff2cc; 
	PADDING-BOTTOM: 10px; 
	PADDING-TOP: 10px; 
	PADDING-LEFT: 10px; 
	MARGIN-LEFT: 5%; 
	PADDING-RIGHT: 10px; 
	MARGIN-RIGHT: 5%
}
/*实例右框*/
div.ex-right{
	BORDER: #656565 2px solid; 
	WIDTH: 250px;
	FLOAT: right; 
	PADDING: 10px; 
	BACKGROUND-COLOR: #ebd6c1; 
	MARGIN-RIGHT: -10px
}
/*基础表格*/
table.basic{
	width: auto;
	border-collapse: collapse;
	border-spacing: 0;
	border: 0;	
}
table.basic td{
	padding: 2px;
}
table.center{
	width: auto;
	border-collapse: collapse;
	border-spacing: 0;
	border: 0;	
	text-align:center;
}
table.center td{
	padding: 2px;
}
table.part {
	width: auto;
	border-collapse: collapse;
	border-spacing: 0;
	border: 0;	
}
table.part td{
	padding: 2px;
}
table.part th{
	padding: 2px;
	font-weight: normal;
	text-align: center;
}
P.sum{
	font-size:18px;
	MARGIN-TOP: -8px;
	margin-bottom: -8px;
	color: #808080;
	font-weight: bold;
	font-family: "fangsong","仿宋";

}