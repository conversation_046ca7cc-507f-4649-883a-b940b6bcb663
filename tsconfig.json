{
  "compilerOptions": {
    "types": [
      "jquery",
      "jqueryui",
      "lodash",
      "toastr",
      "yaml",
    ],
    "target": "ESNext",
    "module": "ESNext",
    "outDir": "dist",
    "baseUrl": "src",
    "paths": {
      "@/*": [
        "./*",
      ]
    },
    "moduleDetection": "auto",
    "moduleResolution": "node",
    "allowUmdGlobalAccess": true,
    "allowSyntheticDefaultImports": true,
    "strictBindCallApply": true,
    "allowJs": true,
    "checkJs": false,
    "sourceMap": true,
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true
  },
  "exclude": [
    "**/dist/**",
    "**/node_modules/**",
  ]
}
