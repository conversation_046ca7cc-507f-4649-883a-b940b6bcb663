// --- 核心数据接口 (JSON Schema v2) ---
export interface Attribute {
  base: number;
  race_bonus: number;
  modifier_bonus: number;
  final: number;
  mod: number;
}

export interface Hp {
  current: number;
  max: number;
}

export interface Currency {
  gold: number;
  silver: number;
  copper: number;
}

export interface Skill {
  name: string;
  proficient: boolean;
  attribute: string;
  modifierValue: number;
  finalValue: number; // This is the correct value to display
}

export interface SpellSlotInfo {
  current: number;
  max: number;
}

export interface Spell {
  name: string;
  level: number;
  source?: string;
  details?: string;
}

// 法术模板接口 - 用于定义法术的完整属性
export interface SpellTemplate {
  name_zh: string;
  name_en: string;
  level: number;
  school: string;
  casting_time: string;
  range: string;
  components: string[];
  duration: string;
  description_short: string;
  description_long: string;
  attack_type?: '法术攻击 (远程)' | '法术攻击 (近战)' | '自动命中';
  damage?: string;
  damage_type?: string;
  save?: {
    attribute: string;
    effect_on_success: string;
    effect_on_fail?: string;
  };
  add_casting_modifier_to_damage?: boolean;
  num_projectiles?: number;
  scaling?: Record<string, string>; // 戏法等级提升
  higher_level_cast?: {
    per_slot_above_base: string;
    effect: string;
  };
  area_of_effect?: {
    type: string;
    size: string;
  };
  conditions_applied?: string[];
}

// 法术伤害计算结果
export interface SpellDamageResult {
  totalDamage: number;
  damageType: string;
  details: string;
  breakdown: { type: string; amount: number; source: string }[];
  projectileCount?: number; // 用于魔法飞弹等多投射物法术
  saveRequired?: {
    attribute: string;
    dc: number;
    effectOnSuccess: string;
  };
}

export interface EquipmentItem {
  name: string;
  type: string; // Type is mandatory for EquipmentItem
  equipped?: boolean;
  details?: string;
  properties?: string[];
  damage?: string;
  damageType?: string;
  id?: string;
  baseItemName?: string;
  magicEffects?: MagicEffect[];
}

export interface MagicEffect {
  type: EffectType;
  value: any;
  condition?: string;
  notes?: string;
  sourceId?: string;
  duration?: string;
}

export type EffectType =
  | 'ATTACK_BONUS'
  | 'DAMAGE_BONUS_STATIC'
  | 'DAMAGE_BONUS_DICE'
  | 'ON_HIT_EFFECT_SAVE'
  | 'ON_HIT_EFFECT_CONDITION'
  | 'ON_HIT_EFFECT_DAMAGE'
  | 'CRITICAL_HIT_MODIFIER'
  | 'WEAPON_PROPERTY_GRANT'
  | 'WEAPON_PROPERTY_MODIFY'
  | 'SPELL_LIKE_ABILITY'
  | 'ATTRIBUTE_REQUIREMENT'
  | 'ATTRIBUTE_MODIFIER'
  | 'SPELL_EFFECT'
  | 'AC_BONUS'
  | 'SAVE_BONUS'
  | 'SKILL_BONUS'
  | 'PASSIVE_EFFECT'
  | 'MOVEMENT_MODIFIER'
  | 'HEALING_EFFECT'
  | 'RESOURCE_MODIFIER';

export interface InventoryItem {
  id?: string;
  name: string;
  quantity: number;
  description?: string;
  baseItemName?: string;
  properties?: string[];
  magicEffects?: MagicEffect[];
  type?: string; // Optional type for items in inventory
}

export interface PlayerState {
  name: string;
  race: string;
  class: string;
  level: number;
  exp: number;
  hp: Hp;
  ac: number;
  currency: Currency;
  attributes: {
    strength: Attribute;
    dexterity: Attribute;
    constitution: Attribute;
    intelligence: Attribute;
    wisdom: Attribute;
    charisma: Attribute;
  };
  proficiencies: string[];
  skills: Skill[];
  spellSlots: Record<string, SpellSlotInfo>;
  equippedSpells: Spell[];
  equipment: EquipmentItem[];
  inventory: InventoryItem[];
  activeQuests: string[];
  exhaustion: number;
  time: string;
  currentLocation: string;
  spellcastingAbility?: 'INT' | 'WIS' | 'CHA' | string;
  lastProcessedSceneUuid?: string | null;
  player?: string;
  age?: string;
  gender?: string;
  alignment?: string;
  faith?: string;
  height?: string;
  weight?: string;
  appearance?: string;
  story?: string;
  background?: string;
  personalityTraits?: string;
  ideals?: string;
  bonds?: string;
  flaws?: string;
  subclass?: string;
}

export interface WeaponTemplate {
  name_zh: string;
  name_en: string;
  category: string;
  damage: string;
  damageType: string;
  properties: string[];
  weight: string;
  cost: string;
  mastery?: string;
}

export interface NarrativeEntry {
  type: 'description' | 'dialogue' | 'systemMessage' | 'actionDescription' | 'thought';
  content: string;
  speaker?: string;
  actor?: string;
  emotion?: string;
}

export interface PlayerChoiceJSON {
  id: string;
  text: string;
  actionCommand: string;
}

export interface VariableUpdateInstruction {
  target: '玩家';
  path: string;
  operation: '设置' | '增加' | '减少' | '添加元素' | '移除元素' | '物品获得' | '物品失去';
  value: any;
}

export interface EnemyStateJSON {
  id: string;
  name: string;
  hp: { current: number; max: number };
  ac: number;
  statusEffects?: string[];
  intent?: string;
}

export interface AdventureSceneJSON {
  sceneType: 'location' | 'dialogue' | 'combat' | 'system_message' | 'puzzle';
  sceneTitle: string;
  currentLocation: string;
  time: string;
  narrative: NarrativeEntry[];
  playerChoices: PlayerChoiceJSON[];
  variableUpdates?: VariableUpdateInstruction[];
  enemies?: EnemyStateJSON[];
  combatLog?: string[];
  sceneUuid?: string;
}

export interface DamageRollResult {
  totalDamage: number;
  damageType: string;
  details: string;
  breakdown: { type: string; amount: number; source: string }[];
}

export interface CheckResult {
  success: boolean;
  roll: number;
  attributeMod: number;
  proficiencyBonusApplied: number;
  total: number;
  dc: number;
  attributeName: string;
  skillName?: string;
  isCritical?: boolean;
  isFumble?: boolean;
  advantageState?: 'advantage' | 'disadvantage' | 'normal';
}

export interface LoadGameResult {
  playerStateLoadedFromMsg: boolean;
  sceneDataLoadedFromMsg: boolean;
}

export interface WeaponRange {
  normal: number;
  long: number | null;
}
