<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Galgame 界面</title>
<style>/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@7.1.2_webpack@5.99.9/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[0].oneOf[5].use[1]!./node_modules/.pnpm/postcss-loader@8.1.1_postcs_07f94889eb412ae0ff5565af90e6a7c5/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[0].oneOf[5].use[2]!./node_modules/.pnpm/sass-loader@16.0.5_sass@1.89.0_webpack@5.99.9/node_modules/sass-loader/dist/cjs.js!./src/galgame/index.scss ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
body {
  margin: 0;
  font-family: sans-serif;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}

#galgame-container {
  width: 90vw;
  max-width: 600px;
  aspect-ratio: 9/16;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

#status-bar {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background-color: #333;
  color: #fff;
  font-size: 0.9em;
  align-items: center;
}
#status-bar #time, #status-bar #energy {
  padding: 5px 10px;
}
#status-bar #character-info-ingame {
  flex-grow: 1;
  text-align: center;
  padding: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

#scene-description {
  padding: 20px;
  text-align: center;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
#scene-description p {
  margin: 0;
  font-size: 1.1em;
}

#choices {
  display: flex;
  flex-direction: column;
  padding: 15px;
  gap: 10px;
}
#choices button {
  padding: 12px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1em;
  transition: background-color 0.3s;
}
#choices button:hover {
  background-color: #0056b3;
}

#dialogue-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  flex-direction: column;
  padding: 15px;
  box-sizing: border-box;
}
#dialogue-area #character-sprite {
  width: 100%;
  aspect-ratio: 16/9;
  background-color: #eee;
  background-size: cover;
  background-position: center;
  margin-bottom: 15px;
  border-radius: 5px;
}
#dialogue-area #dialogue-text {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 15px;
  flex-grow: 1;
  overflow-y: auto;
  font-size: 1em;
  line-height: 1.6;
}
#dialogue-area #dialogue-choices {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
#dialogue-area #dialogue-choices button {
  padding: 10px;
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9em;
  transition: background-color 0.3s;
}
#dialogue-area #dialogue-choices button:hover {
  background-color: #1e7e34;
}
</style></head>
<body>
    <div id="galgame-container">
        <div id="status-bar">
            <div id="time">时间: 08:00</div>
            <div id="character-info-ingame"></div> <!-- Added for character name and favorability -->
            <div id="energy">体力: 100</div>
        </div>
        <div id="scene-description">
            <p>你现在在家里。</p>
        </div>
        <div id="choices">
            <button data-action="go-out">出门</button>
            <button data-action="rest">休息</button>
            <button data-action="pass-time">消磨时间</button>
        </div>
        <div id="dialogue-area" style="display: none;">
            <div id="character-sprite"></div>
            <div id="dialogue-text"></div>
            <div id="dialogue-choices"></div>
        </div>
    </div>
<script>/*! For license information please see index.js.LICENSE.txt */
var __webpack_modules__={"./src/galgame/index.scss":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("__webpack_require__.r(__webpack_exports__);\n// extracted by mini-css-extract-plugin\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvZ2FsZ2FtZS9pbmRleC5zY3NzIiwibWFwcGluZ3MiOiI7QUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3RhdmVybl9oZWxwZXJfdGVtcGxhdGUvLi9zcmMvZ2FsZ2FtZS9pbmRleC5zY3NzPyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBleHRyYWN0ZWQgYnkgbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5cbmV4cG9ydCB7fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/galgame/index.scss\n")},"./src/galgame/index.ts":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.scss */ \"./src/galgame/index.scss\");\n\n// Module-level state variables\nlet statusBar = null;\nlet timeDisplay = null;\nlet energyDisplay = null;\nlet sceneDescription = null;\nlet choicesContainer = null;\nlet dialogueArea = null;\nlet characterSprite = null;\nlet dialogueText = null;\nlet dialogueChoicesContainer = null;\nlet gameState = {\n    time: '08:00',\n    energy: 100,\n    currentScene: 'home',\n    interfaceMode: 'location',\n    otherCharactersInLocation: [],\n    locationActions: [],\n    playerReplyOptions: [],\n};\nlet currentHostMessageId = null;\nlet fullHistoryLog = [];\nconst OLD_HISTORY_START_TAG = '\x3c!-- OLD_GALGAME_HISTORY_CHUNK_START --\x3e';\nconst OLD_HISTORY_END_TAG = '\x3c!-- OLD_GALGAME_HISTORY_CHUNK_END --\x3e';\nconst HISTORY_SEPARATOR = '\\n------------------------\\n';\n// const USER_CHOICE_PREFIX = `用户点击选项--\"`; // Not strictly needed if parsing logic is robust\n// const USER_CHOICE_SUFFIX = `\"--HH:MM`;\nfunction stripOuterWrappers(blockWithWrappers) {\n    return blockWithWrappers\n        .replace(/^查看系统\\s*msg_start\\s*/i, '')\n        .replace(/msg_end\\s*关闭系统\\s*$/i, '')\n        .trim();\n}\nfunction wrapWithSystemTags(sceneContent) {\n    return `查看系统\\nmsg_start\\n${sceneContent}\\nmsg_end\\n关闭系统`;\n}\nfunction extractActualSceneBlock(contentWithPotentialJunk) {\n    const lines = contentWithPotentialJunk.trim().split('\\n');\n    let startTagLineIndex = -1;\n    let tagName = '';\n    let tagType = ''; // '地点' or '人物'\n    for (let i = 0; i < lines.length; i++) {\n        const lineTrimmed = lines[i].trim();\n        const locMatch = lineTrimmed.match(/^<地点:([^>]+)>/i);\n        if (locMatch && locMatch[1]) {\n            startTagLineIndex = i;\n            tagName = locMatch[1];\n            tagType = '地点';\n            break;\n        }\n        const charMatch = lineTrimmed.match(/^<人物:([^>]+)>/i);\n        if (charMatch && charMatch[1]) {\n            startTagLineIndex = i;\n            tagName = charMatch[1];\n            tagType = '人物';\n            break;\n        }\n    }\n    if (startTagLineIndex === -1) {\n        console.warn('[Galgame extractActualSceneBlock] No valid start tag found in:', contentWithPotentialJunk);\n        return null; // No valid start tag found\n    }\n    const endTagPattern = `</${tagType}:${tagName}>`;\n    let blockLines = [];\n    // Collect lines from the start tag line\n    for (let i = startTagLineIndex; i < lines.length; i++) {\n        blockLines.push(lines[i]); // Push the original line, not trimmed, to preserve internal formatting\n        if (lines[i].trim().includes(endTagPattern)) { // Check if the trimmed line contains the end tag\n            return blockLines.join('\\n');\n        }\n    }\n    console.warn('[Galgame extractActualSceneBlock] No valid end tag found for:', tagType, tagName);\n    return null; // No valid end tag found\n}\nfunction safeToastr(type, message, title) {\n    try {\n        if (typeof window.toastr !== 'object' &&\n            typeof parent !== 'undefined' &&\n            typeof parent.toastr === 'object') {\n            window.toastr = parent.toastr;\n        }\n        if (typeof toastr === 'object' && toastr !== null && typeof toastr[type] === 'function') {\n            toastr[type](message, title);\n        }\n        else {\n            const consoleFn = type === 'error' ? console.error : type === 'warning' ? console.warn : console.log;\n            consoleFn(`[Galgame Toastr Fallback - ${type}] ${title ? title + ': ' : ''}${message}`);\n        }\n    }\n    catch (e) {\n        console.error(`[Galgame] safeToastr Error: ${e.message}`);\n    }\n}\nfunction ensureGlobals() {\n    try {\n        if (typeof window !== 'undefined' && typeof parent !== 'undefined' && parent !== window) {\n            const apiKeysToCopy = ['$', 'toastr', 'triggerSlash', 'getLastMessageId', 'setChatMessages'];\n            apiKeysToCopy.forEach(key => {\n                if (typeof window[key] === 'undefined') {\n                    if (typeof parent[key] !== 'undefined') {\n                        window[key] = parent[key];\n                    }\n                    else {\n                        console.warn(`[Galgame ensureGlobals] API \"${key}\" is UNDEFINED in parent.`);\n                    }\n                }\n            });\n        }\n    }\n    catch (e) {\n        safeToastr('error', `ensureGlobals Error: ${e.message}`, 'Galgame Init Error');\n    }\n}\nfunction updateStatusBarDisplay() {\n    if (timeDisplay)\n        timeDisplay.textContent = `时间: ${gameState.time}`;\n    if (energyDisplay)\n        energyDisplay.textContent = `体力: ${gameState.energy}`;\n    const charInfoEl = document.getElementById('character-info-ingame');\n    if (charInfoEl) {\n        charInfoEl.textContent =\n            gameState.interfaceMode === 'characterDialogue' && gameState.currentCharacterName\n                ? `与 ${gameState.currentCharacterName} 对话中 (好感: ${gameState.characterFavorability || 0})`\n                : '';\n    }\n}\nfunction updateSceneDescriptionDisplay() {\n    if (sceneDescription) {\n        let text = `场景: ${gameState.currentScene}`;\n        if (gameState.currentScene.toLowerCase() === '家')\n            text = '你现在在家里。';\n        else if (gameState.otherCharactersInLocation?.length)\n            text = `你来到了 ${gameState.currentScene}。你看到了 ${gameState.otherCharactersInLocation.join('、')}。`;\n        else if (gameState.currentScene)\n            text = `你来到了 ${gameState.currentScene}。`;\n        sceneDescription.innerHTML = `<p>${text}</p>`;\n    }\n}\nfunction extractLatestSceneContent(fullDataString) {\n    const lastOldHistoryEndIndex = fullDataString.lastIndexOf(OLD_HISTORY_END_TAG);\n    const searchString = lastOldHistoryEndIndex !== -1\n        ? fullDataString.substring(lastOldHistoryEndIndex + OLD_HISTORY_END_TAG.length)\n        : fullDataString;\n    const match = searchString.match(/查看系统\\s*msg_start([\\s\\S]+?)msg_end\\s*关闭系统/);\n    return match ? stripOuterWrappers(match[0]) : null;\n}\nfunction rebuildFullHistoryLog(fullDataStringWithHistory) {\n    fullHistoryLog.length = 0;\n    const oldHistoryBlockMatch = fullDataStringWithHistory.match(new RegExp(`${OLD_HISTORY_START_TAG}([\\\\s\\\\S]*?)${OLD_HISTORY_END_TAG}`, 's'));\n    if (oldHistoryBlockMatch && oldHistoryBlockMatch[1]) {\n        const oldHistoryContent = oldHistoryBlockMatch[1].trim();\n        // Split by the separator, but ensure scene data itself is not split if it contains similar lines\n        const historyEntriesText = oldHistoryContent.split(new RegExp(HISTORY_SEPARATOR.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'));\n        historyEntriesText.forEach(entryText => {\n            const sceneContentMatch = entryText.match(/^([\\s\\S]*?)(?=\\n用户点击选项--|$)/s);\n            const userChoiceMatch = entryText.match(/\\n用户点击选项--\"([^\"]+)\"(?:--HH:MM)?$/);\n            if (sceneContentMatch && sceneContentMatch[1] && sceneContentMatch[1].trim()) {\n                fullHistoryLog.push({\n                    sceneContent: sceneContentMatch[1].trim(),\n                    userChoiceText: userChoiceMatch ? userChoiceMatch[1] : undefined,\n                });\n            }\n        });\n    }\n    const latestSceneContentFromStorage = extractLatestSceneContent(fullDataStringWithHistory);\n    if (latestSceneContentFromStorage) {\n        const actualLatestSceneBlock = extractActualSceneBlock(latestSceneContentFromStorage);\n        if (actualLatestSceneBlock) {\n            fullHistoryLog.push({ sceneContent: actualLatestSceneBlock });\n        }\n        else {\n            console.warn('[Galgame rebuildFullHistoryLog] Could not extract actual scene block from latestSceneContentFromStorage:', latestSceneContentFromStorage);\n        }\n    }\n    console.log('[Galgame Debug] Rebuilt fullHistoryLog with', fullHistoryLog.length, 'entries.');\n}\nfunction parseAndApplyGalgameData(sceneContentToParse) {\n    if (!sceneContentToParse || typeof sceneContentToParse !== 'string') {\n        safeToastr('error', 'Galgame: 无效的场景内容传入解析。', '解析错误');\n        return;\n    }\n    safeToastr('info', 'Galgame: 解析场景内容...', '解析');\n    const allLines = sceneContentToParse.trim().split('\\n');\n    let startLineIndex = -1;\n    for (let i = 0; i < allLines.length; i++) {\n        if (allLines[i].match(/^<地点:([^>]+)>/i) || allLines[i].match(/^<人物:([^>]+)>/i)) {\n            startLineIndex = i;\n            break;\n        }\n    }\n    if (startLineIndex === -1) {\n        safeToastr('error', 'Galgame: 未在场景内容中找到有效的<地点:...>或<人物:...>标签。', '解析失败');\n        return;\n    }\n    const lines = allLines.slice(startLineIndex);\n    const firstLine = lines[0] || '';\n    const newState = {\n        ...gameState,\n        otherCharactersInLocation: [],\n        locationActions: [],\n        playerReplyOptions: [],\n        characterDialogueText: '',\n    };\n    let isCharacterDataBlock = false;\n    let currentParsingCharName = '';\n    const locTagMatch = firstLine.match(/^<地点:([^>]+)>/i);\n    const charTagMatch = firstLine.match(/^<人物:([^>]+)>/i);\n    if (locTagMatch && locTagMatch[1]) {\n        newState.currentScene = locTagMatch[1];\n        newState.interfaceMode = 'location';\n        isCharacterDataBlock = false;\n        newState.currentCharacterName = undefined;\n        newState.characterFavorability = undefined;\n    }\n    else if (charTagMatch && charTagMatch[1]) {\n        currentParsingCharName = charTagMatch[1];\n        newState.currentCharacterName = currentParsingCharName;\n        newState.interfaceMode = 'characterDialogue';\n        isCharacterDataBlock = true;\n        newState.characterFavorability = 0;\n        newState.characterDialogueText = '';\n        newState.playerReplyOptions = [];\n    }\n    else {\n        safeToastr('error', `Galgame: 未知的主要数据块标签: ${firstLine}`, '解析失败');\n        return;\n    }\n    lines.slice(1).forEach(line => {\n        // Process all lines after the main tag\n        const trimmedLine = line.trim();\n        if (trimmedLine === `</${locTagMatch?.[0].substring(1)}` || trimmedLine === `</${charTagMatch?.[0].substring(1)}`)\n            return; // Skip closing tag\n        const timeMatch = trimmedLine.match(/^时间--\"([^\"]+)\"--HH:MM/i), energyMatch = trimmedLine.match(/^体力--\"([^\"]+)\"--HH:MM/i), otherChar = trimmedLine.match(/^其他角色名称--\"([^\"]+)\"--HH:MM/i), locAction = trimmedLine.match(/^行动选项([A-Z])--\"([^\"]+)\"--HH:MM/i), favMatch = trimmedLine.match(/^好感度--\"([^\"]+)\"--HH:MM/i), charDia = trimmedLine.match(/^对方的话--\"([^\"]+)\"--HH:MM/i), replyOpt = trimmedLine.match(/^回复选项([A-Z])--\"([^\"]+)\"--HH:MM/i);\n        if (timeMatch && timeMatch[1])\n            newState.time = timeMatch[1];\n        else if (energyMatch && energyMatch[1]) {\n            const e = parseInt(energyMatch[1], 10);\n            if (!isNaN(e))\n                newState.energy = e;\n        }\n        else if (isCharacterDataBlock) {\n            if (favMatch && favMatch[1])\n                newState.characterFavorability = parseInt(favMatch[1], 10) || 0;\n            else if (charDia && charDia[1])\n                newState.characterDialogueText = charDia[1];\n            else if (replyOpt && replyOpt[1] && replyOpt[2])\n                newState.playerReplyOptions?.push({\n                    id: `reply_${replyOpt[1]}`,\n                    text: replyOpt[2],\n                    action: `reply_to_${currentParsingCharName}_option_${replyOpt[1]}`.toLowerCase().replace(/\\s+/g, '_'),\n                });\n        }\n        else {\n            if (otherChar && otherChar[1])\n                newState.otherCharactersInLocation?.push(otherChar[1]);\n            else if (locAction && locAction[1] && locAction[2])\n                newState.locationActions?.push({\n                    id: `loc_action_${locAction[1]}`,\n                    text: locAction[2],\n                    action: locAction[2].toLowerCase().replace(/\\s+/g, '_'),\n                });\n        }\n    });\n    gameState = newState;\n    updateStatusBarDisplay();\n    if (gameState.interfaceMode === 'location')\n        updateSceneDescriptionDisplay();\n    console.log('[Galgame Debug] gameState after parse:', JSON.stringify(gameState, null, 2));\n    safeToastr('success', `Galgame: 数据已应用.`, '解析完成');\n}\nfunction renderButtons(container, choices, clickHandler) {\n    if (!container) {\n        safeToastr('error', `Galgame: 按钮容器丢失。`, '渲染错误');\n        return;\n    }\n    container.innerHTML = '';\n    if (choices?.length) {\n        choices.forEach(c => {\n            const btn = document.createElement('button');\n            btn.textContent = c.text;\n            btn.dataset.action = c.action;\n            btn.dataset.id = c.id;\n            btn.addEventListener('click', () => clickHandler(c.action, c.text));\n            container.appendChild(btn);\n        });\n    }\n}\nconst RETURN_KEYWORDS = ['返回', '离开', '结束对话', '不再打扰', '告辞'];\nlet previousSceneDataStack = []; // For simple UI back, not for AI context history\nasync function handleChoiceClick(actionKey, choiceText) {\n    safeToastr('info', `按钮点击: \"${choiceText}\" (action: ${actionKey})`, '操作');\n    const lowerChoiceText = choiceText.toLowerCase();\n    let isReturnAction = actionKey === 'action_end_dialogue';\n    if (!isReturnAction) {\n        for (const keyword of RETURN_KEYWORDS) {\n            if (lowerChoiceText.includes(keyword.toLowerCase())) {\n                isReturnAction = true;\n                break;\n            }\n        }\n    }\n    if (isReturnAction) {\n        await returnToPreviousSceneOrHome();\n        return;\n    }\n    if (fullHistoryLog.length > 0) {\n        fullHistoryLog[fullHistoryLog.length - 1].userChoiceText = choiceText;\n    }\n    // previousSceneDataStack.push(stripOuterWrappers(getCurrentSceneDataBlock())); // previousSceneDataStack is for UI \"back\" button, not directly related to fullHistoryLog for AI context\n    let prompt = `用户在[${gameState.currentCharacterName || gameState.currentScene}]选择了：\"${choiceText}\"。\\n请给出接下来的Galgame界面内容，并严格遵循所有已定义的格式规则，特别是每行末尾的--HH:MM。`;\n    // ... (prompt construction logic)\n    if (typeof triggerSlash !== 'function') {\n        safeToastr('error', 'triggerSlash API不可用!', 'API错误');\n        return;\n    }\n    try {\n        const aiResponseFullBlock = await triggerSlash(`/gen ${prompt}`);\n        if (aiResponseFullBlock?.trim()) {\n            const strippedContentFromAI = stripOuterWrappers(aiResponseFullBlock);\n            const actualSceneBlock = extractActualSceneBlock(strippedContentFromAI);\n            if (actualSceneBlock) {\n                fullHistoryLog.push({ sceneContent: actualSceneBlock });\n                parseAndApplyGalgameData(actualSceneBlock); // parseAndApplyGalgameData now receives the pure block\n                if (gameState.interfaceMode === 'location')\n                    showLocationSceneInterface();\n                else\n                    showCharacterDialogueInterface();\n                await persistCurrentStateWithHistory();\n            }\n            else {\n                safeToastr('error', 'AI响应中未找到有效的场景数据块。', 'AI交互错误');\n                console.error('[Galgame handleChoiceClick] Failed to extract actual scene block from:', strippedContentFromAI);\n            }\n        }\n        else {\n            safeToastr('warning', 'AI未返回有效数据。', 'AI交互');\n        }\n    }\n    catch (e) {\n        safeToastr('error', `AI交互失败: ${e.message}`, 'AI错误');\n        // if (previousSceneDataStack.length > 0) previousSceneDataStack.pop();\n    }\n}\nfunction showLocationSceneInterface() {\n    if (dialogueArea)\n        dialogueArea.style.display = 'none';\n    if (choicesContainer)\n        choicesContainer.style.display = 'flex';\n    if (sceneDescription)\n        sceneDescription.style.display = 'flex';\n    updateSceneDescriptionDisplay();\n    let actions = [];\n    if (gameState.currentScene.toLowerCase() === '家') {\n        actions = [\n            { text: '出门', action: 'go-out', id: 'home_go' },\n            { text: '休息', action: 'rest', id: 'home_rest' },\n            { text: '消磨时间', action: 'pass-time', id: 'home_pass' },\n        ];\n    }\n    else if (gameState.locationActions?.length)\n        actions = gameState.locationActions;\n    renderButtons(choicesContainer, actions, handleChoiceClick);\n    if (!actions.length && choicesContainer && gameState.currentScene.toLowerCase() !== '家')\n        choicesContainer.innerHTML = '<p>此地暂无行动选项。</p>';\n}\nfunction showCharacterDialogueInterface() {\n    if (choicesContainer)\n        choicesContainer.style.display = 'none';\n    if (sceneDescription)\n        sceneDescription.style.display = 'none';\n    if (dialogueArea)\n        dialogueArea.style.display = 'flex';\n    if (dialogueText)\n        dialogueText.innerHTML = `<p><strong>${gameState.currentCharacterName || '对方'}:</strong> ${gameState.characterDialogueText || '...'}</p>`;\n    renderButtons(dialogueChoicesContainer, gameState.playerReplyOptions, handleChoiceClick);\n    if (dialogueChoicesContainer) {\n        const btn = document.createElement('button');\n        btn.textContent = '结束对话';\n        btn.style.marginTop = '10px';\n        btn.dataset.action = 'action_end_dialogue';\n        btn.addEventListener('click', () => handleChoiceClick('action_end_dialogue', '结束对话'));\n        dialogueChoicesContainer.appendChild(btn);\n    }\n}\nasync function returnToPreviousSceneOrHome() {\n    let sceneContentToParse;\n    if (fullHistoryLog.length > 1) {\n        fullHistoryLog.pop();\n        sceneContentToParse = fullHistoryLog[fullHistoryLog.length - 1].sceneContent;\n        safeToastr('info', '返回上一历史状态...', '导航');\n    }\n    else {\n        safeToastr('info', '没有更多历史或仅初始状态，返回家中...', '导航');\n        const homeSceneContent = stripOuterWrappers(getCurrentSceneDataBlockForHome());\n        fullHistoryLog = [{ sceneContent: homeSceneContent }];\n        sceneContentToParse = homeSceneContent;\n    }\n    if (typeof sceneContentToParse === 'string') {\n        parseAndApplyGalgameData(sceneContentToParse);\n        if (gameState.interfaceMode === 'location')\n            showLocationSceneInterface();\n        else\n            showCharacterDialogueInterface();\n        await persistCurrentStateWithHistory();\n    }\n    else {\n        safeToastr('error', '无法恢复场景：无数据可恢复。', '导航错误');\n    }\n}\nfunction getCurrentSceneDataBlock() {\n    return wrapWithSystemTags(getCurrentSceneContent());\n}\nfunction getCurrentSceneDataBlockForHome() {\n    const homeContent = `<地点:家>\\n时间--\"${gameState.time}\"--HH:MM\\n体力--\"${gameState.energy}\"--HH:MM\\n</地点:家>`;\n    return wrapWithSystemTags(homeContent);\n}\nfunction getCurrentSceneContent() {\n    // Returns just the <地点...> or <人物...> block\n    let content = '';\n    if (gameState.interfaceMode === 'location') {\n        content += `<地点:${gameState.currentScene}>\\n`;\n        content += `时间--\"${gameState.time}\"--HH:MM\\n`;\n        content += `体力--\"${gameState.energy}\"--HH:MM\\n`;\n        gameState.otherCharactersInLocation?.forEach(char => {\n            content += `其他角色名称--\"${char}\"--HH:MM\\n`;\n        });\n        gameState.locationActions?.forEach((act, index) => {\n            content += `行动选项${String.fromCharCode(65 + index)}--\"${act.text}\"--HH:MM\\n`;\n        });\n        content += `</地点:${gameState.currentScene}>`;\n    }\n    else if (gameState.interfaceMode === 'characterDialogue' && gameState.currentCharacterName) {\n        content += `<人物:${gameState.currentCharacterName}>\\n`;\n        content += `时间--\"${gameState.time}\"--HH:MM\\n`;\n        content += `好感度--\"${gameState.characterFavorability || 0}\"--HH:MM\\n`;\n        if (gameState.characterDialogueText)\n            content += `对方的话--\"${gameState.characterDialogueText}\"--HH:MM\\n`;\n        gameState.playerReplyOptions?.forEach((opt, index) => {\n            content += `回复选项${String.fromCharCode(65 + index)}--\"${opt.text}\"--HH:MM\\n`;\n        });\n        content += `</人物:${gameState.currentCharacterName}>`;\n    }\n    else {\n        content += `<地点:家>\\n时间--\"${gameState.time}\"--HH:MM\\n体力--\"${gameState.energy}\"--HH:MM\\n</地点:家>`;\n    }\n    return content.trim();\n}\nasync function persistCurrentStateWithHistory() {\n    if (currentHostMessageId === null || typeof setChatMessages !== 'function')\n        return;\n    let persistedString = '';\n    if (fullHistoryLog.length > 1) {\n        persistedString += OLD_HISTORY_START_TAG + '\\n';\n        for (let i = 0; i < fullHistoryLog.length - 1; i++) {\n            const entry = fullHistoryLog[i];\n            let oldEntryContent = entry.sceneContent;\n            // 如果旧条目错误地包含了包裹，移除它们\n            if (oldEntryContent.startsWith('查看系统\\nmsg_start\\n')) {\n                oldEntryContent = stripOuterWrappers(oldEntryContent); // 使用已有的strip函数更安全\n            }\n            persistedString += oldEntryContent;\n            if (entry.userChoiceText) {\n                persistedString += `\\n用户点击选项--\"${entry.userChoiceText}\"--HH:MM`;\n            }\n            if (i < fullHistoryLog.length - 2) {\n                persistedString += HISTORY_SEPARATOR;\n            }\n        }\n        persistedString += '\\n' + OLD_HISTORY_END_TAG + '\\n';\n    }\n    persistedString +=\n        fullHistoryLog.length > 0\n            ? wrapWithSystemTags(fullHistoryLog[fullHistoryLog.length - 1].sceneContent)\n            : getCurrentSceneDataBlock();\n    safeToastr('info', `Galgame: 持久化状态 (ID: ${currentHostMessageId})。`, '持久化');\n    try {\n        await setChatMessages([{ message_id: currentHostMessageId, message: persistedString }], { refresh: 'affected' });\n        safeToastr('success', `Galgame: 父消息 ${currentHostMessageId} 已更新。`, '持久化');\n    }\n    catch (e) {\n        safeToastr('error', `Galgame: 更新父消息失败: ${e.message}`, '持久化错误');\n    }\n}\nasync function onMounted() {\n    console.log('[Galgame] onMounted.');\n    setTimeout(async () => {\n        ensureGlobals();\n        safeToastr('info', 'Galgame脚本初始化...', '初始化');\n        if (typeof $ !== 'function' ||\n            typeof triggerSlash !== 'function' ||\n            typeof getLastMessageId !== 'function' ||\n            typeof toastr !== 'object') {\n            safeToastr('error', '核心API未加载!', '初始化错误');\n            return;\n        }\n        statusBar = document.getElementById('status-bar');\n        timeDisplay = document.getElementById('time');\n        energyDisplay = document.getElementById('energy');\n        sceneDescription = document.getElementById('scene-description');\n        choicesContainer = document.getElementById('choices');\n        dialogueArea = document.getElementById('dialogue-area');\n        characterSprite = document.getElementById('character-sprite');\n        dialogueText = document.getElementById('dialogue-text');\n        dialogueChoicesContainer = document.getElementById('dialogue-choices');\n        try {\n            const hostId = getLastMessageId();\n            if (typeof hostId === 'number' && hostId >= 0) {\n                currentHostMessageId = hostId;\n                const initialMsgContent = await triggerSlash(`/messages ${currentHostMessageId}`);\n                if (initialMsgContent && initialMsgContent.trim() !== '') {\n                    rebuildFullHistoryLog(initialMsgContent);\n                    const latestSceneContent = fullHistoryLog.length > 0 ? fullHistoryLog[fullHistoryLog.length - 1].sceneContent : null;\n                    if (latestSceneContent) {\n                        parseAndApplyGalgameData(latestSceneContent);\n                    }\n                    else {\n                        const defaultHomeContent = stripOuterWrappers(getCurrentSceneDataBlockForHome());\n                        parseAndApplyGalgameData(defaultHomeContent);\n                        fullHistoryLog = [{ sceneContent: defaultHomeContent }];\n                    }\n                }\n                else {\n                    const defaultHomeContent = stripOuterWrappers(getCurrentSceneDataBlockForHome());\n                    parseAndApplyGalgameData(defaultHomeContent);\n                    fullHistoryLog = [{ sceneContent: defaultHomeContent }];\n                }\n            }\n            else {\n                const defaultHomeContent = stripOuterWrappers(getCurrentSceneDataBlockForHome());\n                parseAndApplyGalgameData(defaultHomeContent);\n                fullHistoryLog = [{ sceneContent: defaultHomeContent }];\n            }\n        }\n        catch (e) {\n            safeToastr('error', `获取初始数据失败: ${e.message}`, '初始数据错误');\n            const defaultHomeContent = stripOuterWrappers(getCurrentSceneDataBlockForHome());\n            parseAndApplyGalgameData(defaultHomeContent);\n            fullHistoryLog = [{ sceneContent: defaultHomeContent }];\n        }\n        if (gameState.interfaceMode === 'location')\n            showLocationSceneInterface();\n        else if (gameState.interfaceMode === 'characterDialogue')\n            showCharacterDialogueInterface();\n        else\n            showLocationSceneInterface();\n        safeToastr('success', 'Galgame初始化完成。', '初始化');\n    }, 200);\n}\ntry {\n    const initFn = () => {\n        onMounted();\n    };\n    if (document.readyState === 'complete' || document.readyState === 'interactive')\n        initFn();\n    else\n        window.addEventListener('DOMContentLoaded', initFn);\n    window.addEventListener('beforeunload', () => console.log('[Galgame] onUnmounted.'));\n}\ncatch (e) {\n    safeToastr('error', `顶层脚本错误: ${e.message}`, '脚本错误');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/galgame/index.ts\n")}},__webpack_module_cache__={};function __webpack_require__(Q){var U=__webpack_module_cache__[Q];if(void 0!==U)return U.exports;var F=__webpack_module_cache__[Q]={exports:{}};return __webpack_modules__[Q](F,F.exports,__webpack_require__),F.exports}__webpack_require__.r=Q=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(Q,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(Q,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__("./src/galgame/index.ts");</script></body>
</html>
