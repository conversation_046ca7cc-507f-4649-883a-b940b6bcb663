# 法术目标选择系统改进

## 🎯 改进目标

修复法术释放目标选择问题，提供更智能的目标选择机制：
1. **固定选项**：自己、场景中的NPC
2. **自定义输入**：用户可以输入任意目标名称
3. **调试信息**：详细的目标选择过程调试

## 🔧 主要改进

### 1. HTML结构更新

**文件**: `src/adventure_log_v3/index.html`

**改进前**:
```html
<input type="text" id="spell-target" placeholder="输入目标名称">
```

**改进后**:
```html
<select id="spell-target-type">
    <option value="self">自己</option>
    <!-- 场景NPC选项将动态添加 -->
    <option value="custom">自定义目标</option>
</select>
<div id="custom-target-input" style="display: none;">
    <input type="text" id="spell-target-custom" placeholder="输入目标名称">
</div>
```

### 2. DOM元素管理更新

**文件**: `src/adventure_log_v3/ui/domElements.ts`

**新增元素**:
- `spellTargetTypeSelect`: 目标类型选择下拉框
- `spellTargetCustomInput`: 自定义目标输入框
- `customTargetInputDiv`: 自定义输入框容器

### 3. 目标选择逻辑

**文件**: `src/adventure_log_v3/ui/render.ts`

**新增函数**:

#### `updateSpellTargetOptions()`
- 动态更新目标选择选项
- 自动检测场景中的NPC
- 为每个NPC显示血量信息

#### `handleTargetTypeChange()`
- 处理目标类型选择变化
- 显示/隐藏自定义输入框

#### `getSelectedTargetName()`
- 获取当前选择的目标名称
- 支持自己、NPC、自定义目标

### 4. 快速施放改进

**文件**: `src/adventure_log_v3/app.ts`

**改进的快速施放流程**:
```typescript
// 创建目标选择选项
const targetOptions = ['自己'];
if (hasNPCs) {
  npcsInScene.forEach(npc => {
    targetOptions.push(`${npc.name} (${npc.hp.current}/${npc.hp.max} HP)`);
  });
}
targetOptions.push('自定义目标');

// 显示选择对话框
const optionsText = targetOptions.map((option, index) => 
  `${index + 1}. ${option}`).join('\n');
const selection = prompt(`选择目标:\n${optionsText}\n\n请输入数字或直接输入目标名称:`);
```

### 5. 调试信息增强

**新增调试分类**:
- `Debug - 目标选择`: 目标选择过程
- `Debug - 创建动作`: 动作创建时的目标检测

**调试信息包括**:
- 场景NPC数量检测
- 目标类型选择变化
- 最终选择的目标名称
- 法术是否需要目标

## 🎮 用户体验改进

### 详细施放流程
1. **打开法术详情**：点击"查看详情"按钮
2. **选择目标类型**：
   - 自己
   - 场景中的NPC（显示血量）
   - 自定义目标
3. **自定义输入**：选择"自定义目标"时显示输入框
4. **施放法术**：点击"施放"按钮

### 快速施放流程
1. **点击快速施放**：直接点击"快速施放"按钮
2. **目标选择对话框**：
   - 显示编号选项列表
   - 支持数字选择或直接输入
   - 包含NPC血量信息
3. **确认施放**：自动创建施放动作

## 🐛 修复的问题

### 1. 目标检测错误
**问题**: 即使用户输入目标，调试信息仍显示"场景中无目标"
**修复**: 改进目标检测逻辑，正确区分目标来源

### 2. 目标选择不便
**问题**: 只能手动输入目标名称，容易出错
**修复**: 提供下拉选择和智能提示

### 3. NPC信息不足
**问题**: 不知道场景中有哪些NPC可以选择
**修复**: 自动列出所有NPC及其血量状态

## 🎯 调试输出示例

### 成功的目标选择流程：
```
Debug - 目标选择: 更新法术目标选择选项
Debug - 目标选择: 场景中有 2 个NPC
Debug - 目标选择: 已添加 2 个NPC目标选项
Debug - 目标选择: 目标类型选择变化: npc:goblin1
Debug - 目标选择: NPC目标名称: 哥布林战士
Debug - 创建动作: 场景检测: 有2个NPC
Debug - 创建动作: 法术是否需要目标: 是
Debug - 创建动作: 目标名称: "哥布林战士"
```

### 自定义目标流程：
```
Debug - 目标选择: 目标类型选择变化: custom
Debug - 目标选择: 显示自定义目标输入框
Debug - 目标选择: 自定义目标名称: 远处的敌人
Debug - 创建动作: 目标名称: "远处的敌人"
```

## 🔮 CSS样式改进

**文件**: `src/adventure_log_v3/index.scss`

**新增样式**:
- 目标选择区域垂直布局
- 自定义输入框特殊样式
- 焦点状态优化
- 占位符文本样式

## 📋 测试建议

### 测试场景
1. **无NPC场景**：只显示"自己"和"自定义目标"
2. **有NPC场景**：显示所有NPC选项
3. **自定义目标**：测试输入框显示/隐藏
4. **快速施放**：测试对话框目标选择

### 预期行为
- 目标选择选项根据场景动态更新
- 自定义输入框正确显示/隐藏
- 调试信息准确反映选择过程
- 最终目标名称正确传递给AI

## 🚀 下一步优化

1. **目标验证**：验证选择的目标是否存在
2. **距离检查**：检查法术射程和目标距离
3. **友敌识别**：区分友方和敌方目标
4. **批量目标**：支持群体法术的多目标选择
5. **目标记忆**：记住最近使用的目标

通过这些改进，法术目标选择系统现在更加智能、用户友好，并提供了详细的调试信息来帮助定位问题。
