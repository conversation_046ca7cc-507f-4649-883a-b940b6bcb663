/**
 * 4环法术生成器
 * 基于AI知识库生成完整的DND5e 4环法术数据
 */

const fs = require('fs');

// 4环法术数据
const LEVEL_4_SPELLS = [
  {
    name_zh: '放逐术',
    name_en: 'Banishment',
    level: 4,
    school: '防护',
    casting_time: '1 动作',
    range: '60尺',
    components: ['V', 'S', 'M (一件令目标厌恶的物品)'],
    duration: '专注，至多1分钟',
    description_short: '将目标放逐到另一个位面。',
    description_long: '你试图将一个生物送到另一个位面。目标必须成功通过一次魅力豁免，否则被放逐。如果目标原本就在其原生位面，它被送到一个无害的半位面。如果目标不是原生于此位面，它被送回原生位面。',
    save: { attribute: '魅力', effect_on_success: '无效果' },
    higher_level_cast: {
      per_slot_above_base: '每高于4环的法术位',
      effect: '额外影响一个目标'
    }
  },
  {
    name_zh: '枯萎术',
    name_en: 'Blight',
    level: 4,
    school: '死灵',
    casting_time: '1 动作',
    range: '30尺',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '吸取生命力造成黯蚀伤害。',
    description_long: '黯蚀能量涌向一个你能看见的生物，吸取其水分和活力。目标必须进行体质豁免。失败时受到8d8黯蚀伤害，成功时伤害减半。植物类生物在此豁免上具有劣势。',
    save: { attribute: '体质', effect_on_success: '伤害减半' },
    damage: '8d8',
    damage_type: '黯蚀',
    higher_level_cast: {
      per_slot_above_base: '每高于4环的法术位',
      effect: '增加1d8伤害'
    }
  },
  {
    name_zh: '混乱术',
    name_en: 'Confusion',
    level: 4,
    school: '惑控',
    casting_time: '1 动作',
    range: '90尺',
    components: ['V', 'S', 'M (三个胡桃壳)'],
    duration: '专注，至多1分钟',
    description_short: '使多个目标行为混乱。',
    description_long: '这个法术攻击并扭曲生物的心智，产生妄想并引发不受控制的行动。10尺半径球体内的每个生物必须成功通过一次感知豁免，否则在法术持续时间内无法正常行动。',
    save: { attribute: '感知', effect_on_success: '无效果' },
    area_of_effect: { type: '球形', size: '10尺半径' },
    higher_level_cast: {
      per_slot_above_base: '每高于4环的法术位',
      effect: '球体半径增加5尺'
    }
  },
  {
    name_zh: '任意门',
    name_en: 'Dimension Door',
    level: 4,
    school: '咒法',
    casting_time: '1 动作',
    range: '500尺',
    components: ['V'],
    duration: '立即',
    description_short: '瞬间传送到远处位置。',
    description_long: '你瞬间传送到射程内一个你指定的地点。你准确到达目的地，可以是你能看见的地点、能够想象的地点，或者通过距离和方向描述的地点。',
    higher_level_cast: {
      per_slot_above_base: '每高于4环的法术位',
      effect: '可以额外携带一个自愿生物'
    }
  },
  {
    name_zh: '支配野兽',
    name_en: 'Dominate Beast',
    level: 4,
    school: '惑控',
    casting_time: '1 动作',
    range: '60尺',
    components: ['V', 'S'],
    duration: '专注，至多1分钟',
    description_short: '魅惑并控制一只野兽。',
    description_long: '你试图魅惑射程内一个你能看见的野兽。目标必须成功通过一次感知豁免，否则在法术持续时间内被你魅惑。如果你与目标处于战斗中，它在豁免上具有优势。',
    save: { attribute: '感知', effect_on_success: '无效果' },
    higher_level_cast: {
      per_slot_above_base: '5环或更高法术位',
      effect: '持续时间变为8小时，无需专注'
    }
  },
  {
    name_zh: '制造术',
    name_en: 'Fabricate',
    level: 4,
    school: '变化',
    casting_time: '10分钟',
    range: '120尺',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '将原材料转化为成品。',
    description_long: '你将原材料转化为该材料制成的成品。例如，你可以将一堆木材制成木桥、将绳索制成梯子、将布料和线制成衣服。',
    higher_level_cast: {
      per_slot_above_base: '每高于4环的法术位',
      effect: '可处理的材料体积增加一倍'
    }
  },
  {
    name_zh: '火焰护盾',
    name_en: 'Fire Shield',
    level: 4,
    school: '塑能',
    casting_time: '1 动作',
    range: '自身',
    components: ['V', 'S', 'M (一点磷或萤火虫)'],
    duration: '10分钟',
    description_short: '火焰或寒冷护盾保护并反击敌人。',
    description_long: '薄薄一层火焰围绕着你的身体，散发出明亮光芒或微光。你选择温暖护盾或寒冷护盾。温暖护盾给予你对寒冷伤害的抗性，寒冷护盾给予你对火焰伤害的抗性。',
    damage: '2d8',
    damage_type: '火焰或寒冷'
  },
  {
    name_zh: '行动自如',
    name_en: 'Freedom of Movement',
    level: 4,
    school: '防护',
    casting_time: '1 动作',
    range: '触及',
    components: ['V', 'S', 'M (一条皮带)'],
    duration: '1小时',
    description_short: '免疫移动限制效果。',
    description_long: '你触碰一个自愿生物。在法术持续时间内，目标的移动不受困难地形影响，法术和其他魔法效果既不能降低目标的速度，也不能使目标麻痹或束缚。',
    higher_level_cast: {
      per_slot_above_base: '每高于4环的法术位',
      effect: '额外影响一个目标'
    }
  },
  {
    name_zh: '巨虫术',
    name_en: 'Giant Insect',
    level: 4,
    school: '变化',
    casting_time: '1 动作',
    range: '30尺',
    components: ['V', 'S'],
    duration: '专注，至多10分钟',
    description_short: '将昆虫变为巨型版本。',
    description_long: '你将最多十只蜈蚣、三只蜘蛛、五只黄蜂或一只蝎子变为巨型版本，持续法术时间。蜈蚣变为巨型蜈蚣，蜘蛛变为巨型蜘蛛，黄蜂变为巨型黄蜂，蝎子变为巨型蝎子。',
    higher_level_cast: {
      per_slot_above_base: '每高于4环的法术位',
      effect: '可影响的昆虫数量翻倍'
    }
  },
  {
    name_zh: '高等隐形术',
    name_en: 'Greater Invisibility',
    level: 4,
    school: '幻术',
    casting_time: '1 动作',
    range: '触及',
    components: ['V', 'S'],
    duration: '专注，至多1分钟',
    description_short: '隐形且攻击不会破除效果。',
    description_long: '你或你触碰的生物变为隐形，直到法术结束。任何目标穿戴或携带的东西只要在目标身上就也会隐形。与普通隐形术不同，攻击或施法不会结束此法术。'
  },
  {
    name_zh: '守护者',
    name_en: 'Guardian of Faith',
    level: 4,
    school: '咒法',
    casting_time: '1 动作',
    range: '30尺',
    components: ['V'],
    duration: '8小时',
    description_short: '召唤守护灵体保护区域。',
    description_long: '一个大型幽灵般的守护者出现并悬浮在射程内一个你指定的空间中，直到法术结束。守护者占据该空间，是模糊半透明的，持有一把符合你信仰的武器。',
    damage: '20',
    damage_type: '光耀',
    area_of_effect: { type: '守护', size: '10尺半径' }
  },
  {
    name_zh: '幻景',
    name_en: 'Hallucinatory Terrain',
    level: 4,
    school: '幻术',
    casting_time: '10分钟',
    range: '300尺',
    components: ['V', 'S', 'M (一块石头、一根树枝和一点绿色植物)'],
    duration: '24小时',
    description_short: '使地形看起来像不同的地形。',
    description_long: '你使射程内一片150尺见方区域的自然地形在视觉上看起来、听起来和闻起来像某种其他自然地形。因此，开阔的田野或道路可能看起来像沼泽、山丘、裂隙或其他困难或无法通过的地形。',
    area_of_effect: { type: '方形', size: '150尺见方' }
  },
  {
    name_zh: '冰风暴',
    name_en: 'Ice Storm',
    level: 4,
    school: '塑能',
    casting_time: '1 动作',
    range: '300尺',
    components: ['V', 'S', 'M (一撮尘土和几滴水)'],
    duration: '立即',
    description_short: '冰雹风暴攻击大范围敌人。',
    description_long: '冰雹大小的冰块从天而降，砸向以射程内一点为中心、半径20尺、高40尺的圆柱形区域。区域内的每个生物必须进行敏捷豁免。失败时受到2d8钝击伤害和4d6寒冷伤害，成功时伤害减半。',
    save: { attribute: '敏捷', effect_on_success: '伤害减半' },
    damage: '2d8钝击 + 4d6寒冷',
    damage_type: '钝击和寒冷',
    area_of_effect: { type: '圆柱', size: '半径20尺，高40尺' },
    higher_level_cast: {
      per_slot_above_base: '每高于4环的法术位',
      effect: '钝击伤害增加1d8'
    }
  },
  {
    name_zh: '定位生物',
    name_en: 'Locate Creature',
    level: 4,
    school: '预言',
    casting_time: '1 动作',
    range: '自身',
    components: ['V', 'S', 'M (一点来自寻血猎犬的毛发)'],
    duration: '专注，至多1小时',
    description_short: '感知特定生物的位置。',
    description_long: '描述或说出一个你熟悉的特定生物的名字。你感知到最近的该类生物的方向，如果它在1000尺内。如果该生物在移动，你知道它移动的方向。'
  },
  {
    name_zh: '变形术',
    name_en: 'Polymorph',
    level: 4,
    school: '变化',
    casting_time: '1 动作',
    range: '60尺',
    components: ['V', 'S', 'M (一个蚕茧)'],
    duration: '专注，至多1小时',
    description_short: '将生物变形为野兽。',
    description_long: '这个法术将一个生物变形为一个新的形态。一个不愿意的生物必须进行感知豁免来避免效果。法术对变形者无效。变形将目标的游戏数据替换为所选野兽的数据。',
    save: { attribute: '感知', effect_on_success: '无效果（不愿意的目标）' },
    higher_level_cast: {
      per_slot_above_base: '每高于4环的法术位',
      effect: '可选择更高挑战等级的野兽形态'
    }
  },
  {
    name_zh: '石塑术',
    name_en: 'Stone Shape',
    level: 4,
    school: '变化',
    casting_time: '1 动作',
    range: '触及',
    components: ['V', 'S', 'M (软粘土)'],
    duration: '立即',
    description_short: '重塑石头的形状。',
    description_long: '你触碰一个石质物体，其任何维度都不超过5尺，并将其塑造成你想要的任何形状。例如，你可以将一块大石头塑造成武器、雕像或箱子，或在石墙上开一个小通道。'
  },
  {
    name_zh: '石肤术',
    name_en: 'Stoneskin',
    level: 4,
    school: '防护',
    casting_time: '1 动作',
    range: '触及',
    components: ['V', 'S', 'M (价值100gp的钻石尘，法术消耗此材料)'],
    duration: '专注，至多1小时',
    description_short: '获得对物理伤害的抗性。',
    description_long: '这个法术将愿意的生物的血肉变得像石头一样坚硬。直到法术结束，目标对非魔法的钝击、穿刺和挥砍伤害具有抗性。'
  },
  {
    name_zh: '火墙术',
    name_en: 'Wall of Fire',
    level: 4,
    school: '塑能',
    casting_time: '1 动作',
    range: '120尺',
    components: ['V', 'S', 'M (一小片磷)'],
    duration: '专注，至多1分钟',
    description_short: '创造火焰墙壁伤害敌人。',
    description_long: '你在射程内一个坚实表面上创造一堵火墙。你可以创造一堵长60尺、高20尺、厚1尺的直火墙，或者一堵直径20尺、高20尺、厚1尺的环形火墙。',
    damage: '5d8',
    damage_type: '火焰',
    save: { attribute: '敏捷', effect_on_success: '伤害减半' },
    area_of_effect: { type: '墙壁', size: '长60尺高20尺或直径20尺环形' },
    higher_level_cast: {
      per_slot_above_base: '每高于4环的法术位',
      effect: '增加1d8伤害'
    }
  },
  {
    name_zh: '死亡守护',
    name_en: 'Death Ward',
    level: 4,
    school: '防护',
    casting_time: '1 动作',
    range: '触及',
    components: ['V', 'S'],
    duration: '8小时',
    description_short: '保护免受死亡和黯蚀伤害。',
    description_long: '你触碰一个生物并赋予它一定的生命力保护。第一次目标的生命值降至0时，目标的生命值变为1，法术结束。法术也能保护目标免受任何会立即杀死目标的效果。'
  },
  {
    name_zh: '预言术',
    name_en: 'Divination',
    level: 4,
    school: '预言',
    casting_time: '1 动作',
    range: '自身',
    components: ['V', 'S', 'M (价值25gp的熏香，法术消耗此材料)'],
    duration: '立即',
    description_short: '获得关于未来的神谕。',
    description_long: '你的魔法和神明接触，询问关于特定目标、事件或活动的问题，这些将在7天内发生。DM提供一个真实的回答，可能是简短的短语、神秘的韵律或预兆。'
  }
];

/**
 * 生成4环法术世界书
 */
function generateLevel4WorldBook() {
  const worldBook = {
    entries: {
      5004: {
        uid: 5004,
        key: ["AI_LEVEL_4_SPELLS", "AI四环法术", "DND5E_AI_LEVEL_4", "AI_SPELLS_LEVEL_4"],
        keysecondary: [],
        comment: `AI生成的DND5e四环法术完整数据 (${LEVEL_4_SPELLS.length}个法术)`,
        content: JSON.stringify(LEVEL_4_SPELLS, null, 2),
        constant: true,
        vectorized: false,
        selective: true,
        selectiveLogic: 0,
        addMemo: true,
        order: 100,
        position: 0,
        disable: false,
        excludeRecursion: false,
        preventRecursion: false,
        delayUntilRecursion: false,
        probability: 100,
        useProbability: true,
        depth: 4,
        group: "",
        groupOverride: false,
        groupWeight: 100,
        scanDepth: null,
        caseSensitive: null,
        matchWholeWords: null,
        useGroupScoring: null,
        automationId: "",
        role: null,
        sticky: 0,
        cooldown: 0,
        delay: 0,
        displayIndex: 5004
      }
    }
  };
  
  const outputPath = 'AI_DND5e_Level4_Complete.json';
  fs.writeFileSync(outputPath, JSON.stringify(worldBook, null, 2), 'utf-8');
  console.log(`生成4环法术世界书: ${outputPath} (${LEVEL_4_SPELLS.length}个法术)`);
  
  return LEVEL_4_SPELLS;
}

// 执行生成
if (require.main === module) {
  console.log('=== 4环法术生成器 ===');
  generateLevel4WorldBook();
  console.log('4环法术世界书生成完成！');
}

module.exports = { LEVEL_4_SPELLS, generateLevel4WorldBook };
