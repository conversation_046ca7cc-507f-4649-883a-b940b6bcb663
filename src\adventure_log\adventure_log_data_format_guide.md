# 冒险日志 - 数据格式指南

本文档详细定义了“冒险日志”功能所使用的各种数据格式，包括持久化存储结构以及AI在游戏各阶段应返回的核心数据块格式。

## 一、整体持久化数据结构

SillyTavern消息楼层中存储的完整数据将包含以下几个主要部分：

```
<!-- PLAYER_STATE_START -->
{
  "name": "艾拉",
  "race": "精灵",
  "class": "游侠",
  "level": 3,
  "exp": 2700,
  "hp": {"current": 25, "max": 30},
  "ac": 14,
  "currency": {"gold": 50, "silver": 10, "copper": 0},
  "attributes": {
    "strength": 12, "dexterity": 16, "constitution": 14,
    "intelligence": 10, "wisdom": 13, "charisma": 8
  },
  "modifiers": {
    "strength_mod": 1, "dexterity_mod": 3, "constitution_mod": 2,
    "intelligence_mod": 0, "wisdom_mod": 1, "charisma_mod": -1
  },
  "proficiencies": ["轻甲", "中甲", "盾牌", "简易武器", "军用武器", "巧手", "隐匿", "生存"],
  "skills": {
    "巧手": 5,
    "隐匿": 5,
    "生存": 3
  },
  "spellSlots": {"1": {"current": 3, "max": 3}},
  "equippedSpells": [
    {"name": "猎人印记", "level": 1, "source": "职业特性"},
    {"name": "纠缠术", "level": 1, "source": "习得"}
  ],
  "equipment": [
    {"name": "短弓", "type": "武器", "equipped": true, "details": "伤害1d6穿刺"},
    {"name": "皮甲", "type": "护甲", "equipped": true, "details": "AC 11 + 敏捷调整值"},
    {"name": "箭袋 (20支箭)", "type": "弹药"},
    {"name": "匕首", "type": "武器"}
  ],
  "inventory": [
    {"name": "治疗药水", "quantity": 1, "description": "恢复2d4+2生命值"},
    {"name": "口粮", "quantity": 3, "description": "一天的食物"},
    {"name": "火把", "quantity": 5}
  ],
  "activeQuests": ["调查神秘的森林神庙"],
  "exhaustion": 0,
  "time": "第一天 午后"
}
<!-- PLAYER_STATE_END -->

<!-- ADVENTURE_LOG_HISTORY_START -->
<场景:冒险启程>
场景描述--"你站在一条尘土飞扬的道路上，前方是未知的冒险。你的行囊已经备好，心中充满了对远方的向往和一丝不安。太阳刚刚升起，照亮了远处的森林和若隐若现的山脉。"--HH:MM
当前地点--"旅途的起点"--HH:MM
生命值--"30/30"--HH:MM
时间--"第一天 清晨"--HH:MM
行动选项A--"向森林深处进发。"--HH:MM
行动选项B--"沿着道路向附近的村庄走去。"--HH:MM
行动选项C--"检查一下行囊，确保一切准备妥当。"--HH:MM
</场景:冒险启程>
[玩家选择文本]: "向森林深处进发。"
<!-- ENTRY_SEP -->
<场景:幽静林缘>
场景描述--"你依从心中的冒险冲动，迈步踏入了幽静森林的边缘。高大茂密的树木遮蔽了大部分阳光，只有缕缕光斑穿透枝叶的缝隙，在覆盖着厚厚落叶的地面上摇曳。空气变得凉爽而湿润，带着泥土、腐木与不知名野花的混合芬芳。道路的痕迹在此处变得模糊，一条依稀可辨的小径蜿蜒向前，没入更深、更暗的林中。四周异常安静，只有你的脚步踩在枯叶上发出的沙沙声，以及从森林深处隐约传来的一两声清脆而奇特的鸟鸣，似在引路，又似在警告。"--HH:MM
当前地点--"幽静林缘"--HH:MM
生命值--"30/30"--HH:MM
时间--"第一天 清晨"--HH:MM
提示信息--"森林中充满了未知，谨慎和敏锐的观察将是你最好的伙伴。"--HH:MM
行动选项A--"沿着那条依稀可辨的小径谨慎地深入森林，仔细观察周围的动静。"--HH:MM
行动选项B--"停下脚步，集中精神仔细聆听那奇特的鸟鸣，尝试判断其来源和是否带有特殊含义。"--HH:MM
行动选项C--"你决定不循常规路径，而是选择一个方向，拨开挡路的灌木和枝条，直接向森林深处探索。"--HH:MM
</场景:幽静林缘>
<!-- ADVENTURE_LOG_HISTORY_END -->

查看系统
msg_start
<!-- 当前AI生成的场景数据块，见下方详细格式 -->
msg_end
关闭系统
```

**关键组成部分说明:**
*   **`<!-- PLAYER_STATE_START -->` 和 `<!-- PLAYER_STATE_END -->`**:
    *   包裹一个JSON对象，该对象代表玩家角色的完整状态。
    *   此JSON对象应包含角色名、种族、职业、等级、经验值、生命值（当前/最大）、护甲等级（AC）、货币、各项属性值及其调整值、熟练项、技能（尤其是熟练的技能及其最终调整值）、法术环位、已装备/已习得的法术、装备、物品栏、当前任务、力竭等级以及游戏内总体时间等。
    *   此部分数据由客户端维护，并在必要时（如AI通过特定指令请求更新）进行修改。AI通常不直接修改此块，而是通过返回特定的变量更新指令。
*   **`<!-- ADVENTURE_LOG_HISTORY_START -->` 和 `<!-- ADVENTURE_LOG_HISTORY_END -->`**:
    *   包裹历史冒险记录。
    *   每条历史记录由一个完整的、纯净的场景数据块（例如 `<场景:xxx>...</场景:xxx>`）和可选的玩家在该场景做出的选择文本（`[玩家选择文本]: "选择内容"`）组成。
    *   历史条目之间使用 `<!-- ENTRY_SEP -->` 分隔。
*   **`查看系统 msg_start ... msg_end 关闭系统`**:
    *   这是AI最新一次回复中包含当前游戏界面所需数据的部分。
    *   `msg_start` 和 `msg_end` 之间的内容是核心数据块。

## 二、AI返回的核心数据块格式 (位于 `msg_start` 和 `msg_end` 之间)

AI的每一次回复，在 `msg_start` 和 `msg_end` 之间，将包含以下几种核心数据块之一。所有数据行严格遵循 `键--"值"--HH:MM` 格式。

### 1. `<场景:场景标题>` - 地点探索与通用事件

用于描述环境、非战斗遭遇、谜题、陷阱等。

**结构示例:**
```
<场景:古老神庙前院>
场景描述--"经过一番跋涉，你来到了一座被藤蔓覆盖的古老神庙前院。空气中弥漫着潮湿的泥土气息和淡淡的焚香余味。一座巨大的石制祭坛立于庭院中央，上面刻满了难以辨认的符文。祭坛前方似乎有一个沉重的石板，像是入口。四周散落着碎石，一条小路通向神庙深处。"--HH:MM
当前地点--"古老神庙前院"--HH:MM
时间--"第一天 黄昏"--HH:MM
生命值--"25/30"--HH:MM
提示信息--"祭坛上的符文似乎与某种古老的仪式有关。石板看起来异常沉重。"--HH:MM
提示信息--"你注意到石板边缘有微小的刮痕，似乎经常被移动。"--HH:MM
行动选项A--"仔细研究祭坛上的符文[DC15 智力(历史)]。"--HH:MM
行动选项B--"尝试推动石板[DC18 力量(运动)]。"--HH:MM
行动选项C--"沿着小路向神庙深处探索。"--HH:MM
行动选项D--"检查四周的碎石，看是否有隐藏的线索[DC13 感知(调查)]。"--HH:MM
</场景:古老神庙前院>
```

**关键键名:**
*   `场景描述`: 对环境、事件的详细文字描述。
*   `当前地点`: 玩家当前所处的具体位置名称。
*   `时间`: 游戏内时间（此时间主要用于AI追踪叙事节奏，不一定与 `PLAYER_STATE` 中的总体时间完全同步，但应大体一致）。
*   `生命值`: 玩家当前/最大生命值（主要用于AI判断，实际HP权威来源是 `PLAYER_STATE`）。
*   `提示信息`: DM给玩家的额外信息、观察到的细节、规则提示、检定结果等。可多条。
*   `行动选项[A-Z]`: 玩家可选择的行动。
    *   **包含检定**: 若选项涉及检定，格式为 `行动选项X--"描述[DC{数值} {属性}({技能})]..."--HH:MM`。客户端将解析此信息进行本地掷骰。

### 2. `<NPC:角色名称>` - 非战斗NPC交互

用于处理与NPC的对话和非战斗互动。

**结构示例:**
```
<NPC:老者寻路者>
场景描述--"你向森林中遇到的神秘老者询问关于神庙的事情。他深邃的眼神似乎看透了你的心思。"--HH:MM
当前地点--"幽静林缘"--HH:MM
时间--"第一天 午后"--HH:MM
好感度--"中立"--HH:MM
对方的话--"那座神庙？年轻人，那可不是什么好去处。传说中，它守护着一个古老的秘密，但也充满了危险。你为何对它感兴趣？"--HH:MM
回复选项A--"我只是个好奇的冒险者，想探索未知的地方。"--HH:MM
回复选项B--"我听说那里有宝藏，想去碰碰运气。"--HH:MM
回复选项C--"我在追寻一个与神庙有关的传说，希望能找到答案。[DC12 魅力(说服)]"--HH:MM
回复选项D--"（结束对话）多谢您的提醒，我会小心的。"--HH:MM
</NPC:老者寻路者>
```
**关键键名:**
*   `场景描述`: (可选) 对当前对话环境或NPC出场时的简要描述。
*   `好感度`: (可选) 玩家与此NPC的好感度状态。
*   `对方的话`: NPC说出的对话内容。
*   `回复选项[A-Z]`: 玩家可选择的回复。同样可以包含检定信息。

### 3. `<战斗:战斗场景描述或ID>` - 战斗遭遇

专门用于处理战斗流程。

**结构示例 (战斗开始):**
```
<战斗:遭遇地精巡逻队>
场景描述--"你们的动静惊动了一队地精巡逻兵！三只手持生锈弯刀的地精从树丛后跳了出来，发出吱吱呀呀的威吓声，眼中闪烁着凶光。"--HH:MM
当前地点--"森林小径"--HH:MM
时间--"第二天 上午"--HH:MM
生命值--"20/30"--HH:MM
敌人信息--"地精A:HP10/10:AC13:意图-冲锋并攻击"--HH:MM
敌人信息--"地精B:HP8/8:AC13:意图-投掷标枪"--HH:MM
敌人信息--"地精C:HP10/10:AC13:意图-躲藏并准备偷袭"--HH:MM
提示信息--"战斗开始！请选择你的行动。"--HH:MM
行动选项A--"使用短弓攻击地精A。"--HH:MM
行动选项B--"施放【纠缠术】尝试困住所有地精。"--HH:MM
行动选项C--"尝试与地精沟通，威吓它们离开。[DC17 魅力(威吓)]"--HH:MM
行动选项D--"后退并采取防御姿态。"--HH:MM
</战斗:遭遇地精巡逻队>
```

**结构示例 (战斗进行中 - AI在玩家行动后返回):**
```
<战斗:与地精战斗中>
场景描述--"你一箭射中了地精A的肩膀，它痛得尖叫！地精B则向你投出了标枪，险些击中！地精C似乎在寻找偷袭的机会。"--HH:MM
当前地点--"森林小径"--HH:MM
时间--"第二天 上午"--HH:MM
生命值--"18/30"--HH:MM
敌人信息--"地精A:HP5/10:AC13:意图-疯狂反击"--HH:MM
敌人信息--"地精B:HP8/8:AC13:意图-再次投掷标枪"--HH:MM
敌人信息--"地精C:HP10/10:AC13:意图-尝试绕后"--HH:MM
玩家行动结果--"你的弓箭对地精A造成了5点穿刺伤害。"--HH:MM
敌人行动--"地精B的标枪攻击:投骰1d20[12]+调整值[3]=15 vs 你的AC[14] -> 命中！伤害1d6[2]点穿刺伤害。"--HH:MM
提示信息--"轮到你了！"--HH:MM
行动选项A--"继续攻击地精A。"--HH:MM
行动选项B--"移动到地精C可能出现的位置，准备迎击。"--HH:MM
行动选项C--"施放【治疗真言】恢复少量生命。"--HH:MM
</战斗:与地精战斗中>
```

**关键键名:**
*   `敌人信息`: 格式 `名称:当前HP/最大HP:AC{数值}:意图-{描述}`。可多条。
*   `玩家行动结果`: (可选) 对玩家上一轮行动的简要文字描述。
*   `敌人行动`: (可选) 描述敌人本轮的行动。**必须提供足够信息供客户端进行本地计算或验证**。
    *   **格式示例**: `敌人行动--"地精A的爪击:攻击投骰1d20[{投骰原始值}]+调整值[{攻击调整值}]={最终攻击骰} vs 你的AC[{玩家AC}] -> {命中/失误}！伤害{伤害骰}[{伤害投骰结果}]+调整值[{伤害调整值}]={最终伤害值}点{伤害类型}伤害。"--HH:MM`
    *   客户端解析此字符串，提取攻击是否命中、伤害数值和类型，并更新玩家HP。
*   `战斗结束信息`: (可选, 在战斗结束时) `战斗结束信息--"所有地精都被击败了！你获得了15点经验值和一些零碎的铜币。"--HH:MM`

### 4. `<系统:消息类型>` - 状态更新、规则应用、奖励等

用于传递不直接构成场景描述或对话，但影响游戏状态或提供重要信息的系统级消息。

**结构示例 (获得物品):**
```
<系统:物品获取>
内容--"你在地精的残骸上找到了一个破旧的钱袋和一把看起来还算锋利的匕首。"--HH:MM
获得物品--"匕首 x1"--HH:MM
获得金钱--"铜币 +12"--HH:MM
提示信息--"你的物品栏已更新。"--HH:MM
时间--"第二天 上午"--HH:MM
行动选项A--"继续前进。"--HH:MM
行动选项B--"在此地稍作休息。"--HH:MM
</系统:物品获取>
```

**结构示例 (变量直接更新指令 - 由AI发起):**
```
<系统:变量更新>
内容--"你喝下了治疗药水，感到一股暖流涌遍全身。"--HH:MM
变量更新--"玩家:hp.current:增加:7"--HH:MM
变量更新--"玩家:inventory.治疗药水.quantity:减少:1"--HH:MM
提示信息--"你的生命值已恢复，药水已消耗。"--HH:MM
行动选项A--"感觉好多了，继续前进！"--HH:MM
</系统:变量更新>
```
**关键键名:**
*   `内容`: 对系统消息的文字描述。
*   `获得物品`, `获得金钱`, `任务更新`, `检定要求` 等: 具体的操作指令和数据。
*   `变量更新`: 格式 `对象标识:属性路径:操作类型:值`。
    *   `对象标识`: 目前主要是 `玩家`，指向 `PLAYER_STATE`。
    *   `属性路径`: 使用点`.`分隔的路径，例如 `hp.current`, `currency.gold`, `inventory.治疗药水.quantity` (对于物品栏中的特定物品数量)。
    *   `操作类型`: `增加`, `减少`, `设置`。
    *   `值`: 要操作的数值或字符串。
    *   客户端需要解析此指令来更新 `PLAYER_STATE`。

## 三、数据行通用规则

*   **行尾标记**: 所有结构化数据行（`键--"值"`）**必须**以 `--HH:MM` 结尾。
*   **引号**: 值部分如果包含文本，应使用标准双引号 `""` 包裹。`--HH:MM` 标记必须在内容引号的外部。
*   **换行**: 如果值需要在UI上显示多行，请在文本内部使用 `<br>` HTML标签。

此数据格式指南旨在确保AI与客户端之间信息传递的准确性和一致性，为实现复杂的D&D游戏逻辑提供支持。
