body {
    margin: 0;
    font-family: sans-serif;
    background-color: #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh; // 允许页面在内容不足时也能垂直居中，但主要界面元素不依赖它
}

#galgame-container {
    width: 90vw; // 基础宽度，可以根据需要调整
    max-width: 600px; // 最大宽度限制，避免在超大屏幕上过宽
    aspect-ratio: 9 / 16; // 保持宽高比，模拟手机屏幕
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden; // 防止内容溢出
    position: relative; // 用于内部绝对定位元素的参考
}

#status-bar {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    background-color: #333;
    color: #fff;
    font-size: 0.9em;
    align-items: center; // Vertically align items in status bar

    #time, #energy {
        padding: 5px 10px;
    }
    #character-info-ingame {
        flex-grow: 1; // Allow it to take available space
        text-align: center; // Center the character info text
        padding: 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis; // Add ellipsis if text is too long
    }
}

#scene-description {
    padding: 20px;
    text-align: center;
    flex-grow: 1; // 占据剩余空间
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    p {
        margin: 0;
        font-size: 1.1em;
    }
}

#choices {
    display: flex;
    flex-direction: column;
    padding: 15px;
    gap: 10px; // 按钮之间的间距

    button {
        padding: 12px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 1em;
        transition: background-color 0.3s;

        &:hover {
            background-color: #0056b3;
        }
    }
}

#dialogue-area {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.95); // 半透明背景，覆盖在场景描述之上
    display: flex;
    flex-direction: column;
    padding: 15px;
    box-sizing: border-box; // 确保 padding 不会增加总宽高

    #character-sprite {
        width: 100%;
        aspect-ratio: 16 / 9; // 精灵图的宽高比
        background-color: #eee; // 占位符颜色
        background-size: cover;
        background-position: center;
        margin-bottom: 15px;
        border-radius: 5px;
    }

    #dialogue-text {
        background-color: #f9f9f9;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 15px;
        flex-grow: 1;
        overflow-y: auto; // 如果对话内容过多，允许滚动
        font-size: 1em;
        line-height: 1.6;
    }

    #dialogue-choices {
        display: flex;
        flex-direction: column;
        gap: 10px;

        button {
            padding: 10px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            transition: background-color 0.3s;

            &:hover {
                background-color: #1e7e34;
            }
        }
    }
}
