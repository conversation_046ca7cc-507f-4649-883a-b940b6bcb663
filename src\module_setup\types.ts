// --- JSON Schema Interfaces ---
export interface NPCProfile {
  name: string;
  role: string;
  description: string;
  motivation?: string;
}

export interface LocationProfile {
  name: string;
  type: string;
  description: string;
  pointsOfInterest?: string[];
}

export interface ModuleCreationData {
  title: string;
  narrativeContent: string;
  themes?: string[];
  dmNotes?: string;
}

export interface AttributeData {
  base: number;
  race_bonus: number;
  modifier_bonus: number;
  final: number;
  mod: number;
}

export interface SkillData {
  name: string;
  proficient: boolean;
  attribute: string; // e.g., "strength", "dexterity"
  modifierValue: number; // User-entered modifier for this skill
  finalValue: number; // Calculated final value
}

export interface CharacterGenerationData {
  name: string;
  race: string;
  class: string;
  level: number;
  attributes: {
    strength: AttributeData;
    dexterity: AttributeData;
    constitution: AttributeData;
    intelligence: AttributeData;
    wisdom: AttributeData;
    charisma: AttributeData;
  };
  hp?: { current: number; max: number };
  ac?: number;
  spellcastingAbility?: 'INT' | 'WIS' | 'CHA' | 'STR' | 'DEX' | 'CON' | string;
  alignment?: string;
  background?: string;
  personalityTraits?: string;
  ideals?: string;
  bonds?: string;
  flaws?: string;
  appearance?: string;
  story?: string;
  skills: SkillData[]; // Updated to store structured skill data
  proficiencies?: string[]; // For tools, weapons, armor, languages, saving throws (non-skill profs)
  equippedSpells?: { name: string; level: number; source?: string }[];
  spellSlots?: Record<string, { current: number; max: number }>;
  equipment?: { name: string; type: string; equipped?: boolean; details?: string }[];
  inventory?: { name: string; quantity: number; description?: string }[];
  currency?: { gold: number; silver: number; copper: number };
  exp?: number;
  age?: string;
  gender?: string;
  faith?: string;
  height?: string;
  weight?: string;
  subclass?: string;
  toolProficienciesText?: string;
}

export interface AIResponseFormat<T> {
  requestType: 'moduleCreation' | 'characterGeneration';
  data: T;
  error?: string;
}
