# 冒险日志 - AI提示词更新计划 (玩家变量处理)

## 1. 目标

更新 `src/adventure_log/adventure_log_ai_prompts.md` 文件，以规范化AI在处理涉及玩家角色状态变量（如经验值、物品、货币、属性、生命值等）变化时的输出格式。目标是让AI返回的特定指令能够被客户端脚本准确解析，并用于更新顶层的 `PLAYER_STATE` JSON对象。

## 2. 当前问题

目前 `adventure_log_ai_prompts.md` 中对玩家变量更新的指导不够明确和统一。例如，AI可能以纯文本形式返回经验值获取信息（如 `系统消息--"战斗结束，你获得了 100 点经验值。"HH:MM`），这使得客户端难以直接提取数值并更新 `PLAYER_STATE` 中的 `exp` 字段。

此外，在 `adventure_log_data_format_guide.md` 中虽然定义了 `<系统:变量更新>` 块和 `变量更新--"{对象标识}:{属性路径}:{操作类型}:{值}"--HH:MM` 的格式，但在 `adventure_log_ai_prompts.md` 中并未充分强调其使用场景和具体细节，特别是针对经验、物品、货币等常见变量。

## 3. 提示词更新方案

将在 `adventure_log_ai_prompts.md` 中增加或修改以下部分：

### 3.1. 强化 `<系统:变量更新>` 块的使用

在“全局AI输出格式核心规范”或新增一个专门的“玩家状态变量更新规范”部分，明确指示AI：

*   **何时使用**: 当游戏事件导致玩家角色的任何可追踪状态发生变化时（例如：获得/失去经验、金钱、物品；HP/MP的增减；属性的临时或永久改变；任务状态变化；力竭等级变化等）。
*   **强制格式**: 严格要求AI使用 `<系统:变量更新>` 作为主要数据块标签，并在内部使用一个或多个 `变量更新--"{对象标识}:{属性路径}:{操作类型}:{值}"--HH:MM` 数据行来描述每一个具体的变量变化。

### 3.2. 详细定义 `变量更新` 指令的各个部分

*   **`{对象标识}`**:
    *   目前主要为 `玩家` (或 `player`)，指代 `PLAYER_STATE` 对象。
    *   未来可扩展至其他对象，如 `NPC状态` 或 `环境状态`。
*   **`{属性路径}`**:
    *   使用点 `.` 分隔的JSON路径，精确指向 `PLAYER_STATE` 中的目标字段。
    *   **示例路径**:
        *   经验值: `exp`
        *   货币: `currency.gold`, `currency.silver`, `currency.copper`
        *   生命值: `hp.current`, `hp.max`
        *   属性基础值: `attributes.strength.base`, `attributes.dexterity.final` (注意区分base, final等)
        *   物品栏 (针对特定物品的数量): `inventory.{物品精确名称}.quantity` (这需要客户端在解析时能够处理物品名称中的空格或特殊字符，或者AI返回的物品名称需要与 `PLAYER_STATE` 中的键完全一致。更好的方式是针对物品栏设计更专门的操作类型，见下文。)
        *   任务列表: `activeQuests` (可能需要 `add` 或 `remove` 操作)
        *   力竭等级: `exhaustion`
*   **`{操作类型}`**:
    *   `设置`: 将属性路径指向的值直接设置为 `{值}`。例如 `exp:设置:150`。
    *   `增加`: 将属性路径指向的数值增加 `{值}`。例如 `currency.gold:增加:20`。
    *   `减少`: 将属性路径指向的数值减少 `{值}`。例如 `hp.current:减少:5`。
    *   **新增针对列表/数组的操作类型**:
        *   `添加元素`: 向数组末尾添加一个元素。例如 `activeQuests:添加元素:"寻找神秘的红宝石"`。
        *   `移除元素`: 从数组中移除一个特定值的元素。例如 `activeQuests:移除元素:"调查废弃的哨塔"`。
        *   `更新元素`: (较复杂，初期可不实现) 更新数组中符合特定条件的元素。
    *   **新增针对物品的操作类型 (替代直接操作 `inventory.{物品名}.quantity`)**:
        *   `物品获得`: `inventory:物品获得:{"name": "治疗药水", "quantity": 2, "description": "恢复少量生命"}` (值为一个JSON对象字符串，包含物品的完整信息。客户端解析后会查找现有物品并增加数量，或添加新物品。)
        *   `物品失去`: `inventory:物品失去:{"name": "火把", "quantity": 1}` (值为一个JSON对象字符串，至少包含名称和数量。客户端解析后会减少指定物品的数量，数量为0则移除该物品。)
*   **`{值}`**:
    *   根据操作类型和属性路径，可以是数字、字符串或JSON字符串。
    *   对于 `物品获得` 和 `物品失去`，值应为一个JSON字符串，描述物品的属性（至少包含 `name` 和 `quantity`）。

### 3.3. 结合场景的示例

在提示词文档中提供清晰的示例，说明AI应如何在不同情境下返回这些变量更新指令。

**示例1: 战斗胜利后获得经验和金币**
```
查看系统
msg_start
<系统:战斗奖励>
内容--"你成功击败了哥布林斥候！"--HH:MM
变量更新--"玩家:exp:增加:75"--HH:MM
变量更新--"玩家:currency.copper:增加:12"--HH:MM
提示信息--"你获得了75点经验值和12枚铜币。"HH:MM
行动选项A--"搜刮战利品。"HH:MM
行动选项B--"继续前进。"HH:MM
</系统:战斗奖励>
msg_end
关闭系统
```

**示例2: 找到并获得物品**
```
查看系统
msg_start
<系统:物品发现>
内容--"在箱子的角落，你发现了一瓶闪烁着微弱红光的治疗药水。"--HH:MM
变量更新--"玩家:inventory:物品获得:{\"name\": \"治疗药水(次级)\", \"quantity\": 1, \"description\": \"恢复2d4+2生命值\"}"--HH:MM
提示信息--"你将[治疗药水(次级) x1]放入了背包。"HH:MM
行动选项A--"继续搜索箱子。"HH:MM
行动选项B--"离开此地。"HH:MM
</系统:物品发现>
msg_end
关闭系统
```

**示例3: 完成任务，更新任务列表并获得奖励**
```
查看系统
msg_start
<系统:任务完成>
内容--"村民对你找回丢失的货物感激不尽。"--HH:MM
变量更新--"玩家:activeQuests:移除元素:\"找回村民丢失的货物\""--HH:MM
变量更新--"玩家:exp:增加:150"--HH:MM
变量更新--"玩家:currency.silver:增加:5"--HH:MM
提示信息--"任务完成：找回村民丢失的货物！你获得了150点经验和5枚银币。"HH:MM
行动选项A--"接受村民的下一个委托。"HH:MM
行动选项B--"告辞离开。"HH:MM
</系统:任务完成>
msg_end
关闭系统
```

**示例4: 属性因诅咒临时降低**
```
查看系统
msg_start
<系统:状态变化>
内容--"你碰触了那个被诅咒的雕像，感到一阵虚弱。"--HH:MM
变量更新--"玩家:attributes.strength.modifier_bonus:减少:1"HH:MM 
提示信息--"你的力量暂时降低了！你的力量调整值临时减少了1点。"HH:MM
行动选项A--"尝试解除诅咒。"HH:MM
行动选项B--"无视它，继续探索。"HH:MM
</系统:状态变化>
msg_end
关闭系统
```
*(注意: 此处修改 `modifier_bonus` 是一个例子，具体如何实现临时属性变化需要客户端逻辑支持，AI只需按约定格式提供指令。客户端需要根据 `modifier_bonus` 重新计算 `final` 和 `mod`)*

### 3.4. 对AI的明确指示

*   **强调精确性**: AI必须确保 `{属性路径}` 与 `PLAYER_STATE` JSON结构中的路径完全匹配。
*   **强调完整性**: 对于物品操作，AI应尽可能提供完整的物品信息（如描述），而不仅仅是名称和数量。
*   **避免纯文本描述**: 明确告知AI，不要仅仅在 `场景描述` 或 `提示信息` 中用自然语言描述变量变化，而是必须配合使用 `<系统:变量更新>` 和 `变量更新--...` 指令。自然语言描述可以作为补充，增强沉浸感。

## 4. 后续客户端实现考量

在更新提示词后，客户端 (`src/adventure_log/index.ts`) 的 `applySceneData` 或类似函数需要：
*   能够解析 `<系统:变量更新>` 块。
*   能够遍历并处理每一条 `变量更新--...` 指令。
*   根据 `{属性路径}` 和 `{操作类型}` 安全地修改 `playerState` 对象。
    *   需要健壮的路径解析逻辑 (例如，`lodash.set` 或 `lodash.update` 可以提供帮助，或者手动实现)。
    *   需要处理数值转换、数组操作、对象创建（如果路径中的中间对象不存在）。
*   特别注意物品栏操作的逻辑：查找现有物品、增减数量、添加新物品、移除数量为0的物品。

## 5. 计划交付物

*   更新后的 `src/adventure_log/adventure_log_ai_prompts.md` 文件。

此计划旨在确保AI生成的玩家状态变量更新信息能够被系统可靠地处理，为后续实现更复杂的游戏逻辑打下坚实基础。
