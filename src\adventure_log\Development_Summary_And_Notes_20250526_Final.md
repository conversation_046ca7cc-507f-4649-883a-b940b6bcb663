# 冒险日志功能开发总结与注意事项 (截至 2025-05-26)

本文档总结了“冒险日志” (`src/adventure_log`) 功能模块在近期开发和调试过程中的主要工作、已实现的功能以及需要注意的事项。

## 一、已完成的主要工作

1.  **AI提示词规范化 (`adventure_log_ai_prompts.md`)**:
    *   针对玩家状态变量（如经验值、货币、物品、任务、属性等）的更新，制定了详细的AI输出规范。
    *   **核心机制**: AI被要求使用 `<系统:变量更新>` 作为主数据块标签，并在内部通过一个或多个 `变量更新--"{对象标识}:{属性路径}:{操作类型}:{值}"--HH:MM` 指令来精确描述每个变量的变化。
    *   **指令详解**:
        *   `{对象标识}`: 当前固定为 `玩家`。
        *   `{属性路径}`: 使用点分隔法指向 `PLAYER_STATE` 中的目标字段 (例如 `exp`, `currency.gold`, `inventory`, `attributes.strength.modifier_bonus`)。
        *   `{操作类型}`: 支持 `设置`, `增加`, `减少`, `添加元素`, `移除元素`, 以及针对物品的 `物品获得` 和 `物品失去`。
        *   `{值}`: 根据操作类型可以是数字、字符串或（对于物品操作）有效的JSON字符串。
    *   **强调**: AI必须优先使用结构化指令更新变量，自然语言描述仅为辅助。禁止AI自行创造数据键名。
    *   提供了多种场景下的示例（如战斗奖励、物品发现/消耗、任务更新、属性临时变化）以指导AI。

2.  **客户端变量更新逻辑实现 (`src/adventure_log/index.ts`)**:
    *   **接口定义**:
        *   添加了 `VariableUpdateInstruction` 接口来描述单条变量更新指令的结构。
        *   更新了 `AdventureScene` 接口，增加了可选的 `variableUpdates?: VariableUpdateInstruction[]` 字段，用于存储从AI响应中解析出的所有变量更新指令。
    *   **`parseAIResponse` 函数增强**:
        *   修改了解析逻辑，使其能够识别并提取嵌入在各种父数据块（如 `<系统:物品发现>`，而不仅限于 `<系统:变量更新>`）内部的 `变量更新--...` 指令。
        *   解析时会对物品操作的值（JSON字符串）进行 `JSON.parse()`，对数值操作的值进行 `parseFloat()`。
        *   解析出的指令对象被添加到 `parsedScene.variableUpdates` 数组中。
    *   **`applyVariableUpdate` 函数实现**:
        *   这是一个新创建的核心函数，负责接收解析后的变量更新指令，并实际修改全局的 `playerState` 对象。
        *   **路径导航**: 能够根据点分隔的 `{属性路径}` 深入到 `playerState` 对象的嵌套结构中。
        *   **操作处理**:
            *   正确处理 `设置`、`增加`、`减少` 数值。
            *   正确处理对数组的 `添加元素` (push) 和 `移除元素` (splice/indexOf)。
            *   **物品库存管理**:
                *   `物品获得`: 检查库存中是否存在同名物品。若存在，则增加数量；若不存在，则将包含名称、数量和描述的新物品对象添加到 `inventory` 数组。
                *   `物品失去`: 查找库存中的物品，减少其数量。若数量减至0或以下，则从 `inventory` 数组中移除该物品。
        *   包含基本的错误处理和通过 `safeToastr` 进行的日志记录。
    *   **`applySceneData` 函数调整**:
        *   在应用场景的其他数据（如描述、选项等）之后，会遍历 `scene.variableUpdates` 数组（如果存在）。
        *   对每条指令调用 `applyVariableUpdate(playerState, ...)`。
        *   确保在所有状态（包括通过变量更新指令修改的状态）都应用完毕后，才调用 `updatePlayerStatusDisplay()` 刷新UI和 `persistGameState()` 保存游戏状态。

3.  **本地投骰与检定反馈**:
    *   AI提示词 (`adventure_log_ai_prompts.md`) 已更新，指导AI如何在行动选项中嵌入检定信息（如 `[DC15 力量(运动)]`）。
    *   客户端 (`src/adventure_log/index.ts`) 中的 `handleActionChoice` 函数已实现本地进行1d20投骰、计算检定结果（包括属性调整值和熟练加值），并将检定结果（如 `[检定成功 DC15 力量(运动) 投骰18+4=22]`）附加到发送给AI的提示词中。
    *   AI被指示根据客户端反馈的检定结果来推进剧情。
    *   **Toastr提示调整**: 大部分非游戏性的 `safeToastr` 调用（如初始化、加载、API错误、持久化、常规变量更新等相关的提示）已被注释掉，以减少干扰。仅保留了与本地投骰/检定结果直接相关的 `safeToastr` 提示。

## 二、重要注意事项与潜在问题

1.  **AI输出格式的严格性**:
    *   **核心依赖**: 整个系统的健壮性高度依赖于AI严格遵守 `adventure_log_ai_prompts.md` 中定义的输出格式，特别是 `变量更新--...` 指令的精确结构、合法的属性路径、正确的操作类型以及有效的JSON值（针对物品）。
    *   **潜在风险**: 如果AI未能遵循格式（例如，路径错误、操作类型错误、JSON格式无效、在不应出现的地方输出非结构化文本），客户端解析可能会失败，导致玩家状态不被更新或更新错误。
    *   **建议**: 持续监控AI输出，并在必要时通过调整提示词或增加客户端的容错处理来应对。

2.  **`PLAYER_STATE` 结构与属性路径**:
    *   `applyVariableUpdate` 函数通过字符串路径访问 `playerState` 的属性。这意味着 `PLAYER_STATE` 接口的结构（特别是嵌套对象的键名）必须与AI提示词中指导的 `{属性路径}` 保持严格一致。
    *   **维护成本**: 未来如果 `PLAYER_STATE` 结构发生变化，所有相关的AI提示词和客户端路径解析逻辑都需要同步更新。

3.  **JSON值的处理**:
    *   对于 `物品获得` 和 `物品失去` 操作，AI返回的值是一个JSON字符串。客户端的 `parseAIResponse` 函数会尝试 `JSON.parse()` 这个字符串。
    *   **AI输出要求**: AI生成的JSON字符串本身必须是语法正确的。提示词中已指示AI，JSON字符串内部应使用标准双引号，并且整个JSON字符串作为 `变量更新` 指令的“值”部分。
    *   **客户端健壮性**: `parseAIResponse` 中对 `JSON.parse()` 的调用被 `try...catch` 包裹，以处理潜在的解析错误，并通过 `safeToastr` 报告。

4.  **游戏状态加载 (`loadGameState`)**:
    *   **当前状态**: 用于从持久化消息中提取最新场景的 `查看系统...` 块的逻辑，在 `src/adventure_log/index.ts` 中，目前仍为 `rawPersistedState.match(/查看系统\s*msg_start([\s\S]*?)msg_end\s*关闭系统/)`。这个逻辑会匹配字符串中**第一个**符合该模式的块。
    *   **潜在问题 (消息回退)**: 如果持久化的字符串中（例如，在历史记录部分被错误地包含了完整的包裹块，或者由于其他原因）存在多个 `查看系统...` 块，此逻辑可能不会加载到代表当前最新状态的、理论上应位于字符串末尾的那个块。这可能导致游戏加载时回退到历史记录中的某个点，或者出现 `Load Warning: No current scene block...` 的提示。
    *   **修复尝试中断**: 之前尝试通过 `lastIndexOf` 和更精确的子串提取来定位并解析**最后一个** `查看系统...` 块的修改，由于该 `write_to_file` 操作被用户中断，该修复**并未实际应用**到 `src/adventure_log/index.ts` 的当前代码中。因此，这个问题仍然存在，是后续需要关注和修复的关键点，以确保游戏状态加载的准确性和鲁棒性。

5.  **物品操作的原子性与错误处理**:
    *   `applyVariableUpdate` 中对物品的操作（增/删/改数量）是直接在 `playerState.inventory` 数组上进行的。
    *   **注意事项**: 需要确保对物品名称的匹配是准确的。如果AI返回的物品名称与库存中的不完全一致（例如，多了或少了空格，或有细微的文字差异），可能会导致找不到物品或错误地添加了重复的物品。
    *   错误处理目前主要依赖 `safeToastr` 报告，更复杂的错误恢复机制（如事务性更新）未实现。

6.  **客户端与AI的职责边界**:
    *   本地投骰的实现将部分计算逻辑移至客户端，AI负责根据结果叙事。这个边界需要清晰，AI不应再自行进行检定判断或覆盖客户端的检定结果。
    *   变量更新也是如此，AI提供指令，客户端执行更新。

## 三、总结

目前，“冒险日志”功能在AI指令规范化、客户端解析与状态更新方面取得了显著进展，特别是实现了对玩家经验、货币、物品库存等核心数据的结构化更新。本地投骰机制的引入也为游戏交互增加了更多可控性。

后续的开发或测试应重点关注AI对新提示词规范的遵循情况，以及 `loadGameState` 函数在复杂持久化数据下的表现，以确保游戏状态的正确加载和恢复。
