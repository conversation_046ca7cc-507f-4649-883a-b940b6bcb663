import './index.scss';
import { initializeAdventureLogApp } from './app';

// Ensure globals like $ or toastr are available if any top-level code here uses them,
// though most should be handled within modules or by ensureGlobals in app.ts.
// We can add specific `declare const ...` here if needed, but ideally,
// `ensureGlobals()` called within `app.ts` should suffice for functions/objects
// passed from the parent SillyTavern environment.

initializeAdventureLogApp();
