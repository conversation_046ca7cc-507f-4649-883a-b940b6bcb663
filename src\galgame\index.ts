import './index.scss';

// 类型声明
declare const $: any;
declare const toastr: {
  success: (message: string, title?: string) => void;
  info: (message: string, title?: string) => void;
  error: (message: string, title?: string) => void;
  warning: (message: string, title?: string) => void;
};
declare function triggerSlash(command: string): Promise<string | undefined>;
declare function getLastMessageId(): number | undefined;
declare function setChatMessages(
  messages: { message_id: number; message: string }[],
  options?: { refresh?: string },
): Promise<void>;

// Module-level state variables
let statusBar: HTMLElement | null = null;
let timeDisplay: HTMLElement | null = null;
let energyDisplay: HTMLElement | null = null;
let sceneDescription: HTMLElement | null = null;
let choicesContainer: HTMLElement | null = null;
let dialogueArea: HTMLElement | null = null;
let characterSprite: HTMLElement | null = null;
let dialogueText: HTMLElement | null = null;
let dialogueChoicesContainer: HTMLElement | null = null;

interface ActionChoice {
  text: string;
  action: string;
  id: string;
}
interface GameState {
  time: string;
  energy: number;
  currentScene: string;
  interfaceMode: 'location' | 'characterDialogue';
  otherCharactersInLocation?: string[];
  locationActions?: ActionChoice[];
  currentCharacterName?: string;
  characterFavorability?: number;
  characterDialogueText?: string;
  playerReplyOptions?: ActionChoice[];
  lastUserChoiceText?: string;
}
let gameState: GameState = {
  time: '08:00',
  energy: 100,
  currentScene: 'home',
  interfaceMode: 'location',
  otherCharactersInLocation: [],
  locationActions: [],
  playerReplyOptions: [],
};
let currentHostMessageId: number | null = null;

// Stores objects: { sceneContent: string (the <地点:..> or <人物:..> block, WITHOUT 查看系统 wrappers), userChoiceText?: string }
interface HistoryLogEntry {
  sceneContent: string;
  userChoiceText?: string;
}
let fullHistoryLog: HistoryLogEntry[] = [];

const OLD_HISTORY_START_TAG = '<!-- OLD_GALGAME_HISTORY_CHUNK_START -->';
const OLD_HISTORY_END_TAG = '<!-- OLD_GALGAME_HISTORY_CHUNK_END -->';
const HISTORY_SEPARATOR = '\n------------------------\n';
// const USER_CHOICE_PREFIX = `用户点击选项--"`; // Not strictly needed if parsing logic is robust
// const USER_CHOICE_SUFFIX = `"--HH:MM`;

function stripOuterWrappers(blockWithWrappers: string): string {
  return blockWithWrappers
    .replace(/^查看系统\s*msg_start\s*/i, '')
    .replace(/msg_end\s*关闭系统\s*$/i, '')
    .trim();
}

function wrapWithSystemTags(sceneContent: string): string {
  return `查看系统\nmsg_start\n${sceneContent}\nmsg_end\n关闭系统`;
}

function extractActualSceneBlock(contentWithPotentialJunk: string): string | null {
  const lines = contentWithPotentialJunk.trim().split('\n');
  let startTagLineIndex = -1;
  let tagName = '';
  let tagType = ''; // '地点' or '人物'

  for (let i = 0; i < lines.length; i++) {
    const lineTrimmed = lines[i].trim();
    const locMatch = lineTrimmed.match(/^<地点:([^>]+)>/i);
    if (locMatch && locMatch[1]) {
      startTagLineIndex = i;
      tagName = locMatch[1];
      tagType = '地点';
      break;
    }
    const charMatch = lineTrimmed.match(/^<人物:([^>]+)>/i);
    if (charMatch && charMatch[1]) {
      startTagLineIndex = i;
      tagName = charMatch[1];
      tagType = '人物';
      break;
    }
  }

  if (startTagLineIndex === -1) {
    console.warn('[Galgame extractActualSceneBlock] No valid start tag found in:', contentWithPotentialJunk);
    return null; // No valid start tag found
  }

  const endTagPattern = `</${tagType}:${tagName}>`;
  let blockLines = [];
  // Collect lines from the start tag line
  for (let i = startTagLineIndex; i < lines.length; i++) {
    blockLines.push(lines[i]); // Push the original line, not trimmed, to preserve internal formatting
    if (lines[i].trim().includes(endTagPattern)) { // Check if the trimmed line contains the end tag
      return blockLines.join('\n');
    }
  }
  console.warn('[Galgame extractActualSceneBlock] No valid end tag found for:', tagType, tagName);
  return null; // No valid end tag found
}

function safeToastr(type: 'info' | 'success' | 'warning' | 'error', message: string, title?: string) {
  try {
    if (
      typeof (window as any).toastr !== 'object' &&
      typeof parent !== 'undefined' &&
      typeof (parent as any).toastr === 'object'
    ) {
      (window as any).toastr = (parent as any).toastr;
    }
    if (typeof toastr === 'object' && toastr !== null && typeof toastr[type] === 'function') {
      toastr[type](message, title);
    } else {
      const consoleFn = type === 'error' ? console.error : type === 'warning' ? console.warn : console.log;
      consoleFn(`[Galgame Toastr Fallback - ${type}] ${title ? title + ': ' : ''}${message}`);
    }
  } catch (e) {
    console.error(`[Galgame] safeToastr Error: ${(e as Error).message}`);
  }
}

function ensureGlobals() {
  try {
    if (typeof window !== 'undefined' && typeof parent !== 'undefined' && parent !== window) {
      const apiKeysToCopy = ['$', 'toastr', 'triggerSlash', 'getLastMessageId', 'setChatMessages'];
      apiKeysToCopy.forEach(key => {
        if (typeof (window as any)[key] === 'undefined') {
          if (typeof (parent as any)[key] !== 'undefined') {
            (window as any)[key] = (parent as any)[key];
          } else {
            console.warn(`[Galgame ensureGlobals] API "${key}" is UNDEFINED in parent.`);
          }
        }
      });
    }
  } catch (e) {
    safeToastr('error', `ensureGlobals Error: ${(e as Error).message}`, 'Galgame Init Error');
  }
}

function updateStatusBarDisplay() {
  if (timeDisplay) timeDisplay.textContent = `时间: ${gameState.time}`;
  if (energyDisplay) energyDisplay.textContent = `体力: ${gameState.energy}`;
  const charInfoEl = document.getElementById('character-info-ingame');
  if (charInfoEl) {
    charInfoEl.textContent =
      gameState.interfaceMode === 'characterDialogue' && gameState.currentCharacterName
        ? `与 ${gameState.currentCharacterName} 对话中 (好感: ${gameState.characterFavorability || 0})`
        : '';
  }
}

function updateSceneDescriptionDisplay() {
  if (sceneDescription) {
    let text = `场景: ${gameState.currentScene}`;
    if (gameState.currentScene.toLowerCase() === '家') text = '你现在在家里。';
    else if (gameState.otherCharactersInLocation?.length)
      text = `你来到了 ${gameState.currentScene}。你看到了 ${gameState.otherCharactersInLocation.join('、')}。`;
    else if (gameState.currentScene) text = `你来到了 ${gameState.currentScene}。`;
    sceneDescription.innerHTML = `<p>${text}</p>`;
  }
}

function extractLatestSceneContent(fullDataString: string): string | null {
  const lastOldHistoryEndIndex = fullDataString.lastIndexOf(OLD_HISTORY_END_TAG);
  const searchString =
    lastOldHistoryEndIndex !== -1
      ? fullDataString.substring(lastOldHistoryEndIndex + OLD_HISTORY_END_TAG.length)
      : fullDataString;

  const match = searchString.match(/查看系统\s*msg_start([\s\S]+?)msg_end\s*关闭系统/);
  return match ? stripOuterWrappers(match[0]) : null;
}

function rebuildFullHistoryLog(fullDataStringWithHistory: string) {
  fullHistoryLog.length = 0;
  const oldHistoryBlockMatch = fullDataStringWithHistory.match(
    new RegExp(`${OLD_HISTORY_START_TAG}([\\s\\S]*?)${OLD_HISTORY_END_TAG}`, 's'),
  );

  if (oldHistoryBlockMatch && oldHistoryBlockMatch[1]) {
    const oldHistoryContent = oldHistoryBlockMatch[1].trim();
    // Split by the separator, but ensure scene data itself is not split if it contains similar lines
    const historyEntriesText = oldHistoryContent.split(
      new RegExp(HISTORY_SEPARATOR.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'),
    );

    historyEntriesText.forEach(entryText => {
      const sceneContentMatch = entryText.match(/^([\s\S]*?)(?=\n用户点击选项--|$)/s);
      const userChoiceMatch = entryText.match(/\n用户点击选项--"([^"]+)"(?:--HH:MM)?$/);

      if (sceneContentMatch && sceneContentMatch[1] && sceneContentMatch[1].trim()) {
        fullHistoryLog.push({
          sceneContent: sceneContentMatch[1].trim(),
          userChoiceText: userChoiceMatch ? userChoiceMatch[1] : undefined,
        });
      }
    });
  }

  const latestSceneContentFromStorage = extractLatestSceneContent(fullDataStringWithHistory);
  if (latestSceneContentFromStorage) {
    const actualLatestSceneBlock = extractActualSceneBlock(latestSceneContentFromStorage);
    if (actualLatestSceneBlock) {
      fullHistoryLog.push({ sceneContent: actualLatestSceneBlock });
    } else {
      console.warn('[Galgame rebuildFullHistoryLog] Could not extract actual scene block from latestSceneContentFromStorage:', latestSceneContentFromStorage);
    }
  }
  console.log('[Galgame Debug] Rebuilt fullHistoryLog with', fullHistoryLog.length, 'entries.');
}

function parseAndApplyGalgameData(sceneContentToParse: string) {
  if (!sceneContentToParse || typeof sceneContentToParse !== 'string') {
    safeToastr('error', 'Galgame: 无效的场景内容传入解析。', '解析错误');
    return;
  }
  safeToastr('info', 'Galgame: 解析场景内容...', '解析');

  const allLines = sceneContentToParse.trim().split('\n');
  let startLineIndex = -1;

  for (let i = 0; i < allLines.length; i++) {
    if (allLines[i].match(/^<地点:([^>]+)>/i) || allLines[i].match(/^<人物:([^>]+)>/i)) {
      startLineIndex = i;
      break;
    }
  }

  if (startLineIndex === -1) {
    safeToastr('error', 'Galgame: 未在场景内容中找到有效的<地点:...>或<人物:...>标签。', '解析失败');
    return;
  }

  const lines = allLines.slice(startLineIndex);
  const firstLine = lines[0] || '';

  const newState: GameState = {
    ...gameState,
    otherCharactersInLocation: [],
    locationActions: [],
    playerReplyOptions: [],
    characterDialogueText: '',
  };
  let isCharacterDataBlock = false;
  let currentParsingCharName = '';

  const locTagMatch = firstLine.match(/^<地点:([^>]+)>/i);
  const charTagMatch = firstLine.match(/^<人物:([^>]+)>/i);

  if (locTagMatch && locTagMatch[1]) {
    newState.currentScene = locTagMatch[1];
    newState.interfaceMode = 'location';
    isCharacterDataBlock = false;
    newState.currentCharacterName = undefined;
    newState.characterFavorability = undefined;
  } else if (charTagMatch && charTagMatch[1]) {
    currentParsingCharName = charTagMatch[1];
    newState.currentCharacterName = currentParsingCharName;
    newState.interfaceMode = 'characterDialogue';
    isCharacterDataBlock = true;
    newState.characterFavorability = 0;
    newState.characterDialogueText = '';
    newState.playerReplyOptions = [];
  } else {
    safeToastr('error', `Galgame: 未知的主要数据块标签: ${firstLine}`, '解析失败');
    return;
  }

  lines.slice(1).forEach(line => {
    // Process all lines after the main tag
    const trimmedLine = line.trim();
    if (trimmedLine === `</${locTagMatch?.[0].substring(1)}` || trimmedLine === `</${charTagMatch?.[0].substring(1)}`)
      return; // Skip closing tag

    const timeMatch = trimmedLine.match(/^时间--"([^"]+)"--HH:MM/i),
      energyMatch = trimmedLine.match(/^体力--"([^"]+)"--HH:MM/i),
      otherChar = trimmedLine.match(/^其他角色名称--"([^"]+)"--HH:MM/i),
      locAction = trimmedLine.match(/^行动选项([A-Z])--"([^"]+)"--HH:MM/i),
      favMatch = trimmedLine.match(/^好感度--"([^"]+)"--HH:MM/i),
      charDia = trimmedLine.match(/^对方的话--"([^"]+)"--HH:MM/i),
      replyOpt = trimmedLine.match(/^回复选项([A-Z])--"([^"]+)"--HH:MM/i);

    if (timeMatch && timeMatch[1]) newState.time = timeMatch[1];
    else if (energyMatch && energyMatch[1]) {
      const e = parseInt(energyMatch[1], 10);
      if (!isNaN(e)) newState.energy = e;
    } else if (isCharacterDataBlock) {
      if (favMatch && favMatch[1]) newState.characterFavorability = parseInt(favMatch[1], 10) || 0;
      else if (charDia && charDia[1]) newState.characterDialogueText = charDia[1];
      else if (replyOpt && replyOpt[1] && replyOpt[2])
        newState.playerReplyOptions?.push({
          id: `reply_${replyOpt[1]}`,
          text: replyOpt[2],
          action: `reply_to_${currentParsingCharName}_option_${replyOpt[1]}`.toLowerCase().replace(/\s+/g, '_'),
        });
    } else {
      if (otherChar && otherChar[1]) newState.otherCharactersInLocation?.push(otherChar[1]);
      else if (locAction && locAction[1] && locAction[2])
        newState.locationActions?.push({
          id: `loc_action_${locAction[1]}`,
          text: locAction[2],
          action: locAction[2].toLowerCase().replace(/\s+/g, '_'),
        });
    }
  });
  gameState = newState;
  updateStatusBarDisplay();
  if (gameState.interfaceMode === 'location') updateSceneDescriptionDisplay();
  console.log('[Galgame Debug] gameState after parse:', JSON.stringify(gameState, null, 2));
  safeToastr('success', `Galgame: 数据已应用.`, '解析完成');
}

function renderButtons(
  container: HTMLElement | null,
  choices: ActionChoice[] | undefined,
  clickHandler: (action: string, text: string) => void,
) {
  if (!container) {
    safeToastr('error', `Galgame: 按钮容器丢失。`, '渲染错误');
    return;
  }
  container.innerHTML = '';
  if (choices?.length) {
    choices.forEach(c => {
      const btn = document.createElement('button');
      btn.textContent = c.text;
      btn.dataset.action = c.action;
      btn.dataset.id = c.id;
      btn.addEventListener('click', () => clickHandler(c.action, c.text));
      container.appendChild(btn);
    });
  }
}

const RETURN_KEYWORDS = ['返回', '离开', '结束对话', '不再打扰', '告辞'];
let previousSceneDataStack: string[] = []; // For simple UI back, not for AI context history

async function handleChoiceClick(actionKey: string, choiceText: string) {
  safeToastr('info', `按钮点击: "${choiceText}" (action: ${actionKey})`, '操作');

  const lowerChoiceText = choiceText.toLowerCase();
  let isReturnAction = actionKey === 'action_end_dialogue';
  if (!isReturnAction) {
    for (const keyword of RETURN_KEYWORDS) {
      if (lowerChoiceText.includes(keyword.toLowerCase())) {
        isReturnAction = true;
        break;
      }
    }
  }

  if (isReturnAction) {
    await returnToPreviousSceneOrHome();
    return;
  }

  if (fullHistoryLog.length > 0) {
    fullHistoryLog[fullHistoryLog.length - 1].userChoiceText = choiceText;
  }
  // previousSceneDataStack.push(stripOuterWrappers(getCurrentSceneDataBlock())); // previousSceneDataStack is for UI "back" button, not directly related to fullHistoryLog for AI context

  let prompt = `用户在[${
    gameState.currentCharacterName || gameState.currentScene
  }]选择了："${choiceText}"。\n请给出接下来的Galgame界面内容，并严格遵循所有已定义的格式规则，特别是每行末尾的--HH:MM。`;
  // ... (prompt construction logic)

  if (typeof triggerSlash !== 'function') {
    safeToastr('error', 'triggerSlash API不可用!', 'API错误');
    return;
  }
  try {
    const aiResponseFullBlock = await triggerSlash(`/gen ${prompt}`);
    if (aiResponseFullBlock?.trim()) {
      const strippedContentFromAI = stripOuterWrappers(aiResponseFullBlock);
      const actualSceneBlock = extractActualSceneBlock(strippedContentFromAI);

      if (actualSceneBlock) {
        fullHistoryLog.push({ sceneContent: actualSceneBlock });
        parseAndApplyGalgameData(actualSceneBlock); // parseAndApplyGalgameData now receives the pure block
        if (gameState.interfaceMode === 'location') showLocationSceneInterface();
        else showCharacterDialogueInterface();
        await persistCurrentStateWithHistory();
      } else {
        safeToastr('error', 'AI响应中未找到有效的场景数据块。', 'AI交互错误');
        console.error('[Galgame handleChoiceClick] Failed to extract actual scene block from:', strippedContentFromAI);
      }
    } else {
      safeToastr('warning', 'AI未返回有效数据。', 'AI交互');
    }
  } catch (e) {
    safeToastr('error', `AI交互失败: ${(e as Error).message}`, 'AI错误');
    // if (previousSceneDataStack.length > 0) previousSceneDataStack.pop();
  }
}

function showLocationSceneInterface() {
  if (dialogueArea) dialogueArea.style.display = 'none';
  if (choicesContainer) choicesContainer.style.display = 'flex';
  if (sceneDescription) sceneDescription.style.display = 'flex';
  updateSceneDescriptionDisplay();
  let actions: ActionChoice[] = [];
  if (gameState.currentScene.toLowerCase() === '家') {
    actions = [
      { text: '出门', action: 'go-out', id: 'home_go' },
      { text: '休息', action: 'rest', id: 'home_rest' },
      { text: '消磨时间', action: 'pass-time', id: 'home_pass' },
    ];
  } else if (gameState.locationActions?.length) actions = gameState.locationActions;
  renderButtons(choicesContainer, actions, handleChoiceClick);
  if (!actions.length && choicesContainer && gameState.currentScene.toLowerCase() !== '家')
    choicesContainer.innerHTML = '<p>此地暂无行动选项。</p>';
}

function showCharacterDialogueInterface() {
  if (choicesContainer) choicesContainer.style.display = 'none';
  if (sceneDescription) sceneDescription.style.display = 'none';
  if (dialogueArea) dialogueArea.style.display = 'flex';
  if (dialogueText)
    dialogueText.innerHTML = `<p><strong>${gameState.currentCharacterName || '对方'}:</strong> ${
      gameState.characterDialogueText || '...'
    }</p>`;
  renderButtons(dialogueChoicesContainer, gameState.playerReplyOptions, handleChoiceClick);
  if (dialogueChoicesContainer) {
    const btn = document.createElement('button');
    btn.textContent = '结束对话';
    btn.style.marginTop = '10px';
    btn.dataset.action = 'action_end_dialogue';
    btn.addEventListener('click', () => handleChoiceClick('action_end_dialogue', '结束对话'));
    dialogueChoicesContainer.appendChild(btn);
  }
}

async function returnToPreviousSceneOrHome() {
  let sceneContentToParse: string | undefined;

  if (fullHistoryLog.length > 1) {
    fullHistoryLog.pop();
    sceneContentToParse = fullHistoryLog[fullHistoryLog.length - 1].sceneContent;
    safeToastr('info', '返回上一历史状态...', '导航');
  } else {
    safeToastr('info', '没有更多历史或仅初始状态，返回家中...', '导航');
    const homeSceneContent = stripOuterWrappers(getCurrentSceneDataBlockForHome());
    fullHistoryLog = [{ sceneContent: homeSceneContent }];
    sceneContentToParse = homeSceneContent;
  }

  if (typeof sceneContentToParse === 'string') {
    parseAndApplyGalgameData(sceneContentToParse);
    if (gameState.interfaceMode === 'location') showLocationSceneInterface();
    else showCharacterDialogueInterface();
    await persistCurrentStateWithHistory();
  } else {
    safeToastr('error', '无法恢复场景：无数据可恢复。', '导航错误');
  }
}

function getCurrentSceneDataBlock(): string {
  return wrapWithSystemTags(getCurrentSceneContent());
}
function getCurrentSceneDataBlockForHome(): string {
  const homeContent = `<地点:家>\n时间--"${gameState.time}"--HH:MM\n体力--"${gameState.energy}"--HH:MM\n</地点:家>`;
  return wrapWithSystemTags(homeContent);
}
function getCurrentSceneContent(): string {
  // Returns just the <地点...> or <人物...> block
  let content = '';
  if (gameState.interfaceMode === 'location') {
    content += `<地点:${gameState.currentScene}>\n`;
    content += `时间--"${gameState.time}"--HH:MM\n`;
    content += `体力--"${gameState.energy}"--HH:MM\n`;
    gameState.otherCharactersInLocation?.forEach(char => {
      content += `其他角色名称--"${char}"--HH:MM\n`;
    });
    gameState.locationActions?.forEach((act, index) => {
      content += `行动选项${String.fromCharCode(65 + index)}--"${act.text}"--HH:MM\n`;
    });
    content += `</地点:${gameState.currentScene}>`;
  } else if (gameState.interfaceMode === 'characterDialogue' && gameState.currentCharacterName) {
    content += `<人物:${gameState.currentCharacterName}>\n`;
    content += `时间--"${gameState.time}"--HH:MM\n`;
    content += `好感度--"${gameState.characterFavorability || 0}"--HH:MM\n`;
    if (gameState.characterDialogueText) content += `对方的话--"${gameState.characterDialogueText}"--HH:MM\n`;
    gameState.playerReplyOptions?.forEach((opt, index) => {
      content += `回复选项${String.fromCharCode(65 + index)}--"${opt.text}"--HH:MM\n`;
    });
    content += `</人物:${gameState.currentCharacterName}>`;
  } else {
    content += `<地点:家>\n时间--"${gameState.time}"--HH:MM\n体力--"${gameState.energy}"--HH:MM\n</地点:家>`;
  }
  return content.trim();
}

async function persistCurrentStateWithHistory() {
  if (currentHostMessageId === null || typeof setChatMessages !== 'function') return;

  let persistedString = '';
  if (fullHistoryLog.length > 1) {
    persistedString += OLD_HISTORY_START_TAG + '\n';
    for (let i = 0; i < fullHistoryLog.length - 1; i++) {
      const entry = fullHistoryLog[i];
      let oldEntryContent = entry.sceneContent;
      // 如果旧条目错误地包含了包裹，移除它们
      if (oldEntryContent.startsWith('查看系统\nmsg_start\n')) {
        oldEntryContent = stripOuterWrappers(oldEntryContent); // 使用已有的strip函数更安全
      }
      persistedString += oldEntryContent;
      if (entry.userChoiceText) {
        persistedString += `\n用户点击选项--"${entry.userChoiceText}"--HH:MM`;
      }
      if (i < fullHistoryLog.length - 2) {
        persistedString += HISTORY_SEPARATOR;
      }
    }
    persistedString += '\n' + OLD_HISTORY_END_TAG + '\n';
  }

  persistedString +=
    fullHistoryLog.length > 0
      ? wrapWithSystemTags(fullHistoryLog[fullHistoryLog.length - 1].sceneContent)
      : getCurrentSceneDataBlock();

  safeToastr('info', `Galgame: 持久化状态 (ID: ${currentHostMessageId})。`, '持久化');
  try {
    await setChatMessages([{ message_id: currentHostMessageId, message: persistedString }], { refresh: 'affected' });
    safeToastr('success', `Galgame: 父消息 ${currentHostMessageId} 已更新。`, '持久化');
  } catch (e) {
    safeToastr('error', `Galgame: 更新父消息失败: ${(e as Error).message}`, '持久化错误');
  }
}

async function onMounted() {
  console.log('[Galgame] onMounted.');
  setTimeout(async () => {
    ensureGlobals();
    safeToastr('info', 'Galgame脚本初始化...', '初始化');
    if (
      typeof $ !== 'function' ||
      typeof triggerSlash !== 'function' ||
      typeof getLastMessageId !== 'function' ||
      typeof toastr !== 'object'
    ) {
      safeToastr('error', '核心API未加载!', '初始化错误');
      return;
    }
    statusBar = document.getElementById('status-bar');
    timeDisplay = document.getElementById('time');
    energyDisplay = document.getElementById('energy');
    sceneDescription = document.getElementById('scene-description');
    choicesContainer = document.getElementById('choices');
    dialogueArea = document.getElementById('dialogue-area');
    characterSprite = document.getElementById('character-sprite');
    dialogueText = document.getElementById('dialogue-text');
    dialogueChoicesContainer = document.getElementById('dialogue-choices');

    try {
      const hostId = getLastMessageId();
      if (typeof hostId === 'number' && hostId >= 0) {
        currentHostMessageId = hostId;
        const initialMsgContent = await triggerSlash(`/messages ${currentHostMessageId}`);
        if (initialMsgContent && initialMsgContent.trim() !== '') {
          rebuildFullHistoryLog(initialMsgContent);
          const latestSceneContent =
            fullHistoryLog.length > 0 ? fullHistoryLog[fullHistoryLog.length - 1].sceneContent : null;

          if (latestSceneContent) {
            parseAndApplyGalgameData(latestSceneContent);
          } else {
            const defaultHomeContent = stripOuterWrappers(getCurrentSceneDataBlockForHome());
            parseAndApplyGalgameData(defaultHomeContent);
            fullHistoryLog = [{ sceneContent: defaultHomeContent }];
          }
        } else {
          const defaultHomeContent = stripOuterWrappers(getCurrentSceneDataBlockForHome());
          parseAndApplyGalgameData(defaultHomeContent);
          fullHistoryLog = [{ sceneContent: defaultHomeContent }];
        }
      } else {
        const defaultHomeContent = stripOuterWrappers(getCurrentSceneDataBlockForHome());
        parseAndApplyGalgameData(defaultHomeContent);
        fullHistoryLog = [{ sceneContent: defaultHomeContent }];
      }
    } catch (e) {
      safeToastr('error', `获取初始数据失败: ${(e as Error).message}`, '初始数据错误');
      const defaultHomeContent = stripOuterWrappers(getCurrentSceneDataBlockForHome());
      parseAndApplyGalgameData(defaultHomeContent);
      fullHistoryLog = [{ sceneContent: defaultHomeContent }];
    }
    if (gameState.interfaceMode === 'location') showLocationSceneInterface();
    else if (gameState.interfaceMode === 'characterDialogue') showCharacterDialogueInterface();
    else showLocationSceneInterface();
    safeToastr('success', 'Galgame初始化完成。', '初始化');
  }, 200);
}

try {
  const initFn = () => {
    onMounted();
  };
  if (document.readyState === 'complete' || document.readyState === 'interactive') initFn();
  else window.addEventListener('DOMContentLoaded', initFn);
  window.addEventListener('beforeunload', () => console.log('[Galgame] onUnmounted.'));
} catch (e) {
  safeToastr('error', `顶层脚本错误: ${(e as Error).message}`, '脚本错误');
}
