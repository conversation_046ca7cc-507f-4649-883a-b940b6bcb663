import { ArmorTemplate, EffectType, MagicEffect, ModuleSetupEquipmentItem, WeaponTemplate } from '../types';

// --- Data Store ---
export let allSelectableWeapons: ModuleSetupEquipmentItem[] = []; // Keep for now, might be refactored for data_handling
export let weaponTemplates: WeaponTemplate[] = []; // Export for data_handling
export let armorTemplates: ArmorTemplate[] = []; // Added armor templates, export for data_handling

// --- Predefined Magic Effects for User Selection ---
// This structure helps in generating UI for magic effect input
export interface PredefinedMagicEffectOption {
  // Exporting for data_handling
  label: string; // User-friendly label, e.g., "攻击加值"
  type: EffectType;
  valueSchema: Array<{
    name: string;
    type: 'text' | 'number' | 'textarea' | 'select';
    label: string;
    options?: string[];
  }>; // Defines input fields for the effect's value
  notes?: string; // Optional default notes for the effect
}

export const predefinedMagicEffectOptions: PredefinedMagicEffectOption[] = [
  // Exporting for data_handling
  {
    label: '攻击加值',
    type: 'ATTACK_BONUS',
    valueSchema: [{ name: 'bonus', type: 'number', label: '加值 (例如: 1)' }],
    notes: '攻击检定加值',
  },
  {
    label: '伤害加值 (固定)',
    type: 'DAMAGE_BONUS_STATIC',
    valueSchema: [
      { name: 'amount', type: 'number', label: '伤害量 (例如: 1)' },
      { name: 'damageType', type: 'text', label: '伤害类型 (可选, 如 火焰)' },
    ],
    notes: '固定伤害加值',
  },
  {
    label: '伤害加值 (伤害骰)',
    type: 'DAMAGE_BONUS_DICE',
    valueSchema: [
      { name: 'dice', type: 'text', label: '伤害骰 (例如: 1d6)' },
      { name: 'type', type: 'text', label: '伤害类型 (例如: 火焰)' },
    ],
    notes: '附加伤害骰',
  },
  {
    label: 'AC 加值',
    type: 'AC_BONUS',
    valueSchema: [{ name: 'bonus', type: 'number', label: 'AC 加值 (例如: 1)' }],
    notes: '防御等级加值',
  },
  // Add more predefined effects as needed based on EffectType
  // Example for ON_HIT_EFFECT_SAVE
  {
    label: '命中时触发豁免效果',
    type: 'ON_HIT_EFFECT_SAVE',
    valueSchema: [
      {
        name: 'saveAttribute',
        type: 'select',
        label: '豁免属性',
        options: ['力量', '敏捷', '体质', '智力', '感知', '魅力'],
      },
      { name: 'dc', type: 'number', label: '豁免DC' },
      { name: 'effectOnFail', type: 'textarea', label: '豁免失败效果描述' },
      { name: 'effectOnSuccess', type: 'textarea', label: '豁免成功效果描述 (可选)' },
    ],
    notes: '命中时触发豁免',
  },
];

// --- Hardcoded Armor Templates ---
const exampleArmorTemplates: ArmorTemplate[] = [
  {
    name_zh: '皮甲',
    name_en: 'Leather',
    category: '轻甲',
    ac_base: 11,
    ac_dex_bonus: true,
    ac_dex_max: null,
    strength_requirement: null,
    stealth_disadvantage: false,
    weight: '10磅',
    cost: '10 GP',
  },
  {
    name_zh: '镶钉皮甲',
    name_en: 'Studded Leather',
    category: '轻甲',
    ac_base: 12,
    ac_dex_bonus: true,
    ac_dex_max: null,
    strength_requirement: null,
    stealth_disadvantage: false,
    weight: '13磅',
    cost: '45 GP',
  },
  {
    name_zh: '链甲衫',
    name_en: 'Chain Shirt',
    category: '中甲',
    ac_base: 13,
    ac_dex_bonus: true,
    ac_dex_max: 2,
    strength_requirement: null,
    stealth_disadvantage: false,
    weight: '20磅',
    cost: '50 GP',
  },
  {
    name_zh: '板条甲',
    name_en: 'Splint',
    category: '重甲',
    ac_base: 17,
    ac_dex_bonus: false,
    ac_dex_max: 0,
    strength_requirement: 15,
    stealth_disadvantage: true,
    weight: '60磅',
    cost: '200 GP',
  },
  {
    name_zh: '盾牌',
    name_en: 'Shield',
    category: '盾牌',
    ac_base: 2,
    ac_dex_bonus: false,
    ac_dex_max: 0,
    strength_requirement: null,
    stealth_disadvantage: false,
    weight: '6磅',
    cost: '10 GP',
  },
];

// --- Hardcoded Magic Weapon Examples (based on docs) ---
const exampleMagicWeapons: ModuleSetupEquipmentItem[] = [
  {
    id: 'dagger_venom_001',
    name: '毒蛇之牙匕首',
    type: '武器',
    baseItemName: '匕首', // Refers to name_zh or name_en in WeaponTemplate
    description: '一把淬毒的匕首，刀刃上闪烁着不祥的绿光。',
    magicEffects: [
      {
        type: 'ON_HIT_EFFECT_SAVE',
        value: {
          saveAttribute: '体质',
          dc: 13,
          effectOnFail: '目标中毒1分钟',
          notes: '毒素攻击',
        },
      },
      {
        type: 'DAMAGE_BONUS_DICE',
        value: { dice: '1d6', type: '毒素' },
        condition: '仅在目标豁免失败时生效',
      },
    ],
  },
  {
    id: 'grubnaks_choppa',
    name: '格鲁克的劈砍小刀',
    type: '武器',
    baseItemName: '短剑',
    description: '一把粗制但锋利的短剑，剑柄上缠着肮脏的布条。',
    magicEffects: [
      { type: 'ATTACK_BONUS', value: 1 },
      { type: 'DAMAGE_BONUS_DICE', value: { dice: '1d4', type: '流血' }, notes: '恶毒伤口' },
    ],
  },
  {
    id: 'longsword_flame_tongue_001',
    name: '焰舌长剑+1',
    type: '武器',
    baseItemName: '长剑',
    description: '剑刃燃烧着熊熊烈焰的魔法长剑。',
    magicEffects: [
      { type: 'ATTACK_BONUS', value: 1 },
      { type: 'DAMAGE_BONUS_STATIC', value: { amount: 1 } },
      { type: 'DAMAGE_BONUS_DICE', value: { dice: '2d6', type: '火焰' }, notes: '焰舌' },
    ],
  },
  {
    name: '长剑+1',
    type: '武器',
    baseItemName: '长剑',
    description: '一把制作精良的长剑，附有微弱的魔法力量。',
    magicEffects: [
      { type: 'ATTACK_BONUS', value: 1 },
      { type: 'DAMAGE_BONUS_STATIC', value: { amount: 1 } },
    ],
  },
];

// --- Weapon Data Loading ---
// This is a placeholder for how weapon_templates.md content would be accessed.
// In a real scenario, this data might be pre-loaded or fetched.
const weaponTemplatesMdContent = `
[
  {
    "name_zh": "匕首",
    "name_en": "Dagger",
    "category": "简易近战",
    "damage": "1d4",
    "damageType": "穿刺",
    "properties": ["灵巧", "轻型", "投掷 (射程 20/60)"],
    "weight": "1磅",
    "cost": "2 GP",
    "mastery": "迅击"
  },
  {
    "name_zh": "短棒",
    "name_en": "Club",
    "category": "简易近战",
    "damage": "1d4",
    "damageType": "钝击",
    "properties": ["轻型"],
    "weight": "2磅",
    "cost": "1 SP",
    "mastery": "缓速"
  },
  {
    "name_zh": "手斧",
    "name_en": "Handaxe",
    "category": "简易近战",
    "damage": "1d6",
    "damageType": "挥砍",
    "properties": ["轻型", "投掷 (射程 20/60)"],
    "weight": "2磅",
    "cost": "5 GP",
    "mastery": "侵扰"
  },
  {
    "name_zh": "标枪",
    "name_en": "Javelin",
    "category": "简易近战",
    "damage": "1d6",
    "damageType": "穿刺",
    "properties": ["投掷 (射程 30/120)"],
    "weight": "2磅",
    "cost": "5 SP",
    "mastery": "缓速"
  },
  {
    "name_zh": "轻锤",
    "name_en": "Light Hammer",
    "category": "简易近战",
    "damage": "1d4",
    "damageType": "钝击",
    "properties": ["轻型", "投掷 (射程 20/60)"],
    "weight": "2磅",
    "cost": "2 GP",
    "mastery": "迅击"
  },
  {
    "name_zh": "硬头锤",
    "name_en": "Mace",
    "category": "简易近战",
    "damage": "1d6",
    "damageType": "钝击",
    "properties": [],
    "weight": "4磅",
    "cost": "5 GP",
    "mastery": "削弱"
  },
  {
    "name_zh": "长棍",
    "name_en": "Quarterstaff",
    "category": "简易近战",
    "damage": "1d6",
    "damageType": "钝击",
    "properties": ["多用 (1d8)"],
    "weight": "4磅",
    "cost": "2 SP",
    "mastery": "失衡"
  },
  {
    "name_zh": "矛",
    "name_en": "Spear",
    "category": "简易近战",
    "damage": "1d6",
    "damageType": "穿刺",
    "properties": ["投掷 (射程 20/60)", "多用 (1d8)"],
    "weight": "3磅",
    "cost": "1 GP",
    "mastery": "削弱"
  },
  {
    "name_zh": "轻弩",
    "name_en": "Light Crossbow",
    "category": "简易远程",
    "damage": "1d8",
    "damageType": "穿刺",
    "properties": ["弹药 (射程 80/320；弩矢)", "装填", "双手"],
    "weight": "5磅",
    "cost": "25 GP",
    "mastery": "缓速"
  },
  {
    "name_zh": "短弓",
    "name_en": "Shortbow",
    "category": "简易远程",
    "damage": "1d6",
    "damageType": "穿刺",
    "properties": ["弹药 (射程 80/320；箭矢)", "双手"],
    "weight": "2磅",
    "cost": "25 GP",
    "mastery": "侵扰"
  },
  {
    "name_zh": "投石索",
    "name_en": "Sling",
    "category": "简易远程",
    "damage": "1d4",
    "damageType": "钝击",
    "properties": ["弹药 (射程 30/120；弹丸)"],
    "weight": "—",
    "cost": "1 SP",
    "mastery": "缓速"
  },
  {
    "name_zh": "战斧",
    "name_en": "Battleaxe",
    "category": "军用近战",
    "damage": "1d8",
    "damageType": "挥砍",
    "properties": ["多用 (1d10)"],
    "weight": "4磅",
    "cost": "10 GP",
    "mastery": "失衡"
  },
  {
    "name_zh": "长剑",
    "name_en": "Longsword",
    "category": "军用近战",
    "damage": "1d8",
    "damageType": "挥砍",
    "properties": ["多用 (1d10)"],
    "weight": "3磅",
    "cost": "15 GP",
    "mastery": "削弱"
  },
  {
    "name_zh": "巨剑",
    "name_en": "Greatsword",
    "category": "军用近战",
    "damage": "2d6",
    "damageType": "挥砍",
    "properties": ["重型", "双手"],
    "weight": "6磅",
    "cost": "50 GP",
    "mastery": "擦掠"
  },
  {
    "name_zh": "刺剑",
    "name_en": "Rapier",
    "category": "军用近战",
    "damage": "1d8",
    "damageType": "穿刺",
    "properties": ["灵巧"],
    "weight": "2磅",
    "cost": "25 GP",
    "mastery": "侵扰"
  },
  {
    "name_zh": "短剑",
    "name_en": "Shortsword",
    "category": "军用近战",
    "damage": "1d6",
    "damageType": "穿刺",
    "properties": ["灵巧", "轻型"],
    "weight": "2磅",
    "cost": "10 GP",
    "mastery": "侵扰"
  },
  {
    "name_zh": "长弓",
    "name_en": "Longbow",
    "category": "军用远程",
    "damage": "1d8",
    "damageType": "穿刺",
    "properties": ["弹药 (射程 150/600；箭矢)", "重型", "双手"],
    "weight": "2磅",
    "cost": "50 GP",
    "mastery": "缓速"
  }
]
`;

function parseWeaponTemplates(): WeaponTemplate[] {
  try {
    // Extract JSON array from the markdown-like content
    const jsonMatch = weaponTemplatesMdContent.match(/(\[[\s\S]*\])/);
    if (jsonMatch && jsonMatch[1]) {
      return JSON.parse(jsonMatch[1]) as WeaponTemplate[];
    }
  } catch (error) {
    console.error('Error parsing weapon templates:', error);
  }
  return [];
}

function initializeEquipmentData() {
  if (weaponTemplates.length === 0) {
    weaponTemplates = parseWeaponTemplates();
  }
  if (armorTemplates.length === 0) {
    armorTemplates = [...exampleArmorTemplates]; // Using example directly for now
  }
  // allSelectableWeapons might need to be rethought if we are building items dynamically
  // For now, it's used by data_handling.ts, so we'll keep populating it with base weapons
  // and example magic weapons. This part will need significant changes later.
  if (allSelectableWeapons.length === 0) {
    const templateWeapons: ModuleSetupEquipmentItem[] = weaponTemplates.map(wt => ({
      name: wt.name_zh,
      type: '武器',
      baseItemName: wt.name_zh,
      description: `${wt.category} - ${wt.damage} ${wt.damageType}. 属性: ${wt.properties.join(', ') || '无'}.`,
      damage: wt.damage,
      damageType: wt.damageType,
      properties: wt.properties,
    }));
    allSelectableWeapons = [...templateWeapons, ...exampleMagicWeapons];
  }
}

// --- Main UI Creation Function for an Equipment Row ---
export function createFullEquipmentRow(initialData?: ModuleSetupEquipmentItem): HTMLElement {
  initializeEquipmentData();

  const itemRow = document.createElement('div');
  itemRow.className = 'dynamic-item-row full-equipment-row';

  // Container for top-level controls (type, name, remove)
  const controlsContainer = document.createElement('div');
  controlsContainer.className = 'equipment-controls-container';

  // 1. Equipment Type Selection
  const typeSelect = document.createElement('select');
  typeSelect.className = 'equipment-type-select';
  ['请选择类型', '武器', '防具', '饰品'].forEach(type => {
    const option = document.createElement('option');
    option.value = type === '请选择类型' ? '' : type;
    option.textContent = type;
    typeSelect.appendChild(option);
  });
  controlsContainer.appendChild(typeSelect);

  // 2. Custom Name Input (for the final item)
  const customNameInput = document.createElement('input');
  customNameInput.type = 'text';
  customNameInput.placeholder = '自定义物品名称 (可选)';
  customNameInput.className = 'equipment-custom-name';
  controlsContainer.appendChild(customNameInput);

  const removeRowButton = document.createElement('button');
  removeRowButton.type = 'button';
  removeRowButton.className = 'remove-item-button';
  removeRowButton.textContent = '移除此物品';
  removeRowButton.onclick = () => itemRow.remove();
  controlsContainer.appendChild(removeRowButton);

  itemRow.appendChild(controlsContainer);

  // --- Containers for dynamic content based on type selection ---
  const templateSelectContainer = document.createElement('div');
  templateSelectContainer.className = 'template-select-container';
  templateSelectContainer.style.display = 'none';
  itemRow.appendChild(templateSelectContainer);

  const basePropertiesDisplay = document.createElement('div');
  basePropertiesDisplay.className = 'base-properties-display';
  itemRow.appendChild(basePropertiesDisplay);

  const magicEffectsContainer = document.createElement('div');
  magicEffectsContainer.className = 'magic-effects-container';
  itemRow.appendChild(magicEffectsContainer);

  const addMagicEffectButton = document.createElement('button');
  addMagicEffectButton.type = 'button';
  addMagicEffectButton.textContent = '添加魔法效果';
  addMagicEffectButton.className = 'add-magic-effect-button';
  addMagicEffectButton.style.display = 'none'; // Initially hidden
  itemRow.appendChild(addMagicEffectButton);

  const customDescriptionTextarea = document.createElement('textarea');
  customDescriptionTextarea.placeholder = '自定义物品描述...';
  customDescriptionTextarea.className = 'equipment-custom-description';
  customDescriptionTextarea.rows = 2;
  itemRow.appendChild(customDescriptionTextarea);

  // --- Event Listener for Type Selection ---
  const populateTemplateSelectLogic = (selectedType: string, baseNameToSelect?: string) => {
    templateSelectContainer.innerHTML = ''; // Clear previous template select
    basePropertiesDisplay.innerHTML = ''; // Clear previous properties
    templateSelectContainer.style.display = 'none';

    if (!selectedType) {
      addMagicEffectButton.style.display = 'none';
      return;
    }
    addMagicEffectButton.style.display = 'block';

    const templateLabel = document.createElement('label');
    const templateSelect = document.createElement('select');
    templateSelect.className = 'equipment-template-select';

    const defaultOption = document.createElement('option');
    defaultOption.value = '';

    templateSelectContainer.appendChild(templateLabel);
    templateSelectContainer.appendChild(templateSelect);

    if (selectedType === '武器') {
      templateLabel.textContent = '选择武器模板:';
      defaultOption.textContent = '选择基础武器';
      templateSelect.appendChild(defaultOption);
      weaponTemplates.forEach(wt => {
        const option = document.createElement('option');
        option.value = wt.name_en; // Use name_en as a consistent key
        option.textContent = wt.name_zh;
        templateSelect.appendChild(option);
      });
      templateSelectContainer.style.display = 'flex';
    } else if (selectedType === '防具') {
      templateLabel.textContent = '选择防具模板:';
      defaultOption.textContent = '选择基础防具/盾牌';
      templateSelect.appendChild(defaultOption);
      armorTemplates.forEach(at => {
        const option = document.createElement('option');
        option.value = at.name_en;
        option.textContent = `${at.name_zh} (${at.category})`;
        templateSelect.appendChild(option);
      });
      templateSelectContainer.style.display = 'flex';
    } else if (selectedType === '饰品') {
      // No templates for "饰品" for now, or could add some generic ones
      basePropertiesDisplay.innerHTML = '饰品通常没有基础模板属性，请直接添加魔法效果和描述。';
      // If initialData is provided for an "饰品", it won't have a baseItemName to select in templateSelect
      // but other fields like customName, description, and magicEffects should still be populated below.
    }

    templateSelect.onchange = () => {
      basePropertiesDisplay.innerHTML = '';
      const selectedTemplateName = templateSelect.value;
      if (!selectedTemplateName) return;

      let detailsHtml = '';
      if (selectedType === '武器') {
        const template = weaponTemplates.find(wt => wt.name_en === selectedTemplateName);
        if (template) {
          detailsHtml = `<strong>基础: ${template.name_zh} (${template.category})</strong><br>
                                   伤害: ${template.damage} ${template.damageType}<br>
                                   属性: ${template.properties.join(', ') || '无'}<br>
                                   重量: ${template.weight}, 价格: ${template.cost}
                                   ${template.mastery ? `<br>精通: ${template.mastery}` : ''}`;
        }
      } else if (selectedType === '防具') {
        const template = armorTemplates.find(at => at.name_en === selectedTemplateName);
        if (template) {
          detailsHtml = `<strong>基础: ${template.name_zh} (${template.category})</strong><br>
                                   AC: ${template.ac_base} ${
            template.ac_dex_bonus ? '+ 敏捷' + (template.ac_dex_max ? ` (最高 ${template.ac_dex_max})` : '') : ''
          }<br>
                                   ${
                                     template.strength_requirement
                                       ? `力量需求: ${template.strength_requirement}<br>`
                                       : ''
                                   }
                                   ${template.stealth_disadvantage ? '隐匿劣势<br>' : ''}
                                   重量: ${template.weight}, 价格: ${template.cost}`;
        }
      }
      basePropertiesDisplay.innerHTML = detailsHtml;
      // Auto-fill custom name if empty and a template is chosen
      const currentCustomName = customNameInput.value.trim();
      if (!currentCustomName) {
        let templateNameZh: string | undefined;
        if (selectedType === '武器') {
          const template = weaponTemplates.find(wt => wt.name_en === selectedTemplateName);
          if (template) templateNameZh = template.name_zh;
        } else if (selectedType === '防具') {
          const template = armorTemplates.find(at => at.name_en === selectedTemplateName);
          if (template) templateNameZh = template.name_zh;
        }
        if (templateNameZh) {
          customNameInput.value = templateNameZh;
        }
      }
    };

    // If a baseNameToSelect is provided (from initialData), try to set it.
    if (baseNameToSelect) {
      // Attempt to find by name_en first (which is used as option value)
      let foundInOptions = Array.from(templateSelect.options).some(opt => opt.value === baseNameToSelect);
      if (foundInOptions) {
        templateSelect.value = baseNameToSelect;
      } else {
        // Fallback: if baseNameToSelect was stored as name_zh, find corresponding name_en
        let templateToMatch;
        if (selectedType === '武器') templateToMatch = weaponTemplates.find(t => t.name_zh === baseNameToSelect);
        else if (selectedType === '防具') templateToMatch = armorTemplates.find(t => t.name_zh === baseNameToSelect);

        if (templateToMatch && templateToMatch.name_en) {
          templateSelect.value = templateToMatch.name_en;
        }
      }
    }
    templateSelect.dispatchEvent(new Event('change')); // Trigger to display base properties even if value didn't change but options did
  };

  typeSelect.onchange = () => {
    populateTemplateSelectLogic(typeSelect.value); // baseNameToSelect will be undefined here, so it won't try to pre-select
  };

  addMagicEffectButton.onclick = () => {
    magicEffectsContainer.appendChild(createMagicEffectEditRow());
  };

  // --- Populate with initialData if provided ---
  if (initialData) {
    typeSelect.value = initialData.type || '';
    // Call populateTemplateSelectLogic directly, passing the baseItemName from initialData
    // This ensures that if a type is pre-selected, its corresponding template list is built,
    // and then the correct template within that list is selected.
    populateTemplateSelectLogic(typeSelect.value, initialData.baseItemName);

    customNameInput.value = initialData.name || '';
    customDescriptionTextarea.value = initialData.description || '';

    magicEffectsContainer.innerHTML = ''; // Clear any default
    initialData.magicEffects?.forEach(effect => {
      magicEffectsContainer.appendChild(createMagicEffectEditRow(effect));
    });
  }

  return itemRow;
}

// Export for use in data_handling.ts if needed for populating from saved data
export function createMagicEffectEditRow(effectToEdit?: MagicEffect): HTMLElement {
  const effectRow = document.createElement('div');
  effectRow.className = 'magic-effect-edit-row';

  const typeSelect = document.createElement('select');
  typeSelect.className = 'magic-effect-type-select';
  const defaultOption = document.createElement('option');
  defaultOption.value = '';
  defaultOption.textContent = '选择效果类型';
  typeSelect.appendChild(defaultOption);

  predefinedMagicEffectOptions.forEach(opt => {
    const option = document.createElement('option');
    option.value = opt.type;
    option.textContent = opt.label;
    typeSelect.appendChild(option);
  });
  effectRow.appendChild(typeSelect);

  const valueInputsContainer = document.createElement('div');
  valueInputsContainer.className = 'magic-effect-value-inputs';
  effectRow.appendChild(valueInputsContainer);

  typeSelect.onchange = () => {
    valueInputsContainer.innerHTML = ''; // Clear previous inputs
    const selectedEffectType = typeSelect.value as EffectType;
    const effectSchema = predefinedMagicEffectOptions.find(opt => opt.type === selectedEffectType);
    if (effectSchema) {
      effectSchema.valueSchema.forEach(fieldSchema => {
        const fieldLabel = document.createElement('label');
        fieldLabel.textContent = fieldSchema.label + ':';
        let inputElement: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;

        if (fieldSchema.type === 'textarea') {
          inputElement = document.createElement('textarea');
          inputElement.rows = 2;
          inputElement.classList.add('full-width-field'); // Add class for styling
        } else if (fieldSchema.type === 'select' && fieldSchema.options) {
          inputElement = document.createElement('select');
          fieldSchema.options.forEach(optVal => {
            const opt = document.createElement('option');
            opt.value = optVal;
            opt.textContent = optVal;
            (inputElement as HTMLSelectElement).appendChild(opt);
          });
        } else {
          inputElement = document.createElement('input');
          (inputElement as HTMLInputElement).type = fieldSchema.type;
        }

        // Set placeholder only if it's not an HTMLSelectElement
        if (!(inputElement instanceof HTMLSelectElement)) {
          inputElement.placeholder = fieldSchema.label;
        }
        inputElement.name = fieldSchema.name; // Used to reconstruct the 'value' object

        valueInputsContainer.appendChild(fieldLabel);
        valueInputsContainer.appendChild(inputElement);
      });
    }
  };

  if (effectToEdit) {
    // If editing an existing effect, try to populate
    typeSelect.value = effectToEdit.type;
    typeSelect.dispatchEvent(new Event('change')); // Trigger onchange to build value inputs
    // Then, iterate through effectToEdit.value and populate the generated inputs
    if (typeof effectToEdit.value === 'object' && effectToEdit.value !== null) {
      Object.entries(effectToEdit.value).forEach(([key, val]) => {
        const input = valueInputsContainer.querySelector(`[name="${key}"]`) as
          | HTMLInputElement
          | HTMLTextAreaElement
          | HTMLSelectElement;
        if (input) {
          input.value = String(val);
        }
      });
    } else {
      // For simple value types like ATTACK_BONUS
      const input = valueInputsContainer.querySelector(`[name="bonus"], [name="value"]`) as HTMLInputElement; // common names
      if (input) input.value = String(effectToEdit.value);
    }
  }

  const removeEffectButton = document.createElement('button');
  removeEffectButton.type = 'button';
  removeEffectButton.textContent = '移除效果';
  removeEffectButton.className = 'remove-magic-effect-button';
  removeEffectButton.onclick = () => effectRow.remove();
  effectRow.appendChild(removeEffectButton);

  return effectRow;
}

export function setupDynamicEquipmentList() {
  const container = document.getElementById('equipment-list-container');
  const addButton = document.getElementById('add-equipment-button');

  initializeEquipmentData(); // Ensure data is loaded on setup

  if (container && addButton) {
    addButton.addEventListener('click', () => {
      // container.appendChild(createWeaponSelectionRow()); // Old function
      container.appendChild(createFullEquipmentRow()); // New function
    });
  }
}

// Keep original inventory functions for now, or adapt them if needed
export function createDynamicInventoryItem(
  name: string = '',
  description: string = '',
  quantity: number = 1,
): HTMLElement {
  const itemRow = document.createElement('div');
  itemRow.className = 'dynamic-item-row';

  const nameInput = document.createElement('input');
  nameInput.type = 'text';
  nameInput.name = 'inventoryItemName[]';
  nameInput.placeholder = '物品名称';
  nameInput.value = name;

  const descInput = document.createElement('input');
  descInput.type = 'text';
  descInput.name = 'inventoryItemDetails[]';
  descInput.placeholder = '描述 (可选)';
  descInput.value = description;

  const quantityInput = document.createElement('input');
  quantityInput.type = 'number';
  quantityInput.name = 'inventoryItemQuantity[]';
  quantityInput.placeholder = '数量';
  quantityInput.value = quantity.toString();
  quantityInput.min = '1';

  const removeButton = document.createElement('button');
  removeButton.type = 'button';
  removeButton.className = 'remove-item-button';
  removeButton.textContent = '移除';
  removeButton.onclick = () => itemRow.remove();

  itemRow.appendChild(nameInput);
  itemRow.appendChild(descInput);
  itemRow.appendChild(quantityInput);
  itemRow.appendChild(removeButton);
  return itemRow;
}

export function setupDynamicInventoryList() {
  const container = document.getElementById('inventory-list-container');
  const addButton = document.getElementById('add-inventory-item-button');
  if (container && addButton) {
    addButton.addEventListener('click', () => {
      container.appendChild(createDynamicInventoryItem());
    });
  }
}
