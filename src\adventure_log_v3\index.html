<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>冒险日志</title>
</head>
<body>
    <div id="start-screen-container">
        <h1>冒险日志</h1>
        <p>准备好开始一段新的旅程了吗？</p>
        <button id="start-new-game-button">开始新冒险 (从世界书加载角色)</button>
        <p class="start-screen-note">注意：开始新冒险将尝试从名为 "RPG_Modules_Test.json" 的世界书中的 "PLAYER" 条目加载角色数据。如果已有进行中的游戏，它可能会被新游戏覆盖（取决于当前消息内容）。</p>
    </div>

    <div id="adventure-log-container" style="display: none;">
        <div id="player-status-area">
            <div class="status-row">
                <div id="health">生命值: ...</div>
                <div id="ac-display">AC: ...</div>
                <div id="time">时间: ...</div>
                <div id="location">地点: ...</div>
            </div>
            <button id="toggle-char-sheet-button">显示/隐藏详细角色卡</button>
            <button id="toggle-backpack-button">打开背包</button>
            <button id="toggle-spellbook-button">打开法术书</button>
            <div id="detailed-character-sheet" style="display: none;">
                <h4>角色信息</h4>
                <div class="status-row">
                    <div id="char-name-display">角色名: ...</div>
                    <div id="char-race-class-display">种族/职业: .../...</div>
                    <div id="char-level-display">等级: ...</div>
                </div>
                <h4>属性</h4>
                <div class="status-row attributes-display">
                    <div id="attr-str-display">力量: ..(..)</div>
                    <div id="attr-dex-display">敏捷: ..(..)</div>
                    <div id="attr-con-display">体质: ..(..)</div>
                    <div id="attr-int-display">智力: ..(..)</div>
                    <div id="attr-wis-display">感知: ..(..)</div>
                    <div id="attr-cha-display">魅力: ..(..)</div>
                </div>
                <h4>状态</h4>
                <div class="status-row simple-list-display">
                    <span>经验: <span id="exp-display">...</span></span>
                    <span>力竭: <span id="exhaustion-display">...</span></span>
                </div>

                <!-- 背包模块已被移除，其功能将由独立的 #backpack-interface 提供 -->

                <h4>熟练项</h4>
                <ul id="proficiencies-display"><li>...</li></ul>
                <h4>技能 (熟练)</h4>
                <ul id="skills-display"><li>...</li></ul>
                <h4>法术槽</h4>
                <div id="spell-slots-display">...</div>
                <h4>已准备法术</h4>
                <ul id="equipped-spells-display"><li>...</li></ul>
                <h4>当前任务</h4>
                <ul id="active-quests-display"><li>...</li></ul>
            </div>
        </div>

        <div id="main-narrative-area">
            <p>冒险即将开始...</p>
        </div>

        <div id="action-choices-area">
        </div>
    </div>

    <!-- 新的独立背包界面 -->
    <div id="backpack-interface" class="backpack-interface" style="display: none;">
        <div class="backpack-header">
            <h3>我的背包</h3>
            <button id="close-backpack-button" class="close-button">&times;</button>
        </div>
        <div class="backpack-content">
            <div id="backpack-currency-area" class="backpack-section">
                <h4>货币</h4>
                <p>金币: <span id="backpack-currency-gold">0</span></p>
                <p>银币: <span id="backpack-currency-silver">0</span></p>
                <p>铜币: <span id="backpack-currency-copper">0</span></p>
            </div>
            <div id="backpack-equipped-items-area" class="backpack-section">
                <h4>已装备</h4>
                <ul id="backpack-equipped-list"><li class="placeholder">无已装备物品。</li></ul>
            </div>
            <div id="backpack-inventory-items-area" class="backpack-section">
                <h4>物品栏</h4>
                <ul id="backpack-inventory-list"><li class="placeholder">物品栏为空。</li></ul>
            </div>
        </div>
    </div>

    <!-- 法术书界面 -->
    <div id="spellbook-interface" class="spellbook-interface" style="display: none;">
        <div class="spellbook-header">
            <h3>法术书</h3>
            <button id="close-spellbook-button" class="close-button">&times;</button>
        </div>
        <div class="spellbook-content">
            <div id="spell-slots-area" class="spellbook-section">
                <h4>法术槽</h4>
                <div id="spellbook-spell-slots-display">...</div>
            </div>
            <div id="prepared-spells-area" class="spellbook-section">
                <h4>已准备法术</h4>
                <ul id="spellbook-prepared-spells-list"><li class="placeholder">无已准备法术。</li></ul>
            </div>
            <div id="available-spells-area" class="spellbook-section">
                <h4>可用法术</h4>
                <div class="spell-filter">
                    <label for="spell-level-filter">法术等级:</label>
                    <select id="spell-level-filter">
                        <option value="all">全部</option>
                        <option value="0">戏法</option>
                        <option value="1">1环</option>
                        <option value="2">2环</option>
                        <option value="3">3环</option>
                        <option value="4">4环</option>
                        <option value="5">5环</option>
                        <option value="6">6环</option>
                        <option value="7">7环</option>
                        <option value="8">8环</option>
                        <option value="9">9环</option>
                    </select>
                    <label for="spell-school-filter">学派:</label>
                    <select id="spell-school-filter">
                        <option value="all">全部</option>
                        <option value="塑能">塑能</option>
                        <option value="咒法">咒法</option>
                        <option value="预言">预言</option>
                        <option value="惑控">惑控</option>
                        <option value="塑能">塑能</option>
                        <option value="幻术">幻术</option>
                        <option value="死灵">死灵</option>
                        <option value="变化">变化</option>
                        <option value="防护">防护</option>
                    </select>
                </div>
                <ul id="spellbook-available-spells-list"><li class="placeholder">正在加载法术...</li></ul>
            </div>
        </div>
    </div>

    <!-- 法术详情弹窗 -->
    <div id="spell-detail-modal" class="spell-modal" style="display: none;">
        <div class="spell-modal-content">
            <div class="spell-modal-header">
                <h3 id="spell-detail-name">法术名称</h3>
                <button id="close-spell-detail-button" class="close-button">&times;</button>
            </div>
            <div class="spell-modal-body">
                <div class="spell-basic-info">
                    <p><strong>等级:</strong> <span id="spell-detail-level">-</span></p>
                    <p><strong>学派:</strong> <span id="spell-detail-school">-</span></p>
                    <p><strong>施法时间:</strong> <span id="spell-detail-casting-time">-</span></p>
                    <p><strong>射程:</strong> <span id="spell-detail-range">-</span></p>
                    <p><strong>成分:</strong> <span id="spell-detail-components">-</span></p>
                    <p><strong>持续时间:</strong> <span id="spell-detail-duration">-</span></p>
                </div>
                <div class="spell-description">
                    <h4>描述</h4>
                    <p id="spell-detail-description">法术描述...</p>
                </div>
                <div class="spell-casting-options" id="spell-casting-options">
                    <h4>施法选项</h4>
                    <div class="spell-slot-selection">
                        <label for="spell-slot-level">使用法术槽:</label>
                        <select id="spell-slot-level">
                            <!-- 动态生成选项 -->
                        </select>
                    </div>
                    <div class="spell-target-selection">
                        <label for="spell-target-type">目标选择:</label>
                        <select id="spell-target-type">
                            <option value="self">自己</option>
                            <!-- 场景NPC选项将动态添加 -->
                            <option value="custom">自定义目标</option>
                        </select>
                        <div id="custom-target-input" style="display: none; margin-top: 8px;">
                            <input type="text" id="spell-target-custom" placeholder="输入目标名称">
                        </div>
                    </div>
                </div>
            </div>
            <div class="spell-modal-footer">
                <button id="cast-spell-button" class="cast-spell-button">施放法术</button>
                <button id="cancel-spell-button" class="cancel-button">取消</button>
            </div>
        </div>
    </div>
</body>
</html>
