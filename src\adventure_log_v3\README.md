# 动态生成式“跑团”/冒险日志 (Adventure Log)

## 1. 项目简介与目标

本项目旨在为 SillyTavern 开发一个动态生成式的文字冒险（跑团）界面。玩家将扮演一名冒险者，在一个由AI实时描述的奇幻或自定义世界中进行探索、互动并经历独特的故事。AI将扮演地下城主（DM）或世界引擎的角色，根据玩家的选择和预设的“世界书”规则，动态生成场景描述、事件、NPC对话以及玩家可进行的行动选项。

**核心目标**:

*   提供一个沉浸式的、由AI驱动的单人文字冒险体验。
*   界面能够清晰地展示环境描述、玩家状态、NPC对话和行动选项。
*   游戏流程和内容完全由AI返回的特定格式数据驱动。
*   借鉴现有 `galgame` 框架的成功经验，特别是在数据持久化、与SillyTavern集成以及响应式布局方面。
*   实现模块化的TypeScript代码结构，便于维护和扩展。

## 2. 核心玩法

1.  **角色创建/导入**: （初期可简化）玩家可以有一个简单的角色概念，其核心属性（如HP、MP、关键物品）由AI在游戏开始时设定或在过程中动态赋予。
2.  **场景探索**: AI描述玩家当前所处的环境、遇到的事物或NPC。
3.  **玩家决策**: 界面上会根据AI的描述显示若干行动选项，玩家选择其中一项。
4.  **AI响应**: AI根据玩家的选择，结合世界规则和当前状态，生成新的场景描述、事件结果、NPC回应，并提供新的行动选项。
5.  **状态管理**: 玩家的关键状态（如生命值、物品、任务线索）会由AI的输出动态更新，并在UI上显示。
6.  **日志记录**: 游戏的全过程（AI的描述和玩家的选择）将被记录下来，形成一个冒险日志。

## 3. UI 界面布局设想

界面将主要包含以下几个区域：

*   **主叙事区 (Main Narrative Area)**:
    *   占据屏幕大部分空间，用于显示AI生成的当前场景描述、事件详情、NPC的对话等。
    *   文本应清晰易读，支持滚动查看较长的描述。
*   **玩家状态区 (Player Status Area)**:
    *   通常位于界面顶部或侧边，固定显示玩家的关键信息。
    *   例如：`生命值: 80/100`, `魔法值: 50/50`, `当前地点: 幽暗森林`, `持有金币: 120`。
*   **行动选项区 (Action Choices Area)**:
    *   通常位于界面底部或主叙事区下方。
    *   以按钮列表的形式展示当前玩家可以进行的行动或对话选项。
*   **物品栏/技能栏 (Inventory/Skills Area - 可选，后期扩展)**:
    *   初期可以简化，物品信息直接在状态区或叙事中提及。
    *   后期可扩展为可交互的物品栏或技能列表。

**布局将遵循移动优先原则，使用宽度和 `aspect-ratio` 控制整体容器的高度，避免使用 `vh`。**

最近的UI调整进一步优化了布局：
*   移除了主容器 `#adventure-log-container` 的 `aspect-ratio` 限制，并调整了其高度管理方式（如移除 `min-height` 和 `overflow-y: auto`），使其高度能完全由内容撑开，以适应较长的叙述和选项，实现类似传统网页的滚动体验，滚动条由父级（最终为SillyTavern主界面）处理。
*   在宽屏显示下（默认样式），增大了 `#adventure-log-container` 的 `max-width` 至 `700px`。同时，通过移除 `body` 的 `flex` 布局并调整 `#adventure-log-container` 的 `margin` (设置为 `0 auto`)，使其在正常的文档流中从靠近页面顶部的位置开始显示，解决了之前在顶部和底部可能出现的多余空白问题。
*   通过在 `body` 背景上叠加一层从两侧向中间的半透明暗色渐变，增强了宽屏下内容区域的视觉焦点和整体美观度。
*   移除了叙述区域 `#main-narrative-area` 内部的滚动条，现在滚动由主容器或页面级别处理。
这些改动旨在提升不同屏幕尺寸下的用户体验，特别是在处理大量文本内容时，并确保布局的灵活性和美观性。

## 4. AI 数据格式约定

AI的交互完全基于JSON。AI返回的完整响应**必须**遵循以下格式：

```
查看系统
##@@_ADVENTURE_BEGIN_@@##
{
  "sceneType": "dialogue", // 或 "location", "combat", "system_message", "puzzle"
  "sceneTitle": "场景标题",
  "currentLocation": "当前地点",
  "time": "游戏内时间",
  // ... 其他 AdventureSceneJSON 结构中定义的字段 ...
  "narrative": [
    {
      "type": "description",
      "content": "这是第一行描述。\\n这是第二行描述，注意换行符必须是 \\\\n。"
    }
    // ...更多叙述条目...
  ],
  "playerChoices": [
    {
      "id": "A",
      "text": "选择A的文本[DC15 属性(技能)]",
      "actionCommand": "action_a_command"
    }
    // ...更多选项...
  ]
}
##@@_ADVENTURE_END_@@##
关闭系统
```

**关键点**:
*   核心JSON数据被独特的标记 `##@@_ADVENTURE_BEGIN_@@##` 和 `##@@_ADVENTURE_END_@@##` 包裹。
*   外部的 `查看系统` 和 `关闭系统` 行由AI按指示生成，客户端在提取核心JSON时会忽略它们，但在保存到历史世界书时会完整保留。
*   JSON对象本身必须严格符合 `src/adventure_log_v2/adventure_log_v2_ai_prompts.md` 中定义的 `AdventureSceneJSON` Schema。
*   **非常重要**：JSON字符串值内部如果需要换行，**必须**使用转义字符 `\\n`，而不是实际的换行符。JSON结构本身的格式化（如缩进）是允许的。
*   客户端 (`index.ts`) 负责解析 `##@@_ADVENTURE_BEGIN_@@##` 和 `##@@_ADVENTURE_END_@@##` 之间的内容为 `AdventureSceneJSON` 对象。如果解析失败（例如由于AI输出了不规范的JSON或错误的标记），会在UI上显示原始AI回复以供调试。

## 5. 主要数据结构 (TypeScript 接口)

核心数据结构定义在 `src/adventure_log_v2/index.ts` 和 `src/adventure_log_v2/adventure_log_v2_ai_prompts.md` 中，主要包括：
*   `PlayerState`: 定义了玩家角色的所有可追踪状态（属性、生命值、物品栏、技能、法术等）。
*   `AdventureSceneJSON`: 定义了AI为每个场景返回的完整JSON对象的结构，包括场景类型、标题、叙述内容、玩家选项、敌人状态（战斗时）、变量更新指令等。
*   `NarrativeEntry`: `AdventureSceneJSON` 中 `narrative` 数组的元素类型，用于承载不同类型的叙述文本（如环境描述、NPC对话、系统消息）。
*   `PlayerChoiceJSON`: `AdventureSceneJSON` 中 `playerChoices` 数组的元素类型，定义了每个玩家选项的显示文本、ID和内部动作指令。
    *注：本地不再维护 `AdventureLogEntry[]` 数组来存储历史记录，历史记录已转移到专用的世界书管理。*

## 6. 模块化脚本 (`src/adventure_log_v2/index.ts`)

所有核心逻辑，包括状态管理、UI渲染、AI交互、检定处理和持久化，都集中在 `index.ts` 文件中。

## 7. 关键功能与流程

*   **初始化 (`onMounted` 在 `index.ts` 中)**:
    *   加载必要的DOM元素引用。
    *   调用 `loadGameState()` 尝试从当前宿主消息恢复 `PlayerState` 和最新的 `AdventureSceneJSON`。
    *   如果成功恢复，则应用场景数据并更新UI；否则，显示开始新游戏的界面。
    *   为“开始新游戏”按钮等添加事件监听器。
*   **开始新游戏 (`handleStartNewGameClick` 在 `index.ts` 中)**:
    *   尝试从 `RPG_Modules_Test.json` 世界书的 "PLAYER" 条目加载角色数据填充 `PlayerState`，若失败则使用默认 `PlayerState`。
    *   生成一个初始的 `AdventureSceneJSON`。
    *   应用此初始场景数据到UI。
    *   将此初始场景（包装成AI回复的完整格式）保存到 `dndRPG_history.json` 世界书的新条目中，并激活该条目。
    *   将当前 `PlayerState` 和此初始场景的JSON持久化到当前宿主消息。
*   **玩家行动交互 (`handleActionChoice` 在 `index.ts` 中)**:
    1.  玩家在UI上点击一个行动选项按钮。
    2.  如果选项文本中包含检定信息（如 `[DC15 力量(运动)]` 或 `[攻击 目标 使用 武器 DC13]`），客户端会调用 `performCheck` 函数在本地执行1d20投骰，结合玩家的属性、熟练项等计算检定结果（成功/失败/重击等）。检定结果会通过 `toastr` 短暂提示给玩家，并附加到发送给AI的提示中。
    3.  构造发送给AI的提示词，其中包含：
        *   当前完整的 `PlayerState` (JSON格式)。
        *   上一个（即当前显示的）`AdventureSceneJSON` (作为直接上下文)。
        *   玩家选择的行动选项文本，以及附加的本地检定结果（如果有）。
        *   明确的指令，要求AI遵循预定义的JSON输出格式（包括 `查看系统`、`##@@_ADVENTURE_BEGIN_@@##` 等标记）。
    4.  通过 `triggerSlash('/gen ...')` 调用SillyTavern的API，将提示词发送给AI并等待回复。
    5.  接收AI的完整原始回复 (`aiRawResponseWithWrappers`)。
    6.  从原始回复中提取由 `##@@_ADVENTURE_BEGIN_@@##` 和 `##@@_ADVENTURE_END_@@##` 包裹的核心JSON字符串 (`coreJSONString`)。
    7.  调用 `parseAIResponse(coreJSONString)` 将提取的字符串解析为 `AdventureSceneJSON` 对象。
        *   **错误处理**：如果核心JSON字符串提取失败，或者 `parseAIResponse` 解析失败，则会在UI的主叙事区域显示AI的完整原始回复，并给出错误提示，方便调试。
    8.  如果解析成功 (`parsedScene` 有效)：
        *   调用 `applySceneData(parsedScene)`：
            *   更新 `currentSceneData`。
            *   如果场景JSON中包含 `variableUpdates` 指令，则调用 `applyVariableUpdate` 修改 `playerState`。
            *   调用 `renderNarrative(parsedScene)` 更新主叙事区。
            *   调用 `updatePlayerStatusDisplay()` 更新玩家状态显示区。
            *   调用 `renderActionChoices(parsedScene.playerChoices)` 更新行动选项按钮。
        *   调用 `persistGameState()` 将最新的 `PlayerState` 和 `currentSceneData`（序列化为JSON并按约定格式包裹）保存回当前宿主消息。
        *   **保存历史到世界书**: 将AI返回的**完整的、经过验证的“干净”回复块**（即从 `查看系统` 开始到 `关闭系统` 结束的文本，其中包含了唯一标记和核心JSON）作为一个新条目保存到名为 `dndRPG_history.json` 的专用世界书中。
            *   条目键名（Key）由 `parsedScene.sceneTitle` 和 `parsedScene.time`（经过字符清理和长度限制）以及一个短随机后缀构成，以提高可读性和唯一性。
            *   新创建的历史条目会通过 `/setentryfield` 命令将其 `constant` 属性设置为 `true`，从而激活“蓝灯”，使SillyTavern能自动将其内容加入到后续AI的上下文中。
*   **UI更新函数**:
    *   `renderNarrative`: 根据 `AdventureSceneJSON.narrative` 数组的内容和类型，动态生成HTML并更新主叙事区。
    *   `updatePlayerStatusDisplay`: 根据当前的 `PlayerState` 更新玩家状态面板的各项显示。
    *   `renderActionChoices`: 根据 `AdventureSceneJSON.playerChoices` 数组动态生成行动选项按钮。

## 8. 持久化机制

*   **当前状态持久化 (`persistGameState` 在 `index.ts` 中)**:
    *   最新的玩家状态 (`PlayerState` 对象，序列化为JSON）使用 `<!-- PLAYER_STATE_START -->` 和 `<!-- PLAYER_STATE_END -->` 标签包裹。
    *   最新的一个AI场景回复（完整的 `查看系统\n##@@_ADVENTURE_BEGIN_@@##\n{JSON}\n##@@_ADVENTURE_END_@@##\n关闭系统` 块）直接附加其后。
    *   这两部分内容组合后，通过 `setChatMessages` API写入当前聊天窗口的**宿主消息**中。
*   **历史记录持久化与上下文管理**:
    *   完整的游戏历史（即每一次AI生成的场景回复）**不再**存储在宿主消息的HTML注释块中。
    *   取而代之的是，每一个AI的“干净回复块”（从 `查看系统` 开始，到 `关闭系统` 结束的完整文本，其中包含了唯一标记和核心JSON）在被成功处理后，会通过 `/createentry` 命令被保存为一个独立的条目到名为 `dndRPG_history.json` 的专用世界书中。
    *   每个新创建的历史条目都会紧接着通过 `/setentryfield` 命令将其 `constant` 属性设置为 `true`（即激活“蓝灯”）。
    *   SillyTavern的机制会自动将所有“蓝灯”激活的世界书条目内容加入到发送给AI的上下文中，从而实现了历史记录的传递。
    *   历史条目的键名（Key）由场景标题和时间（如果可用）以及一个随机后缀构成，例如：`失落神庙的秘密_第一天_黄昏_a1b2c`，以提高在世界书中的可读性和唯一性。
*   **加载机制 (`loadGameState` 在 `index.ts` 中)**:
    *   从当前宿主消息中，分别解析由 `<!-- PLAYER_STATE_START -->` 包裹的 `PlayerState` JSON，以及由 `##@@_ADVENTURE_BEGIN_@@##` 包裹的最新的场景JSON。
    *   不再需要从宿主消息中解析和重建大量的旧历史记录条目。AI通过SillyTavern的世界书机制自动获取历史上下文。

## 9. 与SillyTavern的集成

*   **iframe加载**: UI通过SillyTavern的机制加载到一个iframe中。
*   **API调用**: 使用 `window.TavernHelper` 或 `window.SillyTavern` 提供的API (如 `triggerSlash`, `getLastMessageId`, `setChatMessages`) 进行交互。
*   **正则表达式**: 配置SillyTavern的“格式化输出”以正确处理持久化数据。

---
此计划为初步设想，具体细节将在后续与你讨论和开发过程中进一步完善。
