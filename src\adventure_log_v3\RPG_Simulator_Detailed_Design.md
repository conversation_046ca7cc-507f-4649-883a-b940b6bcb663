# 单人跑团模拟器 - 详细设计文档

## 1. 项目核心目标

基于现有的 `src/adventure_log` 项目框架，开发一个功能完整的单人跑团模拟器。在此模拟器中，用户扮演玩家角色（PL），AI扮演地下城主（DM）。

## 2. 核心功能模块与界面设计

### 2.1. 游戏初始设置界面（模组设定）

*   **目的**: 在角色创建之前，允许用户设定本次冒险的模组（即游戏背景、主题、核心剧情线等）。
*   **功能**:
    1.  **用户自定义模组**: 提供输入区域，允许用户粘贴或输入已有的模组文本内容。
    2.  **AI随机生成模组**: 提供一个按钮，点击后由AI根据预设的某些参数或完全随机地生成一个新的模组。
*   **数据处理**:
    *   无论是用户输入还是AI生成，模组内容在确认后，将自动在当前角色（或即将创建的角色）所绑定的世界书（Lorebook）中创建一个新的条目。
    *   此世界书条目将用于存储该模组的剧本、关键NPC、地点、事件以及对AI扮演DM的特定指导。
*   **界面**: 一个简单的界面，包含文本输入框（用于自定义模组）、“AI生成模组”按钮、以及“确认模组并开始创建角色”按钮。

### 2.2. 角色创建界面

*   **目的**: 允许玩家创建或生成其在游戏中的角色卡。
*   **功能**:
    1.  **手动创建角色**:
        *   界面提供指定的输入框/选择器，供玩家逐项填写角色数据（如姓名、种族、职业、属性点分配、技能选择、初始装备和法术等）。
        *   所有数据由玩家自行定义和添加。
    2.  **AI随机生成角色**:
        *   提供一个“AI生成角色”按钮。
        *   点击后，AI将根据D&D规则和一定的随机性，生成一套完整的角色数据。
*   **数据存储**:
    *   角色创建完成后（无论是手动还是AI生成），所有角色数据将被保存到一个顶层的、易于访问的“玩家数据对象”中。此对象将在整个游戏过程中被引用和更新。
*   **界面**: 包含用于输入各项角色信息的表单字段（文本框、下拉选择等），以及“手动完成创建”和“AI生成角色”按钮。

### 2.3. 玩家信息显示界面 (非AI交互式)

*   **目的**: 实时、清晰地展示当前玩家角色的核心信息。
*   **显示内容 (简洁化，只显示强相关信息)**:
    *   **基本信息**: 种族 (Race), 职业 (Class)
    *   **核心属性 (数值显示)**: 力量 (Str), 敏捷 (Dex), 体质 (Con), 智力 (Int), 感知 (Wis), 魅力 (Cha)
    *   **战斗相关**: 当前生命值 (Current HP), 总生命值 (Max HP), 护甲等级 (AC)
    *   **资源**: 法术环位数量 (Spell Slots per level), 货币 (Currency), 经验值 (EXP)
    *   **能力**: 熟练项 (Proficiencies - 列表), 技能 (Skills - 仅列出技能名称)
    *   **装备与法术**:
        *   已装备的法术 (Equipped Spells - 仅列出法术名称)
        *   装备 (Equipment - 仅列出物品名称)
*   **数据来源**: 直接读取顶层的“玩家数据对象”。
*   **界面**: 一个静态的信息面板，布局清晰，易于玩家随时查看。

### 2.4. 冒险日志历史记录界面 (非AI交互式)

*   **目的**: 提供一个地方给玩家回顾已经发生的冒险剧情。
*   **显示内容**: 按时间顺序展示历史的冒险记录块（即之前 `adventure_log` 中 `fullHistoryLog` 存储的那些纯净场景描述和玩家选择）。
*   **界面**: 一个可滚动的文本区域，用于显示格式化后的历史日志。

### 2.5. 核心游戏交互界面 (基于 `adventure_log`)

此界面是玩家与AI DM进行互动的主要场所。

#### 2.5.1. 常规交互 (非战斗)

*   **功能**: 基本保持 `adventure_log` 的现有功能：显示场景描述、玩家状态、AI提供的行动选项。
*   **本地技能检定逻辑**:
    *   当AI返回的行动选项中明确包含进行某项属性/技能检定的描述时（例如：“行动选项A--尝试撬开[DC14 敏捷（巧手）]这个锁。--HH:MM”）。
    *   玩家点击该选项后，在向AI发送选择结果的短提示词**之前**：
        1.  **本地计算**: 客户端根据当前角色的相关属性值、调整值（如熟练加值）和一次本地随机掷骰（例如，敏捷检定可能基于1d20 + 敏捷调整值 + 熟练（如果适用巧手技能）），计算出本次检定的最终结果。
        2.  **结果判断**: 将计算结果与AI指定的DC进行比较，判断成功或失败。
        3.  **附加提示**: 在发送给AI的短提示词中，明确附加此次检定的结果。例如，如果玩家选择了上述撬锁选项，且本地计算判定为失败，则发送给AI的提示词可能类似于：“玩家选择尝试撬锁，[检定失败]。”
        4.  AI将根据这个附加了检定结果的提示词来生成后续的场景描述和发展。
    *   **界面显示**: 本地计算的过程和结果（例如，“敏捷(巧手)检定: 1d20[8] + 敏捷调整[+2] + 熟练[+3] = 13 vs DC14 -> 失败”）应清晰地显示给玩家，例如通过一个临时的toast提示或者在界面特定区域显示。

#### 2.5.2. 战斗交互界面

*   **触发**: 当游戏逻辑进入战斗状态时，界面布局或元素将发生变化。
*   **敌人信息显示**:
    *   简洁地显示当前面对的一个或多个敌人的关键信息：名称/类型, 等级 (Level, 可选), 当前生命值 (HP), 以及**意图 (Intent)**。
    *   **敌人意图**: 由AI在每次敌人行动时决定并返回（例如，攻击、施法、防御、尝试特殊动作、逃跑等）。这将显示给玩家，作为一种战术提示。
*   **AI攻击处理**:
    *   AI返回的敌人攻击指令将包含特定的格式，例如：`敌人攻击--"火焰箭[2d6 火焰]"--HH:MM` 或 `敌人攻击--"爪击[1d4+2 挥砍]"--HH:MM`。
    *   **本地伤害计算**:
        1.  客户端提取攻击名称（“火焰箭”、“爪击”）和伤害骰（“2d6 火焰”、“1d4+2 挥砍”）。
        2.  本地进行掷骰计算（例如，`2d6` 表示掷两个六面骰并求和；`1d4+2` 表示掷一个四面骰再加2）。
        3.  根据玩家角色的AC（护甲等级）和其他可能的防御状态（如抗性、豁免成功等，这部分需要AI在描述攻击时提供线索或由更复杂的规则处理，初期可简化为直接命中），判定攻击是否命中以及最终伤害值。
        4.  更新玩家的当前HP。
        5.  **文本反馈**: 在界面上清晰显示战斗信息，例如：“[敌人名称]的[攻击名称]对你造成了[计算出的伤害值]点[伤害类型]伤害！”
*   **玩家行动**: 玩家的行动选项保持不变（攻击、施法、使用物品等）。
*   **战斗结束**:
    *   通过检测玩家HP或所有敌人HP是否归零来判断战斗结束。
    *   移除“逃跑”选项（如果之前有的话，因为敌人意图已包含逃跑可能，玩家的逃跑应是另一种行动）。
*   **场景描述**: 战斗中的场景描述依然由AI提供，以渲染战斗氛围。

### 2.6. 变量更新机制

*   **AI指令**: 强制AI在需要更新玩家角色卡上的特定变量时（例如，获得经验值、金钱增加、属性因诅咒临时降低等），返回一种特殊格式的消息。
    *   **示例格式**: `<变量更新:玩家属性> 属性--HP--增加--10--HH:MM` 或 `变量--"经验值"--设置--"150"--HH:MM` （具体格式待定，需要简洁且易于解析）。
*   **客户端处理**:
    *   脚本读取到这种特殊格式的更新指令后，解析出变量名、操作类型（增加、减少、设置）和值。
    *   直接更新顶层的“玩家数据对象”中对应的数据。
    *   玩家信息显示界面将自动反映这些变化。
*   **历史记录**: 玩家数据面板本身不显示历史变更记录，只显示当前最新状态。变量的变更过程可以作为事件记录在冒险日志历史中（例如，通过 `提示信息`）。

## 3. 数据结构设想 (初步)

### 3.1. 玩家角色卡数据 (`playerData`)

一个顶层JavaScript对象，包含所有角色信息。
```typescript
interface PlayerData {
  name: string;
  race: string;
  class: string;
  level: number;
  exp: number;
  hp: { current: number; max: number };
  currency: { type: string; amount: number }; // e.g., { type: "金币", amount: 100 }
  attributes: {
    strength: number;
    dexterity: number;
    constitution: number;
    intelligence: number;
    wisdom: number;
    charisma: number;
  };
  modifiers: { // 根据属性自动计算或设定
    strength_mod: number;
    // ... etc.
  };
  proficiencies: string[]; // 武器、护甲、技能、豁免等
  skills: { [skillName: string]: { proficient: boolean; value?: number } }; // e.g., { "巧手": { proficient: true } }
  spellSlots: { [level: string]: { current: number; max: number } }; // e.g., { "1": { current: 4, max: 4 } }
  equippedSpells: { name: string; type: string; damage?: string; description?: string }[]; // damage e.g., "1d8 火焰"
  equipment: { name: string; type: string; value?: string; description?: string }[]; // e.g., { name: "长剑", type: "武器", value: "1d8 挥砍"}
  inventory: { name: string; quantity: number; description?: string }[];
  // ... 可能还有AC, 速度, 豁免调整值等衍生数据
}
```

### 3.2. 游戏模组数据 (存储在世界书条目中)

可能是一个包含以下内容的文本或JSON结构：
*   模组标题
*   背景故事和设定
*   主要目标/任务线索
*   关键NPC列表及其简介/动机
*   重要地点描述
*   可能的事件和遭遇
*   对AI DM的特定行为指导（例如，强调某种氛围，或在特定条件下触发特定事件）

## 4. AI交互格式扩展 (除了场景和NPC块)

需要定义新的AI返回格式，或在现有格式中加入新的键名来支持：
*   **变量更新指令**: 如上2.6所述。
*   **敌人战斗信息**:
    *   在 `<场景:...>` 或新的 `<战斗状态:...>` 块内。
    *   `敌人信息--"[名称]:[等级]:[当前HP]/[最大HP]:[意图]"--HH:MM` (可多条)
    *   `敌人攻击--"[攻击名称][伤害骰][伤害类型]"--HH:MM`
*   **本地检定提示**: AI的选项中包含检定DC和类型，例如：`行动选项A--"尝试说服守卫[DC15 魅力(说服)]"--HH:MM`

## 5. 基础测试目标

首要的、最基础的测试功能是验证“模组设定”界面的核心逻辑：
1.  创建一个基础的“模组设定”界面。
2.  界面上有一个按钮（例如“创建示例模组并绑定到世界书”）。
3.  点击此按钮后，脚本应能成功调用SillyTavern的API（可能是通过 `triggerSlash` 执行 `/createentry` 或类似的命令），在当前角色（或一个预定义测试角色）的世界书中自动创建一个新的条目，并将一些预设的示例模组内容存入该条目。
4.  能够通过某种方式（例如，开发者工具查看网络请求，或手动检查世界书）确认条目已成功创建。

---

本文档将作为后续开发工作的指导蓝图。
