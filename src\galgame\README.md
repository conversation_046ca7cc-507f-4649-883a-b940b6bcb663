# SillyTavern Galgame 风格交互界面

## 1. 项目简介与目标

本项目旨在为 SillyTavern 开发一个具有 Galgame (视觉小说) 风格的交互界面。用户通过此界面与AI进行互动，体验基于场景和选择的叙事。界面优先考虑在移动设备上的显示效果，并能根据AI返回的特定格式数据动态更新内容和选项。

## 2. 主要特性

*   **状态显示**: 实时显示游戏内的关键状态，如时间、玩家体力。
*   **场景与对话**: 清晰展示当前场景描述、角色对话内容。
*   **动态选项**: 根据当前场景和AI的回复，动态生成供玩家选择的行动或对话选项按钮。
*   **AI驱动更新**: 界面完全由AI返回的特定格式数据驱动，AI的输出决定了UI的呈现。
*   **历史记录与持久化**:
    *   能够记录玩家的选择和AI生成的场景，形成完整的交互历史。
    *   通过SillyTavern的消息持久化机制，将历史记录和当前状态保存在宿主消息中。
    *   界面加载时能从宿主消息中恢复之前的状态。
*   **SillyTavern集成**:
    *   作为iframe嵌入SillyTavern。
    *   利用SillyTavern的正则表达式功能，在主聊天界面中仅显示最新的Galgame UI，并隐藏历史数据块。

## 3. 技术栈

*   **前端**: HTML, SCSS, TypeScript
*   **核心库 (全局提供，无需手动导入)**: jQuery, jQuery-UI, Lodash, Toastr, Yaml
*   **构建与打包**: Webpack (根据 `webpack.config.ts` 和 `package.json` 推断)

## 4. 核心实现细节

### 4.1. 脚本与样式加载

*   所有TypeScript代码的入口为 `src/galgame/index.ts`。
*   SCSS样式 (`src/galgame/index.scss`) 通过在 `index.ts` 中 `import './index.scss';` 的方式导入，而非在HTML中直接链接。
*   不允许在 `index.html` 中直接使用 `<script>` 或 `<link rel="stylesheet">` 引入核心脚本和样式。

### 4.2. 布局与响应式设计

*   界面布局优先考虑移动端。
*   严格禁止使用 `vh` 单位设置元素高度。高度控制应通过宽度配合 `aspect-ratio` CSS属性实现，以确保内容区域能根据设备宽度动态调整高度。

### 4.3. AI 数据格式约定

AI被期望返回特定格式的文本块，以驱动UI更新。
*   **核心数据块**:
    *   场景数据以 `<地点:场景名称> ... </地点:场景名称>` 包裹。
    *   人物对话数据以 `<人物:角色名称> ... </人物:角色名称>` 包裹。
*   **数据行**: 块内部的每一行具体数据（如时间、体力、选项文本）都必须以 `--HH:MM` 作为行尾标记。
    *   示例: `时间--"08:00"--HH:MM`
*   **AI响应包裹**: AI返回的完整响应（包含核心数据块）应由 `查看系统\nmsg_start\n...\nmsg_end\n关闭系统` 包裹。这是SillyTavern正则表达式提取最新场景数据的依据。

### 4.4. 历史记录机制 (`fullHistoryLog`)

*   `fullHistoryLog`: 一个TypeScript数组，存储 `HistoryLogEntry` 对象。每个对象包含：
    *   `sceneContent`: 纯净的核心场景数据块 (即 `<地点:...>` 或 `<人物:...>` 块，**不含** `查看系统...` 包裹，也**不含**AI的思考过程如 `<thinking>...</thinking>`)。
    *   `userChoiceText` (可选): 用户在该场景下做出的选择文本。
*   **持久化格式**:
    *   旧的历史记录条目（`sceneContent` + `userChoiceText`）被拼接起来，并由 `<!-- OLD_GALGAME_HISTORY_CHUNK_START -->` 和 `<!-- OLD_GALGAME_HISTORY_CHUNK_END -->` HTML注释包裹。条目之间用 `\n------------------------\n` 分隔。
    *   最新的场景数据（其 `sceneContent`）则被 `查看系统\nmsg_start\n...\nmsg_end\n关闭系统` 包裹，并附加在旧历史注释块之后。
*   **数据清理**:
    *   `stripOuterWrappers`: 从AI返回的完整响应中移除最外层的 `查看系统...` 包裹。
    *   `extractActualSceneBlock`: 从可能包含AI思考过程（如 `<thinking>` 块）的文本中，提取出纯净的核心场景数据块 (`<地点:...>` 或 `<人物:...>`)。此函数用于确保存入 `fullHistoryLog` 的 `sceneContent` 是干净的。
    *   在持久化旧历史时，会再次检查并确保旧的 `sceneContent` 不包含意外的 `查看系统...` 包裹。

### 4.5. 关键函数

*   `onMounted()`: 界面加载时的入口函数，负责初始化、获取DOM元素、从宿主消息恢复状态。
*   `parseAndApplyGalgameData(sceneContent: string)`: 解析纯净的核心场景数据块，更新 `gameState` 和UI显示。此函数现在能跳过场景数据块之前的无关内容（如AI的思考过程）。
*   `handleChoiceClick(actionKey: string, choiceText: string)`: 处理用户点击选项的逻辑，构造发送给AI的提示，接收AI响应，调用 `extractActualSceneBlock` 清理响应，更新历史记录和UI，并触发持久化。
*   `persistCurrentStateWithHistory()`: 将 `fullHistoryLog` 的内容按约定格式组合，并写回SillyTavern的宿主消息中。
*   `rebuildFullHistoryLog(fullDataString: string)`: 从宿主消息中读取持久化的字符串，重建 `fullHistoryLog`。
*   `extractLatestSceneContent(fullDataString: string)`: 从完整的持久化字符串中提取出最新的、被 `查看系统...` 包裹的场景数据，并移除其包裹。

### 4.6. SillyTavern 集成

*   **iframe加载**: UI通过SillyTavern的机制加载到一个iframe中。
*   **正则表达式**: SillyTavern的“格式化输出”功能使用正则表达式：
    1.  `<!-- OLD_GALGAME_HISTORY_CHUNK_START -->[\s\S]*?<!-- OLD_GALGAME_HISTORY_CHUNK_END -->\s*` 替换为空，以隐藏所有旧历史。
    2.  `查看系统\s*msg_start([\s\S]+?)msg_end\s*关闭系统` 匹配最新的场景数据块（捕获组1为核心内容），用于加载iframe并传递数据。

## 5. 开发中遇到的主要挑战与对策

*   **AI返回格式的不可靠性**:
    *   **问题**: AI有时不严格遵守 `--HH:MM` 行尾标记，或在预期的场景数据外返回额外的思考过程文本（如 `<thinking>...</thinking>` 块），导致解析失败。
    *   **对策**:
        *   不断优化和强调提供给AI的提示词（World Info等），要求其严格遵守格式。
        *   增强 `parseAndApplyGalgameData` 函数的健壮性，使其能够先定位到有效的 `<地点:...>` 或 `<人物:...>` 标签再开始解析，从而忽略前面的无关内容。
        *   引入 `extractActualSceneBlock` 函数，在将AI响应存入历史记录前，主动提取纯净的核心场景数据块，去除AI的思考过程等无关信息。

*   **历史记录管理与持久化的复杂性**:
    *   **问题**: 如何在AI不直接管理历史的情况下，让AI生成回复时拥有上下文，并且UI能在刷新后恢复状态，同时还要配合SillyTavern的显示机制。
    *   **对策**:
        *   设计了 `fullHistoryLog` 来存储纯净的场景历史。
        *   采用 `<!-- OLD_GALGAME_HISTORY_CHUNK_START -->` 和 `<!-- OLD_GALGAME_HISTORY_CHUNK_END -->` HTML注释来包裹旧历史，便于SillyTavern通过正则隐藏。
        *   确保最新的场景数据块（在旧历史注释之外）始终带有 `查看系统...` 包裹，供SillyTavern的iframe加载逻辑使用。
        *   迭代优化了 `persistCurrentStateWithHistory` 和 `rebuildFullHistoryLog` 函数，确保数据存取的正确性。

*   **TypeScript类型与编译问题**:
    *   **问题**: 开发过程中遇到过类型不匹配、模块级变量“找不到名称”等编译错误。
    *   **对策**: 仔细检查接口定义与实际使用，统一变量命名和引用，确保类型安全。有时需要重新加载或重启TypeScript服务来解决IDE层面的状态不同步问题。

*   **与SillyTavern工具链的交互 (如 `replace_in_file` 工具)**:
    *   **问题**: `replace_in_file` 工具对 `SEARCH` 块的精确匹配要求非常高，本地文件的自动格式化（如Prettier）可能导致与模型基于旧状态生成的 `SEARCH` 块不一致，从而引发“Diff Edit Mismatch”错误。
    *   **对策**:
        *   尽量缩小 `SEARCH` 块的范围，只包含绝对必要且独特的几行。
        *   分多次、小步骤地使用 `replace_in_file`，每次修改后都基于工具返回的最新文件内容进行后续操作。
        *   在极端情况下，可以考虑使用 `write_to_file` 覆盖整个文件作为后备方案（但需谨慎，易引入其他问题）。

## 6. 如何使用 (简要)

此界面作为SillyTavern的一个扩展部分运行。
1.  确保相关的HTML (`index.html`), SCSS (`index.scss`), 和 TypeScript (`index.ts`) 文件位于 `src/galgame/` 目录下。
2.  确保 `webpack.config.ts` 正确配置以编译此入口。
3.  在SillyTavern中，通过适当的机制（如自定义按钮、Quick Reply或扩展API）触发加载此界面的iframe。
4.  AI（通过角色卡、世界书、提示词等）需要被配置为能够理解并生成符合上述“AI数据格式约定”的响应。
5.  在SillyTavern的“格式化输出”设置中配置相应的正则表达式，以正确显示UI并隐藏历史数据。

---
*文档由AI辅助生成和总结*
