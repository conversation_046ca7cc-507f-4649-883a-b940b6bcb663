import { getAttributeModifier, getProficiencyBonus } from './attributes';
import type { CharacterGenerationData } from '../types'; // For type hinting

export function calculateSkillFinalValue(
  skillName: string, // Not directly used in calculation but good for context/logging if needed
  attributeModifier: number,
  isProficient: boolean,
  customModifier: number,
  characterLevel: number
): number {
    let final = attributeModifier + customModifier;
    if (isProficient) {
        final += getProficiencyBonus(characterLevel);
    }
    return final;
}

export function updateSkillFinalValue(skillItemElement: HTMLElement) {
    const proficiencyCheckbox = skillItemElement.querySelector('input[type="checkbox"]') as HTMLInputElement;
    const customModInput = skillItemElement.querySelector('.skill-modifier-input') as HTMLInputElement;
    const finalValueDisplay = skillItemElement.querySelector('.skill-final-value') as HTMLInputElement;
    // Ensure dataset.attribute is correctly typed if it's used to index CharacterGenerationData['attributes']
    const attributeName = proficiencyCheckbox.dataset.attribute as keyof CharacterGenerationData['attributes'] | undefined;
    const characterLevelInput = document.getElementById('char-level-1') as HTMLInputElement;

    if (!proficiencyCheckbox || !customModInput || !finalValueDisplay || !attributeName || !characterLevelInput) {
        // console.warn('Missing elements for skill calculation:', proficiencyCheckbox, customModInput, finalValueDisplay, attributeName, characterLevelInput);
        return;
    }
    
    // Construct the ID like 'attr-str-final', 'attr-dex-final', etc.
    // The attributeName from dataset might be 'strength', 'dexterity', so we take the first 3 chars.
    const attrAbbreviation = attributeName.substring(0,3).toLowerCase();
    const attributeFinalValueInput = document.getElementById(`attr-${attrAbbreviation}-final`) as HTMLInputElement;
    
    if (!attributeFinalValueInput) {
        // console.warn(`Attribute final value input not found for: attr-${attrAbbreviation}-final`);
        return;
    }
    
    const attributeScore = parseInt(attributeFinalValueInput.value, 10) || 10;
    const attributeMod = getAttributeModifier(attributeScore);
    const isProficient = proficiencyCheckbox.checked;
    const customMod = parseInt(customModInput.value, 10) || 0;
    const characterLevel = parseInt(characterLevelInput.value, 10) || 1;

    const finalSkillValue = calculateSkillFinalValue(
        proficiencyCheckbox.dataset.skillName || '', 
        attributeMod, 
        isProficient, 
        customMod, 
        characterLevel
    );
    finalValueDisplay.value = (finalSkillValue >= 0 ? '+' : '') + finalSkillValue.toString();
}

export function updateAllSkillFinalValues() {
    document.querySelectorAll('.skill-item').forEach(skillItem => {
        updateSkillFinalValue(skillItem as HTMLElement);
    });
}

export function setupSkillCalculations() {
    document.querySelectorAll('.skill-item').forEach(skillItem => {
        const proficiencyCheckbox = skillItem.querySelector('input[type="checkbox"]') as HTMLInputElement;
        const customModInput = skillItem.querySelector('.skill-modifier-input') as HTMLInputElement;

        if (proficiencyCheckbox) {
            proficiencyCheckbox.addEventListener('change', () => updateSkillFinalValue(skillItem as HTMLElement));
        }
        if (customModInput) {
            customModInput.addEventListener('input', () => updateSkillFinalValue(skillItem as HTMLElement));
        }
        // Initial calculation for each skill
        updateSkillFinalValue(skillItem as HTMLElement);
    });

    const clearSkillsButton = document.getElementById('clear-skills-button');
    if (clearSkillsButton) {
        clearSkillsButton.addEventListener('click', () => {
            document.querySelectorAll('.skill-item').forEach(skillItem => {
                const proficiencyCheckbox = skillItem.querySelector('input[type="checkbox"]') as HTMLInputElement;
                const customModInput = skillItem.querySelector('.skill-modifier-input') as HTMLInputElement;
                if (proficiencyCheckbox) proficiencyCheckbox.checked = false;
                if (customModInput) customModInput.value = '0';
                updateSkillFinalValue(skillItem as HTMLElement);
            });
        });
    }
    // Call once at setup to ensure all values are correct if inputs already have values.
    // updateAllSkillFinalValues(); // This is now done individually in the loop above.
}
