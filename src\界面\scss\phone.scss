.phone-container {
  width: 300px; // 示例宽度，可以根据需要调整
  aspect-ratio: 9 / 19.5; // 模拟iPhone X/XS/11 Pro的比例
  border: 8px solid black;
  border-radius: 36px; // 模拟手机边框圆角
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden; // 确保刘海和屏幕内容在边框内
  background-color: #f0f0f0; // 手机背景色
  margin: 20px auto; // 居中显示
}

.phone-notch {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40%; // 刘海宽度
  height: 20px; // 刘海高度
  background-color: black;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  z-index: 2; // 确保刘海在屏幕内容之上
}

.phone-screen {
  width: 100%;
  height: 100%;
  background-color: white; // 屏幕背景色
  display: flex;
  flex-direction: column;
  overflow: hidden; // 确保屏幕内容不超出
}
