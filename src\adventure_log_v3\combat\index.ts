import {
  EquipmentItem,
  PlayerState,
  WeaponTemplate,
  DamageRollResult,
  CheckResult,
  MagicEffect, // Added as it's used in performCheck
  // EffectType // Not directly used here, but MagicEffect uses it
} from '../types';
import {
  parseDamageString,
  rollDice,
  getProficiencyBonus,
  getEffectiveWeaponProperties
} from '../utils';
// weaponTemplates will be imported from core/state where these functions are called,
// or passed as arguments if these functions are pure.
// For now, getEffectiveWeaponProperties takes weaponTemplates as an argument.

export function calculateDamageRoll(
  attackingWeapon: EquipmentItem,
  attributeModifier: number,
  isCritical: boolean,
  weaponTemplates: WeaponTemplate[],
  useVersatileDamage?: boolean, // Added parameter for versatile handling
): DamageRollResult {
  let baseDamageDice = attackingWeapon.damage || '';
  let baseDamageType = attackingWeapon.damageType || '未知';
  const weaponName = attackingWeapon.name;
  let detailsParts: string[] = [];
  let usedVersatile = false;

  if (attackingWeapon.baseItemName && weaponTemplates.length > 0) {
    const template = weaponTemplates.find(
      wt => wt.name_zh === attackingWeapon.baseItemName || wt.name_en === attackingWeapon.baseItemName,
    );
    if (template) {
      baseDamageDice = template.damage;
      baseDamageType = template.damageType;
    }
  }

  const effectiveProps = getEffectiveWeaponProperties(attackingWeapon, weaponTemplates);

  // Handle Versatile property
  if (useVersatileDamage) {
    const versatileProperty = effectiveProps.find(prop => prop.startsWith('多用'));
    if (versatileProperty) {
      const versatileMatch = versatileProperty.match(/多用\s*\(([^)]+)\)/);
      if (versatileMatch && versatileMatch[1]) {
        baseDamageDice = versatileMatch[1]; // Use versatile damage dice
        usedVersatile = true;
      }
    }
  }
  // If not using versatile or no versatile property, baseDamageDice remains as initialized
  // (either from attackingWeapon.damage or from template if not overridden by versatile)

  let totalDamage = 0;
  const damageBreakdown: { type: string; amount: number; source: string }[] = [];

  const parsedBaseDamage = parseDamageString(baseDamageDice);
  if (parsedBaseDamage) {
    let rolledDamage = rollDice(parsedBaseDamage.count, parsedBaseDamage.die);
    let baseDicePart = `${parsedBaseDamage.count}d${parsedBaseDamage.die}`;
    let damageSourceName = usedVersatile ? `基础(多用 ${baseDicePart})` : `基础(${baseDicePart})`;

    if (isCritical) {
      const criticalRoll = rollDice(parsedBaseDamage.count, parsedBaseDamage.die);
      rolledDamage += criticalRoll;
      detailsParts.push(`重击${damageSourceName}: ${rolledDamage - parsedBaseDamage.modifier}`);
    } else {
      detailsParts.push(`${damageSourceName}: ${rolledDamage - parsedBaseDamage.modifier}`);
    }
    rolledDamage += parsedBaseDamage.modifier;

    totalDamage += rolledDamage;
    damageBreakdown.push({ type: baseDamageType, amount: rolledDamage, source: weaponName });

    if (attributeModifier !== 0) {
      totalDamage += attributeModifier;
      detailsParts.push(`属性: ${attributeModifier}`);
      const attrDamageEntry = damageBreakdown.find(bd => bd.type === baseDamageType && bd.source === weaponName);
      if (attrDamageEntry) {
        attrDamageEntry.amount += attributeModifier;
      }
    }
  } else {
    detailsParts.push('无基础伤害骰');
  }

  attackingWeapon.magicEffects?.forEach(effect => {
    if (effect.type === 'DAMAGE_BONUS_STATIC' && typeof effect.value?.amount === 'number') {
      const bonusAmount = effect.value.amount;
      const bonusType = effect.value.type || baseDamageType;
      totalDamage += bonusAmount;
      detailsParts.push(`固定(${bonusType}): ${bonusAmount}`);
      let entry = damageBreakdown.find(bd => bd.type === bonusType && bd.source === '魔法效果');
      if (entry) {
        entry.amount += bonusAmount;
      } else {
        damageBreakdown.push({ type: bonusType, amount: bonusAmount, source: '魔法效果' });
      }
    }
  });

  attackingWeapon.magicEffects?.forEach(effect => {
    if (
      effect.type === 'DAMAGE_BONUS_DICE' &&
      typeof effect.value?.dice === 'string' &&
      typeof effect.value?.type === 'string'
    ) {
      const parsedBonusDamage = parseDamageString(effect.value.dice);
      if (parsedBonusDamage) {
        let diceCount = parsedBonusDamage.count;
        let dicePartString = `${diceCount}d${parsedBonusDamage.die}`;
        if (isCritical) {
          diceCount *= 2;
          dicePartString = `${parsedBonusDamage.count}d${parsedBonusDamage.die}x2`;
        }
        let rolledBonusDamage = rollDice(diceCount, parsedBonusDamage.die);
        rolledBonusDamage += parsedBonusDamage.modifier;
        totalDamage += rolledBonusDamage;
        detailsParts.push(`附加(${effect.value.type} ${dicePartString}): ${rolledBonusDamage}`);
        let entry = damageBreakdown.find(bd => bd.type === effect.value.type && bd.source === '魔法效果');
        if (entry) {
          entry.amount += rolledBonusDamage;
        } else {
          damageBreakdown.push({ type: effect.value.type, amount: rolledBonusDamage, source: '魔法效果' });
        }
      }
    }
  });

  const detailsString = detailsParts.join(' + ') + ` = ${Math.max(0, totalDamage)}`;
  const allDamageTypes = [...new Set(damageBreakdown.map(db => db.type))].join('/');

  return {
    totalDamage: Math.max(0, totalDamage),
    damageType: allDamageTypes || '未知',
    details: detailsString,
    breakdown: damageBreakdown,
  };
}

export function performCheck(
  dc: number,
  attributeName: string,
  skillName: string | undefined,
  pState: PlayerState,
  attackingWeapon?: EquipmentItem,
  advantageState: 'advantage' | 'disadvantage' | 'normal' = 'normal',
): CheckResult {
  let roll1 = Math.floor(Math.random() * 20) + 1;
  let roll = roll1;

  if (advantageState === 'advantage') {
    const roll2 = Math.floor(Math.random() * 20) + 1;
    roll = Math.max(roll1, roll2);
  } else if (advantageState === 'disadvantage') {
    const roll2 = Math.floor(Math.random() * 20) + 1;
    roll = Math.min(roll1, roll2);
  }

  let attributeMod = 0;
  let proficiencyBonusApplied = 0;
  let attackBonusFromWeapon = 0;
  const attributeKey = attributeName.toLowerCase() as keyof PlayerState['attributes'];

  if (pState.attributes[attributeKey]) {
    attributeMod = pState.attributes[attributeKey].mod;
  }

  if (skillName) {
    const skill = pState.skills.find(s => s.name.toLowerCase() === skillName.toLowerCase());
    if (skill?.proficient) {
      proficiencyBonusApplied = getProficiencyBonus(pState.level);
    } else {
      const weaponProficiency = pState.proficiencies.find(p => p === skillName);
      if (weaponProficiency) {
        proficiencyBonusApplied = getProficiencyBonus(pState.level);
      } else {
        const genericProficiency = pState.proficiencies.find(
          p =>
            p.toLowerCase().includes(skillName.toLowerCase()) && p.toLowerCase().includes(attributeName.toLowerCase()),
        );
        if (genericProficiency) {
          proficiencyBonusApplied = getProficiencyBonus(pState.level);
        }
      }
    }
  }
  if (!skillName && pState.proficiencies.includes(`${attributeName}豁免`)) {
    proficiencyBonusApplied = getProficiencyBonus(pState.level);
  }

  if (attackingWeapon && attackingWeapon.magicEffects) {
    attackingWeapon.magicEffects.forEach((effect: MagicEffect) => { // Explicitly type effect
      if (effect.type === 'ATTACK_BONUS' && typeof effect.value === 'number') {
        attackBonusFromWeapon += effect.value;
      }
    });
  }

  const total = roll + attributeMod + proficiencyBonusApplied + attackBonusFromWeapon;
  let success = total >= dc;
  const isCritical = roll === 20;
  const isFumble = roll === 1;

  if (isCritical && dc > 0) success = true;
  if (isFumble) success = false;

  return {
    success,
    roll,
    attributeMod,
    proficiencyBonusApplied,
    total,
    dc,
    attributeName,
    skillName,
    isCritical,
    isFumble,
    advantageState,
  };
}
