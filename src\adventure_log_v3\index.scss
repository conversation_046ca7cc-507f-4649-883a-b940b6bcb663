// Adventure Log - SCSS Styles

// Global styles for the adventure log iframe
body {
  margin: 0;
  font-family: 'Merriweather', 'Georgia', serif;
  // background-color is set below, after all image layers
  // More thematic texture - simulating old paper/leather
  background-image: 
        // New: Add a subtle vignette/side shadow effect to make the central content pop more - Adjusted for more visibility
    linear-gradient(to right, rgba(0, 0, 0, 0.25) 0%, transparent 15%, transparent 85%, rgba(0, 0, 0, 0.25) 100%),
    // Existing overlay
    linear-gradient(rgba(44, 40, 35, 0.5), rgba(44, 40, 35, 0.5)),
    // Existing SVG texture
    url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234a433b' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  background-color: #2c2823; // Base color for the body
  color: #e0e0e0;
  min-height: 100vh;
  box-sizing: border-box;
}

#adventure-log-container {
  width: 95vw;
  max-width: 700px;
  background-color: #eaddc7;
  border: 2px solid #5a3d2b;
  position: relative;
  border-radius: 12px;
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.7), inset 0 0 20px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  padding: 18px;
  box-sizing: border-box;
  margin: 20px auto; // Added some top/bottom margin

  &::before,
  &::after {
    content: '';
    position: absolute;
    border-radius: 12px;
    z-index: -1;
  }
  &::before {
    top: -6px;
    left: -6px;
    right: -6px;
    bottom: -6px;
    border: 3px dashed #9c7b60;
    opacity: 0.7;
  }
}

#player-status-area {
  padding-bottom: 12px;
  border-bottom: 1px solid #a8886c;
  margin-bottom: 12px;
  display: flex;
  flex-direction: column;
  gap: 9px;
  font-size: 0.88em;

  .status-row {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
    justify-content: space-around;
  }

  #health,
  #ac-display,
  #time,
  #location {
    padding: 6px 9px;
    background-color: #d8c8b3;
    border: 1px solid #7a5c44;
    border-radius: 5px;
    white-space: nowrap;
    color: #422d1a;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    font-weight: 500;
  }
  button {
    // General style for buttons in player-status-area
    padding: 8px 12px;
    background-color: #8c6f4f;
    color: #f0e6d2;
    border: 1px solid #604834;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9em;
    margin-top: 5px;
    text-align: center;
    width: auto; // Allow button to size to content or be styled specifically
    font-weight: 600;
    transition: background-color 0.2s, box-shadow 0.2s, transform 0.1s;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
    &:hover {
      background-color: #7a5c3f;
      box-shadow: 0 0 8px rgba(140, 111, 79, 0.8);
      transform: translateY(-1px);
    }
  }
  #toggle-char-sheet-button,
  #toggle-backpack-button,
  #toggle-spellbook-button {
    width: 100%; // Make these buttons full width
    margin-bottom: 5px;
  }
}

#detailed-character-sheet {
  background-color: rgba(216, 200, 179, 0.85);
  padding: 14px;
  border-radius: 8px;
  margin-top: 8px;
  border: 1px solid #7a5c44;
  max-height: 40vh; // Increased slightly
  overflow-y: auto;
  color: #4a321f;

  h4 {
    margin-top: 14px;
    margin-bottom: 9px;
    color: #5a3d2b;
    font-size: 1.1em;
    border-bottom: 1px solid #7a5c44;
    padding-bottom: 6px;
    font-variant: small-caps;
    position: relative;
    &::before,
    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      height: 2px;
      background-color: #9c7b60;
      width: 20px;
    }
    &::before {
      left: 0;
    }
    &::after {
      right: 0;
    }
  }
  h4:first-child {
    margin-top: 0;
  }

  .status-row {
    display: flex;
    flex-wrap: wrap;
    gap: 9px 13px;
    align-items: center;
    margin-bottom: 9px;

    div,
    span {
      padding: 4px 8px;
      background-color: #eaddc7;
      border: 1px solid #b89a7c;
      border-radius: 4px;
      white-space: nowrap;
      font-size: 0.92em;
      color: #4a321f;
    }
  }

  ul {
    list-style-type: '✧ ';
    padding-left: 15px;
    margin: 8px 0 11px 0;
    font-size: 0.88em;
    li {
      padding: 3px 0;
      border-bottom: 1px dotted rgba(122, 92, 68, 0.5);
      &:last-child {
        border-bottom: none;
      }
    }
  }
  #spell-slots-display {
    font-size: 0.88em;
    padding-left: 12px;
    margin-bottom: 11px;
  }
}

// Styles for the new Independent Backpack Interface
.backpack-interface {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.75); // Darker overlay
  z-index: 1000;
  display: flex;
  flex-direction: column; // Ensure header and content stack vertically
  justify-content: center;
  align-items: center;
  padding: 20px;
  box-sizing: border-box;
  // Backdrop filter for a more "frosted glass" effect if desired
  // backdrop-filter: blur(5px);

  .backpack-header {
    display: flex; 
    align-items: center; 
    width: 100%; 
    padding-bottom: 10px; // Add some padding below the header before sections start
    margin-bottom: 15px; // Space between this header and the first .backpack-section
    // border-bottom: 2px solid #8c6f4f; // Ensure this is removed or commented out if it was re-added
    // position: relative; // Not needed if children are flex items

    h3 {
      // "我的背包" title
      margin: 0;
      color: #e0e0e0; // Or #ffcc80 to match section titles inside
      font-variant: small-caps;
      font-size: 1.6em; // Keep or adjust as needed
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
      text-align: center; 
      flex-grow: 1; 
      // position: absolute; // Removed
      // top: 25px; // Removed
      // left: 50%; // Removed
      // transform: translateX(-50%); // Removed
      // width: auto; // Removed
      // To ensure the title is truly centered, we need to account for the button's width.
      // One way is to have an invisible placeholder on the left, or adjust margins.
      // For now, let flex-grow and text-align try to center it.
      // If the button pushes it, we might need padding on the h3 or a different flex setup.
      // Let's try ensuring the button doesn't take up flow space for centering.
    }

    .close-button {
      // Making it a flex item, not absolutely positioned relative to .backpack-content
      // position: absolute; // Removed
      // top: 20px; // Removed
      // right: 20px; // Removed
      background: none;
      border: none;
      font-size: 2.2em;
      color: #bcaaa4;
      cursor: pointer;
      padding: 0 5px; // Keep some padding for clickability
      line-height: 1;
      font-weight: bold;
      transition: color 0.2s ease;
      // z-index: 1001; // Removed
      margin-left: auto; 
      flex-shrink: 0; 
      &:hover {
        color: #ffcc80;
      }
    }
  }

  .backpack-content {
    position: relative; // THIS IS CRUCIAL for the close button's positioning
    background-image: linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)),
      // Subtle darkening overlay
      url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233e2723' fill-opacity='0.1'%3E%3Cpath d='M0 0h40v40H0zM40 40h40v40H40z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E"); // Subtle texture
    background-color: #4a3b32; // Darker, leather-like base
    padding: 25px; // Reset padding, new header will manage its own spacing
    // padding-top: 45px; // This might not be needed if header handles its own height correctly
    border-radius: 12px; // Slightly more rounded
    border: 3px solid #8d6e63; // More prominent border, like a chest
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.7), inset 0 0 15px rgba(0, 0, 0, 0.4); // Deeper shadow, inset shadow for depth
    width: 90%;
    max-width: 700px; // Slightly wider
    max-height: 88vh;
    overflow-y: auto;
    color: #d7ccc8; // Lighter text color for readability on dark bg

    // Removed .close-button from here as it's now styled within .backpack-header

    // Custom scrollbar for webkit browsers
    &::-webkit-scrollbar {
      width: 12px;
    }
    &::-webkit-scrollbar-track {
      background: #5d4037; // Darker track
      border-radius: 10px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #8d6e63; // Thumb color
      border-radius: 10px;
      border: 2px solid #5d4037; // Padding around thumb
    }

    .backpack-section {
      margin-bottom: 25px; // Increased spacing
      &:last-child {
        margin-bottom: 0;
      }
      h4 {
        // Section titles (e.g., "货币", "武器")
        margin-top: 0;
        margin-bottom: 12px;
        color: #ffcc80; // Gold-like color for titles
        font-size: 1.35em; // Larger
        border-bottom: 2px solid #8d6e63; // Matching border color
        padding-bottom: 8px;
        font-variant: small-caps;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        letter-spacing: 0.5px;
      }
      p,
      ul {
        margin: 0;
        padding: 0;
        font-size: 1em; // Slightly larger base font for items
      }
      ul {
        list-style: none;
        li {
          // Individual item styling
          padding: 12px 8px; // More padding
          border-bottom: 1px solid #6a4f4b; // Darker, subtle separator
          background-color: rgba(255, 255, 255, 0.03); // Very subtle background for item row
          border-radius: 4px;
          margin-bottom: 8px; // Space between items
          transition: background-color 0.2s ease;
          &:last-child {
            border-bottom: none;
            margin-bottom: 0;
          }
          &:hover {
            background-color: rgba(255, 255, 255, 0.07); // Hover effect
          }
          & > div:first-child {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
          }
          .item-name-toggle {
            cursor: pointer;
            font-weight: bold;
            color: #ffe0b2; // Lighter item name for readability
            flex-grow: 1;
            font-size: 1.05em;
            &:hover {
              color: #ffd54f; // Brighter hover for item name
              text-decoration: none; // Remove underline, rely on color change
            }
          }
          .backpack-button {
            // Enhanced buttons
            padding: 6px 12px;
            font-size: 0.9em;
            color: #fff;
            border: 1px solid rgba(0, 0, 0, 0.3);
            border-radius: 5px;
            cursor: pointer;
            margin-left: 12px;
            min-width: 70px;
            text-align: center;
            font-weight: 600;
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.4);
            transition: background-color 0.2s, box-shadow 0.2s, transform 0.1s;
            box-shadow: 0 2px 3px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);

            &:hover {
              box-shadow: 0 3px 5px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.15);
              transform: translateY(-1px);
            }
            &:active {
              transform: translateY(0px);
              box-shadow: 0 1px 1px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(0, 0, 0, 0.1);
            }

            &.equip-button {
              background-image: linear-gradient(to bottom, #689f38, #4caf50);
              border-color: #388e3c;
              &:hover {
                background-image: linear-gradient(to bottom, #7cb342, #558b2f);
              }
            }
            &.unequip-button {
              background-image: linear-gradient(to bottom, #d32f2f, #c62828);
              border-color: #b71c1c;
              &:hover {
                background-image: linear-gradient(to bottom, #e53935, #b71c1c);
              }
            }
            // Add other button types if needed (e.g., use, drop)
            &.use-button {
              background-image: linear-gradient(to bottom, #0288d1, #0277bd);
              border-color: #01579b;
              &:hover {
                background-image: linear-gradient(to bottom, #039be5, #01579b);
              }
            }
          }
          .item-details-content {
            padding: 10px;
            margin-left: 20px; // Indent details more
            background-color: rgba(0, 0, 0, 0.15); // Darker sub-panel
            border: 1px solid #5a453c;
            border-radius: 4px;
            font-size: 0.95em;
            line-height: 1.5;
            color: #c5cae9; // Lighter text for details
            p {
              margin-bottom: 6px;
            }
            ul {
              list-style-type: '◈ '; // More thematic bullet
              padding-left: 18px;
              margin-top: 4px;
              li.magic-effect {
                font-size: 1em;
                border-bottom: none;
                padding: 3px 0;
                em {
                  color: #80cbc4;
                } // Teal for effect type
                .effect-condition {
                  color: #ffab91;
                  font-style: italic;
                } // Light red for condition
              }
            }
          }
        }
        li.placeholder {
          font-style: italic;
          color: #90a4ae; // Lighter placeholder text
          padding: 10px 8px;
          background-color: transparent;
        }
      }
      #backpack-currency-area {
        // Enhanced currency display
        padding: 12px;
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 6px;
        border: 1px solid #5a453c;
        margin-top: 10px; // Add some space if it's the first element in a section
        p {
          margin-bottom: 6px;
          font-size: 1.05em;
          color: #e0e0e0;
          &:last-child {
            margin-bottom: 0;
          }
          span {
            // Coin values
            font-weight: bold;
            color: #ffd54f; // Gold color for currency values
            margin-left: 5px;
          }
          // Optional: Add icons for coins if you have them as font or SVG
          &.gold-coin::before {
            content: '💰 '; /* Example emoji, replace with actual icon */
          }
        }
      }
    }
  }
}

#main-narrative-area {
  flex-grow: 1;
  padding: 14px;
  background-color: #faf0e0;
  border: 1px solid #c8a064;
  border-radius: 8px;
  line-height: 1.7;
  font-size: 1.02em;
  color: #332211;
  margin-bottom: 18px;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.1);

  p {
    margin-top: 0;
    margin-bottom: 1em;
    &:last-child {
      margin-bottom: 0;
    }
  }

  .system-message em {
    color: #7a522f;
    font-style: italic;
    font-weight: 500;
  }
  .thought-message i {
    color: #607d8b;
    font-style: italic;
  }
  strong {
    color: #5a3d2b;
    font-weight: 700;
  }
}

#action-choices-area {
  display: flex;
  flex-direction: column;
  gap: 9px;

  button {
    padding: 13px 17px;
    background-color: #7a5c3f;
    color: #f5e8d0;
    border: 1px solid #503d2e;
    border-bottom: 3px solid #503d2e;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.98em;
    text-align: left;
    font-weight: 600;
    font-family: 'Merriweather', 'Georgia', serif;
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
    transition: background-color 0.15s ease-out, transform 0.1s ease-out, box-shadow 0.15s ease-out,
      border-bottom-width 0.1s ease-out;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0));
      border-radius: 8px;
      pointer-events: none;
    }

    &:hover {
      background-color: #8c6f4f;
      border-color: #604834;
      box-shadow: 0 3px 5px rgba(0, 0, 0, 0.25);
      transform: translateY(-2px);
    }

    &:active {
      background-color: #604834;
      transform: translateY(1px);
      border-bottom-width: 1px;
      box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
    }
  }
}

#start-screen-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 20px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  background-color: #f5e8d0;
  color: #4a2e00;

  h1 {
    font-size: 2em;
    color: #6b4226;
    margin-bottom: 18px;
    font-variant: small-caps;
    text-shadow: 1px 1px 2px rgba(200, 160, 100, 0.5);
  }

  p {
    font-size: 1.05em;
    color: #5a3811;
    margin-bottom: 22px;
    max-width: 88%;
    line-height: 1.55;
  }

  #start-new-game-button {
    padding: 14px 28px;
    font-size: 1.2em;
    background-color: #6b8e23;
    color: #f5f5f5;
    border: 2px outset #8fbc8f;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.25s, transform 0.15s;
    font-weight: 500;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.4);
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.3);

    &:hover {
      background-color: #556b2f;
      transform: scale(1.03);
    }
  }

  .start-screen-note {
    font-size: 0.78em;
    color: #705432;
    margin-top: 18px;
    max-width: 82%;
    line-height: 1.45;
  }
}

@media (max-width: 600px) {
  #adventure-log-container {
    width: 100vw;
    max-width: 100vw;
    border-radius: 0;
    border: none;
    border-image: none;
    padding: 10px;
    aspect-ratio: unset;
  }

  #player-status-area {
    font-size: 0.85em;
    gap: 6px;
    padding-bottom: 10px;
    margin-bottom: 10px;

    .status-row {
      gap: 8px;
    }
    #health,
    #ac-display,
    #time,
    #location {
      padding: 5px 8px;
    }
  }

  #detailed-character-sheet {
    font-size: 0.85em;
    padding: 10px;
    h4 {
      font-size: 1em;
    }
    .status-row {
      flex-direction: column;
      align-items: flex-start;
      div,
      span {
        width: 100%;
        margin-bottom: 4px;
        text-align: left;
        font-size: 0.95em;
      }
    }
    ul,
    #spell-slots-display {
      font-size: 0.95em;
    }
  }
  .backpack-interface .backpack-content {
    // Backpack adjustments for mobile
    width: 95%;
    max-height: 90vh;
    padding: 15px;
    .backpack-section {
      h4 {
        font-size: 1.1em;
      }
      p,
      ul {
        font-size: 0.9em;
      }
      ul li .backpack-button {
        font-size: 0.8em;
        padding: 3px 6px;
        min-width: 50px;
      }
    }
  }

  #main-narrative-area {
    font-size: 1em;
    padding: 10px;
    margin-bottom: 15px;
  }

  #action-choices-area button {
    padding: 12px 15px;
    font-size: 0.95em;
  }

  #toggle-char-sheet-button,
  #player-status-area #toggle-backpack-button,
  #player-status-area #toggle-spellbook-button {
    // Target backpack and spellbook toggle specifically
    font-size: 0.9em;
    padding: 8px 12px;
  }
}

// === Spellbook Interface Styles ===
.spellbook-interface {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  box-sizing: border-box;

  .spellbook-header {
    display: flex;
    align-items: center;
    width: 100%;
    padding-bottom: 10px;
    margin-bottom: 15px;

    h3 {
      margin: 0;
      color: #e0e0e0;
      font-variant: small-caps;
      font-size: 1.6em;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
      text-align: center;
      flex-grow: 1;
    }

    .close-button {
      background: none;
      border: none;
      font-size: 2.2em;
      color: #bcaaa4;
      cursor: pointer;
      padding: 0 5px;
      line-height: 1;
      font-weight: bold;
      transition: color 0.2s ease;
      margin-left: auto;
      flex-shrink: 0;
      &:hover {
        color: #ffcc80;
      }
    }
  }

  .spellbook-content {
    background-image: linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)),
      url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23283593' fill-opacity='0.1'%3E%3Cpath d='M0 0h40v40H0zM40 40h40v40H40z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-color: #2a2a4a;
    padding: 25px;
    border-radius: 12px;
    border: 3px solid #5c6bc0;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.7), inset 0 0 15px rgba(0, 0, 0, 0.4);
    width: 90%;
    max-width: 800px;
    max-height: 88vh;
    overflow-y: auto;
    color: #e8eaf6;

    &::-webkit-scrollbar {
      width: 12px;
    }
    &::-webkit-scrollbar-track {
      background: #3f51b5;
      border-radius: 10px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #5c6bc0;
      border-radius: 10px;
      border: 2px solid #3f51b5;
    }

    .spellbook-section {
      margin-bottom: 25px;
      &:last-child {
        margin-bottom: 0;
      }

      h4 {
        margin-top: 0;
        margin-bottom: 12px;
        color: #c5cae9;
        font-size: 1.35em;
        border-bottom: 2px solid #5c6bc0;
        padding-bottom: 8px;
        font-variant: small-caps;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        letter-spacing: 0.5px;
      }

      .spell-filter {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
        align-items: center;
        flex-wrap: wrap;

        label {
          color: #c5cae9;
          font-weight: 600;
          font-size: 0.95em;
        }

        select {
          padding: 6px 10px;
          background-color: #3f51b5;
          color: #e8eaf6;
          border: 1px solid #5c6bc0;
          border-radius: 4px;
          font-size: 0.9em;
          cursor: pointer;
          &:focus {
            outline: none;
            border-color: #7986cb;
            box-shadow: 0 0 5px rgba(121, 134, 203, 0.5);
          }
        }
      }

      ul {
        list-style: none;
        margin: 0;
        padding: 0;

        li {
          padding: 12px;
          border-bottom: 1px solid #3f51b5;
          background-color: rgba(255, 255, 255, 0.03);
          border-radius: 6px;
          margin-bottom: 8px;
          transition: background-color 0.2s ease;

          &:last-child {
            border-bottom: none;
            margin-bottom: 0;
          }

          &:hover {
            background-color: rgba(255, 255, 255, 0.07);
          }

          &.disabled {
            opacity: 0.6;
            .spell-actions button {
              opacity: 0.5;
              cursor: not-allowed;
            }
          }

          .spell-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            flex-wrap: wrap;
            gap: 10px;

            .spell-name {
              font-weight: bold;
              color: #c5cae9;
              font-size: 1.1em;
              flex-grow: 1;
            }

            .spell-level {
              background-color: #5c6bc0;
              color: white;
              padding: 2px 8px;
              border-radius: 12px;
              font-size: 0.85em;
              font-weight: 600;
            }

            .spell-school {
              background-color: #7986cb;
              color: white;
              padding: 2px 8px;
              border-radius: 12px;
              font-size: 0.8em;
            }
          }

          .spell-summary {
            color: #b39ddb;
            font-size: 0.95em;
            margin-bottom: 10px;
            line-height: 1.4;
          }

          .spell-actions {
            display: flex;
            gap: 10px;
            align-items: center;

            button {
              padding: 6px 12px;
              font-size: 0.9em;
              color: #fff;
              border: 1px solid rgba(0, 0, 0, 0.3);
              border-radius: 5px;
              cursor: pointer;
              font-weight: 600;
              text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.4);
              transition: all 0.2s ease;
              box-shadow: 0 2px 3px rgba(0, 0, 0, 0.3);

              &:hover:not(:disabled) {
                box-shadow: 0 3px 5px rgba(0, 0, 0, 0.4);
                transform: translateY(-1px);
              }

              &.view-spell-button {
                background: linear-gradient(to bottom, #5c6bc0, #3f51b5);
                border-color: #303f9f;
                &:hover {
                  background: linear-gradient(to bottom, #7986cb, #303f9f);
                }
              }

              &.quick-cast-spell-button,
              &.cast-prepared-spell-button {
                background: linear-gradient(to bottom, #ff7043, #d84315);
                border-color: #bf360c;
                &:hover {
                  background: linear-gradient(to bottom, #ff8a65, #bf360c);
                }
              }
            }

            .no-slots {
              color: #f48fb1;
              font-style: italic;
              font-size: 0.85em;
            }
          }

          &.prepared-spell {
            border-left: 4px solid #4caf50;
            .spell-name {
              color: #a5d6a7;
            }
          }
        }

        li.placeholder {
          font-style: italic;
          color: #9575cd;
          padding: 15px;
          text-align: center;
          background-color: transparent;
        }
      }

      .spell-slot-level {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 4px;
        margin-bottom: 6px;
        border-left: 3px solid #5c6bc0;

        .spell-slot-label {
          font-weight: 600;
          color: #c5cae9;
        }

        .spell-slot-count {
          font-weight: bold;
          color: #81c784;
        }
      }

      .no-spell-slots {
        text-align: center;
        color: #f48fb1;
        font-style: italic;
        padding: 15px;
      }
    }
  }
}

// === Spell Detail Modal Styles ===
.spell-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.85);
  z-index: 1100;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  box-sizing: border-box;

  .spell-modal-content {
    background: linear-gradient(135deg, #2a2a4a 0%, #1a1a3a 100%);
    border: 3px solid #5c6bc0;
    border-radius: 12px;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.8);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    color: #e8eaf6;

    .spell-modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 25px 15px;
      border-bottom: 2px solid #5c6bc0;

      h3 {
        margin: 0;
        color: #c5cae9;
        font-size: 1.5em;
        font-variant: small-caps;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
      }

      .close-button {
        background: none;
        border: none;
        font-size: 2em;
        color: #bcaaa4;
        cursor: pointer;
        padding: 0;
        line-height: 1;
        transition: color 0.2s ease;
        &:hover {
          color: #ffcc80;
        }
      }
    }

    .spell-modal-body {
      padding: 20px 25px;

      .spell-basic-info {
        margin-bottom: 20px;
        p {
          margin: 8px 0;
          display: flex;
          align-items: center;
          strong {
            color: #c5cae9;
            min-width: 80px;
            margin-right: 10px;
          }
          span {
            color: #b39ddb;
          }
        }
      }

      .spell-description {
        margin-bottom: 20px;
        h4 {
          color: #c5cae9;
          margin-bottom: 10px;
          font-size: 1.2em;
        }
        p {
          line-height: 1.6;
          color: #e8eaf6;
        }
      }

      .spell-casting-options {
        h4 {
          color: #c5cae9;
          margin-bottom: 15px;
          font-size: 1.2em;
        }

        .spell-slot-selection,
        .spell-target-selection {
          margin-bottom: 15px;
          display: flex;
          align-items: center;
          gap: 10px;

          label {
            color: #c5cae9;
            font-weight: 600;
            min-width: 80px;
          }

          select,
          input {
            padding: 8px 12px;
            background-color: #3f51b5;
            color: #e8eaf6;
            border: 1px solid #5c6bc0;
            border-radius: 4px;
            font-size: 0.95em;
            flex-grow: 1;
            &:focus {
              outline: none;
              border-color: #7986cb;
              box-shadow: 0 0 5px rgba(121, 134, 203, 0.5);
            }
          }
        }

        .spell-target-selection {
          flex-direction: column;
          align-items: flex-start;

          #custom-target-input {
            width: 100%;
            margin-top: 8px;

            input {
              width: 100%;
              background-color: #424242;
              border-color: #616161;

              &::placeholder {
                color: #9e9e9e;
              }

              &:focus {
                background-color: #3f51b5;
                border-color: #7986cb;
              }
            }
          }
        }
      }
    }

    .spell-modal-footer {
      padding: 15px 25px 20px;
      border-top: 1px solid #5c6bc0;
      display: flex;
      gap: 15px;
      justify-content: flex-end;

      button {
        padding: 10px 20px;
        font-size: 1em;
        font-weight: 600;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
        text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);

        &.cast-spell-button {
          background: linear-gradient(to bottom, #ff7043, #d84315);
          color: white;
          &:hover {
            background: linear-gradient(to bottom, #ff8a65, #bf360c);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
          }
        }

        &.cancel-button {
          background: linear-gradient(to bottom, #757575, #424242);
          color: white;
          &:hover {
            background: linear-gradient(to bottom, #9e9e9e, #212121);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
          }
        }
      }
    }
  }
}
