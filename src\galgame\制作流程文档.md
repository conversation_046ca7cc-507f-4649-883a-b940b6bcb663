# SillyTavern 手机聊天界面制作流程文档

## 1. 项目目标与起因

本项目旨在为 SillyTavern 创建一个自定义的用户界面，模拟用户与AI进行手机聊天的效果。该界面需要能够：
*   在 SillyTavern 的消息流中作为一种特殊的状态栏或聊天窗口显示。
*   允许用户通过该界面向当前SillyTavern中的AI角色发送消息。
*   接收AI的回复，并动态更新界面以显示完整的对话历史。
*   确保对话状态能够在SillyTavern中持久化，即在刷新页面或重新加载后，手机界面能恢复到最新的聊天状态。

## 2. 技术选型与环境
*   **前端界面**：HTML, SCSS, TypeScript
*   **构建工具**：Webpack (用于编译TS和SCSS到JS和CSS)
*   **SillyTavern 集成**：
    *   通过 SillyTavern 的扩展机制（如世界书中的“格式化输出”配合正则表达式）将HTML界面加载到 SillyTavern 的消息流中。
    *   正则表达式示例：`查看系统([\s\S]+?)关闭系统`
    *   替换内容示例（用于加载界面）：
      ```html
      <body>
      <script>
      $("body").load("http://localhost:5500/dist/src/界面/index.html") // 假设界面通过本地服务器提供
      </script>
      </body>
      ```
*   **核心 SillyTavern API/命令**：
    *   `triggerSlash(command: string)`: 用于执行SillyTavern的斜杠命令，如 `/gen` (生成回复) 和 `/messages` (获取消息内容)。
    *   `getLastMessageId(): number | undefined`: SillyTavern提供的函数式API，用于获取当前聊天中最后一条消息的ID。
    *   `setChatMessages(messages: {message_id: number, message: string}[], options?: {refresh?: string}): Promise<void>`: SillyTavern提供的函数式API，用于修改现有消息的内容并刷新显示。
*   **依赖的全局库**：
    *   jQuery (`$`): 用于DOM操作。
    *   toastr: 用于在界面上显示非阻塞性的提示信息。

## 3. HTML 界面设计 (`src/界面/index.html`)

手机界面的HTML结构主要包含以下部分：
*   一个顶层容器 `.phone-container`，模拟手机的整体外观。
*   手机顶部的刘海 `.phone-notch`。
*   手机屏幕 `.phone-screen`，包含状态栏和聊天界面。
    *   **状态栏 (`.status-bar`)**:
        *   角色头像 (`.character-avatar > img`)
        *   角色名称 (`.character-name`)
        *   角色状态 (`.character-status`)
    *   **聊天界面 (`.chat-interface`)**:
        *   消息显示区域 (`.chat-messages`): 此区域初始为空（或包含一个注释占位符 `<!-- 初始消息将由脚本动态加载 -->`），由JavaScript动态填充聊天气泡。
        *   输入区域 (`.chat-input`): 包含一个文本输入框 (`<input type="text">`) 和一个发送按钮 (`<button>`)。

SCSS样式 (`src/界面/scss/phone.scss`, `src/界面/scss/statusbar.scss`, `src/界面/scss/chat.scss`) 负责实现手机的视觉外观、状态栏布局、聊天气泡的样式（区分已发送和已接收的消息），以及响应式调整。特别注意，根据项目要求，界面高度不使用 `vh` 单位，而是通过宽度和 `aspect-ratio` 属性来动态调整，以适应不同的显示环境。

## 4. 核心交互脚本制作 (`src/界面/index.ts`)

TypeScript脚本负责处理界面的所有动态行为，包括API的获取、初始数据加载、消息的发送与接收、DOM的更新以及与SillyTavern的交互。

### A. 初始化 (`initializeChatInterface` 函数)

此函数在脚本加载后（通常在 `DOMContentLoaded` 或 `setTimeout` 后）执行，负责设置整个手机界面的初始状态和功能。

1.  **确保全局API可用 (`ensureGlobals` 函数)**：
    *   由于手机界面通常在iframe或动态加载的HTML片段中运行，它需要从父窗口（SillyTavern主环境）获取必要的JavaScript API。
    *   `ensureGlobals` 函数尝试将 `$` (jQuery), `toastr`, `triggerSlash`, `getLastMessageId`, 和 `setChatMessages` 从 `parent.window` 复制到当前脚本的 `window` 对象。
    *   包含错误处理和日志记录，以帮助调试API的加载情况。

2.  **获取当前用户名**：
    *   通过执行 `triggerSlash("/pass {{user}}")` 命令，从SillyTavern获取当前登录用户的名称。
    *   获取到的用户名存储在全局变量 `window.UserName` 中，供后续判断消息发送者使用。如果获取失败，则默认为 "User"。

3.  **加载初始聊天记录（关键步骤，历经多次迭代）**：
    *   **最终成功方案**：
        a.  调用 `(window as any).getLastMessageId()` 获取当前SillyTavern聊天中最后一条消息的ID。根据项目设定，这条“最后消息”就是承载当前手机界面的那条SillyTavern消息。
        b.  将获取到的ID存储在全局变量 `currentHostMessageId` 中，供后续持久化使用。
        c.  使用 `triggerSlash(`/messages ${currentHostMessageId}`)` 命令获取这条父消息的完整文本内容。此内容期望是包含 `[查看系统]\nmsg_start...\nmsg_end\n[关闭系统]` 标记和对话数据的字符串。
        d.  调用 `parseAndRenderAIMessages` 函数，将获取到的完整消息内容传递给它，以渲染初始的聊天界面。
    *   **失败的尝试**：
        *   从 `window.initialChatDataFromST` 读取（依赖SillyTavern端通过特定方式注入此全局变量）。
        *   从父文档的 `textarea#curEditTextarea` 读取（仅在消息处于编辑模式时有效）。
        *   从URL参数 `stMessageId` 获取父消息ID（依赖SillyTavern端在加载iframe时传递此参数）。

4.  **事件绑定**：
    *   为聊天输入区域的发送按钮的 `onclick` 事件绑定 `handleSendMessage` 函数。
    *   为文本输入框的 `onkeypress` 事件绑定逻辑，当用户按下Enter键时也触发 `handleSendMessage`。

5.  **调试与用户反馈 (`safeToastr` 函数)**：
    *   创建一个 `safeToastr` 包装函数，用于安全地调用 `toastr` 的各种方法 (`info`, `success`, `warning`, `error`)。
    *   此函数会先检查 `toastr` 对象及其请求的方法是否真的可用。如果可用，则正常显示 `toastr` 弹窗。
    *   如果 `toastr` 或其方法不可用，对于错误信息 (`error`)，会回退到使用浏览器原生的 `alert()` 弹窗；对于其他类型的提示，则将信息记录到开发者控制台（根据用户最新要求，此控制台回退已移除或最小化，优先确保 `toastr` 工作）。

### B. 消息解析与渲染 (`parseAndRenderAIMessages` 和 `parseMessageContent` 函数)

1.  **`parseAndRenderAIMessages(aiResponseText: string)`**:
    *   接收一个包含完整对话历史的字符串（通常是AI的回复，或初始加载时从父消息获取的内容）。此字符串期望包含 `msg_start` 和 `msg_end` 标记。
    *   从输入字符串中提取 `msg_start` 和 `msg_end` 标记之间的核心对话内容。如果找不到这些标记，会尝试清理并直接显示原始文本（作为回退）。
    *   清空手机界面中的消息显示区域 (`.chat-messages`)。
    *   将提取的核心对话内容按换行符 (`\n`) 分割成多行。
    *   逐行解析：
        *   忽略空行和包含上下文标记（如 `<User和角色名称的私聊>`）的行。
        *   每条有效消息行期望的格式是 `发言人--消息文本--时间戳`（时间戳部分是可选的，解析时会处理）。
        *   调用 `parseMessageContent` 函数处理“消息文本”部分。
        *   根据“发言人”是当前用户 (`window.UserName` 或硬编码的 "user") 还是AI角色（通过与状态栏中显示的当前AI角色名比较），为生成的消息 `<div>` 添加不同的CSS类 (`message sent` 或 `message received`)。
        *   将包含消息文本的 `<p>` 元素追加到消息 `<div>` 中，再将消息 `<div>` 追加到 `.chat-messages` 容器。
    *   渲染完成后，将 `.chat-messages` 容器滚动到最底部，以显示最新的消息。

2.  **`parseMessageContent(rawContent: string)`**:
    *   此辅助函数负责从原始消息文本部分（即 `发言人--` 之后的部分）提取真正的消息内容。
    *   它会检查内容是否被明确的中文双引号 (`“... ”`) 或英文双引号 (`"..."`) 包围。如果是，则只提取引号内部的文本。
    *   如果内容没有被这种引号包围，则提取到第一个 `--`（时间戳分隔符）之前的部分作为消息内容。

### C. 发送消息与处理AI回复 (`handleSendMessage` 函数)

1.  当用户点击发送按钮或在输入框按Enter键时触发。
2.  检查核心API (`$` 和 `triggerSlash`) 是否可用。
3.  获取当前活动的手机界面元素（`.phone-screen`）和其中的输入框元素。
4.  从状态栏获取当前聊天对象的名称。
5.  获取用户在输入框中输入的文本，并去除首尾空格。
6.  如果输入文本不为空：
    a.  构造一个特定格式的prompt字符串，包含聊天对象名称、用户输入以及一些对AI回复格式的指令（例如，要求AI使用“线上格式”回复，并包含所有历史消息）。示例：`给{角色名称}发消息:{用户输入}\n<Request:...>`。
    b.  使用 `triggerSlash(`/gen ${prompt}`)` 命令将此prompt发送给SillyTavern的AI进行处理。
    c.  **处理AI的回复 (`.then(result => ...)` 回调)**：
        i.  `result` 是AI返回的完整字符串，期望格式为 `[查看系统]\n{最新的对话数据}\n[关闭系统]`，其中 `{最新的对话数据}` 是一个 `msg_start...msg_end` 块。
        ii. 调用 `parseAndRenderAIMessages(result)` 来立即刷新当前手机界面的显示，用AI返回的最新完整对话历史替换掉旧的显示内容。
        iii. **持久化更新（关键）**:
            *   检查之前在初始化时存储的 `currentHostMessageId` (即承载此手机界面的SillyTavern父消息的ID) 是否有效。
            *   检查 `setChatMessages` 函数API是否可用。
            *   如果都可用，则调用 `(window as any).setChatMessages([{ message_id: currentHostMessageId, message: result }], { refresh: 'affected' })`。这将用AI返回的最新完整字符串 `result` 去更新SillyTavern中ID为 `currentHostMessageId` 的那条原始父消息的内容。
            *   `{ refresh: 'affected' }` 选项会提示SillyTavern刷新这条被修改的消息的显示，从而确保状态的持久化。
    d.  清空输入框。

## 5. 遇到的问题与解决过程（迭代和调试）

在整个开发过程中，我们遇到了以下主要问题，并通过不断的尝试和用户反馈逐步解决：

*   **初始聊天记录的加载**：这是最核心的挑战之一。
    *   **尝试1 (失败)**: 假设SillyTavern会将初始数据注入到iframe的全局变量 `window.initialChatDataFromST`。后经测试发现此方式无效。
    *   **尝试2 (部分有效但不可靠)**: 尝试从父文档的 `textarea#curEditTextarea` 读取。此方法仅在SillyTavern消息处于编辑模式时有效，不适用于正常的“仅格式显示”场景。
    *   **尝试3 (失败)**: 尝试让SillyTavern通过URL参数 `stMessageId` 将父消息ID传递给iframe，然后iframe用 `/messages <ID>` 获取内容。但由于SillyTavern端未实现此参数传递，导致失败。
    *   **最终成功方案**: 在iframe初始化时，调用 `(window as any).getLastMessageId()` 获取当前（即承载iframe的）SillyTavern消息的ID，然后使用 `triggerSlash(`/messages <ID>`)` 获取该消息的原始内容，再从中提取 `msg_start...msg_end` 数据块进行渲染。

*   **SillyTavern API在iframe中的可用性**：
    *   最初假设许多SillyTavern的函数式API（如 `eventOn`, `tavern_events`, `setChatMessages`, `getLastMessageId`）可以直接通过 `parent.xxx` 访问或简单复制到iframe的 `window` 对象后就能使用。
    *   实际测试发现，某些复杂的API（如 `eventOn` 和 `tavern_events`）可能由于上下文依赖或安全限制而无法在iframe中按预期工作，导致出现“API缺失”的 `toastr` 提示。
    *   通过 `ensureGlobals` 函数，我们逐步确认了哪些API（`$`, `toastr`, `triggerSlash`, `getLastMessageId`, `setChatMessages`）可以通过从父窗口复制的方式在iframe中使用。

*   **`toastr` 提示的可靠性与用户偏好**：
    *   在开发初期，当API加载不稳定时，`toastr` 本身也可能加载失败，导致无法给出任何提示。
    *   一度将主要调试信息改为 `console.log`，但这不符合用户期望。
    *   最终实现了 `safeToastr` 包装函数，它会优先使用 `toastr`，并在 `toastr` 不可用时为错误信息提供 `alert()` 回退，其他信息则记录到控制台（后根据用户要求，进一步减少了控制台回退，确保 `toastr` 是主要反馈渠道）。

*   **TypeScript编译时错误**：
    *   多次遇到因变量（如 `currentChatScreenJQuery`）在某些代码路径下可能为 `null`，或者类型推断不完全符合预期而导致的TypeScript编译错误。
    *   通过更严谨的 `null` 检查、在确认变量有效的作用域内使用变量，或使用类型断言 (`as JQuery<HTMLElement>`) 来解决。

*   **消息持久化**：
    *   最初，手机界面的更新仅限于当前iframe实例内部，刷新SillyTavern页面会导致聊天记录丢失（恢复到初始状态）。
    *   通过在初始化时存储父消息的ID (`currentHostMessageId`)，并在每次AI回复后，使用 `setChatMessages` API将AI返回的包含完整对话历史的最新字符串更新回该父消息的内容，最终实现了对话状态的持久化。

*   **用户反馈驱动的迭代**：整个开发过程高度依赖用户的清晰反馈、截图、对SillyTavern机制的解释以及对 `Document.htm` 和 `slash_command.txt` 等参考资料的提示。每次迭代都是为了更接近用户描述的预期行为。

## 6. 最终结果

通过上述步骤和多次迭代，我们成功实现了一个功能完善的、可在SillyTavern中运行的手机聊天模拟界面。该界面：
*   能够通过 `getLastMessageId()` 和 `/messages` 命令正确加载初始对话内容。
*   允许用户通过界面与AI进行交互。
*   AI的回复能够正确刷新界面，显示完整的对话历史。
*   对话状态能够通过 `setChatMessages` API持久化到SillyTavern的原始消息中，刷新页面后可恢复。
*   使用 `toastr` 提供用户操作和调试过程中的必要提示。

## 7. 关键命令和API总结

*   **`triggerSlash("/pass {{user}}")`**: 用于获取当前SillyTavern的用户名。
*   **`(window as any).getLastMessageId()`**: 函数式API，用于获取当前聊天中最后一条消息的ID。
*   **`triggerSlash("/messages <MESSAGE_ID>")`**: 斜杠命令，用于获取指定ID的SillyTavern消息的完整文本内容。
*   **`triggerSlash("/gen <PROMPT_STRING>")`**: 斜杠命令，用于将构造好的prompt发送给AI并获取其回复。
*   **`(window as any).setChatMessages([{ message_id: <ID>, message: <NEW_CONTENT_STRING> }], { refresh: 'affected' })`**: 函数式API，用于更新指定ID的SillyTavern消息的内容，并触发界面刷新以显示更改。


//指定格式的提示词内容
查看系统
msg_start
<User和角色名称的私聊>
User--用户消息1--HH:MM
角色名称--AI回复1--HH:MM
User--用户消息2--HH:MM
角色名称--[bqb-某个表情]--HH:MM
角色名称--AI回复3--HH:MM
</User和角色名称的私聊>
msg_end
关闭系统

查看系统和关闭系统是用于正则替换的内容，
查看系统([\s\S]+?)关闭系统
由这个会替换为
```
<body>
<script>
$("body").load("http://localhost:5500/dist/src/界面/index.html")
</script>
</body>
```
作用范围仅为ai输出和用户输入且仅作为格式显示


