# 检定解析鲁棒性改进

## 问题描述

原有的检定解析系统只支持固定格式 `[DC{数值} {属性}({技能})]`，但AI可能返回多种变体格式，导致检定无法正确触发。

### 问题示例
- 原格式：`[DC13 魅力(游说)]` ✅ 可以解析
- 变体格式：`[魅力(游说) DC13]` ❌ 无法解析
- 变体格式：`[魅力(威吓) DC14 - 软硬兼施]` ❌ 无法解析

## 解决方案

### 1. 多重正则表达式匹配

实现了5种不同的正则表达式模式来匹配各种可能的检定格式：

```typescript
const checkRegexPatterns = [
  // 标准格式: [DC15 力量(运动)]
  /\[DC(\d+)\s+([^\(\]\s]+)(?:\s*\(([^\)]+)\))?\s*(?:检定)?\]/i,
  
  // 变体格式1: [力量(运动) DC15]
  /\[([^\(\]\s]+)(?:\s*\(([^\)]+)\))?\s+DC(\d+)\s*(?:检定)?\]/i,
  
  // 变体格式2: [魅力(威吓) DC14 - 软硬兼施]
  /\[([^\(\]\s]+)(?:\s*\(([^\)]+)\))?\s+DC(\d+)\s*(?:-\s*[^\]]+)?\]/i,
  
  // 变体格式3: [DC13 魅力检定(游说)]
  /\[DC(\d+)\s+([^\(\]\s]+)(?:检定)?(?:\s*\(([^\)]+)\))?\]/i,
  
  // 变体格式4: [进行DC15的力量检定]
  /\[(?:进行)?DC(\d+)(?:的)?([^\(\]\s]+)(?:检定)?(?:\s*\(([^\)]+)\))?\]/i,
];
```

### 2. 智能参数解析

根据匹配到的正则表达式类型，智能解析DC值、属性和技能参数：

```typescript
for (let i = 0; i < checkRegexPatterns.length; i++) {
  const regex = checkRegexPatterns[i];
  const match = choice.text.match(regex);
  if (match) {
    if (i === 0 || i === 3 || i === 4) {
      // DC在前的格式
      checkDC = parseInt(match[1], 10);
      checkAttribute = match[2].trim();
      checkSkill = match[3] ? match[3].trim() : undefined;
    } else if (i === 1 || i === 2) {
      // 属性在前的格式
      checkAttribute = match[1].trim();
      checkSkill = match[2] ? match[2].trim() : undefined;
      checkDC = parseInt(match[3], 10);
    }
    break;
  }
}
```

### 3. 调试信息增强

添加了详细的调试信息，显示匹配到的格式类型和解析结果：

```typescript
const formatNames = [
  '标准格式 [DC15 力量(运动)]',
  '变体格式1 [力量(运动) DC15]', 
  '变体格式2 [魅力(威吓) DC14 - 描述]',
  '变体格式3 [DC13 魅力检定(游说)]',
  '变体格式4 [进行DC15的力量检定]'
];
safeToastr('info', `检定解析成功: ${formatNames[i]} -> DC${checkDC} ${checkAttribute}${checkSkill ? `(${checkSkill})` : ''}`, '检定解析');
```

### 4. AI提示词强化

在AI提示词中进一步强调标准格式的重要性，同时说明客户端的容错能力：

```markdown
【检定格式重要提醒】在playerChoices的text字段中嵌入检定信息时，请严格使用标准格式：[DC{数值} {属性}({技能})] 或 [DC{数值} {属性}]。示例："尝试撬锁[DC15 敏捷(巧手)]"、"说服守卫[DC12 魅力(游说)]"。虽然客户端支持格式变体，但标准格式能确保最佳的解析稳定性。
```

### 5. 测试功能

添加了完整的测试函数 `testCheckParsing()`，可以在浏览器控制台中调用来验证各种格式的解析效果：

```javascript
// 在浏览器控制台中运行
testCheckParsing();
```

## 支持的格式

现在系统支持以下所有格式：

1. **标准格式**（推荐）：
   - `[DC15 力量(运动)]`
   - `[DC12 魅力(游说)]`
   - `[DC14 感知(察觉)]`

2. **变体格式1**：
   - `[魅力(游说) DC13]`
   - `[力量(运动) DC15]`

3. **变体格式2**（带描述）：
   - `[魅力(威吓) DC14 - 软硬兼施]`
   - `[敏捷(巧手) DC16 - 小心操作]`

4. **变体格式3**（带"检定"字样）：
   - `[DC13 魅力检定(游说)]`
   - `[DC12 感知检定(察觉)]`

5. **变体格式4**（自然语言）：
   - `[进行DC15的力量检定]`
   - `[进行DC12的感知检定]`

## 使用建议

1. **优先使用标准格式**：虽然系统现在支持多种格式，但标准格式 `[DC{数值} {属性}({技能})]` 仍然是最可靠的选择。

2. **调试时使用测试功能**：如果遇到检定解析问题，可以在浏览器控制台运行 `testCheckParsing()` 来验证解析逻辑。

3. **观察调试信息**：系统会通过toastr显示检定解析的详细信息，包括匹配到的格式类型和解析结果。

## 技术细节

- **文件修改**：
  - `src/adventure_log_v3/app.ts`：主要的检定解析逻辑
  - `src/adventure_log_v3/utils/index.ts`：测试函数
  - `src/adventure_log_v3/adventure_log_v2_ai_prompts.md`：AI提示词更新

- **向后兼容**：所有现有的标准格式检定仍然完全兼容。

- **性能影响**：最小化，只是增加了几个正则表达式匹配，对性能影响可忽略不计。

## 测试验证

可以通过以下方式验证改进效果：

1. 在浏览器控制台运行 `testCheckParsing()` 查看所有格式的解析结果
2. 在实际游戏中观察toastr显示的检定解析信息
3. 测试各种AI返回的检定格式是否能正确触发检定

这个改进大大提高了检定系统的鲁棒性，减少了因格式变体导致的检定失败问题。
