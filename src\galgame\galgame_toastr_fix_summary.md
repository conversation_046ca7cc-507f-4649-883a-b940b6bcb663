# Galgame 界面 Toastr 和按钮功能修复总结

本文档总结了在 `src/galgame/index.ts` 文件中解决 `toastr` 提示不显示和按钮功能异常问题的过程和关键修复点。

## 问题描述

项目初期，Galgame 界面在以下方面存在问题：
1.  `toastr` 提示信息无法按预期显示。
2.  界面中的按钮（如“出门”、“休息”等）点击后没有反应，无法触发预期的界面切换或状态更新。

## 关键修复措施与原因分析

通过对用户修改后的 `src/galgame/index.ts` 代码进行分析，可以确定以下关键更改是成功修复问题的原因：

### 1. API 加载时机与 `setTimeout` 延迟

*   **问题根源**：之前版本的代码可能在父窗口的全局API（尤其是 `toastr`，但也包括 `$`、`triggerSlash` 等）完全初始化并准备就绪之前，就尝试从 iframe 内部通过 `ensureGlobals` 函数去访问和复制它们。如果 iframe 中的脚本执行过早，获取到的可能是 `undefined` 或一个尚未完全初始化的对象，导致后续调用失败。
*   **解决方案**：在 `onMounted` 函数的核心逻辑（包括调用 `ensureGlobals`、获取DOM元素、调用SillyTavern API以及渲染初始界面和按钮）被移入一个 `setTimeout` 回调中，并设置了 `initialDelay`（例如200毫秒）。
    ```typescript
    async function onMounted() {
        console.log('[Galgame] onMounted function started.');
        const initialDelay = 200; 
        
        setTimeout(async () => {
            console.log(`[Galgame] setTimeout callback started after ${initialDelay}ms.`);
            ensureGlobals(); 
            safeToastr('info', 'Galgame 脚本初始化流程开始...', 'Galgame 初始化');

            // ... 核心API检查 ...
            // ... DOM元素获取 ...
            // ... 初始数据加载与解析 ...
            // ... 渲染初始选项 (包括事件绑定) ...
            
        }, initialDelay);
    }
    ```
*   **效果**：这个延迟给了父窗口及其脚本（包括 `toastr` 的初始化脚本）足够的时间来完成加载。当 `setTimeout` 的回调执行时，父窗口的API更有可能处于可用状态，从而使得 `ensureGlobals` 能够成功复制它们，并且 `safeToastr` 能够正确调用 `toastr`。

### 2. `safeToastr` 函数的鲁棒性增强

*   **问题根源**：即使 `ensureGlobals` 在某个时间点执行，如果 `toastr` 当时恰好未准备好，后续的 `safeToastr` 调用仍然会失败。
*   **解决方案**：修改后的 `safeToastr` 函数在每次被调用时，都会首先尝试从 `parent` 窗口重新获取 `toastr` 对象，如果当前 `window.toastr` 不可用的话。
    ```typescript
    function safeToastr(type: 'info' | 'success' | 'warning' | 'error', message: string, title?: string) {
        try {
            // Ensure toastr is attempted to be fetched from parent each time
            if (typeof (window as any).toastr !== 'object' && typeof parent !== 'undefined' && typeof (parent as any).toastr === 'object') {
                (window as any).toastr = (parent as any).toastr;
            }

            if (typeof toastr === 'object' && toastr !== null && typeof toastr[type] === 'function') {
                toastr[type](message, title);
            } else {
                // Fallback to console
                // ...
            }
        } catch (e) { /* ... */ }
    }
    ```
*   **效果**：这使得 `safeToastr` 对 `toastr` 的加载时机不那么敏感。即使在 `ensureGlobals` 执行时 `toastr` 未就绪，只要在实际调用 `safeToastr` 时 `parent.toastr` 已经可用，它就能被正确获取和使用。

### 3. 严格的API可用性检查

*   **问题根源**：在执行依赖外部API的核心逻辑前，没有进行充分的检查，可能导致因API未定义而产生的运行时错误。
*   **解决方案**：在 `setTimeout` 回调的开始部分，增加了一个严格的检查，确保所有关键的全局API（`$`、`triggerSlash`、`getLastMessageId`、`toastr`）都已成功加载到当前 `window` 环境。如果任一API缺失，则会通过 `safeToastr` 显示错误并中止后续的初始化流程。
    ```typescript
    if (typeof $ !== 'function' || typeof triggerSlash !== 'function' || typeof getLastMessageId !== 'function' || typeof toastr !== 'object') {
        safeToastr('error', "Galgame: 核心API未完全加载，初始化中止。", "Galgame 初始化严重错误");
        return;
    }
    ```
*   **效果**：这提高了初始化的可靠性，避免了在依赖不满足的情况下继续执行，并能通过 `toastr`（如果可用）或控制台给出明确的错误提示。

### 4. DOMContentLoaded 的正确使用

*   脚本末尾通过 `DOMContentLoaded` 事件监听器来调用 `onMounted` (经由 `initFn`) 的做法是正确的，它确保了在操作DOM之前HTML结构已经构建完毕。结合 `setTimeout` 的延迟，为API和DOM的准备提供了双重保障。

## 结论

之前 `toastr` 提示和按钮功能不工作的主要原因是 **API（尤其是 `toastr`）的加载时机问题**。iframe 中的脚本过早地尝试访问和使用父窗口中可能尚未完全初始化的API。通过在核心初始化逻辑前引入 `setTimeout` 延迟，并在 `safeToastr` 中增加按需获取 `toastr` 的机制，显著提高了脚本在复杂加载环境（如SillyTavern的iframe扩展）中的稳定性和可靠性，从而使得 `toastr` 能够正常显示，并且由于 `onMounted` 在正确的时机执行，按钮的事件监听器也得以正确绑定，恢复了其功能。
