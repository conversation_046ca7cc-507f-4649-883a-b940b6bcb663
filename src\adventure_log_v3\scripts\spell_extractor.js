/**
 * 法术提取器 - 从HTML文件中提取DND5e法术数据
 * 直接解析官方手册的HTML内容，生成标准化的法术数据
 */

const fs = require('fs');
const path = require('path');

// 法术学派映射
const SCHOOL_MAP = {
  '防护': '防护',
  '咒法': '咒法', 
  '预言': '预言',
  '惑控': '惑控',
  '塑能': '塑能',
  '幻术': '幻术',
  '死灵': '死灵',
  '变化': '变化'
};

// 伤害类型映射
const DAMAGE_TYPE_MAP = {
  '强酸': '强酸',
  '钝击': '钝击',
  '寒冷': '寒冷',
  '火焰': '火焰',
  '力场': '力场',
  '闪电': '闪电',
  '黯蚀': '黯蚀',
  '毒素': '毒素',
  '心灵': '心灵',
  '光耀': '光耀',
  '挥砍': '挥砍',
  '雷鸣': '雷鸣',
  '穿刺': '穿刺'
};

/**
 * 解析单个法术的HTML内容
 */
function parseSpell(htmlContent, spellId) {
  try {
    // 提取法术名称（中英文）
    const nameMatch = htmlContent.match(/<H4[^>]*id="([^"]*)"[^>]*>([^<]+)(?:（([^）]+)）)?<\/H4>/);
    if (!nameMatch) return null;
    
    const name_zh = nameMatch[2].trim();
    const name_en = nameMatch[3] || nameMatch[1].replace(/_/g, ' ');
    
    // 提取基本信息行
    const infoMatch = htmlContent.match(/<EM>([^<]+)<\/EM>/);
    if (!infoMatch) return null;
    
    const infoLine = infoMatch[1];
    const infoParts = infoLine.split(/[，、]/);
    
    // 解析环数和学派
    let level = 0;
    let school = '';
    
    if (infoParts[0].includes('戏法')) {
      level = 0;
    } else {
      const levelMatch = infoParts[0].match(/([一二三四五六七八九])环/);
      if (levelMatch) {
        const levelMap = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9};
        level = levelMap[levelMatch[1]] || 0;
      }
    }
    
    // 查找学派
    for (const [key, value] of Object.entries(SCHOOL_MAP)) {
      if (infoLine.includes(key)) {
        school = value;
        break;
      }
    }
    
    // 提取施法信息
    const castingTimeMatch = htmlContent.match(/<STRONG>施法时间：<\/STRONG>([^<]+)/);
    const rangeMatch = htmlContent.match(/<STRONG>施法距离：<\/STRONG>([^<]+)/);
    const componentsMatch = htmlContent.match(/<STRONG>法术成分：<\/STRONG>([^<]+)/);
    const durationMatch = htmlContent.match(/<STRONG>持续时间：<\/STRONG>([^<]+)/);
    
    const casting_time = castingTimeMatch ? castingTimeMatch[1].trim() : '';
    const range = rangeMatch ? rangeMatch[1].trim() : '';
    const duration = durationMatch ? durationMatch[1].trim() : '';
    
    // 解析法术成分
    let components = [];
    if (componentsMatch) {
      const compText = componentsMatch[1];
      if (compText.includes('V')) components.push('V');
      if (compText.includes('S')) components.push('S');
      if (compText.includes('M')) {
        const materialMatch = compText.match(/M[（(]([^）)]+)[）)]/);
        if (materialMatch) {
          components.push(`M (${materialMatch[1]})`);
        } else {
          components.push('M');
        }
      }
    }
    
    // 提取描述
    const descMatch = htmlContent.match(/<\/STRONG>([^<]+(?:<[^>]+>[^<]*<\/[^>]+>[^<]*)*)/);
    let description_long = '';
    let description_short = '';
    
    if (descMatch) {
      description_long = descMatch[1]
        .replace(/<[^>]+>/g, '') // 移除HTML标签
        .replace(/&nbsp;/g, ' ')
        .trim();
      
      // 生成简短描述（取第一句话）
      const firstSentence = description_long.split(/[。！？]/)[0];
      description_short = firstSentence.length > 50 ? firstSentence.substring(0, 47) + '...' : firstSentence;
    }
    
    // 构建基础法术对象
    const spell = {
      name_zh,
      name_en,
      level,
      school,
      casting_time,
      range,
      components,
      duration,
      description_short,
      description_long
    };
    
    // 解析伤害信息
    const damageMatch = description_long.match(/(\d+d\d+(?:\+\d+)?)/);
    if (damageMatch) {
      spell.damage = damageMatch[1];
      
      // 查找伤害类型
      for (const [key, value] of Object.entries(DAMAGE_TYPE_MAP)) {
        if (description_long.includes(key)) {
          spell.damage_type = value;
          break;
        }
      }
    }
    
    // 解析豁免信息
    const saveMatch = description_long.match(/(力量|敏捷|体质|智力|感知|魅力)豁免/);
    if (saveMatch) {
      spell.save = {
        attribute: saveMatch[1],
        effect_on_success: description_long.includes('伤害减半') ? '伤害减半' : 
                          description_long.includes('无效果') ? '无效果' : '见描述'
      };
    }
    
    // 解析攻击类型
    if (description_long.includes('近战法术攻击')) {
      spell.attack_type = '法术攻击 (近战)';
    } else if (description_long.includes('远程法术攻击')) {
      spell.attack_type = '法术攻击 (远程)';
    }
    
    // 解析作用范围
    const areaMatch = description_long.match(/(\d+)尺(半径|直径|长|宽|高|锥形|线形|立方|球形|圆柱)/);
    if (areaMatch) {
      spell.area_of_effect = {
        type: areaMatch[2],
        size: `${areaMatch[1]}尺${areaMatch[2]}`
      };
    }
    
    // 解析升环效果
    const higherLevelMatch = description_long.match(/每高于\d+环的法术位[，：]([^。]+)/);
    if (higherLevelMatch) {
      spell.higher_level_cast = {
        per_slot_above_base: `每高于${level}环的法术位`,
        effect: higherLevelMatch[1].trim()
      };
    }
    
    // 解析专注
    if (duration.includes('专注')) {
      spell.concentration = true;
    }
    
    // 解析仪式
    if (description_long.includes('仪式') || casting_time.includes('仪式')) {
      spell.ritual = true;
    }
    
    return spell;
    
  } catch (error) {
    console.error(`解析法术时出错 (${spellId}):`, error);
    return null;
  }
}

/**
 * 从HTML文件中提取所有法术
 */
function extractSpellsFromFile(filePath, level) {
  try {
    console.log(`正在处理 ${level}环法术文件: ${filePath}`);
    
    const content = fs.readFileSync(filePath, 'utf-8');
    
    // 按H4标签分割法术
    const spellSections = content.split(/<H4[^>]*id="([^"]*)"[^>]*>/);
    const spells = [];
    
    for (let i = 1; i < spellSections.length; i += 2) {
      const spellId = spellSections[i];
      const spellContent = spellSections[i + 1];
      
      if (spellContent && spellContent.trim()) {
        const fullContent = `<H4 id="${spellId}">${spellContent}`;
        const spell = parseSpell(fullContent, spellId);
        
        if (spell) {
          spells.push(spell);
          console.log(`  ✓ 提取法术: ${spell.name_zh} (${spell.name_en})`);
        }
      }
    }
    
    console.log(`${level}环法术提取完成，共 ${spells.length} 个法术\n`);
    return spells;
    
  } catch (error) {
    console.error(`读取文件失败 (${filePath}):`, error);
    return [];
  }
}

/**
 * 生成法术世界书
 */
function generateSpellWorldBook(spells, level) {
  const levelName = level === 0 ? '戏法' : `${level}环法术`;
  
  const worldBook = {
    entries: {
      [5000 + level]: {
        uid: 5000 + level,
        key: [`AI_LEVEL_${level}_SPELLS`, `AI${levelName}`, `DND5E_AI_LEVEL_${level}`, `AI_SPELLS_LEVEL_${level}`],
        keysecondary: [],
        comment: `从官方手册提取的DND5e ${levelName}完整数据 (${spells.length}个法术)`,
        content: JSON.stringify(spells, null, 2),
        constant: true,
        vectorized: false,
        selective: true,
        selectiveLogic: 0,
        addMemo: true,
        order: 100,
        position: 0,
        disable: false,
        excludeRecursion: false,
        preventRecursion: false,
        delayUntilRecursion: false,
        probability: 100,
        useProbability: true,
        depth: 4,
        group: "",
        groupOverride: false,
        groupWeight: 100,
        scanDepth: null,
        caseSensitive: null,
        matchWholeWords: null,
        useGroupScoring: null,
        automationId: "",
        role: null,
        sticky: 0,
        cooldown: 0,
        delay: 0,
        displayIndex: 5000 + level
      }
    }
  };
  
  const outputPath = `Extracted_DND5e_Level${level}_Complete.json`;
  fs.writeFileSync(outputPath, JSON.stringify(worldBook, null, 2), 'utf-8');
  console.log(`生成${levelName}世界书: ${outputPath} (${spells.length}个法术)`);
  
  return spells;
}

/**
 * 主函数 - 提取所有环数的法术
 */
function main() {
  console.log('🔍 DND5e法术提取器');
  console.log('=====================================\n');
  
  const baseDir = '../../../DND5e_chm-main/DND5e_chm-main/玩家手册2024/法术详述';
  const allSpells = [];
  
  // 处理0-9环法术
  for (let level = 0; level <= 9; level++) {
    const fileName = level === 0 ? '0环.txt' : `${level}环.txt`;
    const filePath = path.join(baseDir, fileName);
    
    if (fs.existsSync(filePath)) {
      const spells = extractSpellsFromFile(filePath, level);
      if (spells.length > 0) {
        generateSpellWorldBook(spells, level);
        allSpells.push(...spells);
      }
    } else {
      console.log(`⚠️  文件不存在: ${filePath}`);
    }
  }
  
  // 生成完整合并的世界书
  if (allSpells.length > 0) {
    console.log('\n=== 生成完整法术库 ===');
    
    const completeWorldBook = {
      entries: {
        6000: {
          uid: 6000,
          key: ["EXTRACTED_ALL_SPELLS", "提取的所有法术", "DND5E_EXTRACTED_COMPLETE", "官方法术完整库"],
          keysecondary: ["法术库", "完整法术", "DND5E法术", "官方法术数据"],
          comment: `从官方手册提取的DND5e完整法术库 (${allSpells.length}个法术)`,
          content: JSON.stringify(allSpells, null, 2),
          constant: true,
          vectorized: false,
          selective: true,
          selectiveLogic: 0,
          addMemo: true,
          order: 100,
          position: 0,
          disable: false,
          excludeRecursion: false,
          preventRecursion: false,
          delayUntilRecursion: false,
          probability: 100,
          useProbability: true,
          depth: 4,
          group: "",
          groupOverride: false,
          groupWeight: 100,
          scanDepth: null,
          caseSensitive: null,
          matchWholeWords: null,
          useGroupScoring: null,
          automationId: "",
          role: null,
          sticky: 0,
          cooldown: 0,
          delay: 0,
          displayIndex: 6000
        }
      }
    };
    
    const outputPath = 'Extracted_DND5e_Complete_Spell_Library.json';
    fs.writeFileSync(outputPath, JSON.stringify(completeWorldBook, null, 2), 'utf-8');
    console.log(`\n完整法术库已生成: ${outputPath}`);
    console.log(`包含 ${allSpells.length} 个法术数据`);
    
    // 统计信息
    const levelCounts = {};
    allSpells.forEach(spell => {
      levelCounts[spell.level] = (levelCounts[spell.level] || 0) + 1;
    });
    
    console.log('\n📊 法术分布统计:');
    Object.entries(levelCounts).forEach(([level, count]) => {
      const levelName = level == 0 ? '戏法' : `${level}环`;
      console.log(`  ${levelName}: ${count}个`);
    });
  }
  
  console.log('\n🎉 法术提取完成！');
}

// 执行主函数
if (require.main === module) {
  main();
}

module.exports = {
  extractSpellsFromFile,
  generateSpellWorldBook,
  parseSpell,
  main
};
