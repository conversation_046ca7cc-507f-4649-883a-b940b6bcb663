# AI提示词法术目标更新

## 🎯 问题分析

### 当前问题
用户发现法术释放目标选择系统无法识别场景中的NPC，原因是：

1. **AI返回格式缺失NPC数据**：当前AI只在 `narrative` 中描述NPC，但没有在 `enemies` 字段中提供结构化数据
2. **提示词不够明确**：原有提示词只要求在战斗场景中提供 `enemies` 数据
3. **目标选择系统依赖结构化数据**：客户端的法术目标选择功能需要从 `currentSceneData.enemies` 中获取可选目标

### 用户提供的AI返回示例
```json
{
  "sceneType": "dialogue",
  "sceneTitle": "初次接触与回应",
  "narrative": [
    {
      "type": "dialogue",
      "speaker": "受伤的少女",
      "content": "...谁？别...别过来..."
    }
  ],
  // 缺少 enemies 字段，导致无法识别"受伤的少女"为可施法目标
}
```

## 🔧 解决方案

### 1. 扩展 `enemies` 字段定义

**文件**: `src/adventure_log_v3/adventure_log_v2_ai_prompts.md`

**修改内容**:
- 将 `enemies` 字段的使用范围从"仅战斗场景"扩展到"任何包含可施法目标的场景"
- 明确指出该字段应包含所有可能成为法术目标的NPC，无论其敌友关系

**新增指南**:
```markdown
### 9. `enemies` (可选, EnemyStateJSON[] 数组)
*   **指南**: 
    *   **战斗场景**: 在 `sceneType` 为 `"combat"` 时提供
    *   **法术目标场景**: **重要！** 在任何场景中，如果存在可以作为法术目标的NPC，都应该在此字段中列出，包括：
        *   受伤需要治疗的友方NPC
        *   可以被魅惑、命令等法术影响的中立NPC
        *   可以被攻击法术瞄准的敌对NPC
        *   任何可能成为法术作用对象的角色
```

### 2. 新增 `relationship` 字段

为了更好地区分NPC类型，在 `EnemyStateJSON` 结构中添加了 `relationship` 字段：

```typescript
interface EnemyStateJSON {
  id: string;
  name: string;
  hp: { current: number; max: number };
  ac: number;
  statusEffects?: string[];
  intent?: string;
  relationship?: string; // 新增：NPC与玩家的关系
}
```

**可能的值**:
- `"敌对"`: 敌人、怪物
- `"友好"`: 盟友、需要救助的角色
- `"中立"`: 商人、村民、守卫
- `"未知"`: 初次遇到的角色

### 3. 添加非战斗场景示例

**新增示例**: 包含可施法目标的对话场景
```json
{
  "sceneType": "dialogue",
  "sceneTitle": "救助受伤的旅人",
  "enemies": [
    {
      "id": "injured_girl_1",
      "name": "受伤的少女",
      "hp": { "current": 3, "max": 8 },
      "ac": 10,
      "statusEffects": ["重伤", "恐惧"],
      "intent": "需要医疗救助",
      "relationship": "友好"
    }
  ],
  "playerChoices": [
    {
      "id": "A",
      "text": "对她施放治疗真言法术[消耗1环法术槽]。",
      "actionCommand": "cast_cure_wounds_on_girl"
    }
  ]
}
```

### 4. 新增专门章节

**第六章**: 法术施放与目标选择特别说明

**内容包括**:
- 法术目标识别规则
- 必需字段说明
- 示例场景类型
- 客户端使用说明
- 特别提醒

## 🎮 预期效果

### AI应该返回的格式
对于用户提供的场景，AI现在应该返回：

```json
{
  "sceneType": "dialogue",
  "sceneTitle": "初次接触与回应",
  "currentLocation": "螺壳舰残骸内部通道",
  "time": "清晨，光线昏暗",
  "narrative": [
    {
      "type": "dialogue",
      "speaker": "受伤的少女",
      "content": "...谁？别...别过来..."
    }
  ],
  "enemies": [
    {
      "id": "injured_girl_mengyao",
      "name": "董梦瑶",
      "hp": { "current": 2, "max": 8 },
      "ac": 10,
      "statusEffects": ["重伤", "恐惧"],
      "intent": "需要医疗救助",
      "relationship": "友好"
    }
  ],
  "playerChoices": [
    {
      "id": "A",
      "text": "对她施放治疗真言法术帮助她恢复。",
      "actionCommand": "cast_cure_wounds_on_mengyao"
    }
  ]
}
```

### 客户端行为改进
1. **目标选择列表**：现在会显示"董梦瑶 (2/8 HP)"
2. **关系标识**：可以显示"友好"标识，帮助玩家识别
3. **法术验证**：可以验证治疗法术对友方目标的有效性
4. **状态更新**：法术效果可以正确更新NPC的HP和状态

## 📋 测试建议

### 测试场景
1. **救助场景**：受伤的NPC需要治疗
2. **社交场景**：可以被魅惑的商人或守卫
3. **探索场景**：遇到的中立角色
4. **混合场景**：同时包含友方和敌方的复杂场景

### 验证要点
- AI是否在非战斗场景中提供了 `enemies` 数组
- NPC数据是否包含所有必需字段
- `relationship` 字段是否正确标识NPC类型
- 客户端是否能正确识别和显示目标选项

## 🚀 后续优化

### 可能的改进
1. **字段重命名**：考虑将 `enemies` 重命名为 `npcs` 或 `characters` 以更准确反映其用途
2. **目标类型**：添加 `targetType` 字段区分不同类型的法术目标
3. **距离信息**：添加距离字段用于法术射程验证
4. **免疫信息**：添加法术免疫和抗性信息

### TypeScript类型更新
可能需要更新客户端的类型定义以反映新的字段：

```typescript
interface NPCData {
  id: string;
  name: string;
  hp: { current: number; max: number };
  ac: number;
  statusEffects?: string[];
  intent?: string;
  relationship?: '敌对' | '友好' | '中立' | '未知';
  targetType?: 'combat' | 'social' | 'healing' | 'utility';
}
```

## 📝 总结

通过这次AI提示词更新，我们：

1. **解决了核心问题**：AI现在会在所有相关场景中提供NPC数据
2. **改进了用户体验**：法术目标选择更加智能和直观
3. **增强了系统功能**：支持更多类型的法术和场景
4. **提供了清晰指导**：AI有明确的规范来遵循

这个更新确保了法术系统能够正常工作，并为未来的功能扩展奠定了基础。
