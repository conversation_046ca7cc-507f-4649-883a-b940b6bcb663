# AI生成的DND5e法术世界书使用指南

## 📋 文件说明

### 单独环数文件（推荐按需使用）
- `AI_DND5e_Cantrips_Complete.json` - 戏法（0环）完整数据 (21个法术)
- `AI_DND5e_Level1_Complete.json` - 1环法术完整数据 (19个法术)
- `AI_DND5e_Level2_Complete.json` - 2环法术完整数据 (20个法术)
- `AI_DND5e_Level3_Complete.json` - 3环法术完整数据 (20个法术)
- `AI_DND5e_Level4_Placeholder.json` - 4环法术占位符（待补充）
- `AI_DND5e_Level5_Placeholder.json` - 5环法术占位符（待补充）
- `AI_DND5e_Level6_Placeholder.json` - 6环法术占位符（待补充）
- `AI_DND5e_Level7_Placeholder.json` - 7环法术占位符（待补充）
- `AI_DND5e_Level8_Placeholder.json` - 8环法术占位符（待补充）
- `AI_DND5e_Level9_Placeholder.json` - 9环法术占位符（待补充）

### 完整合并文件
- `AI_DND5e_Complete_Spell_Library.json` - 包含所有0-3环完整法术数据

## 🎯 使用方式

### 1. 按需导入（推荐）
根据角色等级和需要，选择性导入对应环数的世界书：
- 低级角色：导入戏法和1-2环法术
- 中级角色：导入1-5环法术
- 高级角色：导入所有法术

### 2. 完整导入
直接导入完整法术库，一次性获得所有可用法术数据。

## 🔍 检索关键词

每个世界书都包含多个检索关键词：
- `AI_CANTRIPS`, `AI戏法` - 戏法
- `AI_LEVEL_1_SPELLS`, `AI一环法术` - 1环法术
- `AI_ALL_SPELLS`, `AI所有法术` - 完整库
- `DND5E_AI_COMPLETE`, `完整法术` - 完整库

## 📊 法术数据格式

每个法术包含完整的游戏属性：
- 中英文名称
- 法术等级和学派
- 施法时间、距离、成分、持续时间
- 详细描述
- 伤害、豁免、攻击类型
- 升环效果
- 作用范围等

## 🔧 系统集成

这些世界书文件可以直接与现有的法术系统集成：
1. 导入酒馆世界书
2. 修改 `src/adventure_log_v3/spells/index.ts` 加载世界书法术
3. 在法术书界面中显示和使用

## 📝 后续扩展

4-9环法术数据将在后续版本中补充完整。目前的0-3环法术数据已经足够支持大部分低中级游戏内容。

---
生成时间: 2025/6/3 23:19:34
总法术数: 80个完整法术
