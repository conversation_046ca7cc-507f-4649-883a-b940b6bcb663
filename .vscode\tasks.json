{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "开始监听源代码并编译",
      "type": "shell",
      "command": "pnpm",
      "args": [
        "watch"
      ],
      "problemMatcher": [
        {
          "pattern": [
            {
              "regexp": ".*",
              "file": 1,
              "location": 2,
              "message": 3
            }
          ],
          "background": {
            "activeOnStart": true,
            "beginsPattern": ".*",
            "endsPattern": "webpack (?:\\d+\\.){2}\\d+ compiled"
          }
        }
      ],
      "group": {
        "kind": "build",
        "isDefault": true
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": true,
        "revealProblems": "never"
      },
      "isBackground": true
    },
    {
      "label": "启用网页MCP",
      "type": "shell",
      "command": "npx",
      "args": [
        "@agentdeskai/browser-tools-server@latest"
      ],
      "problemMatcher": [
        {
          "pattern": [
            {
              "regexp": ".*",
              "file": 1,
              "location": 2,
              "message": 3
            }
          ],
          "background": {
            "activeOnStart": true,
            "beginsPattern": ".*",
            "endsPattern": "For local access use.*"
          }
        }
      ],
      "group": {
        "kind": "build",
        "isDefault": true
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": true,
        "revealProblems": "never"
      },
      "isBackground": true
    },
    {
      "label": "开始任务",
      "dependsOn": [
        "开始监听源代码并编译",
        "启用网页MCP"
      ]
    },
    {
      "label": "结束任务",
      "command": "echo ${input:terminate}",
      "type": "shell"
    },
  ],
  "inputs": [
    {
      "id": "terminate",
      "type": "command",
      "command": "workbench.action.tasks.terminate",
      "args": "terminateAll"
    }
  ]
}