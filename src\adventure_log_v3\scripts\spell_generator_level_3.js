/**
 * 3环法术生成器
 * 基于AI知识库生成完整的DND5e 3环法术数据
 */

const fs = require('fs');

// 3环法术数据
const LEVEL_3_SPELLS = [
  {
    name_zh: '火球术',
    name_en: 'Fireball',
    level: 3,
    school: '塑能',
    casting_time: '1 动作',
    range: '150尺',
    components: ['V', 'S', 'M (一小团蝙蝠粪便和硫磺)'],
    duration: '立即',
    description_short: '爆炸性火球攻击大范围敌人。',
    description_long: '一道明亮的光束从你指尖射出，在射程内一点爆炸成火焰。20尺半径球体内的每个生物必须进行敏捷豁免。失败时受到8d6火焰伤害，成功时伤害减半。',
    save: { attribute: '敏捷', effect_on_success: '伤害减半' },
    damage: '8d6',
    damage_type: '火焰',
    area_of_effect: { type: '球形', size: '20尺半径' },
    higher_level_cast: {
      per_slot_above_base: '每高于3环的法术位',
      effect: '增加1d6伤害'
    }
  },
  {
    name_zh: '闪电束',
    name_en: 'Lightning Bolt',
    level: 3,
    school: '塑能',
    casting_time: '1 动作',
    range: '自身（100尺线形）',
    components: ['V', 'S', 'M (一点毛皮和一根琥珀、玻璃或水晶棒)'],
    duration: '立即',
    description_short: '直线闪电攻击多个目标。',
    description_long: '一道闪电从你身上射出，形成100尺长、5尺宽的线形。线形上的每个生物必须进行敏捷豁免。失败时受到8d6闪电伤害，成功时伤害减半。',
    save: { attribute: '敏捷', effect_on_success: '伤害减半' },
    damage: '8d6',
    damage_type: '闪电',
    area_of_effect: { type: '线形', size: '100尺长5尺宽' },
    higher_level_cast: {
      per_slot_above_base: '每高于3环的法术位',
      effect: '增加1d6伤害'
    }
  },
  {
    name_zh: '反制法术',
    name_en: 'Counterspell',
    level: 3,
    school: '防护',
    casting_time: '1 反应',
    range: '60尺',
    components: ['S'],
    duration: '立即',
    description_short: '阻止敌人施法。',
    description_long: '你试图打断一个正在施法的生物。如果该生物施展的是3环或更低的法术，其法术失败且没有效果。如果是4环或更高的法术，进行属性检定。',
    higher_level_cast: {
      per_slot_above_base: '每高于3环的法术位',
      effect: '自动反制的法术等级提高1环'
    }
  },
  {
    name_zh: '解除魔法',
    name_en: 'Dispel Magic',
    level: 3,
    school: '防护',
    casting_time: '1 动作',
    range: '120尺',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '移除魔法效果。',
    description_long: '选择射程内一个生物、物体或魔法效果。目标上任何3环或更低的法术结束。对于4环或更高的法术，进行属性检定。',
    higher_level_cast: {
      per_slot_above_base: '每高于3环的法术位',
      effect: '自动解除的法术等级提高1环'
    }
  },
  {
    name_zh: '飞行术',
    name_en: 'Fly',
    level: 3,
    school: '变化',
    casting_time: '1 动作',
    range: '触及',
    components: ['V', 'S', 'M (一根鸟的羽毛)'],
    duration: '专注，至多10分钟',
    description_short: '赋予目标飞行能力。',
    description_long: '你触碰一个自愿的生物。目标获得60尺飞行速度，持续法术时间。当法术结束时，如果目标仍在空中，它会坠落。',
    higher_level_cast: {
      per_slot_above_base: '每高于3环的法术位',
      effect: '额外影响一个目标'
    }
  },
  {
    name_zh: '加速术',
    name_en: 'Haste',
    level: 3,
    school: '变化',
    casting_time: '1 动作',
    range: '30尺',
    components: ['V', 'S', 'M (一片甘草根)'],
    duration: '专注，至多1分钟',
    description_short: '大幅提升目标的行动能力。',
    description_long: '选择射程内一个自愿的生物。直到法术结束，目标的速度翻倍，AC获得+2加值，敏捷豁免检定具有优势，且每回合获得一个额外动作。'
  },
  {
    name_zh: '缓慢术',
    name_en: 'Slow',
    level: 3,
    school: '变化',
    casting_time: '1 动作',
    range: '120尺',
    components: ['V', 'S', 'M (一滴糖蜜)'],
    duration: '专注，至多1分钟',
    description_short: '减缓多个目标的行动。',
    description_long: '你改变时间对最多六个射程内生物的影响。每个目标必须成功通过一次感知豁免，否则受到法术影响。受影响的生物速度减半，AC减少2，敏捷豁免检定具有劣势。',
    save: { attribute: '感知', effect_on_success: '无效果' },
    higher_level_cast: {
      per_slot_above_base: '每高于3环的法术位',
      effect: '额外影响两个目标'
    }
  },
  {
    name_zh: '恐惧术',
    name_en: 'Fear',
    level: 3,
    school: '惑控',
    casting_time: '1 动作',
    range: '自身（30尺锥形）',
    components: ['V', 'S', 'M (一根白色羽毛或一颗鸡心)'],
    duration: '专注，至多1分钟',
    description_short: '使敌人恐惧并逃跑。',
    description_long: '你投射出一个恐惧的幻象。30尺锥形范围内的每个生物必须成功通过一次感知豁免，否则丢弃手持物品并在法术持续时间内恐惧。',
    save: { attribute: '感知', effect_on_success: '无效果' },
    area_of_effect: { type: '锥形', size: '30尺' }
  },
  {
    name_zh: '催眠图纹',
    name_en: 'Hypnotic Pattern',
    level: 3,
    school: '幻术',
    casting_time: '1 动作',
    range: '120尺',
    components: ['S', 'M (一根发光的熏香或一个水晶)'],
    duration: '专注，至多1分钟',
    description_short: '催眠大范围敌人。',
    description_long: '你在射程内一点创造出扭曲的彩色光纹。30尺立方范围内能看到图纹的每个生物必须进行感知豁免。失败时，该生物在法术持续时间内被魅惑且失能。',
    save: { attribute: '感知', effect_on_success: '无效果' },
    area_of_effect: { type: '立方', size: '30尺' }
  },
  {
    name_zh: '高等幻象',
    name_en: 'Major Image',
    level: 3,
    school: '幻术',
    casting_time: '1 动作',
    range: '120尺',
    components: ['V', 'S', 'M (一点羊毛)'],
    duration: '专注，至多10分钟',
    description_short: '创造大型复杂幻象。',
    description_long: '你创造一个物体、生物或其他可见现象的幻象，不大于20尺立方。幻象包括声音、气味、温度和其他刺激，但只对该区域内的生物明显。',
    area_of_effect: { type: '立方', size: '20尺' },
    higher_level_cast: {
      per_slot_above_base: '6环法术位',
      effect: '法术持续到被解除，无需专注'
    }
  },
  {
    name_zh: '复生术',
    name_en: 'Revivify',
    level: 3,
    school: '死灵',
    casting_time: '1 动作',
    range: '触及',
    components: ['V', 'S', 'M (价值300gp的钻石)'],
    duration: '立即',
    description_short: '复活刚死去的生物。',
    description_long: '你触碰一个死去不超过1分钟的生物。该生物复活并恢复1点生命值。这个法术不能复活死于老年的生物，也不能恢复缺失的身体部位。'
  },
  {
    name_zh: '灵体守卫',
    name_en: 'Spirit Guardians',
    level: 3,
    school: '咒法',
    casting_time: '1 动作',
    range: '自身（15尺半径）',
    components: ['V', 'S', 'M (一个圣徽)'],
    duration: '专注，至多10分钟',
    description_short: '召唤守护灵体攻击敌人。',
    description_long: '你召唤灵体保护你。它们在你周围15尺半径内飞舞。当敌对生物首次在回合中进入该区域或在其中开始回合时，必须进行感知豁免。失败时受到3d8光耀或黯蚀伤害。',
    save: { attribute: '感知', effect_on_success: '伤害减半' },
    damage: '3d8',
    damage_type: '光耀或黯蚀',
    area_of_effect: { type: '球形', size: '15尺半径' },
    higher_level_cast: {
      per_slot_above_base: '每高于3环的法术位',
      effect: '增加1d8伤害'
    }
  },
  {
    name_zh: '吸血鬼之触',
    name_en: 'Vampiric Touch',
    level: 3,
    school: '死灵',
    casting_time: '1 动作',
    range: '自身',
    components: ['V', 'S'],
    duration: '专注，至多1分钟',
    description_short: '吸取生命力治疗自己。',
    description_long: '你的触碰吸取生物的生命力。进行一次近战法术攻击检定对抗一个生物。命中时，目标受到3d6黯蚀伤害，你恢复等于伤害一半的生命值。',
    attack_type: '法术攻击 (近战)',
    damage: '3d6',
    damage_type: '黯蚀',
    higher_level_cast: {
      per_slot_above_base: '每高于3环的法术位',
      effect: '增加1d6伤害'
    }
  },
  {
    name_zh: '水下呼吸',
    name_en: 'Water Breathing',
    level: 3,
    school: '变化',
    casting_time: '1 动作',
    range: '30尺',
    components: ['V', 'S', 'M (一小段芦苇或稻草)'],
    duration: '24小时',
    description_short: '使目标能在水下呼吸。',
    description_long: '这个法术赋予最多十个自愿生物在水下呼吸的能力，直到法术结束。受影响的生物也保留它们正常的呼吸模式。'
  },
  {
    name_zh: '水上行走',
    name_en: 'Water Walk',
    level: 3,
    school: '变化',
    casting_time: '1 动作',
    range: '30尺',
    components: ['V', 'S', 'M (一片软木)'],
    duration: '1小时',
    description_short: '使目标能在水面行走。',
    description_long: '这个法术赋予最多十个自愿生物在任何液体表面行走的能力，就像它是坚实地面一样，持续法术时间。'
  },
  {
    name_zh: '召雷术',
    name_en: 'Call Lightning',
    level: 3,
    school: '咒法',
    casting_time: '1 动作',
    range: '120尺',
    components: ['V', 'S'],
    duration: '专注，至多10分钟',
    description_short: '从天空召唤闪电攻击。',
    description_long: '一朵暴风云出现在射程内一个你能看见的点上方100尺处。当你施展法术时，以及在每个回合用一个动作，你可以从云中召唤闪电攻击。',
    damage: '3d10',
    damage_type: '闪电',
    save: { attribute: '敏捷', effect_on_success: '伤害减半' },
    higher_level_cast: {
      per_slot_above_base: '每高于3环的法术位',
      effect: '增加1d10伤害'
    }
  },
  {
    name_zh: '千里眼',
    name_en: 'Clairvoyance',
    level: 3,
    school: '预言',
    casting_time: '10分钟',
    range: '1英里',
    components: ['V', 'S', 'M (价值100gp的水晶球)'],
    duration: '专注，至多10分钟',
    description_short: '在远处创造隐形感应器。',
    description_long: '你在射程内一个你熟悉的地点创造一个隐形的感应器。感应器停留在该地点，持续法术时间，且不能被攻击或以其他方式互动。'
  },
  {
    name_zh: '造粮造水',
    name_en: 'Create Food and Water',
    level: 3,
    school: '咒法',
    casting_time: '1 动作',
    range: '30尺',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '创造食物和水。',
    description_long: '你在射程内地面上创造45磅食物和30加仑水，足够供养十五个类人生物或五匹坐骑24小时。食物平淡但有营养，如果不食用会在24小时后腐坏。'
  },
  {
    name_zh: '昼明术',
    name_en: 'Daylight',
    level: 3,
    school: '塑能',
    casting_time: '1 动作',
    range: '60尺',
    components: ['V', 'S'],
    duration: '1小时',
    description_short: '创造明亮的阳光。',
    description_long: '一个60尺半径的明亮光球在射程内一点出现。该球体是明亮光芒，额外60尺半径是微光。这光芒是阳光。',
    area_of_effect: { type: '球形', size: '60尺半径明亮光芒' }
  },
  {
    name_zh: '气化形体',
    name_en: 'Gaseous Form',
    level: 3,
    school: '变化',
    casting_time: '1 动作',
    range: '触及',
    components: ['V', 'S', 'M (一点薄纱和一缕烟)'],
    duration: '专注，至多1小时',
    description_short: '将目标变为气体形态。',
    description_long: '你将一个自愿的生物及其携带的所有东西变为雾状云朵，持续法术时间。在这种形态下，目标的唯一移动方式是10尺飞行速度。'
  }
];

/**
 * 生成3环法术世界书
 */
function generateLevel3WorldBook() {
  const worldBook = {
    entries: {
      5003: {
        uid: 5003,
        key: ["AI_LEVEL_3_SPELLS", "AI三环法术", "DND5E_AI_LEVEL_3", "AI_SPELLS_LEVEL_3"],
        keysecondary: [],
        comment: `AI生成的DND5e三环法术完整数据 (${LEVEL_3_SPELLS.length}个法术)`,
        content: JSON.stringify(LEVEL_3_SPELLS, null, 2),
        constant: true,
        vectorized: false,
        selective: true,
        selectiveLogic: 0,
        addMemo: true,
        order: 100,
        position: 0,
        disable: false,
        excludeRecursion: false,
        preventRecursion: false,
        delayUntilRecursion: false,
        probability: 100,
        useProbability: true,
        depth: 4,
        group: "",
        groupOverride: false,
        groupWeight: 100,
        scanDepth: null,
        caseSensitive: null,
        matchWholeWords: null,
        useGroupScoring: null,
        automationId: "",
        role: null,
        sticky: 0,
        cooldown: 0,
        delay: 0,
        displayIndex: 5003
      }
    }
  };
  
  const outputPath = 'AI_DND5e_Level3_Complete.json';
  fs.writeFileSync(outputPath, JSON.stringify(worldBook, null, 2), 'utf-8');
  console.log(`生成3环法术世界书: ${outputPath} (${LEVEL_3_SPELLS.length}个法术)`);
  
  return LEVEL_3_SPELLS;
}

// 执行生成
if (require.main === module) {
  console.log('=== 3环法术生成器 ===');
  generateLevel3WorldBook();
  console.log('3环法术世界书生成完成！');
}

module.exports = { LEVEL_3_SPELLS, generateLevel3WorldBook };
