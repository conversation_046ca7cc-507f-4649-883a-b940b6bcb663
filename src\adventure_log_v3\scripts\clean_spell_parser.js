/**
 * 绠€鍖栫増娉曟湳瑙ｆ瀽鑴氭湰 - 涓嶄緷璧栧閮ㄥ寘锛岀洿鎺ュ鐞咷B2312缂栫爜
 */

const fs = require('fs');
const path = require('path');

// 鐜暟鍚嶇О鏄犲皠
const LEVEL_NAMES = {
  0: '鎴忔硶',
  1: '涓€鐜硶鏈�',
  2: '浜岀幆娉曟湳', 
  3: '涓夌幆娉曟湳',
  4: '鍥涚幆娉曟湳',
  5: '浜旂幆娉曟湳',
  6: '鍏幆娉曟湳',
  7: '涓冪幆娉曟湳',
  8: '鍏幆娉曟湳',
  9: '涔濈幆娉曟湳'
};

/**
 * 绠€鍗曠殑GB2312瀛楃鏄犲皠琛紙甯哥敤瀛楃锛�
 */
const GB2312_MAP = {
  // 杩欓噷鍙寘鍚竴浜涘叧閿瓧绗︾殑鏄犲皠
  '戏锟斤拷': '鎴忔硶',
  '锟斤拷锟斤拷': '濉戣兘',
  '锟斤拷锟斤拷': '闃叉姢',
  '锟斤拷锟斤拷': '姝荤伒',
  '锟斤拷锟斤拷': '骞绘湳',
  '锟戒化': '鍙樺寲',
  '预锟斤拷': '棰勮█',
  '锟斤拷锟斤拷': '鍜掓硶',
  '锟斤拷锟斤拷': '闄勯瓟',
  '施锟斤拷时锟斤拷': '鏂芥硶鏃堕棿',
  '施锟斤拷锟斤拷锟�': '鏂芥硶璺濈',
  '锟斤拷锟斤拷锟缴凤拷': '娉曟湳鎴愬垎',
  '锟斤拷锟斤拷时锟斤拷': '鎸佺画鏃堕棿',
  '锟斤拷锟斤拷': '鍔ㄤ綔',
  '锟斤拷锟斤拷': '瑙︾',
  '锟斤拷锟斤拷': '绔嬪嵆',
  '专注': '涓撴敞',
  '锟斤拷锟斤拷': '鍒嗛挓',
  '小时': '灏忔椂',
  '锟斤拷': '灏�',
  '锟斤拷': '鐐�',
  '锟剿猴拷': '浼ゅ',
  '锟斤拷锟斤拷': '寮洪吀',
  '锟斤拷锟斤拷': '鐏劙',
  '锟斤拷锟斤拷': '瀵掑喎',
  '锟斤拷锟斤拷': '闂數',
  '锟斤拷锟斤拷': '闆烽福',
  '锟斤拷锟斤拷': '姣掔礌',
  '锟斤拷耀': '鍏夎€€',
  '锟斤拷蚀': '鏆楄殌',
  '锟斤拷锟斤拷': '鍔涘満',
  '锟斤拷锟斤拷': '蹇冪伒'
};

/**
 * 绠€鍗曠殑GB2312瑙ｇ爜鍑芥暟
 */
function decodeGB2312(buffer) {
  let content = buffer.toString('binary');
  
  // 搴旂敤瀛楃鏄犲皠
  for (const [gb, utf8] of Object.entries(GB2312_MAP)) {
    content = content.replace(new RegExp(gb, 'g'), utf8);
  }
  
  return content;
}

/**
 * 璇诲彇骞惰В鏋怘TML鏂囦欢
 */
function readAndParseHTMLFile(filepath) {
  try {
    const buffer = fs.readFileSync(filepath);
    const content = decodeGB2312(buffer);
    
    console.log(`璇诲彇鏂囦欢: ${filepath}`);
    console.log(`鏂囦欢澶у皬: ${buffer.length} 瀛楄妭`);
    
    // 绠€鍗曠殑娉曟湳瑙ｆ瀽 - 鏌ユ壘H4鏍囩
    const spells = [];
    const h4Matches = content.match(/<H4[^>]*id="([^"]*)"[^>]*>([^<]*)<\/H4>/g);
    
    if (h4Matches) {
      console.log(`鎵惧埌 ${h4Matches.length} 涓狧4鏍囩`);
      
      h4Matches.forEach((h4, index) => {
        const idMatch = h4.match(/id="([^"]*)"/);
        const nameMatch = h4.match(/>([^<]*)</);
        
        if (idMatch && nameMatch) {
          const id = idMatch[1];
          const name = nameMatch[1];
          
          // 瑙ｆ瀽涓嫳鏂囧悕绉�
          let name_zh, name_en;
          if (name.includes('锝�')) {
            [name_zh, name_en] = name.split('锝�').map(s => s.trim());
          } else if (name.includes('|')) {
            [name_zh, name_en] = name.split('|').map(s => s.trim());
          } else {
            name_zh = name.trim();
            name_en = id.replace(/_/g, ' ');
          }
          
          spells.push({
            id,
            name_zh,
            name_en,
            raw_name: name
          });
        }
      });
    }
    
    return spells;
  } catch (error) {
    console.error(`澶勭悊鏂囦欢 ${filepath} 鏃跺嚭閿�:`, error);
    return [];
  }
}

/**
 * 瑙ｆ瀽鎵€鏈夋硶鏈枃浠�
 */
function parseAllSpells() {
  const allSpells = [];
  const baseDir = 'DND5e_chm-main/DND5e_chm-main/鐜╁鎵嬪唽2024/娉曟湳璇﹁堪';
  
  for (let level = 0; level <= 9; level++) {
    const filename = `${level}鐜�.htm`;
    const filepath = path.join(baseDir, filename);
    
    console.log(`\n=== 澶勭悊 ${level}鐜硶鏈� ===`);
    
    if (fs.existsSync(filepath)) {
      const spells = readAndParseHTMLFile(filepath);
      
      // 涓烘瘡涓硶鏈坊鍔犵瓑绾т俊鎭�
      spells.forEach(spell => {
        spell.level = level;
        spell.level_name = LEVEL_NAMES[level];
      });
      
      console.log(`${level}鐜硶鏈В鏋愬畬鎴愶紝鍏� ${spells.length} 涓硶鏈痐);
      allSpells.push(...spells);
      
      // 鏄剧ず鍓嶅嚑涓硶鏈殑淇℃伅
      if (spells.length > 0) {
        console.log('鍓嶅嚑涓硶鏈�:');
        spells.slice(0, 3).forEach(spell => {
          console.log(`  - ${spell.name_zh} (${spell.name_en})`);
        });
      }
    } else {
      console.log(`鏂囦欢涓嶅瓨鍦�: ${filepath}`);
    }
  }
  
  return allSpells;
}

/**
 * 鐢熸垚鎸夌幆鏁板垎缁勭殑涓栫晫涔�
 */
function generateWorldBooks(spells) {
  console.log(`\n=== 鐢熸垚涓栫晫涔︽枃浠� ===`);
  console.log(`鎬诲叡瑙ｆ瀽浜� ${spells.length} 涓硶鏈痐);
  
  // 鎸夌瓑绾у垎缁�
  const spellsByLevel = {};
  spells.forEach(spell => {
    if (!spellsByLevel[spell.level]) {
      spellsByLevel[spell.level] = [];
    }
    spellsByLevel[spell.level].push(spell);
  });
  
  // 涓烘瘡涓瓑绾х敓鎴愪笘鐣屼功
  for (const [level, levelSpells] of Object.entries(spellsByLevel)) {
    const levelNum = parseInt(level);
    const levelName = LEVEL_NAMES[levelNum];
    
    const worldBook = {
      entries: {
        [1000 + levelNum]: {
          uid: 1000 + levelNum,
          key: [`SPELLS_LEVEL_${levelNum}`, `${levelName}`, `DND5E_${levelName}`],
          keysecondary: [],
          comment: `DND5e ${levelName}鍚堥泦 (${levelSpells.length}涓硶鏈�)`,
          content: JSON.stringify(levelSpells, null, 2),
          constant: true,
          vectorized: false,
          selective: true,
          selectiveLogic: 0,
          addMemo: true,
          order: 100,
          position: 0,
          disable: false,
          excludeRecursion: false,
          preventRecursion: false,
          delayUntilRecursion: false,
          probability: 100,
          useProbability: true,
          depth: 4,
          group: "",
          groupOverride: false,
          groupWeight: 100,
          scanDepth: null,
          caseSensitive: null,
          matchWholeWords: null,
          useGroupScoring: null,
          automationId: "",
          role: null,
          sticky: 0,
          cooldown: 0,
          delay: 0,
          displayIndex: 1000 + levelNum
        }
      }
    };
    
    const outputPath = `DND5e_${levelName}_WorldBook.json`;
    fs.writeFileSync(outputPath, JSON.stringify(worldBook, null, 2), 'utf-8');
    console.log(`鐢熸垚: ${outputPath} (${levelSpells.length}涓硶鏈�)`);
  }
  
  // 鐢熸垚瀹屾暣涓栫晫涔�
  const completeWorldBook = {
    entries: {
      2000: {
        uid: 2000,
        key: ["SPELLS_ALL", "鎵€鏈夋硶鏈�", "DND5E_ALL_SPELLS"],
        keysecondary: [],
        comment: `DND5e瀹屾暣娉曟湳搴� (${spells.length}涓硶鏈�)`,
        content: JSON.stringify(spells, null, 2),
        constant: true,
        vectorized: false,
        selective: true,
        selectiveLogic: 0,
        addMemo: true,
        order: 100,
        position: 0,
        disable: false,
        excludeRecursion: false,
        preventRecursion: false,
        delayUntilRecursion: false,
        probability: 100,
        useProbability: true,
        depth: 4,
        group: "",
        groupOverride: false,
        groupWeight: 100,
        scanDepth: null,
        caseSensitive: null,
        matchWholeWords: null,
        useGroupScoring: null,
        automationId: "",
        role: null,
        sticky: 0,
        cooldown: 0,
        delay: 0,
        displayIndex: 2000
      }
    }
  };
  
  const completeOutputPath = 'DND5e_Complete_Spells_WorldBook.json';
  fs.writeFileSync(completeOutputPath, JSON.stringify(completeWorldBook, null, 2), 'utf-8');
  console.log(`鐢熸垚: ${completeOutputPath} (${spells.length}涓硶鏈�)`);
}

// 涓绘墽琛屽嚱鏁�
if (require.main === module) {
  console.log('寮€濮嬭В鏋怐ND5e娉曟湳...\n');
  
  const spells = parseAllSpells();
  
  if (spells.length > 0) {
    generateWorldBooks(spells);
    console.log('\n瑙ｆ瀽瀹屾垚锛�');
  } else {
    console.log('\n娌℃湁瑙ｆ瀽鍒颁换浣曟硶鏈紝璇锋鏌ユ枃浠惰矾寰勩€�');
  }
}
      level: 2,
      school: '幻术',
      casting_time: '1 动作',
      range: '触及',
      components: ['V', 'S', 'M (一根睫毛包在阿拉伯胶中)'],
      duration: '专注，至多1小时',
      description_short: '使目标隐身。',
      description_long: '你触碰的生物变为隐身，直到法术结束。任何目标穿戴或携带的物品只要在目标身上就同样隐身。',
      higher_level_cast: {
        per_slot_above_base: '每高于2环的法术位',
        effect: '额外影响一个目标'
      }
    },
    {
      name_zh: '朦胧步',
      name_en: 'Misty Step',
      level: 2,
      school: '咒法',
      casting_time: '1 附赠动作',
      range: '自身',
      components: ['V'],
      duration: '立即',
      description_short: '瞬间传送30尺。',
      description_long: '你被银雾包围，然后传送至你能看见的30尺内的一个未被占据的空间。'
    },
    {
      name_zh: '灼热射线',
      name_en: 'Scorching Ray',
      level: 2,
      school: '塑能',
      casting_time: '1 动作',
      range: '120尺',
      components: ['V', 'S'],
      duration: '立即',
      description_short: '发射多道火焰射线。',
      description_long: '你创造三道火焰射线，并向射程内的目标发射。你可以将射线指向同一目标或不同目标。每道射线进行单独的攻击检定。',
      attack_type: '法术攻击 (远程)',
      damage: '2d6',
      damage_type: '火焰',
      num_projectiles: 3,
      higher_level_cast: {
        per_slot_above_base: '每高于2环的法术位',
        effect: '增加一道射线'
      }
    },

    // 3环法术
    {
      name_zh: '火球术',
      name_en: 'Fireball',
      level: 3,
      school: '塑能',
      casting_time: '1 动作',
      range: '150尺',
      components: ['V', 'S', 'M (一小撮蝙蝠粪和硫磺)'],
      duration: '立即',
      description_short: '爆炸性火球造成大范围伤害。',
      description_long: '一道明亮的火焰从你指尖射出，在射程内的一个点爆炸成火球。区域内的每个生物必须进行敏捷豁免。',
      damage: '8d6',
      damage_type: '火焰',
      save: { attribute: '敏捷', effect_on_success: '伤害减半' },
      area_of_effect: { type: '球形', size: '20尺半径' },
      higher_level_cast: {
        per_slot_above_base: '每高于3环的法术位',
        effect: '增加1d6伤害'
      }
    },
    {
      name_zh: '闪电束',
      name_en: 'Lightning Bolt',
      level: 3,
      school: '塑能',
      casting_time: '1 动作',
      range: '自身 (100尺线形)',
      components: ['V', 'S', 'M (一点毛皮和一根琥珀、玻璃或水晶棒)'],
      duration: '立即',
      description_short: '发射闪电线形攻击。',
      description_long: '一道闪电从你身上射出，形成100尺长、5尺宽的线形。线形路径上的每个生物必须进行敏捷豁免。',
      damage: '8d6',
      damage_type: '闪电',
      save: { attribute: '敏捷', effect_on_success: '伤害减半' },
      area_of_effect: { type: '线形', size: '100尺长5尺宽' },
      higher_level_cast: {
        per_slot_above_base: '每高于3环的法术位',
        effect: '增加1d6伤害'
      }
    },
    {
      name_zh: '反制法术',
      name_en: 'Counterspell',
      level: 3,
      school: '防护',
      casting_time: '1 反应',
      range: '60尺',
      components: ['S'],
      duration: '立即',
      description_short: '阻止敌人施法。',
      description_long: '你试图打断一个正在施法的生物。如果该生物施放的是3环或更低的法术，其法术失败且没有效果。',
      higher_level_cast: {
        per_slot_above_base: '每高于3环的法术位',
        effect: '自动反制更高环的法术'
      }
    }
  ];
}

/**
 * 生成世界书格式的法术数据
 */
function generateWorldBookEntries(spells) {
  const entries = {};
  let uid = 1000;
  
  // 按等级分组法术
  const spellsByLevel = {};
  spells.forEach(spell => {
    if (!spellsByLevel[spell.level]) {
      spellsByLevel[spell.level] = [];
    }
    spellsByLevel[spell.level].push(spell);
  });
  
  // 为每个等级创建法术包条目
  for (const [level, levelSpells] of Object.entries(spellsByLevel)) {
    const levelNum = parseInt(level);
    const levelName = levelNum === 0 ? '戏法' : `${levelNum}环法术`;
    
    entries[uid] = {
      uid: uid,
      key: [`SPELLS_LEVEL_${levelNum}`, `${levelName}`, `LEVEL_${levelNum}_SPELLS`],
      keysecondary: [],
      comment: `${levelName}合集 (${levelSpells.length}个法术)`,
      content: JSON.stringify(levelSpells, null, 2),
      constant: true,
      vectorized: false,
      selective: true,
      selectiveLogic: 0,
      addMemo: true,
      order: 100,
      position: 0,
      disable: false,
      excludeRecursion: false,
      preventRecursion: false,
      delayUntilRecursion: false,
      probability: 100,
      useProbability: true,
      depth: 4,
      group: "",
      groupOverride: false,
      groupWeight: 100,
      scanDepth: null,
      caseSensitive: null,
      matchWholeWords: null,
      useGroupScoring: null,
      automationId: "",
      role: null,
      sticky: 0,
      cooldown: 0,
      delay: 0,
      displayIndex: uid
    };
    uid++;
  }
  
  // 创建完整法术库条目
  entries[uid] = {
    uid: uid,
    key: ["SPELLS_ALL", "所有法术", "ALL_SPELLS", "法术库"],
    keysecondary: [],
    comment: `完整法术库 (${spells.length}个法术)`,
    content: JSON.stringify(spells, null, 2),
    constant: true,
    vectorized: false,
    selective: true,
    selectiveLogic: 0,
    addMemo: true,
    order: 100,
    position: 0,
    disable: false,
    excludeRecursion: false,
    preventRecursion: false,
    delayUntilRecursion: false,
    probability: 100,
    useProbability: true,
    depth: 4,
    group: "",
    groupOverride: false,
    groupWeight: 100,
    scanDepth: null,
    caseSensitive: null,
    matchWholeWords: null,
    useGroupScoring: null,
    automationId: "",
    role: null,
    sticky: 0,
    cooldown: 0,
    delay: 0,
    displayIndex: uid
  };
  
  return { entries };
}

// 主函数
function main() {
  const spells = createBasicSpells();
  const worldBook = generateWorldBookEntries(spells);
  
  // 保存到文件
  const outputPath = '../../../DND5e_Clean_Spells_WorldBook.json';
  fs.writeFileSync(outputPath, JSON.stringify(worldBook, null, 2), 'utf-8');
  console.log(`清理版世界书文件已生成: ${outputPath}`);
  console.log(`包含 ${spells.length} 个法术，分为 ${Object.keys(worldBook.entries).length} 个世界书条目`);
  
  // 显示法术统计
  const levelCounts = {};
  spells.forEach(spell => {
    levelCounts[spell.level] = (levelCounts[spell.level] || 0) + 1;
  });
  
  console.log('\n法术等级分布:');
  for (let level = 0; level <= 3; level++) {
    const count = levelCounts[level] || 0;
    const levelName = level === 0 ? '戏法' : `${level}环`;
    console.log(`${levelName}: ${count}个法术`);
  }
}

if (require.main === module) {
  main();
}
