// SCSS for Module Setup Interface

body {
  font-family: sans-serif;
  padding: 20px;
  background-color: #f4f4f4;
  color: #333;
}

#module-setup-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  max-width: 700px;
  margin: auto;
}

h1 {
  color: #333;
  text-align: center;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input[type='text'],
.form-group textarea {
  width: calc(100% - 22px); // Account for padding and border
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

.form-group textarea {
  resize: vertical; // Allow vertical resizing
  min-height: 100px;
}

// Button container for better layout if multiple buttons
.button-group {
  display: flex;
  gap: 10px; // Space between buttons
  margin-top: 20px;
}

button {
  flex-grow: 1; // Allow buttons to share space if in a group
  padding: 12px 15px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  // margin-top: 10px; // Remove individual margin-top if using button-group
}

#save-custom-module-button {
  // Specific styles if needed
}

#ai-generate-content-button {
  background-color: #28a745; // Green color for AI button
}

button:hover {
  opacity: 0.9;
}

#output-message {
  margin-top: 20px;
  padding: 10px;
  border: 1px solid #ddd;
  background-color: #e9e9e9;
  min-height: 30px;
  border-radius: 4px;
  word-wrap: break-word;
}

hr {
  border: 0;
  height: 1px;
  background-color: #ccc;
  margin: 30px 0;
}

.navigation-buttons button {
  background-color: #5a6268; // A neutral color for navigation
  &:hover {
    background-color: #4a4f54;
    }
}

// Styles for the new Weapon Selection Row
#character-sheet-form .dynamic-list-section .weapon-selection-row {
    display: flex;
    flex-direction: column; // Stack select and details vertically
    align-items: stretch; // Stretch items
    gap: 8px;

    .weapon-details-display {
        padding: 8px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        background-color: #f8f9fa;
        font-size: 0.9em;
        line-height: 1.4;
        min-height: 40px; // Ensure it has some height even when empty
        word-break: break-word;

        strong {
            color: #333;
        }
        br {
            display: block; // Ensure <br> creates a new line
            content: "";
            margin-top: 4px;
        }
    }

    // Container for label, select, and remove button to keep them in a row
    .weapon-select-control-group {
        display: flex;
        align-items: center;
        gap: 10px;
        flex-wrap: nowrap; // Prevent wrapping of these controls

        label {
            font-weight: normal; // Match other labels
            margin-bottom: 0; // Remove bottom margin as it's inline
            flex-shrink: 0; // Prevent label from shrinking
        }

        select {
            flex-grow: 1; // Allow select to take available space
            min-width: 150px; // Ensure select has a decent width
        }

        .remove-item-button {
            margin-left: 0; // Reset margin from general .dynamic-item-row
            flex-shrink: 0; // Prevent button from shrinking
        }
    }
}

// Responsive adjustments for weapon selection
@media (max-width: 768px) {
    #character-sheet-form .dynamic-list-section .weapon-selection-row {
        .weapon-select-control-group {
            flex-wrap: wrap; // Allow controls to wrap on very small screens if necessary
            
            select {
                min-width: calc(100% - 80px); // Adjust select width if label and button are present
            }
        }
    }
}

#character-creation-container h1 {
    color: #333;
    text-align: center;
    margin-bottom: 20px;
}

#character-sheet-form .form-section {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px dashed #eee;
}
#character-sheet-form .form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

#character-sheet-form .form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px; /* Gap between items in a row */
    margin-bottom: 10px;
}

#character-sheet-form .form-group-inline {
    flex: 1 1 calc(50% - 15px); /* Default to two columns on wider screens, adjust 15px for gap */
    min-width: 180px; /* Minimum width before wrapping to full width */
    display: flex;
    flex-direction: column; /* Stack label and input vertically */
}
@media (max-width: 600px) { /* For smaller screens, make them full width */
    #character-sheet-form .form-group-inline {
        flex-basis: 100%;
    }
}


#character-sheet-form .form-group-inline label {
    margin-bottom: 3px;
    font-size: 0.9em;
}

#character-sheet-form .form-group-inline input[type="text"],
#character-sheet-form .form-group-inline input[type="number"],
#character-sheet-form .form-group-inline select {
    width: 100%; /* Make input take full width of its flex container */
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
}

#character-sheet-form textarea {
    min-height: 60px; /* Adjust as needed */
}

#character-sheet-form .clear-section-button {
    background-color: #dc3545; // Red for clear/delete actions
    font-size: 0.8em;
    padding: 6px 10px;
    margin-top: 10px;
    flex-grow: 0; // Don't let it grow like other buttons in a group
    max-width: 120px; // Limit width
}

#character-sheet-form .attribute-grid {
    display: flex; /* Changed to flex for easier stacking */
    flex-direction: column;
    gap: 15px; /* Gap between each attribute row */
    margin-bottom: 10px;
}

#character-sheet-form .attribute-row {
    display: flex;
    flex-direction: column; /* Stack label and inputs vertically for each attribute */
    gap: 5px;
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 4px;
}

#character-sheet-form .attribute-row > strong { /* Attribute Name (e.g., 力量) */
    font-size: 1.1em;
    margin-bottom: 8px;
    color: #333;
}

#character-sheet-form .attribute-inputs {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr)); /* Smaller minmax for more items per row */
    gap: 5px 8px; /* Reduced gap */
    align-items: center;
}

#character-sheet-form .attribute-inputs > div { /* Wrapper for label + input */
    display: flex;
    flex-direction: column;
}

#character-sheet-form .attribute-inputs label {
    font-size: 0.75em; /* Smaller label */
    color: #555;
    margin-bottom: 1px;
    text-align: center;
}

#character-sheet-form .attribute-inputs input[type="number"],
#character-sheet-form .attribute-inputs input[type="text"] {
    width: 100%;
    padding: 6px; /* Reduced padding */
    font-size: 13px; /* Smaller font */
    text-align: center;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
}

#character-sheet-form .attribute-inputs input[readonly] {
    background-color: #e9ecef;
    font-weight: bold;
}

/* Hide original grid headers and spans if they are still in HTML, or remove them from HTML */
#character-sheet-form .attribute-grid .grid-header,
#character-sheet-form .attribute-grid span:not(.op),
#character-sheet-form .attribute-grid span.op {
    display: none; 
}

#character-sheet-form h3,
#character-sheet-form h4 {
    margin-top: 20px;
    margin-bottom: 10px;
    color: #495057;
}
#character-sheet-form h4 {
    font-size: 1.1em;
}


#character-sheet-form .spell-list {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-bottom: 10px;
}

#character-sheet-form .spell-list {
    .spell-item { // New class for each spell input + button row
        display: flex;
        align-items: center;
        gap: 5px;
        margin-bottom: 5px;
    }
    input[type="text"] {
        flex-grow: 1;
        padding: 8px; // Consistent padding
        border: 1px solid #ccc;
        border-radius: 4px;
    }
}

#character-sheet-form .spell-list .remove-spell-button {
    background-color: #6c757d; // A more neutral, less prominent color
    color: white;
    border: none;
    padding: 2px 6px;      // Make it smaller
    font-size: 0.75em;     // Smaller font
    line-height: 1;        // Ensure text fits
    cursor: pointer;
    border-radius: 3px;    // Slightly smaller radius
    flex-shrink: 0;
    min-width: auto; 
    flex-grow: 0; 
    margin-left: 5px;      // Add some space from the input
}
#character-sheet-form .spell-list .remove-spell-button:hover {
    background-color: #5a6268;
    opacity: 1; 
}

#character-sheet-form #add-cantrip-button,
#character-sheet-form #add-level1-spell-button {
    background-color: #6c757d;
    font-size: 0.9em;
    padding: 10px 15px; // Consistent padding
    margin-top: 8px; // Adjusted margin
    margin-bottom: 15px;
    flex-grow: 0;
    max-width: 180px; // Slightly wider
}

/* Ensure buttons in .form-row (like clear section in spell header) behave well */
#character-sheet-form .form-row .clear-section-button {
    margin-left: auto; /* Push to the right if in a flex row */
    align-self: center; /* Align with other items if row is taller */
}

// Styles for the Skills Section
#character-sheet-form .skills-section {
    h4 {
        margin-bottom: 10px;
    }
    .skills-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr); // Two columns by default
        gap: 10px 20px; // Row gap and column gap
        margin-bottom: 10px;

        @media (max-width: 600px) { // Single column on smaller screens
            grid-template-columns: repeat(1, 1fr);
        }
    }

    .skill-item {
        display: flex;
        flex-wrap: wrap; // Allow items to wrap within a skill item
        align-items: center;
        gap: 5px; // Gap between elements within a skill item
        padding: 5px;
        border: 1px solid #efefef;
        border-radius: 4px;

        label {
            font-weight: normal;
            margin-left: 5px;
            margin-right: auto; // Push modifier and final value to the right
            font-size: 0.9em;
            flex-basis: calc(100% - 100px); // Allow label to take most space, leave room for inputs
        }

        input[type="checkbox"] {
            margin-right: 5px;
        }

        .skill-modifier-input,
        .skill-final-value {
            width: 50px; // Fixed width for these small inputs
            padding: 5px;
            font-size: 0.9em;
            text-align: center;
            border: 1px solid #ccc;
            border-radius: 3px;
            box-sizing: border-box;
        }
        .skill-modifier-input {
            // Specific styles if any
        }
        .skill-final-value {
            background-color: #e9ecef;
            font-weight: bold;
        }
    }
    .clear-section-button { // Already styled, but ensure it's placed correctly in HTML
        margin-top: 10px;
    }
}

// Styles for Dynamic Equipment/Inventory Lists
#character-sheet-form .dynamic-list-section {
    margin-bottom: 20px;
    h4 {
        margin-bottom: 8px;
    }
    .dynamic-item-row {
        display: flex;
        gap: 10px;
        margin-bottom: 8px;
        align-items: center;

        input[type="text"], input[type="number"] {
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        input[name\$="Name[]"] { // Ends with Name[]
            flex-grow: 2;
        }
        input[name\$="Details[]"], input[name\$="Description[]"] { // Ends with Details[] or Description[]
            flex-grow: 3;
        }
        input[name\$="Quantity[]"] { // Ends with Quantity[]
            width: 70px;
            flex-grow: 0;
            text-align: center;
        }
        .remove-item-button {
            padding: 6px 10px;
            font-size: 0.8em;
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            flex-shrink: 0;
        }
    }
    .add-item-button {
        background-color: #5cb85c;
        font-size: 0.9em;
        padding: 10px 15px;
        margin-top: 5px;
        flex-grow: 0;
        max-width: 150px;
    }
}

// New styles for .full-equipment-row to ensure vertical stacking of its main sections
#character-sheet-form .dynamic-list-section {
    .full-equipment-row {
        display: flex;
        flex-direction: column; // Stack main sections vertically
        align-items: stretch;   // Make sections take full width
        gap: 12px;              // Gap between vertical sections
        padding: 12px;
        border: 1px solid #b0b0b0; // Slightly darker border for better visibility
        border-radius: 5px;
        margin-bottom: 15px;
        background-color: #fdfdfd; // Light background for the row itself

        // Top controls: Type select, Custom Name, Remove Item Button
            .equipment-controls-container {
                display: flex;
                flex-wrap: wrap; // Allow wrapping on smaller screens
                gap: 8px; // Reduce gap slightly
                align-items: center;
                margin-bottom: 8px; // Add some space below this control group

                .equipment-type-select {
                    min-width: 120px; 
                    flex-basis: 120px; // Give it a base size
                    flex-grow: 1;    // Allow to grow if space allows
                }
                .equipment-custom-name {
                    min-width: 150px;
                    flex-basis: 200px; // Give it a base size
                    flex-grow: 2;     // Allow to grow more than select, but not excessively
                    // max-width: 250px; // Constrain its maximum growth if needed
                }
                .remove-item-button { // Inherits general button styles, specific adjustments here
                    padding: 7px 12px; // Slightly adjust padding
                    font-size: 0.85em;
                    background-color: #dc3545; // Standard remove color
                    flex-shrink: 0; // Prevent shrinking
                    margin-left: auto; // Push to the right if space allows in non-wrapping row
                }

                // Responsive adjustments for the controls container itself
                @media (max-width: 500px) {
                    flex-direction: column; // Stack controls vertically on very small screens
                    align-items: stretch;   // Make them full width when stacked

                    .equipment-type-select,
                    .equipment-custom-name {
                        flex-basis: auto; // Reset basis when stacked
                        width: 100%;      // Take full width
                    }
                    .remove-item-button {
                        margin-left: 0;     // Reset margin when stacked
                        margin-top: 8px;    // Add some space above when stacked
                        width: 100%;        // Make button full width when stacked
                    }
                }
            }

        // Template selection: Label, Dropdown for weapon/armor template
        .template-select-container {
            display: flex; 
            gap: 8px;
            align-items: center;
            // JavaScript handles display: none initially

            label {
                margin-bottom: 0; 
                flex-shrink: 0;
                font-weight: bold;
            }
            .equipment-template-select {
                flex-grow: 1;
            }
        }

        // Display for base properties of the selected template
        .base-properties-display {
            padding: 10px;
            border: 1px solid #dcdcdc;
            border-radius: 4px;
            background-color: #f7f7f7;
            font-size: 0.9em;
            line-height: 1.5;
            min-height: 35px;
            word-break: break-word; // Ensure long property lists wrap
        }

        // Container for all magic effect rows
        .magic-effects-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 8px; 
            padding-top: 8px;
            border-top: 1px dashed #ccc; // Separator before magic effects
        }

        // Individual magic effect row
        .magic-effect-edit-row {
            display: flex;
            flex-wrap: wrap; 
            gap: 10px;      // Increased gap for better separation
            padding: 10px;
            border: 1px solid #c9c9c9;
            border-radius: 4px;
            background-color: #f0f0f0;

            .magic-effect-type-select {
                min-width: 180px;
                flex-basis: 220px; 
                flex-grow: 1;     
            }

            .magic-effect-value-inputs {
                display: flex;
                flex-wrap: wrap; 
                gap: 8px 12px;  // Row and column gap for label-input pairs
                align-items: center; 
                flex-basis: 320px;     
                flex-grow: 2;          

                label {
                    margin-bottom: 0; 
                    margin-right: 5px;
                    font-size: 0.85em;
                    flex-shrink: 0;
                }
                input, textarea, select {
                    padding: 7px;
                    font-size: 0.9em;
                    border: 1px solid #a0a0a0;
                    border-radius: 3px;
                    flex-grow: 1; 
                    min-width: 100px; 
                }
                // If a field needs to span full width within value-inputs
                .full-width-field {
                    flex-basis: 100%;
                    display: flex;
                    flex-direction: column; // Stack label above input
                    label { margin-bottom: 3px; }
                }
                textarea.full-width-field { // If textarea itself has this class
                     min-height: 45px;
                }
            }
            
            .remove-magic-effect-button {
                padding: 5px 9px;
                font-size: 0.8em;
                background-color: #c82333; 
                align-self: center; 
                margin-left: auto; 
            }
        }

        .add-magic-effect-button {
            align-self: flex-start; 
            max-width: 200px;
            background-color: #17a2b8; // Info color
            font-size: 0.9em;
        }

        .equipment-custom-description {
            width: 100%;
            min-height: 60px;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
    }
}
