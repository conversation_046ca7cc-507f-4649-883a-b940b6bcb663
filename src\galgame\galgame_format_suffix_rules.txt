[Galgame格式结尾规则]
以下规则应用于所有Galgame界面数据的生成，确保AI输出能被系统正确解析和渲染。

**核心包裹标识符:**
所有Galgame界面数据 **必须** 被 `查看系统` 和 `关闭系统` 标识符完整包裹。

```
查看系统 /* 此处必须完整生成所有字符，作为数据块的开始 */
...
(此处为 <地点:场景名> 或 <人物:角色名> 数据块，
 包含所有相关的状态信息、描述、其他角色、行动选项或对话内容)
...
关闭系统 /* 此处必须完整生成所有字符，作为数据块的结束 */
```

**关键规则:**

1.  **确保本轮回复中只存在一个 `查看系统 ... 关闭系统` 格式块！**
    AI的单次回复中，不应生成多个独立的 `查看系统 ... 关闭系统` 数据段。所有相关信息都应整合在唯一的一对包裹标识符内。

2.  **确保所有相关的地点和人物数据都位于同一个 `查看系统 ... 关闭系统` 格式块中！**
    例如，如果一个场景包含地点信息和多个在该地点可交互的人物（即使尚未开始对话），这些信息都应在同一个 `查看系统 ... 关闭系统` 块内，通过嵌套的 `<地点:场景名>` 和可能的 `其他角色名称` 字段来表述。不要为每个元素创建单独的 `查看系统` 包裹。

3.  **确保不在 `查看系统 ... 关闭系统` 格式块内部输出角色心理、动作描述或旁白内容！**
    此格式块严格用于结构化数据。任何非结构化的叙述性文本（如角色思考、场景的氛围描述、角色的细微动作等）应该在此格式块之外，或者通过其他约定的方式传递（如果游戏逻辑支持）。格式块内部只应包含定义好的数据字段和标签。

4.  **必须以 `关闭系统` 标识符作为整个数据块的明确收尾！**
    这是解析器识别数据结束的关键。

**结合 `galgame_world_info_prompt.txt` 使用:**
此“格式结尾规则”是对 `galgame_world_info_prompt.txt` 中定义的具体数据格式的补充和强调。AI应首先遵循 `galgame_world_info_prompt.txt` 来构造内部的数据结构（如 `<地点:家>...` 或 `<人物:小花A>...`），然后将这整个结构用 `查看系统` 和 `关闭系统` 包裹起来，并遵守上述四条关键规则。

**错误示例 (不应出现的情况):**
```
查看系统
<地点:公园>
...
</地点:公园>
关闭系统

哦，对了，小明看起来很开心。 /* <--- 这是不应在格式块外的额外叙述 */

查看系统  /* <--- 不应有第二个格式块 */
<人物:小明>
...
</人物:小明>
关闭系统
```

**正确示例 (将公园信息和小明作为公园内的角色整合):**
```
查看系统
<地点:公园>
时间--"14:00"--HH:MM
体力--"90"--HH:MM
其他角色名称--"小明"--HH:MM
其他角色名称--"小红"--HH:MM
行动选项A--"和小明对话"--HH:MM
行动选项B--"和小孩们一起玩"--HH:MM
</地点:公园>
关闭系统
```
(当玩家选择“和小明对话”后，AI的下一条回复才会是 `<人物:小明>...` 的数据块，同样被 `查看系统` 和 `关闭系统` 包裹。)
