/**
 * 5环法术生成器
 * 基于AI知识库生成完整的DND5e 5环法术数据
 */

const fs = require('fs');

// 5环法术数据
const LEVEL_5_SPELLS = [
  {
    name_zh: '活化死尸',
    name_en: 'Animate Dead',
    level: 5,
    school: '死灵',
    casting_time: '1分钟',
    range: '10尺',
    components: ['V', 'S', 'M (一滴血、一片血肉和一撮骨粉)'],
    duration: '立即',
    description_short: '创造不死生物仆从。',
    description_long: '这个法术创造一个不死仆从。选择射程内一堆骨头或一具尸体。你的法术向目标注入邪恶的模仿生命，将其复活为不死生物。',
    higher_level_cast: {
      per_slot_above_base: '每高于5环的法术位',
      effect: '可额外活化或重新控制两个不死生物'
    }
  },
  {
    name_zh: '反制法术',
    name_en: 'Counterspell',
    level: 5,
    school: '防护',
    casting_time: '1 反应',
    range: '60尺',
    components: ['S'],
    duration: '立即',
    description_short: '阻止敌人施法。',
    description_long: '你试图打断一个正在施法的生物。如果该生物施展的是5环或更低的法术，其法术失败且没有效果。如果是6环或更高的法术，进行属性检定。',
    higher_level_cast: {
      per_slot_above_base: '每高于5环的法术位',
      effect: '自动反制的法术等级提高1环'
    }
  },
  {
    name_zh: '锥形寒冰',
    name_en: 'Cone of Cold',
    level: 5,
    school: '塑能',
    casting_time: '1 动作',
    range: '自身（60尺锥形）',
    components: ['V', 'S', 'M (一个小水晶或玻璃锥)'],
    duration: '立即',
    description_short: '锥形寒冷攻击多个敌人。',
    description_long: '一阵寒冷的空气从你手中爆发。60尺锥形范围内的每个生物必须进行体质豁免。失败时受到8d8寒冷伤害，成功时伤害减半。',
    save: { attribute: '体质', effect_on_success: '伤害减半' },
    damage: '8d8',
    damage_type: '寒冷',
    area_of_effect: { type: '锥形', size: '60尺' },
    higher_level_cast: {
      per_slot_above_base: '每高于5环的法术位',
      effect: '增加1d8伤害'
    }
  },
  {
    name_zh: '支配人类',
    name_en: 'Dominate Person',
    level: 5,
    school: '惑控',
    casting_time: '1 动作',
    range: '60尺',
    components: ['V', 'S'],
    duration: '专注，至多1分钟',
    description_short: '魅惑并控制一个类人生物。',
    description_long: '你试图魅惑射程内一个你能看见的类人生物。目标必须成功通过一次感知豁免，否则在法术持续时间内被你魅惑。如果你与目标处于战斗中，它在豁免上具有优势。',
    save: { attribute: '感知', effect_on_success: '无效果' },
    higher_level_cast: {
      per_slot_above_base: '6环法术位',
      effect: '持续时间变为10分钟；7环法术位持续时间变为1小时；8环或更高法术位持续时间变为8小时'
    }
  },
  {
    name_zh: '梦境',
    name_en: 'Dream',
    level: 5,
    school: '幻术',
    casting_time: '1分钟',
    range: '特殊',
    components: ['V', 'S', 'M (一把沙子、一滴墨水和一根羽毛笔)'],
    duration: '8小时',
    description_short: '进入他人梦境传递信息。',
    description_long: '这个法术塑造一个生物的梦境。选择一个你认识的生物作为法术目标。目标必须在与你相同的存在位面上。你或一个你触碰的自愿生物进入恍惚状态，充当梦境使者。',
    higher_level_cast: {
      per_slot_above_base: '每高于5环的法术位',
      effect: '可以同时影响额外一个目标'
    }
  },
  {
    name_zh: '焰击术',
    name_en: 'Flame Strike',
    level: 5,
    school: '塑能',
    casting_time: '1 动作',
    range: '60尺',
    components: ['V', 'S', 'M (一撮硫磺)'],
    duration: '立即',
    description_short: '神圣火焰柱攻击敌人。',
    description_long: '一道垂直的火焰柱从天而降，攻击射程内一个你指定的地点。10尺半径、40尺高的圆柱形区域内的每个生物必须进行敏捷豁免。失败时受到4d6火焰伤害和4d6光耀伤害，成功时伤害减半。',
    save: { attribute: '敏捷', effect_on_success: '伤害减半' },
    damage: '4d6火焰 + 4d6光耀',
    damage_type: '火焰和光耀',
    area_of_effect: { type: '圆柱', size: '半径10尺，高40尺' },
    higher_level_cast: {
      per_slot_above_base: '每高于5环的法术位',
      effect: '火焰伤害增加1d6'
    }
  },
  {
    name_zh: '高等复原术',
    name_en: 'Greater Restoration',
    level: 5,
    school: '防护',
    casting_time: '1 动作',
    range: '触及',
    components: ['V', 'S', 'M (价值100gp的钻石粉，法术消耗此材料)'],
    duration: '立即',
    description_short: '移除强力负面效果。',
    description_long: '你向一个生物注入正能量来消除一个影响它的衰弱效果。你可以减少目标的一项属性值1点的永久减值，或结束目标身上的下述效果之一：魅惑、石化、诅咒、属性值减少或最大生命值减少。'
  },
  {
    name_zh: '坚持术',
    name_en: 'Hold Monster',
    level: 5,
    school: '惑控',
    casting_time: '1 动作',
    range: '90尺',
    components: ['V', 'S', 'M (一小段直铁)'],
    duration: '专注，至多1分钟',
    description_short: '麻痹一个生物。',
    description_long: '选择射程内一个你能看见的生物。目标必须成功通过一次感知豁免，否则在法术持续时间内被麻痹。这个法术对不死生物无效。',
    save: { attribute: '感知', effect_on_success: '无效果' },
    higher_level_cast: {
      per_slot_above_base: '每高于5环的法术位',
      effect: '额外影响一个目标'
    }
  },
  {
    name_zh: '昆虫灾害',
    name_en: 'Insect Plague',
    level: 5,
    school: '咒法',
    casting_time: '1 动作',
    range: '300尺',
    components: ['V', 'S', 'M (几粒蝗虫腿)'],
    duration: '专注，至多10分钟',
    description_short: '召唤昆虫群攻击敌人。',
    description_long: '蜂拥的昆虫填满一个以射程内一点为中心的20尺半径球体。该区域在法术持续时间内成为困难地形，且区域被轻度遮蔽。',
    damage: '4d10',
    damage_type: '穿刺',
    save: { attribute: '体质', effect_on_success: '伤害减半' },
    area_of_effect: { type: '球形', size: '20尺半径' },
    higher_level_cast: {
      per_slot_above_base: '每高于5环的法术位',
      effect: '增加1d10伤害'
    }
  },
  {
    name_zh: '群体治疗轻伤',
    name_en: 'Mass Cure Wounds',
    level: 5,
    school: '塑能',
    casting_time: '1 动作',
    range: '60尺',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '治疗多个目标。',
    description_long: '治疗能量波从你身上涌出。选择射程内最多六个生物。每个目标恢复3d8+你的施法属性调整值点生命值。这个法术对不死生物或构装体无效。',
    damage: '3d8',
    damage_type: '治疗',
    add_casting_modifier_to_damage: true,
    higher_level_cast: {
      per_slot_above_base: '每高于5环的法术位',
      effect: '增加1d8治疗'
    }
  },
  {
    name_zh: '误导术',
    name_en: 'Mislead',
    level: 5,
    school: '幻术',
    casting_time: '1 动作',
    range: '自身',
    components: ['S'],
    duration: '专注，至多1小时',
    description_short: '变隐形并创造幻象分身。',
    description_long: '你变为隐形，同时在你的位置出现一个你的幻象复制品。这个幻象持续法术时间，但如果你攻击或施法，隐形效果结束。'
  },
  {
    name_zh: '变化自如',
    name_en: 'Modify Memory',
    level: 5,
    school: '惑控',
    casting_time: '1 动作',
    range: '30尺',
    components: ['V', 'S'],
    duration: '专注，至多1分钟',
    description_short: '修改目标的记忆。',
    description_long: '你试图重塑另一个生物的记忆。射程内一个你能看见的生物必须进行感知豁免。如果你与目标处于战斗中，这次豁免具有优势。失败时，目标被魅惑，持续法术时间。',
    save: { attribute: '感知', effect_on_success: '无效果' },
    higher_level_cast: {
      per_slot_above_base: '6环法术位',
      effect: '可以修改10分钟的记忆；7环法术位可以修改1小时的记忆；8环法术位可以修改1天的记忆；9环法术位可以修改任意时长的记忆'
    }
  },
  {
    name_zh: '通神术',
    name_en: 'Commune',
    level: 5,
    school: '预言',
    casting_time: '1分钟',
    range: '自身',
    components: ['V', 'S', 'M (熏香和一个装有圣水或邪水的小瓶)'],
    duration: '1分钟',
    description_short: '与神明或强大存在交流。',
    description_long: '你与你的神明或神明的代理人接触，询问最多三个可以用是或否回答的问题。你必须在法术结束前问完这些问题。你得到每个问题的正确答案。'
  },
  {
    name_zh: '通晓语言',
    name_en: 'Tongues',
    level: 5,
    school: '预言',
    casting_time: '1 动作',
    range: '触及',
    components: ['V', 'M (一小块粘土)'],
    duration: '1小时',
    description_short: '理解和说任何语言。',
    description_long: '这个法术赋予你触碰的生物理解任何口语的能力。此外，当目标说话时，任何知道至少一种语言且能听到目标说话的生物都能理解目标所说的话。'
  },
  {
    name_zh: '传送术',
    name_en: 'Teleportation Circle',
    level: 5,
    school: '咒法',
    casting_time: '1分钟',
    range: '10尺',
    components: ['V', 'M (价值50gp的稀有粉笔和墨水，法术消耗此材料)'],
    duration: '1轮',
    description_short: '创造传送法阵。',
    description_long: '当你施展这个法术时，你在地面上画出一个10尺半径的圆圈，连接到你选择的一个永久传送法阵。一个临时传送法阵出现在圆圈内。'
  },
  {
    name_zh: '树木通行',
    name_en: 'Tree Stride',
    level: 5,
    school: '咒法',
    casting_time: '1 动作',
    range: '自身',
    components: ['V', 'S'],
    duration: '专注，至多1分钟',
    description_short: '通过树木传送。',
    description_long: '你获得进入一棵活树并从另一棵同种类的活树中出现的能力，两棵树都必须至少是大型。你必须使用5尺移动力进入一棵树。'
  },
  {
    name_zh: '石墙术',
    name_en: 'Wall of Stone',
    level: 5,
    school: '塑能',
    casting_time: '1 动作',
    range: '120尺',
    components: ['V', 'S', 'M (一小块花岗岩)'],
    duration: '专注，至多10分钟',
    description_short: '创造石墙阻挡敌人。',
    description_long: '一堵非魔法的石墙从地面升起。墙壁厚6英寸，由十个10尺×10尺的面板组成。每个面板必须与至少一个其他面板相邻。',
    area_of_effect: { type: '墙壁', size: '十个10尺×10尺面板' },
    higher_level_cast: {
      per_slot_above_base: '每高于5环的法术位',
      effect: '增加两个面板'
    }
  },
  {
    name_zh: '探知',
    name_en: 'Scrying',
    level: 5,
    school: '预言',
    casting_time: '10分钟',
    range: '自身',
    components: ['V', 'S', 'M (价值1000gp的水晶球、银镜或装满圣水的字体)'],
    duration: '专注，至多10分钟',
    description_short: '远程观察特定生物。',
    description_long: '你能看到和听到你选择的特定生物，该生物与你在同一存在位面上。目标必须进行感知豁免。如果目标认识你，它在豁免上具有劣势。如果你拥有目标的一部分身体，它在豁免上具有劣势。',
    save: { attribute: '感知', effect_on_success: '无法被探知' }
  },
  {
    name_zh: '复活术',
    name_en: 'Raise Dead',
    level: 5,
    school: '死灵',
    casting_time: '1小时',
    range: '触及',
    components: ['V', 'S', 'M (价值500gp的钻石，法术消耗此材料)'],
    duration: '立即',
    description_short: '复活死去的生物。',
    description_long: '你让一个死去不超过10天的生物复活。如果该生物的灵魂自由且愿意，该生物复活并恢复1点生命值。这个法术还能封闭致命伤口并恢复缺失的身体部位。'
  },
  {
    name_zh: '钢铁意志',
    name_en: 'Geas',
    level: 5,
    school: '惑控',
    casting_time: '1分钟',
    range: '60尺',
    components: ['V'],
    duration: '30天',
    description_short: '强制目标执行任务。',
    description_long: '你在射程内一个你能看见的生物身上施加魔法命令。目标必须成功通过一次感知豁免，否则在法术持续时间内被魅惑。当被魅惑时，目标受到你给予的魔法命令约束。',
    save: { attribute: '感知', effect_on_success: '无效果' },
    higher_level_cast: {
      per_slot_above_base: '7环或8环法术位',
      effect: '持续时间变为1年；9环法术位持续时间变为永久'
    }
  }
];

/**
 * 生成5环法术世界书
 */
function generateLevel5WorldBook() {
  const worldBook = {
    entries: {
      5005: {
        uid: 5005,
        key: ["AI_LEVEL_5_SPELLS", "AI五环法术", "DND5E_AI_LEVEL_5", "AI_SPELLS_LEVEL_5"],
        keysecondary: [],
        comment: `AI生成的DND5e五环法术完整数据 (${LEVEL_5_SPELLS.length}个法术)`,
        content: JSON.stringify(LEVEL_5_SPELLS, null, 2),
        constant: true,
        vectorized: false,
        selective: true,
        selectiveLogic: 0,
        addMemo: true,
        order: 100,
        position: 0,
        disable: false,
        excludeRecursion: false,
        preventRecursion: false,
        delayUntilRecursion: false,
        probability: 100,
        useProbability: true,
        depth: 4,
        group: "",
        groupOverride: false,
        groupWeight: 100,
        scanDepth: null,
        caseSensitive: null,
        matchWholeWords: null,
        useGroupScoring: null,
        automationId: "",
        role: null,
        sticky: 0,
        cooldown: 0,
        delay: 0,
        displayIndex: 5005
      }
    }
  };
  
  const outputPath = 'AI_DND5e_Level5_Complete.json';
  fs.writeFileSync(outputPath, JSON.stringify(worldBook, null, 2), 'utf-8');
  console.log(`生成5环法术世界书: ${outputPath} (${LEVEL_5_SPELLS.length}个法术)`);
  
  return LEVEL_5_SPELLS;
}

// 执行生成
if (require.main === module) {
  console.log('=== 5环法术生成器 ===');
  generateLevel5WorldBook();
  console.log('5环法术世界书生成完成！');
}

module.exports = { LEVEL_5_SPELLS, generateLevel5WorldBook };
