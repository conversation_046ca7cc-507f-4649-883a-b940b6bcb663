import './scss/phone.scss';
import './scss/statusbar.scss';
import './scss/chat.scss';

declare const $: any; 
declare const toastr: { 
    success: (message: string, title?: string) => void;
    info: (message: string, title?: string) => void;
    error: (message: string, title?: string) => void;
    warning: (message: string, title?: string) => void;
};
declare function triggerSlash(command: string): Promise<string | undefined>;
declare function getLastMessageId(): number | undefined; 
declare function setChatMessages(messages: {message_id: number, message: string}[], options?: {refresh?: string}): Promise<void>;

declare global {
    interface Window { 
        UserName?: string;
    }
}

let currentHostMessageId: number | null = null; 

function safeToastr(type: 'info' | 'success' | 'warning' | 'error', message: string, title?: string) {
    if (typeof toastr === 'object' && toastr !== null && typeof toastr[type] === 'function') {
        toastr[type](message, title);
    } else {
        if (type === 'error') alert(`[${title || '错误'}]: ${message}`);
        console.log(`[Toastr Fallback - ${type}] ${title ? title + ': ' : ''}${message}`);
    }
}

function ensureGlobals() {
    try {
        if (typeof window !== 'undefined' && typeof parent !== 'undefined' && parent !== window) {
            const apiKeysToCopy = ['$','toastr','triggerSlash', 'getLastMessageId', 'setChatMessages']; 
            apiKeysToCopy.forEach(key => {
                if (typeof (window as any)[key] === 'undefined') {
                    if (typeof (parent as any)[key] !== 'undefined') {
                        (window as any)[key] = (parent as any)[key];
                        console.log(`API "${key}" copied from parent.`);
                    } else {
                         console.warn(`API "${key}" is UNDEFINED in parent window.`);
                         if (key !== 'toastr' && key !== 'setChatMessages' && key !== 'getLastMessageId') { // Avoid toastr for these specific missing APIs initially
                            safeToastr('warning', `API "${key}" 在父窗口中未定义。`, "API警告");
                         } else if (key === 'setChatMessages' || key === 'getLastMessageId') {
                            // User will see specific warnings later if these are used and still undefined
                         }
                    }
                }
            });
        }
    } catch (e) {
        safeToastr('error', `确保全局变量时出错: ${(e as Error).message}`, "初始化错误");
    }
}

function parseMessageContent(rawContent: string): string {
    const quotedMatch = rawContent.match(/^“([\s\S]*)”$/) || rawContent.match(/^"([\s\S]*)"$/);
    if (quotedMatch && quotedMatch[1]) return quotedMatch[1];
    return rawContent.split('--')[0].trim();
}

function parseAndRenderAIMessages(aiResponseText: string) {
    if (typeof $ !== 'function') {
        safeToastr('error', "jQuery ($) 未定义，无法渲染。", "渲染错误");
        return;
    }
    safeToastr('info', `渲染AI或初始回复 (前50字符): ${aiResponseText.substring(0,50)}...`, "调试");

    const activeChatScreen = $('.phone-screen:visible').length ? $('.phone-screen:visible') : $('.phone-screen').first();
    if (activeChatScreen.length === 0) {
        safeToastr('warning', "无 .phone-screen 元素可供渲染。", "渲染提示");
        return;
    }
    const activeChatMessages = activeChatScreen.find('.chat-interface .chat-messages');
    if (activeChatMessages.length === 0) {
        safeToastr('error', "未找到消息容器 .chat-messages", "渲染错误");
        return;
    }

    const coreMessageBlockMatch = aiResponseText.match(/msg_start([\s\S]+?)msg_end/i);
    if (!coreMessageBlockMatch || !coreMessageBlockMatch[1]) {
        safeToastr('warning', `回复格式不符或无数据 (前50): ${aiResponseText.substring(0,50)}`, "解析警告");
        activeChatMessages.empty(); 
        const cleanedText = aiResponseText.replace(/<think>[\s\S]*?<\/think>/gi, '').trim();
        if (cleanedText && !cleanedText.toLowerCase().includes("[查看系统]") && !cleanedText.toLowerCase().includes("msg_start")) { 
            activeChatMessages.append($('<div>').addClass('message received').append($('<p>').text(cleanedText)));
        }
        activeChatMessages.scrollTop(activeChatMessages[0].scrollHeight);
        return;
    }

    const chatContent = coreMessageBlockMatch[1].trim();
    activeChatMessages.empty();
    const lines = chatContent.split('\n');
    const currentAICharacterName = activeChatScreen.find('.status-bar .character-name').text().trim() || "角色名称";

    for (const line of lines) {
        const trimmedLine = line.trim();
        if (!trimmedLine || trimmedLine.startsWith('<') || trimmedLine.startsWith('</')) continue;

        const parts = trimmedLine.split('--');
        if (parts.length >= 2) {
            const sender = parts[0].trim();
            const messageText = parseMessageContent(parts.slice(1).join('--').trim());
            const messageDiv = $('<div>').addClass(
                sender.toLowerCase() === "user" || (window.UserName && sender.toLowerCase() === window.UserName.toLowerCase()) ? 'message sent' : 'message received'
            );
            messageDiv.append($('<p>').text(messageText));
            activeChatMessages.append(messageDiv);
        } else {
            safeToastr('warning', `无法解析行: ${trimmedLine}`, "解析警告");
        }
    }
    activeChatMessages.scrollTop(activeChatMessages[0].scrollHeight);
    safeToastr('success', "消息渲染完成", "调试");
}

function handleSendMessage(event?: MouseEvent | KeyboardEvent) { 
    safeToastr('info', 'handleSendMessage 调用', "调试"); 
    if (typeof $ !== 'function' || typeof triggerSlash !== 'function') { 
        safeToastr('error', "核心API未就绪", "发送错误");
        return;
    }

    let currentInputElement: HTMLInputElement | null = null;
    let chatScreenForContext: JQuery<HTMLElement> | null = null; 

    if (event && event.currentTarget) {
        const target = $(event.currentTarget as HTMLElement);
        chatScreenForContext = target.closest('.phone-screen');
        if (chatScreenForContext && chatScreenForContext.length > 0) { // Check here
            currentInputElement = chatScreenForContext.find('.chat-input input').get(0) as HTMLInputElement | null;
        }
    }
    if (!chatScreenForContext || chatScreenForContext.length === 0) {
        chatScreenForContext = $('.phone-screen:visible');
        if (chatScreenForContext && chatScreenForContext.length > 0) { // Check here
            currentInputElement = chatScreenForContext.find('.chat-input input').get(0) as HTMLInputElement | null;
        }
    }
    
    if (!chatScreenForContext || chatScreenForContext.length === 0) { // Final check
        safeToastr('error', "未找到活动聊天窗口", "发送错误"); return;
    }
    // From this point, chatScreenForContext is considered valid by the logic, even if TS complains without further hints
    const validChatScreen = chatScreenForContext as JQuery<HTMLElement>; // Type assertion for TS

    if (!currentInputElement) {
        currentInputElement = validChatScreen.find('.chat-input input').get(0) as HTMLInputElement | null; // Try one last time
        if (!currentInputElement) {
            safeToastr('error', "未找到消息输入框", "发送错误"); return;
        }
    }

    const characterName = validChatScreen.find('.status-bar .character-name').text().trim() || "角色名称";
    const rawMessageText = currentInputElement.value.trim();

    if (rawMessageText !== '') {
        const formattedMessage = `给${characterName}发消息:${rawMessageText}\n<Request:本次响应忽略其他上下文任何要求,必须使用线上格式回复,且${characterName}本次发了消息的角色都要回复${characterName}的消息,同时输出一条动态内容>`;
        safeToastr('info', `构造 prompt (前70): ${formattedMessage.substring(0,70)}...`, "调试");
        const command = `/gen ${formattedMessage}`; 
        safeToastr('info', `执行 triggerSlash: ${command}`, "调试");
        
        triggerSlash(command)
            .then(async (result: any) => { 
                safeToastr('success', `AI回复 (前100): ${typeof result === 'string' ? result.substring(0,100) : JSON.stringify(result)}`, "调试");
                if (typeof result === 'string') {
                    parseAndRenderAIMessages(result); 

                    if (currentHostMessageId !== null && typeof setChatMessages === 'function') {
                        safeToastr('info', `准备更新父消息 (ID: ${currentHostMessageId}) 的内容。`, "持久化");
                        try {
                            await setChatMessages(
                                [{ message_id: currentHostMessageId, message: result }],
                                { refresh: 'affected' } 
                            );
                            safeToastr('success', `父消息 (ID: ${currentHostMessageId}) 内容已更新。`, "持久化");
                        } catch (setErr: any) {
                            safeToastr('error', `更新父消息 (ID: ${currentHostMessageId}) 失败: ${setErr.message || JSON.stringify(setErr)}`, "持久化错误");
                        }
                    } else {
                        if (currentHostMessageId === null) safeToastr('warning', "未存储父消息ID，无法持久化更新。", "持久化");
                        if (typeof setChatMessages !== 'function') safeToastr('warning', "setChatMessages API不可用，无法持久化更新。", "持久化");
                    }
                } else {
                    safeToastr('warning', `AI未返回字符串: ${typeof result}`, "回复处理");
                }
            })
            .catch((err: any) => {
                safeToastr('error', `triggerSlash失败: ${(err instanceof Error ? err.message : JSON.stringify(err))}`, "发送失败"); 
            });
        currentInputElement.value = ''; 
    } else {
        safeToastr('warning', "消息为空未发送", "提示");
    }
}

async function initializeChatInterface() {
    safeToastr('info', "脚本加载，初始化...", "初始化");
    const initialDelay = 200; 

    setTimeout(async () => { 
        safeToastr('info', "setTimeout 执行...", "初始化");
        ensureGlobals(); 

        if (typeof $ !== 'function' || typeof triggerSlash !== 'function') {
            safeToastr('error', "核心API ($或triggerSlash) 未加载，中止。", "初始化严重错误");
            return;
        }

        if (typeof window.UserName === 'undefined') {
            try {
                window.UserName = await triggerSlash("/pass {{user}}") || "User"; 
                safeToastr('info', `用户名设为: ${window.UserName}`, "初始化");
            } catch (e) {
                window.UserName = "User"; 
                safeToastr('error', `获取用户名出错: ${(e as Error).message}`, "初始化错误");
            }
        }
        
        if (typeof getLastMessageId === 'function' && typeof triggerSlash === 'function') {
            try {
                const lastId = getLastMessageId();
                if (typeof lastId === 'number' && lastId >= 0) {
                    currentHostMessageId = lastId; 
                    safeToastr('info', `获取到最后消息ID: ${lastId} (设为父消息ID)，尝试加载其内容。`, "初始加载");
                    const command = `/messages ${lastId}`;
                    const initialMessageContent = await triggerSlash(command);

                    if (typeof initialMessageContent === 'string' && initialMessageContent.trim() !== "") {
                        safeToastr('success', `成功从父消息 (ID: ${lastId}) 获取初始数据。`, "初始加载");
                        parseAndRenderAIMessages(initialMessageContent);
                    } else {
                        safeToastr('warning', `从父消息 (ID: ${lastId}) 获取的内容为空或无效。聊天框将为空。`, "初始加载");
                        $('.phone-screen:visible .chat-interface .chat-messages').empty();
                    }
                } else {
                    safeToastr('warning', `getLastMessageId 返回无效 (${lastId})。无法加载初始聊天记录。`, "初始加载");
                     $('.phone-screen:visible .chat-interface .chat-messages').empty();
                }
            } catch (e) {
                safeToastr('error', `用 getLastMessageId 和 /messages 获取初始数据失败: ${(e as Error).message}`, "初始加载");
                 $('.phone-screen:visible .chat-interface .chat-messages').empty();
            }
        } else {
            safeToastr('warning', "getLastMessageId API 或 triggerSlash 不可用，无法加载初始聊天记录。", "初始加载");
            if (typeof getLastMessageId !== 'function') safeToastr('warning', "getLastMessageId API 未定义", "API警告");
            if (typeof triggerSlash !== 'function') safeToastr('warning', "triggerSlash API 未定义", "API警告");
            $('.phone-screen:visible .chat-interface .chat-messages').empty();
        }
        
        const chatInputDiv = document.querySelector('.chat-input');
        if (!chatInputDiv) {
            safeToastr('error', "错误：未能找到 .chat-input 容器！", "DOM查找失败"); return;
        }
        safeToastr('success', ".chat-input 容器已找到！", "DOM查找");
        
        const messageInputField = chatInputDiv.querySelector('input') as HTMLInputElement | null;
        const sendButton = chatInputDiv.querySelector('button') as HTMLButtonElement | null;

        if (!messageInputField) safeToastr('error', "错误: 未找到消息输入框", "DOM查找失败");
        if (!sendButton) safeToastr('error', "错误: 未找到发送按钮", "DOM查找失败");

        if (sendButton && messageInputField) {
            sendButton.onclick = (event: MouseEvent) => { 
                safeToastr('info', "发送按钮被点击！", "事件"); handleSendMessage(event); 
            };
            messageInputField.onkeypress = (event: KeyboardEvent) => {
                if (event.key === 'Enter' || event.keyCode === 13) { 
                    safeToastr('info', "回车键被按下！", "事件"); handleSendMessage(event); 
                }
            };
            safeToastr('success', "发送功能已准备就绪！", "初始化");
        } else {
            safeToastr('error', "输入或发送按钮未找到，事件绑定失败。", "初始化失败");
        }
    }, initialDelay); 
}

try {
    const initFn = () => { initializeChatInterface(); };
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        console.log("DOM ready, calling initFn."); initFn();
    } else {
        console.log("DOM not ready, adding DOMContentLoaded listener.");
        window.addEventListener('DOMContentLoaded', initFn);
    }
} catch (e) {
    safeToastr('error', `顶层脚本错误: ${(e as Error).message}`, "脚本错误");
    console.error("Top-level script execution error", e);
}
