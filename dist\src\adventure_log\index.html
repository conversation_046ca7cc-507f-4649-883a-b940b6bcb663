<!doctype html><html lang="zh-CN"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1"><title>冒险日志</title><style>body{margin:0;font-family:"Segoe UI",Tahoma,Geneva,Verdana,sans-serif;background-color:#2c3e50;color:#ecf0f1;display:flex;justify-content:center;align-items:center;min-height:100vh;box-sizing:border-box}#adventure-log-container{width:95vw;max-width:450px;aspect-ratio:9/17;background-color:#34495e;border:2px solid #7f8c8d;border-radius:10px;box-shadow:0 5px 15px rgba(0,0,0,.3);display:flex;flex-direction:column;overflow:hidden;padding:15px;box-sizing:border-box;margin:auto}#player-status-area{padding-bottom:10px;border-bottom:1px solid #7f8c8d;margin-bottom:10px;display:flex;flex-direction:column;gap:8px;font-size:.85em}#player-status-area .status-row{display:flex;flex-wrap:wrap;gap:10px;align-items:center;justify-content:space-around}#player-status-area #health,#player-status-area #ac-display,#player-status-area #time,#player-status-area #location{padding:4px 7px;background-color:#2c3e50;border-radius:3px;white-space:nowrap}#toggle-char-sheet-button{padding:8px 12px;background-color:#4a5568;color:#ecf0f1;border:1px solid #7f8c8d;border-radius:4px;cursor:pointer;font-size:.9em;margin-top:10px;margin-bottom:10px;text-align:center;width:100%}#toggle-char-sheet-button:hover{background-color:#2d3748}#detailed-character-sheet{background-color:rgba(44,62,80,.8);padding:10px;border-radius:6px;margin-top:5px;border:1px solid #7f8c8d;max-height:35vh;overflow-y:auto}#detailed-character-sheet h4{margin-top:12px;margin-bottom:8px;color:#95a5a6;font-size:1em;border-bottom:1px solid #7f8c8d;padding-bottom:4px}#detailed-character-sheet h4:first-child{margin-top:0}#detailed-character-sheet .status-row{display:flex;flex-wrap:wrap;gap:8px 12px;align-items:center;margin-bottom:8px}#detailed-character-sheet .status-row div,#detailed-character-sheet .status-row span{padding:3px 6px;background-color:#2c3e50;border-radius:3px;white-space:nowrap;font-size:.9em}#detailed-character-sheet ul{list-style-type:none;padding-left:5px;margin:5px 0 10px 0;font-size:.85em}#detailed-character-sheet ul li{padding:3px 0}#detailed-character-sheet #spell-slots-display{font-size:.85em;padding-left:5px;margin-bottom:10px}#main-narrative-area{flex-grow:1;padding:10px;background-color:rgba(0,0,0,.2);border-radius:6px;overflow-y:auto;line-height:1.6;font-size:1em;margin-bottom:15px;min-height:100px}#main-narrative-area p{margin-top:0;margin-bottom:.8em}#main-narrative-area p:last-child{margin-bottom:0}#action-choices-area{display:flex;flex-direction:column;gap:8px}#action-choices-area button{padding:12px 15px;background-color:#3498db;color:#fff;border:none;border-radius:5px;cursor:pointer;font-size:.95em;text-align:left;transition:background-color .2s ease-in-out}#action-choices-area button:hover{background-color:#2980b9}#action-choices-area button:active{background-color:#1f638f}#start-screen-container{display:flex;flex-direction:column;justify-content:center;align-items:center;text-align:center;padding:20px;box-sizing:border-box;width:100%;height:100%;overflow-y:auto}#start-screen-container h1{font-size:1.8em;color:#ecf0f1;margin-bottom:15px}#start-screen-container p{font-size:1em;color:#bdc3c7;margin-bottom:20px;max-width:90%}#start-screen-container #start-new-game-button{padding:12px 25px;font-size:1.1em;background-color:#27ae60;color:#fff;border:none;border-radius:8px;cursor:pointer;transition:background-color .3s}#start-screen-container #start-new-game-button:hover{background-color:#229954}#start-screen-container .start-screen-note{font-size:.75em;color:#95a5a6;margin-top:15px;max-width:85%;line-height:1.4}@media(max-width: 600px){#adventure-log-container{width:100vw;height:100vh;max-width:100vw;border-radius:0;border:none;padding:10px;aspect-ratio:unset;overflow-y:auto}#player-status-area{font-size:.8em;gap:5px}#player-status-area .status-row{gap:5px;justify-content:space-around}#detailed-character-sheet .status-row{flex-direction:column;align-items:flex-start}#detailed-character-sheet .status-row div,#detailed-character-sheet .status-row span{width:100%;margin-bottom:3px;text-align:left}#main-narrative-area{font-size:.95em;padding:8px}#action-choices-area button{padding:10px 12px;font-size:.9em}#toggle-char-sheet-button{font-size:.85em}#detailed-character-sheet{font-size:.8em;max-height:35vh}#detailed-character-sheet ul,#detailed-character-sheet #spell-slots-display{font-size:1em}}
</style></head><body><div id="start-screen-container"><h1>冒险日志</h1><p>准备好开始一段新的旅程了吗？</p><button id="start-new-game-button">开始新冒险 (从世界书加载角色)</button><p class="start-screen-note">注意：开始新冒险将尝试从名为 "RPG_Modules_Test.json" 的世界书中的 "PLAYER" 条目加载角色数据。如果已有进行中的游戏，它可能会被新游戏覆盖（取决于当前消息内容）。</p></div><div id="adventure-log-container" style="display:none"><div id="player-status-area"><div class="status-row"><div id="health">生命值: ...</div><div id="ac-display">AC: ...</div><div id="time">时间: ...</div><div id="location">地点: ...</div></div><button id="toggle-char-sheet-button">显示/隐藏详细角色卡</button><div id="detailed-character-sheet" style="display:none"><h4>角色信息</h4><div class="status-row"><div id="char-name-display">角色名: ...</div><div id="char-race-class-display">种族/职业: .../...</div><div id="char-level-display">等级: ...</div></div><h4>属性</h4><div class="status-row attributes-display"><div id="attr-str-display">力量: ..(..)</div><div id="attr-dex-display">敏捷: ..(..)</div><div id="attr-con-display">体质: ..(..)</div><div id="attr-int-display">智力: ..(..)</div><div id="attr-wis-display">感知: ..(..)</div><div id="attr-cha-display">魅力: ..(..)</div></div><h4>状态</h4><div class="status-row simple-list-display"><span>金币: <span id="currency-gold-display">...</span></span> <span>经验: <span id="exp-display">...</span></span> <span>力竭: <span id="exhaustion-display">...</span></span></div><h4>熟练项</h4><ul id="proficiencies-display"><li>...</li></ul><h4>技能 (熟练)</h4><ul id="skills-display"><li>...</li></ul><h4>法术槽</h4><div id="spell-slots-display">...</div><h4>已准备法术</h4><ul id="equipped-spells-display"><li>...</li></ul><h4>装备</h4><ul id="equipment-display"><li>...</li></ul><h4>物品栏</h4><ul id="inventory-display"><li>...</li></ul><h4>当前任务</h4><ul id="active-quests-display"><li>...</li></ul></div></div><div id="main-narrative-area"><p>冒险即将开始...</p></div><div id="action-choices-area"></div></div><script>let e={name:"新冒险者",race:"人类",class:"平民",level:1,exp:0,hp:{current:10,max:10},ac:10,currency:{gold:10,silver:0,copper:0},attributes:{strength:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},dexterity:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},constitution:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},intelligence:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},wisdom:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},charisma:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0}},proficiencies:["匕首"],skills:[],spellSlots:{},equippedSpells:[],equipment:[{name:"平民服装",type:"衣物",equipped:!0},{name:"匕首",type:"武器",equipped:!0}],inventory:[{name:"背包",quantity:1},{name:"火绒盒",quantity:1},{name:"口粮",quantity:2,description:"一天的食物"}],activeQuests:[],exhaustion:0,time:"第一天 清晨",currentLocation:"旅途的起点"},t=[],n=null,i=null,a=null,s=null,o=null,r=null,l=null,c=null,d=null,u=null,p=null,m=null,y=null,f=null,g=null,b=null,h=null,$=null,x=null,M=null,v=null,w=null,_=null,I=null,C=null,E=null,L=null,S=null,k=null,B=null,T=null,q=null,A=null;function H(e,t,n){try{if("object"!=typeof window.toastr&&"undefined"!=typeof parent&&"object"==typeof parent.toastr&&(window.toastr=parent.toastr),"object"==typeof toastr&&null!==toastr&&"function"==typeof toastr[e])toastr[e](t,n);else{("error"===e?console.error:"warning"===e?console.warn:console.log)(`[AdvLog Toastr Fallback - ${e}] ${n?n+": ":""}${t}`)}}catch(e){console.error(`[AdvLog] safeToastr Error: ${e.message}`)}}function N(){if(!e)return;m&&(m.textContent=`角色名: ${e.name||"N/A"}`),y&&(y.textContent=`种族/职业: ${e.race||"N/A"} / ${e.class||"N/A"}`),f&&(f.textContent=`等级: ${e.level||0}`),d&&(d.textContent=`生命值: ${e.hp?.current||"?"}/${e.hp?.max||"?"}`),g&&(g.textContent=`AC: ${e.ac||0}`),p&&(p.textContent=`时间: ${e.time||"N/A"}`),u&&(u.textContent=`地点: ${e.currentLocation||"N/A"}`);const t=e=>e?`${e.final}(${e.mod>=0?"+":""}${e.mod})`:"N/A";if(b&&(b.textContent=`力量: ${t(e.attributes?.strength)}`),h&&(h.textContent=`敏捷: ${t(e.attributes?.dexterity)}`),$&&($.textContent=`体质: ${t(e.attributes?.constitution)}`),x&&(x.textContent=`智力: ${t(e.attributes?.intelligence)}`),M&&(M.textContent=`感知: ${t(e.attributes?.wisdom)}`),v&&(v.textContent=`魅力: ${t(e.attributes?.charisma)}`),w&&(w.textContent=`${e.currency?.gold||0}`),_&&(_.textContent=`${e.exp||0}`),I&&(I.textContent=`${e.exhaustion||0}`),L&&(L.innerHTML=e.proficiencies?.map((e=>`<li>${e}</li>`)).join("")||"<li>无</li>"),S&&(S.innerHTML=e.skills?.filter((e=>e.proficient)).map((e=>`<li>${e.name} (${e.value>=0?"+":""}${e.value})</li>`)).join("")||"<li>无熟练技能</li>"),k){let t="";if(e.spellSlots&&Object.keys(e.spellSlots).length>0)for(const n in e.spellSlots)if(e.spellSlots.hasOwnProperty(n)){const i=e.spellSlots[n];t+=`<div>${n}环: ${i.current}/${i.max}</div>`}k.innerHTML=t||"无"}B&&(B.innerHTML=e.equippedSpells?.map((e=>`<li>${e.name} (${e.level}环)</li>`)).join("")||"<li>无</li>"),T&&(T.innerHTML=e.equipment?.map((e=>`<li>${e.name} (${e.type})${e.equipped?" [已装备]":""}</li>`)).join("")||"<li>无</li>"),q&&(q.innerHTML=e.inventory?.map((e=>`<li>${e.name} x${e.quantity} ${e.description?"("+e.description+")":""}</li>`)).join("")||"<li>空</li>"),A&&(A.innerHTML=e.activeQuests?.map((e=>`<li>${e}</li>`)).join("")||"<li>无</li>")}function U(i){c&&(c.innerHTML="",i&&i.length>0?i.forEach((i=>{const a=document.createElement("button");a.id=i.id,a.textContent=i.text,a.dataset.action=i.actionCommand,a.addEventListener("click",(()=>async function(i){c&&c.querySelectorAll("button").forEach((e=>e.disabled=!0));l&&(l.innerHTML+="<p><i>正在等待AI响应...</i></p>");let a="";n?.rawInputBlock?a+=`之前的场景核心内容是:\n${n.rawInputBlock}\n`:n&&(a+=`之前的场景是关于 "${n.title||n.sceneType}"。\n`,a+=`描述为: "${n.description}"\n`,n.systemMessage&&(a+=`系统信息: "${n.systemMessage}"\n`));const s=2;if(t.length>0){a+="\n最近的互动历史摘要:\n";for(let e=Math.max(0,t.length-s);e<t.length;e++){const n=t[e],i=O(n.rawSceneBlock);a+=i?.description?`- 场景: ${i.title||"..."} "${i.description.substring(0,70)}..."\n`:`- 场景: (摘要不可用) "${n.rawSceneBlock.substring(0,70)}..."\n`,n.playerChoiceText&&(a+=`  玩家选择: "${n.playerChoiceText}"\n`)}}let o="你是一名D&D 5e的地下城主(DM)，正在主持一个文字冒险游戏“冒险日志”。\n";o+=`当前玩家状态：\n角色名: ${e.name}\n种族: ${e.race}\n职业: ${e.class} ${e.level}级\n`,o+=`地点: ${e.currentLocation}\n生命值: ${e.hp.current}/${e.hp.max}\n时间: ${e.time}\n`,e.inventory&&e.inventory.length>0&&(o+=`物品: ${e.inventory.map((e=>`${e.name} x${e.quantity}`)).join(", ")}\n`);e.exhaustion&&(o+=`力竭等级: ${e.exhaustion}\n`);o+=`\n${a}`;let r="";const d=/\[(?:DC(\d+)\s*)?([^\(\]\s]+)(?:\s*\(([^\)]+)\))?\s*(检定)?\]/i,u=i.text.match(d);if(u){const t=u[1],n=u[2].trim(),a=u[3]?u[3].trim():void 0,s=void 0!==u[4];if(n.toLowerCase().includes("先攻")){const t=j(0,"敏捷",void 0,e);let n=`投骰1d20[${t.roll}]`;0!==t.attributeMod&&(n+=`${t.attributeMod>=0?"+":""}${t.attributeMod}(敏捷)`),n+=`=${t.total}`,r=` [先攻检定 ${n}]`;const i=`先攻检定: ${t.total} (${n})`;H("info",i,"先攻结果"),l&&(l.innerHTML+=`<p class="check-result"><em>${i}</em></p>`)}else if(t){const i=j(parseInt(t,10),n,a,e);let s=`投骰1d20[${i.roll}]`;0!==i.attributeMod&&(s+=`${i.attributeMod>=0?"+":""}${i.attributeMod}(${i.attributeName})`),0!==i.proficiencyBonusApplied&&(s+=`${i.proficiencyBonusApplied>=0?"+":""}${i.proficiencyBonusApplied}(熟练)`),s+=`=${i.total}`,r=` [${i.success?"检定成功":"检定失败"} DC${i.dc} ${i.attributeName}${i.skillName?"("+i.skillName+")":""} ${s}]`;const o=`${i.attributeName}${i.skillName?"("+i.skillName+")":""} 检定: ${i.total} vs DC ${i.dc} -> ${i.success?"成功！":"失败。"} (${s})`;H(i.success?"success":"error",o,"检定结果"),l&&(l.innerHTML+=`<p class="check-result"><em>${o}</em></p>`)}else s&&H("warning",`选项 "${i.text}" 包含通用检定但未指定DC，无法本地处理。请AI提供DC。`,"检定信息不完整")}if(o+=`\n玩家刚刚选择了行动： "${i.text}"${r}\n`,o+=`\n请根据玩家的选择${r?"和检定结果":""}，结合D&D 5e的规则和风格，继续发展剧情，并生成下一个场景的内容。`,o+="\n你需要提供生动的场景描述、合理的事件发展，以及2-3个供玩家选择的新的行动选项。",o+="\n如果发生战斗，请使用 <战斗:...> 标签，并描述战斗过程和结果，更新相关生命值。",o+="\n\n【非常重要：输出格式指令】\n",o+="你的完整回复必须严格遵循以下格式，以便游戏界面能够正确解析和显示：\n",o+='1. 整个回复必须由 "查看系统\\nmsg_start" 开始，并以 "msg_end\\n关闭系统" 结束。\n',o+='2. 在 "msg_start" 和 "msg_end" 之间，是核心的游戏数据块。\n',o+="3. 核心数据块必须以一个主要标签开始，例如 <场景:场景标题> 或 <NPC:NPC名称> 或 <系统:消息类型>。\n",o+="4. 在主要标签之后，是多行结构化数据。每一行结构化数据都必须采用 '键--\"值\"--HH:MM' 的格式。例如：'场景描述--\"你看到一个古老的祭坛。\"HH:MM'。\n",o+='5. 确保所有的引号都正确配对，并且 "--HH:MM" 标记在值的引号之外，作为行尾。\n',o+="6. 常见的键包括：场景描述, 当前地点, 生命值, 时间, 提示信息, 内容, 敌人信息, 对方的话, 行动选项A, 行动选项B, 回复选项A 等。\n",o+="7. 必须提供至少两个行动选项（例如 行动选项A, 行动选项B）。\n",o+="请务必严格遵守！\n","function"!=typeof triggerSlash){c&&c.querySelectorAll("button").forEach((e=>e.disabled=!1));const e=l?.querySelector("p > i");return void(e&&e.parentElement&&l?.removeChild(e.parentElement))}try{const e=await triggerSlash(`/gen ${o}`),n=l?.querySelector("p > i");if(n&&n.parentElement&&l&&l.removeChild(n.parentElement),e&&""!==e.trim()){if(!1)return l&&(l.innerHTML=`<p style="color: lightcoral; font-family: monospace; white-space: pre-wrap; border: 1px solid orange; padding: 10px; background-color: #333;"><strong>[调试] AI 原始回复 (长度: ${e.length}):</strong>\n${e.replace(/</g,"<").replace(/>/g,">")}</p>`),void(c&&c.querySelectorAll("button").forEach((e=>{e.disabled=!1})));let n=null;const a=e.match(/查看系统\s*msg_start([\s\S]*?)msg_end\s*关闭系统/);if(a&&a[1])n=a[1].trim();else{const t=e.match(/msg_start([\s\S]*?)msg_end/);t&&t[1]&&(n=t[1].trim())}if(!n)return console.error("Raw AI Response that failed extraction:",e),l&&(l.innerHTML+=`<p style='color:red;'>AI响应内容提取失败。原始回复已打印到控制台。</p><div style="font-family:monospace; white-space:pre-wrap; border:1px solid red; padding:5px; max-height:200px; overflow-y:auto;">${e.replace(/</g,"<").replace(/>/g,">")}</div>`),void(c&&c.querySelectorAll("button").forEach((e=>{e.disabled=!1})));const s=O(n);if(s&&s.rawInputBlock){R(s);const e={rawSceneBlock:s.rawInputBlock,playerChoiceText:i.text,timestamp:Date.now()};t.push(e),await z()}else l&&(l.innerHTML+=`<p style='color:red;'>AI响应解析失败。提取到的内容: </p><div style="font-family:monospace; white-space:pre-wrap; border:1px solid red; padding:5px; max-height:200px; overflow-y:auto;">${n.replace(/</g,"<").replace(/>/g,">")}</div>`)}else l&&(l.innerHTML+="<p style='color:orange;'>AI未返回有效数据。请尝试其他选项或检查连接。</p>")}catch(e){const t=l?.querySelector("p > i");t&&t.parentElement&&l&&l.removeChild(t.parentElement),l&&(l.innerHTML+=`<p style='color:red;'>与AI交互时发生错误: ${e.message}</p>`)}finally{c&&c.querySelectorAll("button").forEach((e=>{e.disabled=!1}))}}(i))),c.appendChild(a)})):c.innerHTML="<p>暂无行动选项。</p>")}function D(e){return e>=17?6:e>=13?5:e>=9?4:e>=5?3:2}function j(e,t,n,i){const a=Math.floor(20*Math.random())+1;let s=0,o=0;const r=t.toLowerCase();if(i.attributes[r]&&(s=i.attributes[r].mod),n){const e=i.skills.find((e=>e.name.toLowerCase()===n.toLowerCase()));if(e?.proficient)o=D(i.level);else{i.proficiencies.find((e=>e.toLowerCase().includes(n.toLowerCase())&&e.toLowerCase().includes(t.toLowerCase())))&&(o=D(i.level))}}const l=a+s+o;return{success:l>=e,roll:a,attributeMod:s,proficiencyBonusApplied:o,total:l,dc:e,attributeName:t,skillName:n}}function O(e){if(!e||"string"!=typeof e)return null;const t=e.split("\n"),n=[];let i=0;for(;i<t.length;){let e=t[i],a=e.trim();const s=/^(.+?)--"(.*)$/;if(a.match(s)&&!a.endsWith('"--HH:MM')){let a=e,s=i+1,o=!1;for(;s<t.length;){if(a+="\n"+t[s],t[s].trim().endsWith('"--HH:MM')){n.push(a),i=s,o=!0;break}s++}o||n.push(a)}else n.push(e);i++}const a=n.map((e=>e.trim())).filter((e=>e));let s,o,r,l,c=-1,d=null;for(let e=0;e<a.length;e++){const t=a[e];if(!d){if(s=t.match(/^<场景:([^>]+)>/i),s&&s[1]){c=e,d=s[0];break}if(o=t.match(/^<NPC:([^>]+)>/i),o&&o[1]){c=e,d=o[0];break}if(r=t.match(/^<系统:([^>]+)>/i),r&&r[1]){c=e,d=r[0];break}if(l=t.match(/^<战斗:([^>]+)>/i),l&&l[1]){c=e,d=l[0];break}}}if(-1===c||!d)return null;const u={playerChoices:[]};if(s&&s[1])u.sceneType="location",u.title=s[1];else if(o&&o[1])u.sceneType="dialogue",u.npcName=o[1],u.title=`与 ${o[1]} 对话`;else if(l&&l[1])u.sceneType="combat",u.title=l[1];else{if(!r||!r[1])return null;{const e=r[1].toLowerCase();!e.includes("战斗")&&!e.includes("combat")||l?e.includes("谜题")||e.includes("puzzle")?u.sceneType="puzzle":u.sceneType="system_message":u.sceneType="combat",u.title=r[1]}}let p="";s?p=`</场景:${s[1]}>`:o?p=`</NPC:${o[1]}>`:l?p=`</战斗:${l[1]}>`:r&&(p=`</系统:${r[1]}>`);let m,y=-1;for(let e=c+1;e<a.length;e++)if(a[e]===p){y=e;break}if(-1===y?(y=a.length-1,u.rawInputBlock=t.slice(c).join("\n")):u.rawInputBlock=t.slice(c,y+1).join("\n"),m=a[y]===p?a.slice(c+1,y):a.slice(c+1),u.variableUpdates=[],m.forEach((e=>{const t=e.match(/^变量更新--"([^:]+):([^:]+):([^:]+):(.+)"--HH:MM$/i);if(t){const[e,n,i,a,s]=t;let o=s.trim();if("物品获得"!==a.trim()&&"物品失去"!==a.trim()||!o){if("增加"===a.trim()||"减少"===a.trim()||"设置"===a.trim()){const e=parseFloat(s);o=isNaN(e)||e.toString()!==s.trim()?s.trim():e}}else try{o=JSON.parse(s)}catch(e){return}u.variableUpdates?.push({target:n.trim(),path:i.trim(),operation:a.trim(),value:o})}else{const t=e.match(/^(.+?)--"([\s\S]*?)"--HH:MM\s*$/i);if(t){const e=t[1].trim();let n=t[2];if("场景描述"===e||"对方的话"===e||"提示信息"===e){const t=["行动选项A--","行动选项B--","行动选项C--","行动选项D--","行动选项E--","回复选项A--","回复选项B--","回复选项C--","回复选项D--","回复选项E--","当前地点--","生命值--","时间--","提示信息--","内容--","敌人信息--","对方的话--","变量更新--"];let i=-1;for(const a of t){if("提示信息"===e&&"提示信息--"===a)continue;if("对方的话"===e&&"对方的话--"===a)continue;const t=n.indexOf(a);-1!==t&&(-1===i||t<i)&&(i=t)}-1!==i&&(n=n.substring(0,i).trim())}switch(e){case"场景描述":u.description=n;break;case"当前地点":u.locationUpdate=n;break;case"生命值":u.healthUpdate=n;break;case"金币":u.goldUpdate=parseInt(n,10),isNaN(u.goldUpdate)&&delete u.goldUpdate;break;case"时间":u.timeUpdate=n;break;case"力竭等级":u.exhaustionUpdate=parseInt(n,10),isNaN(u.exhaustionUpdate)&&delete u.exhaustionUpdate;break;case"提示信息":case"内容":case"伤害提示":u.systemMessage=(u.systemMessage?u.systemMessage+"\n":"")+n;break;case"敌人信息":u.enemyInfo=n;break;case"对方的话":"dialogue"!==u.sceneType||u.description?u.systemMessage=(u.systemMessage?u.systemMessage+"\n":"")+`${u.npcName||"对方"}说: ${n}`:u.description=n;break;case"玩家状态":u.systemMessage=(u.systemMessage?u.systemMessage+"\n":"")+`状态更新: ${n}`;break;default:const t=e.match(/^(行动选项|回复选项)([A-Z])$/i);t&&(u.playerChoices||(u.playerChoices=[]),u.playerChoices.push({id:`choice_${t[2].toUpperCase()}_${Date.now()}`,text:n,actionCommand:`${t[1].toLowerCase()}_${t[2].toUpperCase()}`}))}}else e&&e!==p&&e.startsWith("变量更新--")}})),!u.sceneType)return null;if(!u.rawInputBlock||""===u.rawInputBlock.trim())return null;let f=!1;return u.description&&""!==u.description.trim()&&(f=!0),u.playerChoices&&u.playerChoices.length>0&&(f=!0),"combat"===u.sceneType&&u.enemyInfo&&""!==u.enemyInfo.trim()&&(f=!0),u.systemMessage&&""!==u.systemMessage.trim()&&(f=!0),!f&&u.sceneType,u}function R(t){if(!t)return l&&(l.innerHTML="<p>错误：无法加载场景数据。请检查AI响应或联系开发者。</p>"),void(c&&(c.innerHTML=""));if(n=t,t.healthUpdate){const n=t.healthUpdate.split("/");2===n.length&&(e.hp.current=parseInt(n[0],10)||e.hp.current,e.hp.max=parseInt(n[1],10)||e.hp.max)}void 0===t.goldUpdate||t.variableUpdates?.find((e=>"currency.gold"===e.path))||(e.currency.gold=t.goldUpdate),t.locationUpdate&&!t.variableUpdates?.find((e=>"currentLocation"===e.path))&&(e.currentLocation=t.locationUpdate),t.timeUpdate&&!t.variableUpdates?.find((e=>"time"===e.path))&&(e.time=t.timeUpdate),void 0===t.exhaustionUpdate||t.variableUpdates?.find((e=>"exhaustion"===e.path))||(e.exhaustion=t.exhaustionUpdate),t.variableUpdates&&t.variableUpdates.length>0&&t.variableUpdates.forEach((t=>{!function(e,t,n,i){const a=t.split(".");let s=e;for(let e=0;e<a.length-1;e++){if(void 0===s[a[e]]||"object"!=typeof s[a[e]])return;s=s[a[e]]}const o=a[a.length-1];switch(n){case"设置":"object"==typeof s&&null!==s&&(s[o]=i);break;case"增加":"object"==typeof s&&null!==s&&"number"==typeof s[o]&&"number"==typeof i&&(s[o]+=i);break;case"减少":"object"==typeof s&&null!==s&&"number"==typeof s[o]&&"number"==typeof i&&(s[o]-=i);break;case"添加元素":"object"==typeof s&&null!==s&&Array.isArray(s[o])&&s[o].push(i);break;case"移除元素":if("object"==typeof s&&null!==s&&Array.isArray(s[o])){const e=s[o].indexOf(i);e>-1&&s[o].splice(e,1)}break;case"物品获得":if("inventory"===t&&Array.isArray(s.inventory)&&"object"==typeof i&&i.name&&"number"==typeof i.quantity){const e=s.inventory.find((e=>e.name===i.name));e?(e.quantity+=i.quantity,i.description&&!e.description&&(e.description=i.description)):s.inventory.push({name:i.name,quantity:i.quantity,description:i.description||""})}break;case"物品失去":if("inventory"===t&&Array.isArray(s.inventory)&&"object"==typeof i&&i.name&&"number"==typeof i.quantity){const e=s.inventory.findIndex((e=>e.name===i.name));e>-1&&(s.inventory[e].quantity-=i.quantity,s.inventory[e].quantity<=0&&s.inventory.splice(e,1))}}}(e,t.path,t.operation,t.value)})),function(e){if(l){const t="string"==typeof e.description?e.description:"";l.innerHTML=`<p>${t.replace(/\n/g,"<br>")}</p>`,e.systemMessage&&(l.innerHTML+=`<p class="system-message"><em>${e.systemMessage.replace(/\n/g,"<br>")}</em></p>`),e.enemyInfo&&(l.innerHTML+=`<p class="enemy-info"><strong>遭遇敌人:</strong> ${e.enemyInfo}</p>`)}}(t),N(),U(t.playerChoices)}const F="\x3c!-- ADVENTURE_LOG_HISTORY_START --\x3e",P="\x3c!-- ADVENTURE_LOG_HISTORY_END --\x3e",Y="\n\x3c!-- ENTRY_SEP --\x3e\n",J="\x3c!-- PLAYER_STATE_START --\x3e",Q="\x3c!-- PLAYER_STATE_END --\x3e";async function z(){if(null===i||"function"!=typeof setChatMessages)return;let a="";a+=`${J}\n`,a+=`${JSON.stringify(e,null,2)}\n`,a+=`${Q}\n\n`;let s,o=[...t];n&&n.rawInputBlock?s=n.rawInputBlock:t.length>0&&(s=o.pop()?.rawSceneBlock),o.length>0&&(a+=`${F}\n`,a+=o.map((e=>{let t=e.rawSceneBlock;return e.playerChoiceText&&(t+=`\n[玩家选择文本]: "${e.playerChoiceText}"`),t})).join(Y),a+=`\n${P}\n\n`),s&&(a+=`查看系统\nmsg_start\n${s}\nmsg_end\n关闭系统`);try{await setChatMessages([{message_id:i,message:a}],{refresh:"affected"})}catch(e){}}async function G(){if(!s||!o||!a)return;s.disabled=!0,s.textContent="正在加载角色...";const r=await async function(e){if("function"!=typeof triggerSlash)return null;const t="RPG_Modules_Test.json";try{const n=`/findentry file="${t}" "${e}"`,i=await triggerSlash(n);if(!i||""===i.trim()||"[]"===i.trim())return null;let a=null;try{const e=JSON.parse(i);Array.isArray(e)&&e.length>0?a=e[0].toString():"string"==typeof e&&""!==e.trim()?a=e.trim():"number"==typeof e&&(a=e.toString())}catch(e){"string"==typeof i&&""!==i.trim()&&(a=i.trim())}if(!a)return null;const s=`/getentryfield file="${t}" field=content "${a}"`,o=await triggerSlash(s);return o&&""!==o.trim()?o:null}catch(e){return null}}("PLAYER");if(r)try{const l=JSON.parse(r);if(l&&"string"==typeof l.name&&l.attributes){e=l,n=null,t=[];const r=O(function(e){const t="冒险的序章";return`<场景:${t}>\n场景描述--"你已准备好踏上征程！这个世界的故事将根据你所激活的冒险模组（通常位于 RPG_Modules_Test.json 世界书中）展开。请选择你的第一个行动，让传奇开始！"--HH:MM\n当前地点--"${e.currentLocation||"未知起点"}"--HH:MM\n生命值--"${e.hp.current}/${e.hp.max}"--HH:MM\n时间--"${e.time||"某个时刻"}"--HH:MM\n行动选项A--"根据我的模组设定，正式开始冒险！"--HH:MM\n行动选项B--"我应该先了解一下我所处的环境（基于模组）。"--HH:MM\n行动选项C--"查看我的角色状态。"--HH:MM\n</场景:${t}>`}(e));r&&r.rawInputBlock?(R(r),t.push({rawSceneBlock:r.rawInputBlock,timestamp:Date.now()}),N(),a.style.display="none",o.style.display="flex",null!==i&&await z()):(s.disabled=!1,s.textContent="开始新冒险 (从世界书加载角色)")}else e={name:"新冒险者",race:"人类",class:"平民",level:1,exp:0,hp:{current:10,max:10},ac:10,currency:{gold:10,silver:0,copper:0},attributes:{strength:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},dexterity:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},constitution:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},intelligence:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},wisdom:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},charisma:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0}},proficiencies:["匕首"],skills:[],spellSlots:{},equippedSpells:[],equipment:[{name:"平民服装",type:"衣物",equipped:!0},{name:"匕首",type:"武器",equipped:!0}],inventory:[{name:"背包",quantity:1},{name:"火绒盒",quantity:1},{name:"口粮",quantity:2,description:"一天的食物"}],activeQuests:[],exhaustion:0,time:"第一天 清晨",currentLocation:"旅途的起点"},s.disabled=!1,s.textContent="开始新冒险 (从世界书加载角色)"}catch(t){e={name:"新冒险者",race:"人类",class:"平民",level:1,exp:0,hp:{current:10,max:10},ac:10,currency:{gold:10,silver:0,copper:0},attributes:{strength:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},dexterity:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},constitution:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},intelligence:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},wisdom:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},charisma:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0}},proficiencies:["匕首"],skills:[],spellSlots:{},equippedSpells:[],equipment:[{name:"平民服装",type:"衣物",equipped:!0},{name:"匕首",type:"武器",equipped:!0}],inventory:[{name:"背包",quantity:1},{name:"火绒盒",quantity:1},{name:"口粮",quantity:2,description:"一天的食物"}],activeQuests:[],exhaustion:0,time:"第一天 清晨",currentLocation:"旅途的起点"},s.disabled=!1,s.textContent="开始新冒险 (从世界书加载角色)"}else e={name:"新冒险者",race:"人类",class:"平民",level:1,exp:0,hp:{current:10,max:10},ac:10,currency:{gold:10,silver:0,copper:0},attributes:{strength:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},dexterity:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},constitution:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},intelligence:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},wisdom:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},charisma:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0}},proficiencies:["匕首"],skills:[],spellSlots:{},equippedSpells:[],equipment:[{name:"平民服装",type:"衣物",equipped:!0},{name:"匕首",type:"武器",equipped:!0}],inventory:[{name:"背包",quantity:1},{name:"火绒盒",quantity:1},{name:"口粮",quantity:2,description:"一天的食物"}],activeQuests:[],exhaustion:0,time:"第一天 清晨",currentLocation:"旅途的起点"},s.disabled=!1,s.textContent="开始新冒险 (从世界书加载角色)"}async function W(){setTimeout((async()=>{if(function(){try{"undefined"!=typeof window&&"undefined"!=typeof parent&&parent!==window&&["$","toastr","triggerSlash","getLastMessageId","setChatMessages"].forEach((e=>{void 0===window[e]&&void 0!==parent[e]&&(window[e]=parent[e])}))}catch(e){}}(),a=document.getElementById("start-screen-container"),s=document.getElementById("start-new-game-button"),o=document.getElementById("adventure-log-container"),r=document.getElementById("player-status-area"),l=document.getElementById("main-narrative-area"),c=document.getElementById("action-choices-area"),d=document.getElementById("health"),u=document.getElementById("location"),p=document.getElementById("time"),m=document.getElementById("char-name-display"),y=document.getElementById("char-race-class-display"),f=document.getElementById("char-level-display"),g=document.getElementById("ac-display"),b=document.getElementById("attr-str-display"),h=document.getElementById("attr-dex-display"),$=document.getElementById("attr-con-display"),x=document.getElementById("attr-int-display"),M=document.getElementById("attr-wis-display"),v=document.getElementById("attr-cha-display"),w=document.getElementById("currency-gold-display"),_=document.getElementById("exp-display"),I=document.getElementById("exhaustion-display"),C=document.getElementById("toggle-char-sheet-button"),E=document.getElementById("detailed-character-sheet"),L=document.getElementById("proficiencies-display"),S=document.getElementById("skills-display"),k=document.getElementById("spell-slots-display"),B=document.getElementById("equipped-spells-display"),T=document.getElementById("equipment-display"),q=document.getElementById("inventory-display"),A=document.getElementById("active-quests-display"),!(a&&s&&o&&r&&l&&c&&C&&E))return;C.addEventListener("click",(()=>{if(E&&C){const e="none"===E.style.display;E.style.display=e?"block":"none",C.textContent=e?"隐藏详细角色卡":"显示详细角色卡"}})),s.addEventListener("click",G);const H=await async function(){if("function"!=typeof getLastMessageId||"function"!=typeof triggerSlash)return{playerStateLoadedFromMsg:!1,sceneDataLoadedFromMsg:!1};const a=getLastMessageId();if(i=void 0===a?null:a,null===i)return{playerStateLoadedFromMsg:!1,sceneDataLoadedFromMsg:!1};let s;try{s=await triggerSlash(`/messages ${i}`)}catch(e){return{playerStateLoadedFromMsg:!1,sceneDataLoadedFromMsg:!1}}if(!s||""===s.trim())return{playerStateLoadedFromMsg:!1,sceneDataLoadedFromMsg:!1};let o=!1,r=!1;n=null,t=[];const l=s.match(new RegExp(`${J}\\n([\\s\\S]*?)\\n${Q}`));if(l&&l[1])try{const t=JSON.parse(l[1]);t&&"string"==typeof t.name&&t.attributes&&(e=t,o=!0)}catch(e){}const c=s.match(new RegExp(`${F}\\n([\\s\\S]*?)\\n${P}`));if(c&&c[1]){const e=c[1].trim();e&&e.split(new RegExp(Y.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"g")).forEach((e=>{let n;const i=e.match(/\n\[玩家选择文本\]: "([^"]+)"$/);let a=e.trim();i&&i[1]&&(n=i[1],a=e.substring(0,e.lastIndexOf("\n[玩家选择文本]:")).trim()),a&&t.push({rawSceneBlock:a,playerChoiceText:n,timestamp:0})}))}const d=s.lastIndexOf("查看系统\nmsg_start");if(-1!==d){const e=s.substring(d);if(-1!==e.indexOf("msg_end\n关闭系统")){const t=e.match(/msg_start([\s\S]*?)msg_end\s*关闭系统/);if(t&&t[1]){const e=t[1].trim();if(e){const t=O(e);t&&t.rawInputBlock&&(n=t,r=!0)}}}}if(!r&&t.length>0){const e=O(t[t.length-1].rawSceneBlock);e&&e.rawInputBlock&&(n=e,r=!0)}return{playerStateLoadedFromMsg:o,sceneDataLoadedFromMsg:r}}();H.playerStateLoadedFromMsg&&H.sceneDataLoadedFromMsg&&n?(R(n),N(),a.style.display="none",o.style.display="flex"):(a.style.display="flex",o.style.display="none",N())}),200)}"complete"===document.readyState||"interactive"===document.readyState?W():window.addEventListener("DOMContentLoaded",W);</script></body></html>