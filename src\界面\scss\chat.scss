.chat-interface {
  flex-grow: 1; // 占据剩余空间
  display: flex;
  flex-direction: column;
  padding: 10px; // 恢复统一内边距
  overflow: hidden; // 确保聊天内容不超出

  .chat-messages {
    flex-grow: 1;
    overflow-y: auto; // 消息过多时可滚动
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;

    .message {
      max-width: 70%;
      padding: 8px 12px;
      border-radius: 18px;
      margin-bottom: 8px;
      word-wrap: break-word; // 长单词换行

      p {
        margin: 0;
      }

      &.received {
        background-color: #e9e9eb; // 接收消息背景色
        align-self: flex-start; // 左对齐
        border-bottom-left-radius: 4px;
      }

      &.sent {
        background-color: #007bff; // 发送消息背景色
        color: white;
        align-self: flex-end; // 右对齐
        border-bottom-right-radius: 4px;
      }
    }
  }

  .chat-input {
    display: flex;
    flex-shrink: 0; // 防止输入框在flex布局中被压缩

    input { // 修改选择器，不再依赖 type="text"
      flex-grow: 1;
      padding: 10px;
      border-top: 1px solid #ccc;
      border-bottom: 1px solid #ccc;
      border-left: 1px solid #ccc;
      border-right: none; // 明确移除右边框
      border-radius: 20px 0 0 20px; // 左侧圆角
      margin-right: 0; // 移除间距
      outline: none;
    }

    button {
      padding: 10px 15px;
      background-color: #007bff;
      color: white;
      border-top: 1px solid #007bff;
      border-bottom: 1px solid #007bff;
      border-right: 1px solid #007bff;
      border-left: none; // 明确移除左边框
      border-radius: 0 20px 20px 0; // 右侧圆角
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background-color: #0056b3;
      }
    }
  }
}
